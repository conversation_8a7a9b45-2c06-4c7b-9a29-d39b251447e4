#!/usr/bin/env python3
"""
Unified Chess Bot - A comprehensive chess AI with multiple advanced features
Combines the best aspects of all previous implementations
"""

import chess
import chess.engine
import chess.polyglot  # <--- ADD THIS LINE
import random
import time
import math
from typing import Optional, Tuple, List, Dict
from collections import defaultdict

class UnifiedChessBot:
    """
    A unified chess bot that combines multiple advanced features:
    - Minimax with alpha-beta pruning
    - Iterative deepening
    - Quiescence search
    - Transposition tables
    - Advanced evaluation with piece-square tables
    - Opening book knowledge
    """
    
    def __init__(self, depth: int = 4, name: str = "UnifiedChessBot", 
                 use_iterative_deepening: bool = True, 
                 use_quiescence_search: bool = True,
                 use_transposition_table: bool = True,
                 max_quiescence_depth: int = 8): # can lower from 6 to 4 so can limit search depth to improve performance 
        """
        Initialize the unified chess bot.
        
        Args:
            depth: Maximum search depth
            name: Name of the bot
            use_iterative_deepening: Enable iterative deepening
            use_quiescence_search: Enable quiescence search
            use_transposition_table: Enable transposition tables
            max_quiescence_depth: Maximum depth for quiescence search
        """
        self.depth = depth
        self.name = name
        self.use_iterative_deepening = use_iterative_deepening
        self.use_quiescence_search = use_quiescence_search
        self.use_transposition_table = use_transposition_table
        self.max_quiescence_depth = max_quiescence_depth
        
        # Statistics
        self.nodes_evaluated = 0
        self.transposition_hits = 0
        self.quiescence_nodes = 0
        
        # Move quality tracking
        self.move_quality_history = []  # List of (move_san, search_score, static_score, depth_used, time_taken)
        
        # Transposition table: key = board hash, value = (depth, score, flag, best_move)
        # flag: 0 = exact, 1 = lower bound, 2 = upper bound
        self.transposition_table = {}
        self.max_table_size = 100000
        
        # Performance optimization: evaluation cache for move quality assessment
        self.evaluation_cache = {}
        self.max_cache_size = 10000
        self.use_lightweight_human_evaluation = True  # Use faster evaluation for human moves
        self.human_evaluation_depth = 1  # Depth for human move evaluation (0=static, 1=shallow, 2=full)
        
        # Piece values
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
        
        # Advanced piece-square tables
        self._init_piece_square_tables()
        
        # Opening book (simple)
        self._init_opening_book()
        
        # Testing flags
        self.use_opening_book = True
        
    def _init_piece_square_tables(self):
        """Initialize piece-square tables for positional evaluation."""
        
        self.pawn_table = [
            0,  0,  0,  0,  0,  0,  0,  0,   # Rank 1
            5,  5,  5,  5,  5,  5,  5,  5,   # Rank 2
            10, 10, 20, 30, 30, 20, 10, 10,  # Rank 3
            15, 15, 25, 35, 35, 25, 15, 15,  # Rank 4
            20, 20, 30, 40, 40, 30, 20, 20,  # Rank 5
            35, 35, 40, 50, 50, 40, 35, 35,  # Rank 6
            50, 50, 60, 70, 70, 60, 50, 50,  # Rank 7
            0,  0,  0,  0,  0,  0,  0,  0    # Rank 8
        ]
        
        self.knight_table = [
            -50,-40,-30,-30,-30,-30,-40,-50,
            -40,-20,  0,  0,  0,  0,-20,-40,
            -30,  0, 10, 15, 15, 10,  0,-30,
            -30,  5, 15, 20, 20, 15,  5,-30,
            -30,  0, 15, 20, 20, 15,  0,-30,
            -30,  5, 10, 15, 15, 10,  5,-30,
            -40,-20,  0,  5,  5,  0,-20,-40,
            -50,-40,-30,-30,-30,-30,-40,-50
        ]

        self.bishop_table = [
            -20,-10,-10,-10,-10,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5, 10, 10,  5,  0,-10,
            -10,  5,  5, 10, 10,  5,  5,-10,
            -10,  0, 10, 10, 10, 10,  0,-10,
            -10, 10, 10, 10, 10, 10, 10,-10,
            -10,  5,  0,  0,  0,  0,  5,-10,
            -20,-10,-10,-10,-10,-10,-10,-20
        ]

        self.rook_table = [
             0,  0,  0,  0,  0,  0,  0,  0,
             5, 10, 10, 10, 10, 10, 10,  5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
             0,  0,  0,  5,  5,  0,  0,  0
        ]

        self.queen_table = [
            -20,-10,-10, -5, -5,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5,  5,  5,  5,  0,-10,
             -5,  0,  5,  5,  5,  5,  0, -5,
              0,  0,  5,  5,  5,  5,  0, -5,
            -10,  5,  5,  5,  5,  5,  0,-10,
            -10,  0,  5,  0,  0,  0,  0,-10,
            -20,-10,-10, -5, -5,-10,-10,-20
        ]

        # King tables for middlegame and endgame
        self.king_mg_table = [
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -20,-30,-30,-40,-40,-30,-30,-20,
            -10,-20,-20,-20,-20,-20,-20,-10,
             20, 20,  0,  0,  0,  0, 20, 20,
             20, 30, 10,  0,  0, 10, 30, 20
        ]
        
        self.king_eg_table = [
            -50,-40,-30,-20,-20,-30,-40,-50,
            -30,-20,-10,  0,  0,-10,-20,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-30,  0,  0,  0,  0,-30,-30,
            -50,-30,-30,-30,-30,-30,-30,-50
        ]
        
    def _init_opening_book(self):
        """Initialize a simple opening book."""
        self.opening_moves = {
            # Common opening moves from starting position
            chess.Board().fen(): ["e2e4", "d2d4", "g1f3", "c2c4"],
            # Italian Game continuation
            "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1": ["e7e5"],
            "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2": ["g1f3"],
            # Add more as needed
        }
        
    def _is_endgame(self, board: chess.Board) -> bool:
        """Determine if the game is in endgame phase."""
        # Count major pieces
        white_queens = len(board.pieces(chess.QUEEN, chess.WHITE))
        black_queens = len(board.pieces(chess.QUEEN, chess.BLACK))
        
        # No queens = endgame
        if white_queens == 0 and black_queens == 0:
            return True
        
        # Few pieces remaining
        white_material = sum(len(board.pieces(pt, chess.WHITE)) 
                           for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])
        black_material = sum(len(board.pieces(pt, chess.BLACK)) 
                           for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])
        
        return (white_material + black_material) <= 6
        
    def _get_piece_square_value(self, piece: chess.Piece, square: int, is_endgame: bool) -> int:
        """Get piece-square table value for a piece on a square."""
        # Mirror square for black pieces
        sq = square if piece.color == chess.WHITE else chess.square_mirror(square)
        
        if piece.piece_type == chess.PAWN:
            return self.pawn_table[sq]
        elif piece.piece_type == chess.KNIGHT:
            return self.knight_table[sq]
        elif piece.piece_type == chess.BISHOP:
            return self.bishop_table[sq]
        elif piece.piece_type == chess.ROOK:
            return self.rook_table[sq]
        elif piece.piece_type == chess.QUEEN:
            return self.queen_table[sq]
        elif piece.piece_type == chess.KING:
            table = self.king_eg_table if is_endgame else self.king_mg_table
            return table[sq]
        
        return 0
        
    def evaluate_board(self, board: chess.Board) -> int:
        """
        Comprehensive board evaluation function.
        
        Args:
            board: Chess board to evaluate
            
        Returns:
            Evaluation score (positive for white advantage)
        """
        if board.is_checkmate():
            return -20000 if board.turn == chess.WHITE else 20000
        
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        is_endgame = self._is_endgame(board)
        
        # Material and positional evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                # Base piece value
                value = self.piece_values[piece.piece_type]
                
                # Add positional bonus
                value += self._get_piece_square_value(piece, square, is_endgame)
                
                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value
        
        # Mobility evaluation
        original_turn = board.turn
        
        board.turn = chess.WHITE
        white_mobility = len(list(board.legal_moves))
        board.turn = chess.BLACK
        black_mobility = len(list(board.legal_moves))
        board.turn = original_turn
        
        mobility_score = (white_mobility - black_mobility) * 3
        score += mobility_score
        
        # King safety in middlegame
        if not is_endgame:
            # Penalize exposed king
            white_king_sq = board.king(chess.WHITE)
            black_king_sq = board.king(chess.BLACK)
            
            if white_king_sq and not board.has_castling_rights(chess.WHITE):
                if chess.square_file(white_king_sq) in [3, 4]:  # King on central files
                    score -= 30
                    
            if black_king_sq and not board.has_castling_rights(chess.BLACK):
                if chess.square_file(black_king_sq) in [3, 4]:  # King on central files
                    score += 30
        
        # Pawn structure bonuses
        score += self._evaluate_pawn_structure(board)
        
        # Center control
        score += self._evaluate_center_control(board)
        
        return score
        
    def _evaluate_pawn_structure(self, board: chess.Board) -> int:
        """Evaluate pawn structure."""
        score = 0
        
        # Doubled pawns penalty
        for file in range(8):
            white_pawns = len([sq for sq in chess.SquareSet(chess.BB_FILES[file]) 
                             if board.piece_at(sq) and board.piece_at(sq).piece_type == chess.PAWN 
                             and board.piece_at(sq).color == chess.WHITE])
            black_pawns = len([sq for sq in chess.SquareSet(chess.BB_FILES[file]) 
                             if board.piece_at(sq) and board.piece_at(sq).piece_type == chess.PAWN 
                             and board.piece_at(sq).color == chess.BLACK])
            
            if white_pawns > 1:
                score -= (white_pawns - 1) * 20
            if black_pawns > 1:
                score += (black_pawns - 1) * 20
        
        return score
        
    def _evaluate_center_control(self, board: chess.Board) -> int:
        """Evaluate center control."""
        center_squares = [chess.D4, chess.D5, chess.E4, chess.E5]
        score = 0
        
        for square in center_squares:
            piece = board.piece_at(square)
            if piece:
                if piece.color == chess.WHITE:
                    score += 10
                else:
                    score -= 10
        
        return score
    
    def _get_cached_evaluation(self, board: chess.Board) -> Optional[int]:
        """Get cached evaluation if available."""
        board_hash = chess.polyglot.zobrist_hash(board)
        return self.evaluation_cache.get(board_hash)
    
    def _cache_evaluation(self, board: chess.Board, evaluation: int):
        """Cache an evaluation result."""
        if len(self.evaluation_cache) >= self.max_cache_size:
            # Remove oldest entries (simple FIFO)
            oldest_key = next(iter(self.evaluation_cache))
            del self.evaluation_cache[oldest_key]
        
        board_hash = chess.polyglot.zobrist_hash(board)
        self.evaluation_cache[board_hash] = evaluation
    
    def _lightweight_evaluation(self, board: chess.Board) -> int:
        """
        Improved lightweight evaluation for human move quality assessment.
        Balances accuracy with performance by including key positional factors.
        """
        # Check cache first
        cached_eval = self._get_cached_evaluation(board)
        if cached_eval is not None:
            return cached_eval
        
        # Quick game state checks
        if board.is_checkmate():
            result = -20000 if board.turn == chess.WHITE else 20000
            self._cache_evaluation(board, result)
            return result
        
        if board.is_stalemate() or board.is_insufficient_material():
            self._cache_evaluation(board, 0)
            return 0
        
        score = 0
        is_endgame = self._is_endgame(board)
        
        # Material and basic positional evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                # Base piece value
                value = self.piece_values[piece.piece_type]
                
                # Add simplified positional bonus (faster than full piece-square tables)
                if piece.piece_type == chess.PAWN:
                    # Encourage pawn advancement
                    rank = chess.square_rank(square)
                    if piece.color == chess.WHITE:
                        value += rank * 5
                    else:
                        value += (7 - rank) * 5
                elif piece.piece_type == chess.KNIGHT:
                    # Knights prefer center
                    file, rank = chess.square_file(square), chess.square_rank(square)
                    center_distance = abs(3.5 - file) + abs(3.5 - rank)
                    value += max(0, 20 - int(center_distance * 5))
                elif piece.piece_type == chess.BISHOP:
                    # Bishops prefer long diagonals
                    if square in [chess.A1, chess.H8, chess.A8, chess.H1]:
                        value -= 10
                elif piece.piece_type == chess.KING:
                    # King safety in middlegame, activity in endgame
                    if not is_endgame:
                        file = chess.square_file(square)
                        if file in [0, 1, 6, 7]:  # Safer on sides
                            value += 10
                    else:
                        # King activity in endgame
                        file, rank = chess.square_file(square), chess.square_rank(square)
                        center_distance = abs(3.5 - file) + abs(3.5 - rank)
                        value += max(0, 15 - int(center_distance * 3))
                
                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value
        
        # Mobility evaluation (simplified)
        original_turn = board.turn
        
        board.turn = chess.WHITE
        white_mobility = len(list(board.legal_moves))
        board.turn = chess.BLACK
        black_mobility = len(list(board.legal_moves))
        board.turn = original_turn
        
        mobility_score = (white_mobility - black_mobility) * 3
        score += mobility_score
        
        # Basic center control
        center_squares = [chess.D4, chess.D5, chess.E4, chess.E5]
        for square in center_squares:
            piece = board.piece_at(square)
            if piece:
                if piece.color == chess.WHITE:
                    score += 10
                else:
                    score -= 10
        
        # King safety and piece development checks (simplified)
        if not is_endgame:
            white_king_sq = board.king(chess.WHITE)
            black_king_sq = board.king(chess.BLACK)
            
            if white_king_sq and not board.has_castling_rights(chess.WHITE):
                if chess.square_file(white_king_sq) in [3, 4]:  # King on central files
                    score -= 30
                    
            if black_king_sq and not board.has_castling_rights(chess.BLACK):
                if chess.square_file(black_king_sq) in [3, 4]:  # King on central files
                    score += 30
            
            # Penalize early queen development (anti-pattern detection)
            white_queen_sq = None
            black_queen_sq = None
            for square in chess.SQUARES:
                piece = board.piece_at(square)
                if piece and piece.piece_type == chess.QUEEN:
                    if piece.color == chess.WHITE:
                        white_queen_sq = square
                    else:
                        black_queen_sq = square
            
            # Check if queen moved early (before minor pieces are developed)
            if white_queen_sq and white_queen_sq != chess.D1:
                # Count undeveloped minor pieces
                undeveloped = 0
                for sq in [chess.B1, chess.C1, chess.F1, chess.G1]:  # Starting squares for minor pieces
                    piece = board.piece_at(sq)
                    if piece and piece.color == chess.WHITE and piece.piece_type in [chess.KNIGHT, chess.BISHOP]:
                        undeveloped += 1
                
                if undeveloped >= 2:  # Queen out too early
                    # More severe penalty for very early queen development
                    queen_rank = chess.square_rank(white_queen_sq)
                    if queen_rank >= 4:  # Queen advanced far
                        score -= 200
                    else:
                        score -= 120
            
            if black_queen_sq and black_queen_sq != chess.D8:
                # Count undeveloped minor pieces
                undeveloped = 0
                for sq in [chess.B8, chess.C8, chess.F8, chess.G8]:  # Starting squares for minor pieces
                    piece = board.piece_at(sq)
                    if piece and piece.color == chess.BLACK and piece.piece_type in [chess.KNIGHT, chess.BISHOP]:
                        undeveloped += 1
                
                if undeveloped >= 2:  # Queen out too early
                    # More severe penalty for very early queen development
                    queen_rank = chess.square_rank(black_queen_sq)
                    if queen_rank <= 3:  # Queen advanced far (for black)
                        score += 200
                    else:
                        score += 120
        
        # Cache and return
        self._cache_evaluation(board, score)
        return score
        
    def _order_moves(self, board: chess.Board, moves: List[chess.Move], 
                    hash_move: Optional[chess.Move] = None) -> List[chess.Move]:
        """Order moves for better alpha-beta pruning."""
        def move_score(move):
            score = 0
            
            # Hash move gets highest priority
            if hash_move and move == hash_move:
                return 10000
            
            # Captures
            if board.is_capture(move):
                captured_piece = board.piece_at(move.to_square)
                moving_piece = board.piece_at(move.from_square)
                if captured_piece and moving_piece:
                    # MVV-LVA (Most Valuable Victim - Least Valuable Attacker)
                    score += (self.piece_values[captured_piece.piece_type] - 
                             self.piece_values[moving_piece.piece_type] // 10)
            
            # Promotions
            if move.promotion:
                score += self.piece_values[move.promotion]
            
            # Checks
            board.push(move)
            if board.is_check():
                score += 50
            board.pop()
            
            # Castle
            if board.is_castling(move):
                score += 30
                
            return score
        
        return sorted(moves, key=move_score, reverse=True)
        
    def _quiescence_search(self, board: chess.Board, alpha: int, beta: int, depth: int) -> int:
        """
        Quiescence search to handle tactical sequences, consistent with minimax.
        Only considers captures and checks to avoid horizon effect.
        """
        self.quiescence_nodes += 1
    
        # Use the static evaluation as a baseline
        evaluation = self.evaluate_board(board)
    
        if depth <= 0:
            return evaluation
    
        # --- White's turn (Maximizer) ---
        if board.turn == chess.WHITE:
            if evaluation >= beta:
                return beta  # Fail-high
            alpha = max(alpha, evaluation)
            
            moves = [move for move in board.legal_moves if board.is_capture(move) or board.gives_check(move)]
            moves = self._order_moves(board, moves)
    
            for move in moves:
                board.push(move)
                score = self._quiescence_search(board, alpha, beta, depth - 1)
                board.pop()
                
                alpha = max(alpha, score)
                if alpha >= beta:
                    return beta  # Pruning
            return alpha
            
        # --- Black's turn (Minimizer) ---
        else:
            if evaluation <= alpha:
                return alpha  # Fail-low
            beta = min(beta, evaluation)
    
            moves = [move for move in board.legal_moves if board.is_capture(move) or board.gives_check(move)]
            moves = self._order_moves(board, moves)
    
            for move in moves:
                board.push(move)
                score = self._quiescence_search(board, alpha, beta, depth - 1)
                board.pop()
    
                beta = min(beta, score)
                if beta <= alpha:
                    return alpha  # Pruning
            return beta
        
    def _store_transposition(self, board: chess.Board, depth: int, score: int, 
                           flag: int, best_move: Optional[chess.Move]):
        """Store position in transposition table."""
        if not self.use_transposition_table:
            return
            
        # Use a simple hash of the FEN as key (fallback method)
        key = chess.polyglot.zobrist_hash(board)
        
        # Clear table if it gets too large
        if len(self.transposition_table) > self.max_table_size:
            self.transposition_table.clear()
            
        self.transposition_table[key] = (depth, score, flag, best_move)
        
    def _probe_transposition(self, board: chess.Board, depth: int, alpha: int, beta: int) -> Tuple[Optional[int], Optional[chess.Move]]:
        """Probe transposition table."""
        if not self.use_transposition_table:
            return None, None
            
        # Use a simple hash of the FEN as key (fallback method)
        key = chess.polyglot.zobrist_hash(board)
        
        if key in self.transposition_table:
            stored_depth, stored_score, flag, best_move = self.transposition_table[key]
            
            if stored_depth >= depth:
                self.transposition_hits += 1
                
                if flag == 0:  # Exact score
                    return stored_score, best_move
                elif flag == 1 and stored_score >= beta:  # Lower bound
                    return stored_score, best_move
                elif flag == 2 and stored_score <= alpha:  # Upper bound
                    return stored_score, best_move
                    
            return None, best_move  # Return best move even if depth doesn't match
            
        return None, None
        
    def _minimax(self, board: chess.Board, depth: int, alpha: int, beta: int, 
                maximizing_player: bool) -> Tuple[int, Optional[chess.Move]]:
        """
        Enhanced minimax with alpha-beta pruning, transposition tables, and quiescence search.
        """
        self.nodes_evaluated += 1
        
        # Probe transposition table
        tt_score, tt_move = self._probe_transposition(board, depth, alpha, beta)
        if tt_score is not None:
            return tt_score, tt_move
            
        if depth == 0 or board.is_game_over():
            if self.use_quiescence_search and depth == 0 and not board.is_game_over():
                score = self._quiescence_search(board, alpha, beta, self.max_quiescence_depth)
            else:
                score = self.evaluate_board(board)
            return score, None
        
        moves = list(board.legal_moves)
        moves = self._order_moves(board, moves, tt_move)
        
        best_move = None
        original_alpha = alpha
        
        if maximizing_player:
            max_eval = float('-inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self._minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            # Store in transposition table
            if max_eval <= original_alpha:
                flag = 2  # Upper bound
            elif max_eval >= beta:
                flag = 1  # Lower bound
            else:
                flag = 0  # Exact
            self._store_transposition(board, depth, max_eval, flag, best_move)
            
            return max_eval, best_move
        else:
            min_eval = float('inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self._minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            # Store in transposition table
            if min_eval <= original_alpha:
                flag = 2  # Upper bound
            elif min_eval >= beta:
                flag = 1  # Lower bound
            else:
                flag = 0  # Exact
            self._store_transposition(board, depth, min_eval, flag, best_move)
            
            return min_eval, best_move
            
    def _iterative_deepening(self, board: chess.Board, max_time: float = 10.0) -> Tuple[Optional[chess.Move], int]:
        """
        Iterative deepening search with time management.
        Returns both the best move and its evaluation score.
        """
        start_time = time.time()
        best_move = None
        best_score = 0
        
        for current_depth in range(1, self.depth + 1):
            if time.time() - start_time > max_time:
                break
                
            maximizing = board.turn == chess.WHITE
            try:
                score, move = self._minimax(board, current_depth, float('-inf'), float('inf'), maximizing)
                if move:
                    best_move = move
                    best_score = score
            except:
                break  # Time's up or other issue
                
        return best_move, best_score
        
    def get_best_move(self, board: chess.Board, record_quality: bool = True) -> Optional[chess.Move]:
        """
        Get the best move for the current position.
        
        Args:
            board: Current board position
            record_quality: Whether to record move quality (default True)
            
        Returns:
            Best move according to the bot's evaluation
        """
        move, _ = self.get_best_move_with_score(board, record_quality=record_quality)
        return move
    
    def get_best_move_with_score(self, board: chess.Board, record_quality: bool = True) -> Tuple[Optional[chess.Move], int]:
        """
        Get the best move and its evaluation score for the current position.
        
        Args:
            board: Current board position
            record_quality: Whether to record move quality (default True)
            
        Returns:
            Tuple of (best_move, evaluation_score)
        """
        # Reset statistics
        self.nodes_evaluated = 0
        self.transposition_hits = 0
        self.quiescence_nodes = 0
        
        start_time = time.time()
        best_score = 0
        depth_used = self.depth
        
        # Check opening book first (if enabled)
        if self.use_opening_book:
            fen = board.fen()
            if fen in self.opening_moves:
                move_uci = random.choice(self.opening_moves[fen])
                try:
                    move = chess.Move.from_uci(move_uci)
                    if move in board.legal_moves:
                        print(f"{self.name} played from opening book: {move}")
                        # For opening book moves, return static evaluation
                        best_score = self.evaluate_board(board)
                        elapsed_time = time.time() - start_time
                        
                        # Record move quality
                        if record_quality:
                            self._record_move_quality(board, move, best_score, best_score, 0, elapsed_time, "Opening Book")
                        
                        return move, best_score
                except:
                    pass
        
        # Use iterative deepening if enabled
        if self.use_iterative_deepening:
            best_move, best_score = self._iterative_deepening(board)
        else:
            maximizing = board.turn == chess.WHITE
            best_score, best_move = self._minimax(board, self.depth, float('-inf'), float('inf'), maximizing)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # Fallback to random move
        if best_move is None:
            legal_moves = list(board.legal_moves)
            if legal_moves:
                best_move = random.choice(legal_moves)
                best_score = self.evaluate_board(board)
                depth_used = 0
        
        # Record move quality for analysis
        if best_move and record_quality:
            static_score = self.evaluate_board(board)
            self._record_move_quality(board, best_move, best_score, static_score, depth_used, elapsed_time)
        
        # Print statistics
        print(f"{self.name} statistics:")
        print(f"  - Time: {elapsed_time:.2f}s")
        print(f"  - Nodes evaluated: {self.nodes_evaluated}")
        print(f"  - Quiescence nodes: {self.quiescence_nodes}")
        print(f"  - Search evaluation: {best_score}")
        if self.use_transposition_table:
            print(f"  - Transposition hits: {self.transposition_hits}")
            print(f"  - Table size: {len(self.transposition_table)}")
        
        return best_move, best_score
    
    def _record_move_quality(self, board: chess.Board, move: chess.Move, search_score: int, 
                           static_score: int, depth_used: int, time_taken: float, move_type: str = "Search"):
        """
        Record move quality information for debugging and analysis.
        
        Args:
            board: Current board position
            move: The move that was chosen
            search_score: Score from the search algorithm
            static_score: Static evaluation of the position
            depth_used: Search depth used
            time_taken: Time taken to find the move
            move_type: Type of move (Search, Opening Book, etc.)
        """
        try:
            move_san = board.san(move)
            
            # For opening book moves, always classify as Normal
            if move_type == "Opening Book":
                quality = "Normal"
                score_difference = 0  # No meaningful comparison for opening book moves
                # Store the passed values for opening book moves
                actual_search_score = search_score
                actual_static_score = static_score
            elif move_type == "Human":
                # For human moves, use optimized evaluation to avoid performance issues
                if self.use_lightweight_human_evaluation and self.human_evaluation_depth == 0:
                    # Use lightweight static evaluation for best performance
                    eval_before = self._lightweight_evaluation(board)
                    
                    # Make the human move and evaluate the resulting position
                    board.push(move)
                    eval_after = self._lightweight_evaluation(board)
                    board.pop()
                elif self.use_lightweight_human_evaluation and self.human_evaluation_depth == 1:
                    # Use shallow search (depth 1) for balanced performance and accuracy
                    maximizing_before = board.turn == chess.WHITE
                    eval_before, _ = self._minimax(board, 1, float('-inf'), float('inf'), maximizing_before)
                    
                    # Make the human move and evaluate the resulting position
                    board.push(move)
                    maximizing_after = board.turn == chess.WHITE
                    eval_after, _ = self._minimax(board, 1, float('-inf'), float('inf'), maximizing_after)
                    board.pop()
                else:
                    # Use full search evaluation (original behavior, depth 2)
                    # Get the best move evaluation before making the human move
                    maximizing_before = board.turn == chess.WHITE
                    eval_before, _ = self._minimax(board, 2, float('-inf'), float('inf'), maximizing_before)
                    
                    # Make the human move and evaluate the resulting position
                    board.push(move)
                    maximizing_after = board.turn == chess.WHITE
                    eval_after, _ = self._minimax(board, 2, float('-inf'), float('inf'), maximizing_after)
                    board.pop()
                
                # Convert evaluations to the perspective of the player who just moved
                if not board.turn:  # If black just moved
                    eval_before = -eval_before
                    eval_after = -eval_after
                
                # Score difference: how much the position changed from the moving player's perspective
                # Negative means the position got worse for the player who moved
                score_difference = eval_after - eval_before
                
                # Store the actual evaluation values used for transparency
                actual_search_score = eval_before
                actual_static_score = eval_after
                
                # Classify move quality based on position change with mode-specific thresholds
                if move_type == "Human":
                    # Adjust thresholds based on evaluation mode
                    if self.use_lightweight_human_evaluation and self.human_evaluation_depth == 0:
                        # Static evaluation - more lenient thresholds due to lack of tactical depth
                        if score_difference > 80:
                            quality = "Excellent"
                        elif score_difference > 20:
                            quality = "Good"
                        elif score_difference >= -40:
                            quality = "Normal"
                        elif score_difference >= -80:
                            quality = "Questionable"
                        elif score_difference >= -150:
                            quality = "Poor"
                        else:
                            quality = "Blunder"
                    elif self.use_lightweight_human_evaluation and self.human_evaluation_depth == 1:
                        # Shallow search - balanced thresholds with opening consideration
                        # In opening, small negative differences are normal due to opponent's best response
                        move_count = len(board.move_stack)
                        if move_count <= 10:  # Opening phase
                            # Check if it's a capture or tactical move
                            is_capture = board.is_capture(move)
                            if score_difference > 80:
                                quality = "Excellent"
                            elif score_difference > 0:  # Any positive change is good in opening
                                quality = "Good"
                            elif is_capture and score_difference >= -30:  # Captures are often good even with small negative diff
                                quality = "Good"
                            elif score_difference >= -80:  # More lenient in opening
                                quality = "Normal"
                            elif score_difference >= -150:
                                quality = "Questionable"
                            elif score_difference >= -300:
                                quality = "Poor"
                            else:
                                quality = "Blunder"
                        else:  # Middlegame/Endgame
                            if score_difference > 100:
                                quality = "Excellent"
                            elif score_difference > 20:
                                quality = "Good"
                            elif score_difference >= -40:
                                quality = "Normal"
                            elif score_difference >= -100:
                                quality = "Questionable"
                            elif score_difference >= -200:
                                quality = "Poor"
                            else:
                                quality = "Blunder"
                    else:
                        # Full search - original thresholds
                        if score_difference > 150:
                            quality = "Excellent"
                        elif score_difference > 30:
                            quality = "Good"
                        elif score_difference >= -30:
                            quality = "Normal"
                        elif score_difference >= -100:
                            quality = "Questionable"
                        elif score_difference >= -250:
                            quality = "Poor"
                        else:
                            quality = "Blunder"
                else:
                    # Bot moves - use original thresholds
                    if score_difference > 150:
                        quality = "Excellent"
                    elif score_difference > 30:
                        quality = "Good"
                    elif score_difference >= -30:
                        quality = "Normal"
                    elif score_difference >= -100:
                        quality = "Questionable"
                    elif score_difference >= -250:
                        quality = "Poor"
                    else:
                        quality = "Blunder"
            else:
                # Calculate position evaluation after making the move
                board.push(move)
                position_after_move = self.evaluate_board(board)
                board.pop()
                
                # Adjust for perspective (negate if it's black's turn since evaluation is from white's perspective)
                if not board.turn:  # If it's black's turn
                    position_after_move = -position_after_move
                    static_score = -static_score
                
                # Score difference: how much the position improved after making the move
                score_difference = position_after_move - static_score
                
                # Store the actual evaluation values used for transparency
                actual_search_score = static_score
                actual_static_score = position_after_move
                
                # Classify move quality based on position improvement
                # Consistent thresholds with human moves
                if score_difference > 150:
                    quality = "Excellent"
                elif score_difference > 30:
                    quality = "Good"
                elif score_difference >= -30:
                    quality = "Normal"  # Moves that don't lose much are normal
                elif score_difference >= -100:
                    quality = "Questionable"
                elif score_difference >= -250:
                    quality = "Poor"
                else:
                    quality = "Blunder"
            
            move_info = {
                'move_number': len(self.move_quality_history) + 1,
                'move_san': move_san,
                'move_uci': move.uci(),
                'search_score': actual_search_score,  # Evaluation before the move
                'static_score': actual_static_score,   # Evaluation after the move
                'score_difference': score_difference,
                'depth_used': depth_used,
                'time_taken': time_taken,
                'nodes_evaluated': self.nodes_evaluated,
                'move_type': move_type,
                'quality': quality,
                'position_fen': board.fen()
            }
            
            self.move_quality_history.append(move_info)
            
            # Keep only last 50 moves to prevent memory issues
            if len(self.move_quality_history) > 50:
                self.move_quality_history.pop(0)
                
        except Exception as e:
            print(f"Error recording move quality: {e}")
    
    def get_move_quality_history(self) -> List[Dict]:
        """Get the move quality history for analysis."""
        return self.move_quality_history.copy()
    
    def clear_move_quality_history(self):
        """Clear the move quality history."""
        self.move_quality_history.clear()
        
    def set_depth(self, depth: int):
        """Set the search depth."""
        self.depth = depth
        print(f"{self.name} depth set to {depth}")
        
    def set_features(self, iterative_deepening: bool = None, 
                    quiescence_search: bool = None, 
                    transposition_table: bool = None):
        """Enable/disable bot features."""
        if iterative_deepening is not None:
            self.use_iterative_deepening = iterative_deepening
        if quiescence_search is not None:
            self.use_quiescence_search = quiescence_search
        if transposition_table is not None:
            self.use_transposition_table = transposition_table
            if not transposition_table:
                self.transposition_table.clear()
                
        print(f"{self.name} features updated:")
        print(f"  - Iterative deepening: {self.use_iterative_deepening}")
        print(f"  - Quiescence search: {self.use_quiescence_search}")
        print(f"  - Transposition table: {self.use_transposition_table}")
        
    def get_evaluation(self, board: chess.Board) -> int:
        """Get the evaluation of the current position."""
        return self.evaluate_board(board)
        
    def clear_transposition_table(self):
        """Clear the transposition table."""
        self.transposition_table.clear()
    
    def clear_evaluation_cache(self):
        """Clear the evaluation cache."""
        self.evaluation_cache.clear()
    
    def set_human_evaluation_mode(self, lightweight: bool = True, depth: int = 1):
        """
        Set the evaluation mode for human moves.
        
        Args:
            lightweight: If True, use optimized evaluation modes.
                        If False, use full depth-2 search (slower but most accurate).
            depth: Evaluation depth when lightweight=True (0=static, 1=shallow search, 2=full search)
        """
        self.use_lightweight_human_evaluation = lightweight
        if lightweight:
            self.human_evaluation_depth = depth
            if depth == 0:
                print("Human move evaluation: Static evaluation mode (fastest)")
            elif depth == 1:
                print("Human move evaluation: Shallow search mode (balanced)")
            else:
                print("Human move evaluation: Full search mode (most accurate)")
        else:
            self.human_evaluation_depth = 2
            print("Human move evaluation: Full depth-2 search mode (slowest but most accurate)")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get statistics about the caches."""
        return {
            'evaluation_cache_size': len(self.evaluation_cache),
            'transposition_table_size': len(self.transposition_table),
            'max_evaluation_cache_size': self.max_cache_size,
            'max_transposition_table_size': self.max_table_size
        }


# Preset configurations for different difficulty levels
class EasyBot(UnifiedChessBot):
    """Easy difficulty bot - fast and basic."""
    def __init__(self):
        super().__init__(
            depth=2, 
            name="EasyBot",
            use_iterative_deepening=False,
            use_quiescence_search=False,
            use_transposition_table=False
        )

class MediumBot(UnifiedChessBot):
    """Medium difficulty bot - balanced."""
    def __init__(self):
        super().__init__(
            depth=3,
            name="MediumBot", 
            use_iterative_deepening=True,
            use_quiescence_search=True,
            use_transposition_table=True
        )

class HardBot(UnifiedChessBot):
    """Hard difficulty bot - strong play."""
    def __init__(self):
        super().__init__(
            depth=4,
            name="HardBot",
            use_iterative_deepening=True,
            use_quiescence_search=True,
            use_transposition_table=True
        )

class ExpertBot(UnifiedChessBot):
    """Expert difficulty bot - very strong play."""
    def __init__(self):
        super().__init__(
            depth=5,
            name="ExpertBot",
            use_iterative_deepening=True,
            use_quiescence_search=True,
            use_transposition_table=True,
            max_quiescence_depth=8
        )