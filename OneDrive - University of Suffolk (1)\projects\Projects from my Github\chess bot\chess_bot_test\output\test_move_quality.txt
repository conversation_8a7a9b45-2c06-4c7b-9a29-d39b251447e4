PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_move_quality.py
=== Test Case 1: Opening move d4 ===
Position before: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Static score before: 0
Move: d4
Quality: Good
Score difference: 64

=== Test Case 2: Blunder move Qd2 ===
Position before: rnbqk2r/pppp1ppp/3bp3/8/4n3/8/PPP2PPP/RNBQKBNR w KQkq - 0 5
Static score before: -331
Move: Qd2
Quality: Normal
Score difference: 2

=== Test Case 3: Good move dxe6 ===
Position before: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Static score before: -24
Move: dxe6
Quality: Excellent
Score difference: 119



------------------------



PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_move_quality.py     
=== Test Case 1: Opening move d4 ===
Position before: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Static score before: 0
Move: d4
Quality: Normal
Score difference: 47

=== Test Case 2: Blunder move Qd2 ===
Position before: rnbqk2r/pppp1ppp/3bp3/8/4n3/8/PPP2PPP/RNBQKBNR w KQkq - 0 5
Static score before: -331
Move: Qd2
Quality: Blunder
Score difference: -534

=== Test Case 3: Good move dxe6 ===
Position before: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Static score before: -24
Move: dxe6
Quality: Normal
Score difference: 40


---------------------

move quality fixes:


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_move_quality.py     
=== Test Case 1: Opening move d4 ===
Position before: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Static score before: 0
Move: d4
Quality: Good
Score difference: 47

=== Test Case 2: Blunder move Qd2 ===
Position before: rnbqk2r/pppp1ppp/3bp3/8/4n3/8/PPP2PPP/RNBQKBNR w KQkq - 0 5
Static score before: -331
Move: Qd2
Quality: Blunder
Score difference: -534

=== Test Case 3: Good move dxe6 ===
Position before: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Static score before: -24
Move: dxe6
Quality: Good
Score difference: 40









==================================

UI / test cases:

PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_move_quality.py      
=== Test Case 1: Opening move d4 ===
Position before: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Static score before: 0
Move: d4
Quality: Normal
Score difference: -70

=== Test Case 2: Blunder move Qd2 ===
Position before: rnbqk2r/pppp1ppp/3bp3/8/4n3/8/PPP2PPP/RNBQKBNR w KQkq - 0 5
Static score before: -331
Move: Qd2
Quality: Blunder
Score difference: -570

=== Test Case 3: Good move dxe6 ===
Position before: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Static score before: -24
Move: dxe6
Quality: Good
Score difference: -13