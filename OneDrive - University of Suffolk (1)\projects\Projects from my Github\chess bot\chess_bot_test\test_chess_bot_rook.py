#!/usr/bin/env python3
"""
Unit tests for the chess_bot_rook.py implementation.
"""

import unittest
import chess
import time
from chess_bot_rook import ChessBot

class TestChessBotRook(unittest.TestCase):
    """Test cases for the ChessBot class in chess_bot_rook.py."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.bot = ChessBot(depth=2, name="TestRookBot")
        self.board = chess.Board()
    
    def test_bot_initialization(self):
        """Test bot initialization with default and custom parameters."""
        # Test default initialization
        default_bot = ChessBot()
        self.assertEqual(default_bot.depth, 3)
        self.assertEqual(default_bot.name, "ChessBot")
        
        # Test custom initialization
        custom_bot = ChessBot(depth=4, name="CustomBot")
        self.assertEqual(custom_bot.depth, 4)
        self.assertEqual(custom_bot.name, "CustomBot")
        
        # Test piece values are properly initialized
        self.assertEqual(self.bot.piece_values[chess.PAWN], 100)
        self.assertEqual(self.bot.piece_values[chess.KNIGHT], 320)
        self.assertEqual(self.bot.piece_values[chess.BISHOP], 330)
        self.assertEqual(self.bot.piece_values[chess.ROOK], 500)
        self.assertEqual(self.bot.piece_values[chess.QUEEN], 900)
        self.assertEqual(self.bot.piece_values[chess.KING], 20000)
    
    def test_positional_tables(self):
        """Test that positional tables are properly initialized."""
        # Test pawn table
        self.assertEqual(len(self.bot.pawn_table), 64)
        # Test knight table
        self.assertEqual(len(self.bot.knight_table), 64)
        # Test bishop table
        self.assertEqual(len(self.bot.bishop_table), 64)
        # Test rook table
        self.assertEqual(len(self.bot.rook_table), 64)
        # Test queen table
        self.assertEqual(len(self.bot.queen_table), 64)
        # Test king tables
        self.assertEqual(len(self.bot.king_middle_game_table), 64)
        self.assertEqual(len(self.bot.king_end_game_table), 64)
    
    def test_evaluate_board_starting_position(self):
        """Test board evaluation at starting position."""
        score = self.bot.evaluate_board(self.board, 0)
        # Starting position should be roughly equal
        self.assertAlmostEqual(score, 0, delta=100)
    
    def test_evaluate_board_checkmate(self):
        """Test board evaluation in checkmate position."""
        # Set up a simple checkmate position (Fool's mate)
        self.board = chess.Board("rnb1kbnr/pppp1ppp/8/4p3/6Pq/5P2/PPPPP2P/RNBQKBNR w KQkq - 1 3")
        
        # Verify it's checkmate
        self.assertTrue(self.board.is_checkmate())
        
        # Evaluate the position
        score = self.bot.evaluate_board(self.board, 0)
        
        # Should heavily favor black (negative score)
        self.assertLess(score, -10000)
    
    def test_evaluate_board_stalemate(self):
        """Test board evaluation in stalemate position."""
        # Set up a stalemate position directly
        # This is a known stalemate position with black to move
        self.board = chess.Board("8/8/8/8/8/8/p7/k1K5 b - - 0 1")
        
        # Verify it's stalemate
        self.assertTrue(self.board.is_stalemate())
        
        # Evaluate the position
        score = self.bot.evaluate_board(self.board, 0)
        
        # Stalemate should be evaluated as 0
        self.assertEqual(score, 0)
    
    def test_evaluate_board_material_advantage(self):
        """Test board evaluation with material advantage."""
        # Set up a position with white having a queen advantage
        self.board = chess.Board("rnbk1bnr/pppp1ppp/8/4p3/8/8/PPPPQPPP/RNB1KBNR b KQ - 0 1")
        
        # Evaluate the position
        score = self.bot.evaluate_board(self.board, 0)
        
        # White should have a significant advantage (positive score)
        self.assertGreater(score, 500)
    
    def test_minimax_depth_zero(self):
        """Test minimax with depth 0."""
        score, move = self.bot.minimax(self.board, 0, float('-inf'), float('inf'), True)
        self.assertIsInstance(score, int)
        # At depth 0, move should be None (just evaluation)
        self.assertIsNone(move)
    
    def test_minimax_returns_best_move(self):
        """Test that minimax returns a valid move that improves position."""
        # Set up a position where white has a clear advantage
        self.board = chess.Board("rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")
        
        # Evaluate the current position
        initial_score = self.bot.evaluate_board(self.board, 0)
        
        # Get the best move using minimax
        score, move = self.bot.minimax(self.board, 2, float('-inf'), float('inf'), True)
        
        # Should return a valid move
        self.assertIsNotNone(move)
        self.assertIn(move, self.board.legal_moves)
        
        # Make the move and evaluate the new position
        self.board.push(move)
        new_score = self.bot.evaluate_board(self.board, 0)
        
        # The score should be at least as good as before
        # (Note: we're comparing the raw evaluation, not the minimax score which includes lookahead)
        self.assertGreaterEqual(new_score, initial_score - 50)  # Allow small fluctuation
    
    def test_get_best_move_returns_legal_move(self):
        """Test that get_best_move returns a legal move."""
        move = self.bot.get_best_move(self.board)
        self.assertIsNotNone(move)
        self.assertIn(move, self.board.legal_moves)
    
    def test_get_best_move_improves_position(self):
        """Test that bot makes moves that improve its position."""
        # Set up a position where white has a clear advantage
        self.board = chess.Board("rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")
        
        # Evaluate the current position
        initial_score = self.bot.evaluate_board(self.board, 0)
        
        # Get the best move
        move = self.bot.get_best_move(self.board)
        
        # Should return a valid move
        self.assertIsNotNone(move)
        self.assertIn(move, self.board.legal_moves)
        
        # Make the move and evaluate the new position
        self.board.push(move)
        new_score = self.bot.evaluate_board(self.board, 0)
        
        # The score should not get significantly worse
        # (Note: we're comparing raw evaluations, not considering opponent's response)
        self.assertGreaterEqual(new_score, initial_score - 50)  # Allow small fluctuation
    
    def test_set_depth(self):
        """Test setting bot depth."""
        original_depth = self.bot.depth
        new_depth = 4
        self.bot.set_depth(new_depth)
        self.assertEqual(self.bot.depth, new_depth)
        self.assertNotEqual(self.bot.depth, original_depth)
    
    def test_nodes_evaluated_counter(self):
        """Test that nodes_evaluated counter works correctly."""
        # Reset counter
        self.bot.nodes_evaluated = 0
        
        # Get a move
        self.bot.get_best_move(self.board)
        
        # Should have evaluated some nodes
        self.assertGreater(self.bot.nodes_evaluated, 0)
    
    def test_positional_evaluation(self):
        """Test that positional evaluation works correctly."""
        # Create a position where a knight is in a good position vs a bad position
        good_position = chess.Board("rnbqkbnr/pppp1ppp/8/4p3/4N3/8/PPPPPPPP/RNBQKB1R w KQkq - 0 1")
        bad_position = chess.Board("rnbqkbnr/pppp1ppp/8/4p3/8/7N/PPPPPPPP/RNBQKB1R w KQkq - 0 1")
        
        # Evaluate both positions
        good_score = self.bot.evaluate_board(good_position, 0)
        bad_score = self.bot.evaluate_board(bad_position, 0)
        
        # The good position should be evaluated higher
        self.assertGreater(good_score, bad_score)
    
    def test_end_game_detection(self):
        """Test that end game is correctly detected."""
        # Create an endgame position with few pieces
        endgame_position = chess.Board("4k3/8/8/8/8/8/4P3/4K3 w - - 0 1")
        
        # Create a middle game position with more pieces
        middlegame_position = chess.Board("r1bqkb1r/pppp1ppp/2n2n2/4p3/4P3/2N2N2/PPPP1PPP/R1BQKB1R w KQkq - 0 1")
        
        # Evaluate both positions to trigger end game detection
        self.bot.evaluate_board(endgame_position, 0)
        self.bot.evaluate_board(middlegame_position, 0)
        
        # No explicit assertion since we can't directly test the internal is_end_game variable,
        # but this ensures the code path is executed
    
    def test_move_ordering(self):
        """Test that move ordering prioritizes captures."""
        # Create a position with captures available
        position = chess.Board("rnbqkbnr/ppp2ppp/8/3pp3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 1")
        
        # Get all legal moves
        legal_moves = list(position.legal_moves)
        
        # Sort them as the bot would
        sorted_moves = sorted(legal_moves, key=lambda move: position.is_capture(move), reverse=True)
        
        # The first moves should be captures
        for i in range(len(sorted_moves)):
            if position.is_capture(sorted_moves[i]):
                # All captures should come before non-captures
                for j in range(i+1, len(sorted_moves)):
                    if not position.is_capture(sorted_moves[j]):
                        # This is the expected behavior
                        break
                break


def run_performance_test():
    """Run a performance test to check bot speed."""
    print("\nRunning performance test...")
    
    bot = ChessBot(depth=3, name="PerfBot")
    board = chess.Board()
    
    start_time = time.time()
    
    # Test 3 moves
    for i in range(3):
        move = bot.get_best_move(board)
        if move:
            board.push(move)
        else:
            break
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"Performance test completed:")
    print(f"- 3 moves calculated in {total_time:.2f} seconds")
    print(f"- Average time per move: {total_time/3:.2f} seconds")
    print(f"- Total nodes evaluated: {bot.nodes_evaluated}")


if __name__ == "__main__":
    # Run unit tests
    print("Running Chess Bot Rook Unit Tests...")
    unittest.main(verbosity=2, exit=False)
    
    # Run performance test
    run_performance_test()