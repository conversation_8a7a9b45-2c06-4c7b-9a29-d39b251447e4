import chess
import chess.pgn
from typing import Optional, List
from chess_bot import ChessBot
from utils import (
    format_board_unicode, parse_move_input, get_move_quality_description,
    save_game_pgn, analyze_position, format_analysis, get_opening_name
)

class ChessGame:
    """
    Main chess game class that handles game flow and user interaction.
    """
    
    def __init__(self):
        """Initialize a new chess game."""
        self.board = chess.Board()
        self.bot = ChessBot(depth=3, name="ChessBot")
        self.game_history = []
        self.human_color = chess.WHITE  # Human plays white by default
    
    def display_board(self):
        """Display the current board state."""
        print("\n" + "="*60)
        print("Current Board Position:")
        print("="*60)

        # Use Unicode board display from utils
        print(format_board_unicode(self.board))

        # Display game status
        print(f"\nTurn: {'White' if self.board.turn == chess.WHITE else 'Black'}")

        # Show opening name
        if len(self.game_history) <= 8:  # First few moves
            opening = get_opening_name(self.board, self.game_history)
            print(f"Opening: {opening}")

        if self.board.is_check():
            print("CHECK!")

        if self.board.is_checkmate():
            winner = "Black" if self.board.turn == chess.WHITE else "White"
            print(f"CHECKMATE! {winner} wins!")
        elif self.board.is_stalemate():
            print("STALEMATE! Game is a draw.")
        elif self.board.is_insufficient_material():
            print("Insufficient material! Game is a draw.")

        print("="*60)
    
    def get_human_move(self) -> Optional[chess.Move]:
        """
        Get a move from the human player.
        
        Returns:
            Valid chess move or None if invalid input
        """
        while True:
            try:
                print(f"\nYour turn ({'White' if self.human_color == chess.WHITE else 'Black'})")
                print("Enter your move in algebraic notation (e.g., 'e2e4', 'Nf3', 'O-O')")
                print("Type 'quit' to exit, 'help' for move format help, 'moves' to see legal moves")
                print("Type 'analysis' to see position analysis")

                user_input = input("Your move: ").strip()

                if user_input.lower() == 'quit':
                    return None

                if user_input.lower() == 'help':
                    self.show_help()
                    continue

                if user_input.lower() == 'moves':
                    self.show_legal_moves()
                    continue

                if user_input.lower() == 'analysis':
                    self.show_position_analysis()
                    continue

                # Use improved move parsing from utils
                move = parse_move_input(user_input, self.board)

                if move is None:
                    print("Invalid move format. Type 'help' for examples.")
                    continue

                # Check if move is legal
                if move in self.board.legal_moves:
                    return move
                else:
                    print("Illegal move! Try again.")
                    continue
                    
            except KeyboardInterrupt:
                print("\nGame interrupted.")
                return None
            except Exception as e:
                print(f"Error parsing move: {e}")
                continue
    
    def show_help(self):
        """Display help information for move input."""
        print("\n" + "="*40)
        print("MOVE INPUT HELP")
        print("="*40)
        print("You can enter moves in several formats:")
        print("1. Standard Algebraic Notation:")
        print("   - Pawn moves: e4, d5, exd5")
        print("   - Piece moves: Nf3, Bb5, Qh5")
        print("   - Castling: O-O (kingside), O-O-O (queenside)")
        print("   - Captures: Nxf7, exd5")
        print("   - Check: Qh5+")
        print("   - Promotion: e8=Q")
        print()
        print("2. UCI Notation (from-to):")
        print("   - e2e4, g1f3, e7e8q")
        print()
        print("Examples:")
        print("   e4     - Move pawn to e4")
        print("   Nf3    - Move knight to f3")
        print("   O-O    - Castle kingside")
        print("   e2e4   - Move piece from e2 to e4")
        print("="*40)

    def show_position_analysis(self):
        """Display detailed position analysis."""
        analysis = analyze_position(self.board)
        print("\n" + format_analysis(analysis))

    def show_legal_moves(self):
        """Display all legal moves in the current position."""
        legal_moves = list(self.board.legal_moves)
        print(f"\nLegal moves ({len(legal_moves)}):")
        
        # Group moves by piece type for better readability
        move_strings = []
        for move in legal_moves:
            try:
                san = self.board.san(move)
                move_strings.append(san)
            except:
                move_strings.append(str(move))
        
        # Display moves in rows of 8
        for i in range(0, len(move_strings), 8):
            row = move_strings[i:i+8]
            print("  " + "  ".join(f"{move:6}" for move in row))
        print()
    
    def make_move(self, move: chess.Move) -> bool:
        """
        Make a move on the board.
        
        Args:
            move: Chess move to make
            
        Returns:
            True if move was successful, False otherwise
        """
        try:
            # Store move in history
            san_move = self.board.san(move)
            self.game_history.append(san_move)
            
            # Make the move
            self.board.push(move)
            return True
        except Exception as e:
            print(f"Error making move: {e}")
            return False
    
    def play_game(self):
        """Main game loop."""
        print("Welcome to Chess Bot!")
        print("You are playing as White. The bot will play as Black.")
        print("Type 'help' during the game for move input instructions.")
        
        self.display_board()
        
        while not self.board.is_game_over():
            if self.board.turn == self.human_color:
                # Human's turn
                move = self.get_human_move()
                if move is None:
                    print("Game ended by user.")
                    break
                
                if self.make_move(move):
                    print(f"You played: {self.game_history[-1]}")
                else:
                    continue
            else:
                # Bot's turn
                print(f"\n{self.bot.name} is thinking...")
                move = self.bot.get_best_move(self.board)
                
                if move:
                    san_move = self.board.san(move)
                    self.make_move(move)
                    print(f"{self.bot.name} played: {san_move}")
                else:
                    print("Bot couldn't find a move!")
                    break
            
            self.display_board()
        
        # Game over
        self.show_game_result()
    
    def show_game_result(self):
        """Display the final game result."""
        print("\n" + "="*60)
        print("GAME OVER")
        print("="*60)

        if self.board.is_checkmate():
            winner = "You" if self.board.turn != self.human_color else self.bot.name
            print(f"Checkmate! {winner} won!")
        elif self.board.is_stalemate():
            print("Stalemate! The game is a draw.")
        elif self.board.is_insufficient_material():
            print("Insufficient material! The game is a draw.")
        else:
            print("Game ended.")

        print(f"\nGame lasted {len(self.game_history)} moves.")
        print("Move history:", " ".join(self.game_history))

        # Offer to save the game
        try:
            save_choice = input("\nSave game to PGN file? (y/n): ").strip().lower()
            if save_choice in ['y', 'yes']:
                white_player = "Human" if self.human_color == chess.WHITE else self.bot.name
                black_player = self.bot.name if self.human_color == chess.WHITE else "Human"
                filename = save_game_pgn(self.board, self.game_history,
                                       white_player=white_player, black_player=black_player)
                print(f"Game saved to: {filename}")
        except KeyboardInterrupt:
            pass

        print("="*60)
    
    def switch_colors(self):
        """Switch human and bot colors."""
        self.human_color = not self.human_color
        print(f"You are now playing as {'White' if self.human_color == chess.WHITE else 'Black'}")
    
    def reset_game(self):
        """Reset the game to starting position."""
        self.board = chess.Board()
        self.game_history = []
        print("Game reset to starting position.")
