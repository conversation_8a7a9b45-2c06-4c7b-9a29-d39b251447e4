import chess
import chess.engine
import random
from typing import Optional, Tuple, List, Dict
import time
import logging
import os

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("qs_analysis4_gui_QS.log"), # can rename 'junk.log' if just need to see its working
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ChessBotQS")

class ChessBot:
    """
    A chess bot that uses minimax algorithm with alpha-beta pruning
    to make intelligent moves.
    """
    
    def __init__(self, depth: int = 3, name: str = "ChessBot", use_quiescence: bool = True):
        """
        Initialize the chess bot.
        
        Args:
            depth: Search depth for minimax algorithm
            name: Name of the bot
            use_quiescence: Whether to use quiescence search
        """
        self.depth = depth
        self.name = name
        self.nodes_evaluated = 0
        self.use_quiescence = use_quiescence
        
        # Stats for analysis
        self.qs_stats = {
            "calls": 0,
            "positions_evaluated": 0,
            "max_depth_reached": 0,
            "score_improvements": 0,
            "total_score_improvement": 0,
            "captures_found": 0,
            "time_spent": 0.0
        }
        
        # Piece values for evaluation
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
        
        # Position tables for piece placement evaluation
        self.pawn_table = [
            0,  0,  0,  0,  0,  0,  0,  0,
            50, 50, 50, 50, 50, 50, 50, 50,
            10, 10, 20, 30, 30, 20, 10, 10,
            5,  5, 10, 25, 25, 10,  5,  5,
            0,  0,  0, 20, 20,  0,  0,  0,
            5, -5,-10,  0,  0,-10, -5,  5,
            5, 10, 10,-20,-20, 10, 10,  5,
            0,  0,  0,  0,  0,  0,  0,  0
        ]
        
        self.knight_table = [
            -50,-40,-30,-30,-30,-30,-40,-50,
            -40,-20,  0,  0,  0,  0,-20,-40,
            -30,  0, 10, 15, 15, 10,  0,-30,
            -30,  5, 15, 20, 20, 15,  5,-30,
            -30,  0, 15, 20, 20, 15,  0,-30,
            -30,  5, 10, 15, 15, 10,  5,-30,
            -40,-20,  0,  5,  5,  0,-20,-40,
            -50,-40,-30,-30,-30,-30,-40,-50
        ]
    
    def evaluate_board(self, board: chess.Board) -> int:
        """
        Evaluate the current board position.
        
        Args:
            board: Chess board to evaluate
            
        Returns:
            Evaluation score (positive for white advantage, negative for black)
        """
        if board.is_checkmate():
            return -20000 if board.turn else 20000
        
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        
        # Material evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = self.piece_values[piece.piece_type]
                
                # Add positional bonuses
                if piece.piece_type == chess.PAWN:
                    if piece.color == chess.WHITE:
                        value += self.pawn_table[square]
                    else:
                        value += self.pawn_table[chess.square_mirror(square)]
                elif piece.piece_type == chess.KNIGHT:
                    if piece.color == chess.WHITE:
                        value += self.knight_table[square]
                    else:
                        value += self.knight_table[chess.square_mirror(square)]
                
                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value
        
        # Mobility bonus
        legal_moves = len(list(board.legal_moves))
        if board.turn == chess.WHITE:
            score += legal_moves * 2
        else:
            score -= legal_moves * 2
        
        return score
    
    def _order_moves(self, board: chess.Board, moves: List[chess.Move]) -> List[chess.Move]:
        """Order moves to improve alpha-beta pruning efficiency."""
        def move_priority(move):
            priority = 0
            
            # Prioritize captures (MVV-LVA: Most Valuable Victim - Least Valuable Attacker)
            if board.is_capture(move):
                captured_piece = board.piece_at(move.to_square)
                attacking_piece = board.piece_at(move.from_square)
                if captured_piece and attacking_piece:
                    priority += self.piece_values[captured_piece.piece_type] * 10
                    priority -= self.piece_values[attacking_piece.piece_type]
            
            # Prioritize checks
            if board.gives_check(move):
                priority += 50
            
            # Prioritize promotions
            if move.promotion:
                priority += self.piece_values[move.promotion] * 5
            
            # Prioritize castling
            if board.is_castling(move):
                priority += 30
            
            return priority
        
        return sorted(moves, key=move_priority, reverse=True)
    
    def quiescence_search(self, board: chess.Board, alpha: int, beta: int, 
                          maximizing_player: bool, depth: int = 0) -> int:
        """
        Quiescence search to avoid horizon effect by searching captures until a quiet position.
        
        Args:
            board: Current board position
            alpha: Alpha value for pruning
            beta: Beta value for pruning
            maximizing_player: True if maximizing player's turn
            depth: Current quiescence search depth (for limiting)
            
        Returns:
            Evaluation score
        """
        # Skip quiescence search if disabled
        if not self.use_quiescence and depth > 0:
            return self.evaluate_board(board)
        
        start_time = time.time()
        self.nodes_evaluated += 1
        self.qs_stats["calls"] += 1
        self.qs_stats["positions_evaluated"] += 1
        
        # Update max depth reached
        if depth > self.qs_stats["max_depth_reached"]:
            self.qs_stats["max_depth_reached"] = depth
        
        # Stand-pat score (evaluation without making any moves)
        stand_pat = self.evaluate_board(board)
        original_score = stand_pat
        
        # Maximum depth for quiescence search to prevent excessive searching
        if depth >= 5 or board.is_game_over():
            end_time = time.time()
            self.qs_stats["time_spent"] += (end_time - start_time)
            return stand_pat
        
        # Get captures and promotions
        captures = [move for move in board.legal_moves if board.is_capture(move) or move.promotion]
        captures = self._order_moves(board, captures)
        
        # Log the position if there are captures
        if len(captures) > 0 and depth == 0:
            logger.info(f"QS analyzing position with {len(captures)} captures/promotions: {board.fen()}")
            logger.info(f"Initial evaluation: {stand_pat}")
        
        if maximizing_player:
            if stand_pat >= beta:
                end_time = time.time()
                self.qs_stats["time_spent"] += (end_time - start_time)
                return beta  # Beta cutoff
            
            alpha = max(alpha, stand_pat)
            
            for move in captures:
                board.push(move)
                score = self.quiescence_search(board, alpha, beta, False, depth + 1)
                board.pop()
                
                if score > stand_pat:
                    self.qs_stats["captures_found"] += 1
                    if depth == 0:
                        logger.info(f"QS found improvement: {move} improves score from {stand_pat} to {score}")
                
                if score >= beta:
                    end_time = time.time()
                    self.qs_stats["time_spent"] += (end_time - start_time)
                    return beta  # Beta cutoff
                
                alpha = max(alpha, score)
            
            # If we improved the score, record it
            if alpha > original_score and depth == 0:
                improvement = alpha - original_score
                self.qs_stats["score_improvements"] += 1
                self.qs_stats["total_score_improvement"] += improvement
                logger.info(f"QS improved evaluation by {improvement} points")
            
            end_time = time.time()
            self.qs_stats["time_spent"] += (end_time - start_time)
            return alpha
        else:
            if stand_pat <= alpha:
                end_time = time.time()
                self.qs_stats["time_spent"] += (end_time - start_time)
                return alpha  # Alpha cutoff
            
            beta = min(beta, stand_pat)
            
            for move in captures:
                board.push(move)
                score = self.quiescence_search(board, alpha, beta, True, depth + 1)
                board.pop()
                
                if score < stand_pat:
                    self.qs_stats["captures_found"] += 1
                    if depth == 0:
                        logger.info(f"QS found improvement: {move} improves score from {stand_pat} to {score}")
                
                if score <= alpha:
                    end_time = time.time()
                    self.qs_stats["time_spent"] += (end_time - start_time)
                    return alpha  # Alpha cutoff
                
                beta = min(beta, score)
            
            # If we improved the score, record it
            if beta < original_score and depth == 0:
                improvement = original_score - beta
                self.qs_stats["score_improvements"] += 1
                self.qs_stats["total_score_improvement"] += improvement
                logger.info(f"QS improved evaluation by {improvement} points")
            
            end_time = time.time()
            self.qs_stats["time_spent"] += (end_time - start_time)
            return beta
    
    def minimax(self, board: chess.Board, depth: int, alpha: int, beta: int, 
                maximizing_player: bool) -> Tuple[int, Optional[chess.Move]]:
        """
        Minimax algorithm with alpha-beta pruning.
        
        Args:
            board: Current board position
            depth: Remaining search depth
            alpha: Alpha value for pruning
            beta: Beta value for pruning
            maximizing_player: True if maximizing player's turn
            
        Returns:
            Tuple of (evaluation_score, best_move)
        """
        self.nodes_evaluated += 1
        
        # Check for game over
        if board.is_game_over():
            return self.evaluate_board(board), None
        
        # If we've reached our depth limit, use quiescence search if enabled
        if depth <= 0:
            if self.use_quiescence:
                logger.info(f"Depth limit reached, starting quiescence search at position: {board.fen()}")
                return self.quiescence_search(board, alpha, beta, maximizing_player), None
            else:
                return self.evaluate_board(board), None
        
        best_move = None
        legal_moves = list(board.legal_moves)
        
        # Order moves for better alpha-beta pruning
        ordered_moves = self._order_moves(board, legal_moves)
        
        if maximizing_player:
            max_eval = float('-inf')
            for move in ordered_moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            return max_eval, best_move
        else:
            min_eval = float('inf')
            for move in ordered_moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            return min_eval, best_move
    
    def get_best_move(self, board: chess.Board) -> chess.Move:
        """
        Get the best move for the current position.
        
        Args:
            board: Current board position
            
        Returns:
            Best move according to the bot's evaluation
        """
        self.nodes_evaluated = 0
        self.reset_qs_stats()
        start_time = time.time()
        
        # Log the current position
        logger.info(f"Finding best move for position: {board.fen()}")
        logger.info(f"Quiescence search is {'enabled' if self.use_quiescence else 'disabled'}")
        
        # Use minimax to find the best move
        maximizing = board.turn == chess.WHITE
        score, best_move = self.minimax(board, self.depth, float('-inf'), float('inf'), maximizing)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        if best_move is None:
            # Fallback to random move if no move found
            legal_moves = list(board.legal_moves)
            best_move = random.choice(legal_moves) if legal_moves else None
            logger.warning("No best move found, falling back to random move")
        
        # Get QS stats
        qs_stats = self.get_qs_stats()
        
        # Log detailed information
        logger.info(f"Best move found: {best_move}, evaluation: {score}")
        logger.info(f"Total positions evaluated: {self.nodes_evaluated}")
        logger.info(f"Total time: {total_time:.2f} seconds")
        
        # Calculate percentage of time spent in QS
        qs_time_percent = 0
        if total_time > 0:
            qs_time_percent = (qs_stats["time_spent"] / total_time) * 100
        
        # Calculate percentage of positions evaluated in QS
        qs_positions_percent = 0
        if self.nodes_evaluated > 0:
            qs_positions_percent = (qs_stats["positions_evaluated"] / self.nodes_evaluated) * 100
        
        logger.info(f"QS time: {qs_stats['time_spent']:.2f}s ({qs_time_percent:.1f}% of total)")
        logger.info(f"QS positions: {qs_stats['positions_evaluated']} ({qs_positions_percent:.1f}% of total)")
        
        if qs_stats["score_improvements"] > 0:
            logger.info(f"QS improved evaluation {qs_stats['score_improvements']} times")
            logger.info(f"Average improvement: {qs_stats['avg_score_improvement']:.2f} points")
        
        print(f"{self.name} evaluated {self.nodes_evaluated} positions in {total_time:.2f} seconds")
        if self.use_quiescence:
            print(f"QS improved evaluation {qs_stats['score_improvements']} times, avg: {qs_stats['avg_score_improvement']:.2f} points")
        
        return best_move
    
    def set_depth(self, depth: int):
        """Set the search depth for the bot."""
        self.depth = depth
        print(f"{self.name} search depth set to {depth}")
    
    def toggle_quiescence(self, use_quiescence: bool = None):
        """Toggle quiescence search on or off."""
        if use_quiescence is None:
            self.use_quiescence = not self.use_quiescence
        else:
            self.use_quiescence = use_quiescence
        
        status = "enabled" if self.use_quiescence else "disabled"
        logger.info(f"Quiescence search {status}")
        print(f"{self.name} quiescence search {status}")
        return self.use_quiescence
    
    def get_qs_stats(self) -> Dict:
        """Get statistics about quiescence search usage."""
        # Calculate average score improvement if there were any improvements
        avg_improvement = 0
        if self.qs_stats["score_improvements"] > 0:
            avg_improvement = self.qs_stats["total_score_improvement"] / self.qs_stats["score_improvements"]
        
        # Add calculated stats
        stats = self.qs_stats.copy()
        stats["avg_score_improvement"] = avg_improvement
        
        # Log the stats
        logger.info(f"QS Stats: {stats}")
        
        return stats
    
    def reset_qs_stats(self):
        """Reset quiescence search statistics."""
        self.qs_stats = {
            "calls": 0,
            "positions_evaluated": 0,
            "max_depth_reached": 0,
            "score_improvements": 0,
            "total_score_improvement": 0,
            "captures_found": 0,
            "time_spent": 0.0
        }