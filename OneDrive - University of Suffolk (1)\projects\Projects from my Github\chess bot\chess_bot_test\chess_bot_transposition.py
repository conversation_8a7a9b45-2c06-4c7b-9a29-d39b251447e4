import chess
import chess.engine
import random
from typing import Optional, Tuple, List, Dict
import time
from enum import Enum


class TTEntryType(Enum):
    """Types of transposition table entries."""
    EXACT = 0      # Exact score
    LOWER_BOUND = 1  # Alpha cutoff (score >= beta)
    UPPER_BOUND = 2  # Beta cutoff (score <= alpha)


class TranspositionTableEntry:
    """Entry in the transposition table."""
    
    def __init__(self, depth: int, score: int, entry_type: TTEntryType, 
                 best_move: Optional[chess.Move] = None):
        self.depth = depth
        self.score = score
        self.entry_type = entry_type
        self.best_move = best_move


class TranspositionTable:
    """
    Transposition table for storing previously evaluated positions.
    Uses Zobrist hashing for position identification.
    """
    
    def __init__(self, max_size: int = 1000000):
        """
        Initialize the transposition table.
        
        Args:
            max_size: Maximum number of entries to store
        """
        self.table: Dict[int, TranspositionTableEntry] = {}
        self.max_size = max_size
        self.hits = 0
        self.misses = 0
    
    def store(self, board: chess.Board, depth: int, score: int, 
              entry_type: TTEntryType, best_move: Optional[chess.Move] = None):
        """
        Store a position evaluation in the transposition table.
        
        Args:
            board: Chess board position
            depth: Search depth used for this evaluation
            score: Evaluation score
            entry_type: Type of the entry (exact, lower bound, upper bound)
            best_move: Best move found for this position
        """
        position_hash = self._get_position_hash(board)
        
        # Replace if we have a deeper search or the slot is empty
        if (position_hash not in self.table or 
            self.table[position_hash].depth <= depth):
            
            # If table is full, remove oldest entry (simple replacement strategy)
            if len(self.table) >= self.max_size and position_hash not in self.table:
                # Remove a random entry (could be improved with LRU)
                old_key = next(iter(self.table))
                del self.table[old_key]
            
            self.table[position_hash] = TranspositionTableEntry(
                depth, score, entry_type, best_move
            )
    
    def lookup(self, board: chess.Board, depth: int, alpha: int, beta: int) -> Tuple[Optional[int], Optional[chess.Move]]:
        """
        Look up a position in the transposition table.
        
        Args:
            board: Chess board position
            depth: Current search depth
            alpha: Alpha value
            beta: Beta value
            
        Returns:
            Tuple of (score, best_move) if usable entry found, (None, move) otherwise
        """
        position_hash = self._get_position_hash(board)
        
        if position_hash not in self.table:
            self.misses += 1
            return None, None
        
        entry = self.table[position_hash]
        
        # Entry must be from a search at least as deep as current
        if entry.depth < depth:
            self.misses += 1
            return None, entry.best_move
        
        self.hits += 1
        
        # Check if we can use this score
        if entry.entry_type == TTEntryType.EXACT:
            return entry.score, entry.best_move
        elif entry.entry_type == TTEntryType.LOWER_BOUND and entry.score >= beta:
            return entry.score, entry.best_move
        elif entry.entry_type == TTEntryType.UPPER_BOUND and entry.score <= alpha:
            return entry.score, entry.best_move
        
        # Can't use the score, but might use the move for move ordering
        return None, entry.best_move
    
    def _get_position_hash(self, board: chess.Board) -> int:
        """
        Get a hash for the current board position.
        Uses FEN string as the basis for hashing.
        
        Args:
            board: Chess board position
            
        Returns:
            Hash value for the position
        """
        # Use FEN string for hashing - this includes position, castling rights,
        # en passant, turn, and move counters
        return hash(board.fen())
    
    def clear(self):
        """Clear the transposition table."""
        self.table.clear()
        self.hits = 0
        self.misses = 0
    
    def get_stats(self) -> Dict[str, int]:
        """Get transposition table statistics."""
        total_lookups = self.hits + self.misses
        hit_rate = (self.hits / total_lookups * 100) if total_lookups > 0 else 0
        
        return {
            'size': len(self.table),
            'max_size': self.max_size,
            'hits': self.hits,
            'misses': self.misses,
            'hit_rate': hit_rate
        }

class ChessBot:
    """
    A chess bot that uses minimax algorithm with alpha-beta pruning
    and transposition tables to make intelligent moves.
    """
    
    def __init__(self, depth: int = 3, name: str = "ChessBot", use_transposition_table: bool = True):
        """
        Initialize the chess bot.
        
        Args:
            depth: Search depth for minimax algorithm
            name: Name of the bot
            use_transposition_table: Whether to use transposition table optimization
        """
        self.depth = depth
        self.name = name
        self.nodes_evaluated = 0
        self.use_transposition_table = use_transposition_table
        
        # Initialize transposition table
        if self.use_transposition_table:
            self.transposition_table = TranspositionTable()
        else:
            self.transposition_table = None
        
        # Track move history to avoid repetition
        self.move_history = []
        self.position_history = []
        
        # Piece values for evaluation
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
        
        # Position tables for piece placement evaluation
        self.pawn_table = [
            0,  0,  0,  0,  0,  0,  0,  0,
            50, 50, 50, 50, 50, 50, 50, 50,
            10, 10, 20, 30, 30, 20, 10, 10,
            5,  5, 10, 25, 25, 10,  5,  5,
            0,  0,  0, 20, 20,  0,  0,  0,
            5, -5,-10,  0,  0,-10, -5,  5,
            5, 10, 10,-20,-20, 10, 10,  5,
            0,  0,  0,  0,  0,  0,  0,  0
        ]
        
        self.knight_table = [
            -50,-40,-30,-30,-30,-30,-40,-50,
            -40,-20,  0,  0,  0,  0,-20,-40,
            -30,  0, 10, 15, 15, 10,  0,-30,
            -30,  5, 15, 20, 20, 15,  5,-30,
            -30,  0, 15, 20, 20, 15,  0,-30,
            -30,  5, 10, 15, 15, 10,  5,-30,
            -40,-20,  0,  5,  5,  0,-20,-40,
            -50,-40,-30,-30,-30,-30,-40,-50
        ]
    
    def evaluate_board(self, board: chess.Board) -> int:
        """
        Evaluate the current board position.
        
        Args:
            board: Chess board to evaluate
            
        Returns:
            Evaluation score (positive for white advantage, negative for black)
        """
        if board.is_checkmate():
            return -20000 if board.turn else 20000
        
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        
        # Material evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = self.piece_values[piece.piece_type]
                
                # Add positional bonuses
                if piece.piece_type == chess.PAWN:
                    if piece.color == chess.WHITE:
                        value += self.pawn_table[square]
                    else:
                        value += self.pawn_table[chess.square_mirror(square)]
                elif piece.piece_type == chess.KNIGHT:
                    if piece.color == chess.WHITE:
                        value += self.knight_table[square]
                    else:
                        value += self.knight_table[chess.square_mirror(square)]
                
                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value
        
        # Mobility bonus
        legal_moves = len(list(board.legal_moves))
        if board.turn == chess.WHITE:
            score += legal_moves * 2
        else:
            score -= legal_moves * 2
        
        # Development bonus (encourage moving pieces from starting squares)
        score += self._evaluate_development(board)
        
        # Penalize repetitive positions
        score += self._evaluate_repetition_penalty(board)
        
        # Penalize shuffling moves
        score += self._evaluate_shuffling_penalty(board)
        
        # King safety evaluation
        score += self._evaluate_king_safety(board)
        
        return score
    
    def _evaluate_development(self, board: chess.Board) -> int:
        """Evaluate piece development."""
        score = 0
        
        # Penalize pieces still on starting squares in opening/middlegame
        if len(board.move_stack) < 20:  # Opening/early middlegame
            # White pieces
            if board.piece_at(chess.B1) and board.piece_at(chess.B1).piece_type == chess.KNIGHT:
                score -= 10
            if board.piece_at(chess.G1) and board.piece_at(chess.G1).piece_type == chess.KNIGHT:
                score -= 10
            if board.piece_at(chess.C1) and board.piece_at(chess.C1).piece_type == chess.BISHOP:
                score -= 15
            if board.piece_at(chess.F1) and board.piece_at(chess.F1).piece_type == chess.BISHOP:
                score -= 15
            
            # Black pieces
            if board.piece_at(chess.B8) and board.piece_at(chess.B8).piece_type == chess.KNIGHT:
                score += 10
            if board.piece_at(chess.G8) and board.piece_at(chess.G8).piece_type == chess.KNIGHT:
                score += 10
            if board.piece_at(chess.C8) and board.piece_at(chess.C8).piece_type == chess.BISHOP:
                score += 15
            if board.piece_at(chess.F8) and board.piece_at(chess.F8).piece_type == chess.BISHOP:
                score += 15
        
        # Evaluate rook development more carefully
        score += self._evaluate_rook_development(board)
        
        # Add strategic considerations
        score += self._evaluate_piece_coordination(board)
        
        return score
    
    def _evaluate_rook_development(self, board: chess.Board) -> int:
        """Evaluate rook development and positioning."""
        score = 0
        
        # Find all rooks
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece and piece.piece_type == chess.ROOK:
                rook_score = 0
                
                # Penalize rooks on starting squares in opening
                if len(board.move_stack) < 15:
                    if piece.color == chess.WHITE:
                        if square in [chess.A1, chess.H1]:
                            rook_score -= 8
                    else:
                        if square in [chess.A8, chess.H8]:
                            rook_score -= 8
                
                # Penalize rooks on edge files without purpose
                file = chess.square_file(square)
                rank = chess.square_rank(square)
                
                # Check if rook is on an open or semi-open file
                file_has_own_pawns = False
                file_has_enemy_pawns = False
                
                for r in range(8):
                    check_square = chess.square(file, r)
                    check_piece = board.piece_at(check_square)
                    if check_piece and check_piece.piece_type == chess.PAWN:
                        if check_piece.color == piece.color:
                            file_has_own_pawns = True
                        else:
                            file_has_enemy_pawns = True
                
                # Bonus for open files (no pawns)
                if not file_has_own_pawns and not file_has_enemy_pawns:
                    rook_score += 15
                # Bonus for semi-open files (no own pawns)
                elif not file_has_own_pawns:
                    rook_score += 10
                # Penalty for blocked files (own pawns present)
                elif file_has_own_pawns:
                    rook_score -= 5
                
                # Penalize rooks that just move back and forth on back rank
                if len(board.move_stack) > 10:  # Mid-game
                    if piece.color == chess.WHITE and rank == 0:  # White back rank
                        if file in [6, 7]:  # g1, h1 area - often just shuffling
                            rook_score -= 15
                    elif piece.color == chess.BLACK and rank == 7:  # Black back rank
                        if file in [6, 7]:  # g8, h8 area - often just shuffling
                            rook_score -= 15
                
                # Bonus for active rook positions
                if piece.color == chess.WHITE:
                    if rank >= 6:  # 7th or 8th rank
                        rook_score += 20
                    elif rank >= 4:  # 5th or 6th rank
                        rook_score += 10
                else:
                    if rank <= 1:  # 2nd or 1st rank
                        rook_score += 20
                    elif rank <= 3:  # 4th or 3rd rank
                        rook_score += 10
                
                if piece.color == chess.WHITE:
                    score += rook_score
                else:
                    score -= rook_score
        
        return score
    
    def _evaluate_piece_coordination(self, board: chess.Board) -> int:
        """Evaluate piece coordination and strategic positioning."""
        score = 0
        
        # Encourage piece development over rook activity in opening
        if len(board.move_stack) < 15:  # Opening phase
            # Count developed pieces
            white_developed = 0
            black_developed = 0
            
            # Check minor piece development
            for square in chess.SQUARES:
                piece = board.piece_at(square)
                if piece and piece.piece_type in [chess.KNIGHT, chess.BISHOP]:
                    # Consider piece developed if not on starting square
                    if piece.color == chess.WHITE:
                        starting_squares = [chess.B1, chess.G1, chess.C1, chess.F1]
                        if square not in starting_squares:
                            white_developed += 1
                    else:
                        starting_squares = [chess.B8, chess.G8, chess.C8, chess.F8]
                        if square not in starting_squares:
                            black_developed += 1
            
            # Bonus for having more developed pieces
            development_diff = white_developed - black_developed
            score += development_diff * 15
            
            # Penalize early rook moves when minor pieces aren't developed
            for square in chess.SQUARES:
                piece = board.piece_at(square)
                if piece and piece.piece_type == chess.ROOK:
                    if piece.color == chess.WHITE:
                        starting_squares = [chess.A1, chess.H1]
                        if square not in starting_squares and white_developed < 2:
                            score -= 25  # Penalty for premature rook development
                    else:
                        starting_squares = [chess.A8, chess.H8]
                        if square not in starting_squares and black_developed < 2:
                            score += 25  # Penalty for black's premature rook development
        
        # Encourage central control
        score += self._evaluate_center_control(board)
        
        return score
    
    def _evaluate_center_control(self, board: chess.Board) -> int:
        """Evaluate control of central squares."""
        score = 0
        center_squares = [chess.D4, chess.D5, chess.E4, chess.E5]
        extended_center = [chess.C3, chess.C4, chess.C5, chess.C6,
                          chess.D3, chess.D6, chess.E3, chess.E6,
                          chess.F3, chess.F4, chess.F5, chess.F6]
        
        # Check piece occupation of center
        for square in center_squares:
            piece = board.piece_at(square)
            if piece:
                if piece.color == chess.WHITE:
                    score += 10
                else:
                    score -= 10
        
        # Check piece attacks on center (simplified)
        for square in center_squares:
            white_attackers = len(board.attackers(chess.WHITE, square))
            black_attackers = len(board.attackers(chess.BLACK, square))
            score += (white_attackers - black_attackers) * 3
        
        return score
    
    def _evaluate_repetition_penalty(self, board: chess.Board) -> int:
        """Penalize repetitive positions."""
        current_fen = board.fen().split(' ')[0]  # Just the position part
        
        # Count how many times this position has appeared recently
        recent_positions = self.position_history[-10:]  # Last 10 positions
        repetition_count = recent_positions.count(current_fen)
        
        # Penalize repetitions
        penalty = repetition_count * -20
        
        return penalty
    
    def _evaluate_shuffling_penalty(self, board: chess.Board) -> int:
        """Penalize shuffling moves (moving pieces back and forth without purpose)."""
        if len(self.move_history) < 4:
            return 0
        
        penalty = 0
        recent_moves = self.move_history[-4:]
        
        # Check for A-B-A-B pattern (piece moving back and forth)
        for i in range(len(recent_moves) - 3):
            move1 = recent_moves[i]
            move2 = recent_moves[i + 1]
            move3 = recent_moves[i + 2]
            move4 = recent_moves[i + 3]
            
            # Check if moves 1&3 are the same and moves 2&4 are the same (A-B-A-B pattern)
            if (move1.from_square == move3.from_square and move1.to_square == move3.to_square and
                move2.from_square == move4.from_square and move2.to_square == move4.to_square):
                penalty -= 50  # Penalty for shuffling pattern
        
        return penalty
    
    def _evaluate_king_safety(self, board: chess.Board) -> int:
        """Evaluate king safety."""
        score = 0
        
        # Bonus for having castling rights
        if board.has_castling_rights(chess.WHITE):
            if board.has_kingside_castling_rights(chess.WHITE):
                score += 5
            if board.has_queenside_castling_rights(chess.WHITE):
                score += 5
        else:
            # Check if king has moved from starting position (likely castled or lost castling rights)
            white_king_square = board.king(chess.WHITE)
            if white_king_square and white_king_square in [chess.G1, chess.C1]:
                score += 20  # Bonus for castled king position
            elif white_king_square != chess.E1:
                score -= 10  # Penalty for exposed king
        
        if board.has_castling_rights(chess.BLACK):
            if board.has_kingside_castling_rights(chess.BLACK):
                score -= 5
            if board.has_queenside_castling_rights(chess.BLACK):
                score -= 5
        else:
            # Check if king has moved from starting position
            black_king_square = board.king(chess.BLACK)
            if black_king_square and black_king_square in [chess.G8, chess.C8]:
                score -= 20  # Bonus for castled king position (negative because it's black)
            elif black_king_square != chess.E8:
                score += 10  # Penalty for exposed king (positive because it's bad for black)
        
        return score
    
    def _order_moves(self, board: chess.Board, moves: List[chess.Move], tt_move: Optional[chess.Move] = None) -> List[chess.Move]:
        """
        Order moves to improve alpha-beta pruning efficiency.
        
        Args:
            board: Current board position
            moves: List of legal moves
            tt_move: Move from transposition table (highest priority)
            
        Returns:
            Ordered list of moves
        """
        def move_score(move):
            score = 0
            
            # Transposition table move gets highest priority
            if tt_move and move == tt_move:
                return 10000
            
            # Captures
            if board.is_capture(move):
                captured_piece = board.piece_at(move.to_square)
                moving_piece = board.piece_at(move.from_square)
                if captured_piece and moving_piece:
                    # MVV-LVA (Most Valuable Victim - Least Valuable Attacker)
                    score += self.piece_values[captured_piece.piece_type] * 10
                    score -= self.piece_values[moving_piece.piece_type]
            
            # Checks
            board.push(move)
            if board.is_check():
                score += 50
            board.pop()
            
            # Promotions
            if move.promotion:
                score += 800
            
            # Castling
            if board.is_castling(move):
                score += 60
            
            # Prioritize development in opening
            if len(board.move_stack) < 15:  # Opening phase
                moving_piece = board.piece_at(move.from_square)
                if moving_piece:
                    # Bonus for developing minor pieces
                    if moving_piece.piece_type in [chess.KNIGHT, chess.BISHOP]:
                        if moving_piece.color == chess.WHITE:
                            starting_squares = [chess.B1, chess.G1, chess.C1, chess.F1]
                            if move.from_square in starting_squares:
                                score += 40  # Good development move
                        else:
                            starting_squares = [chess.B8, chess.G8, chess.C8, chess.F8]
                            if move.from_square in starting_squares:
                                score += 40
                    
                    # Penalty for early rook moves (unless it's a capture or very good move)
                    elif moving_piece.piece_type == chess.ROOK:
                        if not board.is_capture(move):
                            # Count developed minor pieces
                            developed_pieces = 0
                            for sq in chess.SQUARES:
                                p = board.piece_at(sq)
                                if p and p.color == moving_piece.color and p.piece_type in [chess.KNIGHT, chess.BISHOP]:
                                    if moving_piece.color == chess.WHITE:
                                        starting_squares = [chess.B1, chess.G1, chess.C1, chess.F1]
                                    else:
                                        starting_squares = [chess.B8, chess.G8, chess.C8, chess.F8]
                                    if sq not in starting_squares:
                                        developed_pieces += 1
                            
                            # Heavy penalty if fewer than 2 minor pieces developed
                            if developed_pieces < 2:
                                score -= 100  # Strong discouragement of early rook moves
                            else:
                                score -= 20  # Mild discouragement
            
            # Penalize moving the same piece repeatedly
            if len(self.move_history) >= 2:
                recent_moves = self.move_history[-4:]  # Last 4 moves
                same_piece_moves = sum(1 for m in recent_moves if m.from_square == move.from_square)
                if same_piece_moves >= 2:
                    score -= 30  # Penalize repetitive piece movement
                    
                # Extra penalty for rook shuffling on back rank
                moving_piece = board.piece_at(move.from_square)
                if moving_piece and moving_piece.piece_type == chess.ROOK:
                    from_rank = chess.square_rank(move.from_square)
                    to_rank = chess.square_rank(move.to_square)
                    
                    # Penalize rook moves on back rank that don't accomplish much
                    if ((moving_piece.color == chess.WHITE and from_rank == 0 and to_rank == 0) or
                        (moving_piece.color == chess.BLACK and from_rank == 7 and to_rank == 7)):
                        
                        # Heavy penalty for any back rank rook shuffling
                        score -= 200  # Increased penalty
                        
                        # Check if this is just shuffling between g and h files
                        from_file = chess.square_file(move.from_square)
                        to_file = chess.square_file(move.to_square)
                        if {from_file, to_file}.issubset({6, 7}):  # g and h files
                            score -= 500  # Massive penalty for g-h rook shuffling
            
            # Penalize moving to squares we just moved from
            if len(self.move_history) >= 1:
                last_move = self.move_history[-1]
                if move.to_square == last_move.from_square and move.from_square == last_move.to_square:
                    score -= 100  # Strong penalty for immediate move reversal
                    
                    # Even stronger penalty if it's a rook doing this
                    moving_piece = board.piece_at(move.from_square)
                    if moving_piece and moving_piece.piece_type == chess.ROOK:
                        score -= 200
            
            return score
        
        # Sort moves by score (highest first)
        return sorted(moves, key=move_score, reverse=True)
    
    def minimax(self, board: chess.Board, depth: int, alpha: int, beta: int, 
                maximizing_player: bool) -> Tuple[int, Optional[chess.Move]]:
        """
        Minimax algorithm with alpha-beta pruning and transposition table.
        
        Args:
            board: Current board position
            depth: Remaining search depth
            alpha: Alpha value for pruning
            beta: Beta value for pruning
            maximizing_player: True if maximizing player's turn
            
        Returns:
            Tuple of (evaluation_score, best_move)
        """
        self.nodes_evaluated += 1
        original_alpha = alpha
        
        # Check transposition table
        tt_score = None
        tt_move = None
        if self.transposition_table:
            tt_score, tt_move = self.transposition_table.lookup(board, depth, alpha, beta)
            if tt_score is not None:
                return tt_score, tt_move
        
        if depth == 0 or board.is_game_over():
            score = self.evaluate_board(board)
            # Store in transposition table
            if self.transposition_table:
                self.transposition_table.store(board, depth, score, TTEntryType.EXACT)
            return score, None
        
        best_move = tt_move  # Use transposition table move as initial best move
        legal_moves = list(board.legal_moves)
        
        # Improve move ordering
        legal_moves = self._order_moves(board, legal_moves, tt_move)
        
        if maximizing_player:
            max_eval = float('-inf')
            
            for move in legal_moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            # Store in transposition table
            if self.transposition_table:
                if max_eval <= original_alpha:
                    entry_type = TTEntryType.UPPER_BOUND
                elif max_eval >= beta:
                    entry_type = TTEntryType.LOWER_BOUND
                else:
                    entry_type = TTEntryType.EXACT
                
                self.transposition_table.store(board, depth, max_eval, entry_type, best_move)
            
            return max_eval, best_move
        else:
            min_eval = float('inf')
            
            for move in legal_moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            # Store in transposition table
            if self.transposition_table:
                if min_eval <= original_alpha:
                    entry_type = TTEntryType.UPPER_BOUND
                elif min_eval >= beta:
                    entry_type = TTEntryType.LOWER_BOUND
                else:
                    entry_type = TTEntryType.EXACT
                
                self.transposition_table.store(board, depth, min_eval, entry_type, best_move)
            
            return min_eval, best_move
    
    def get_best_move(self, board: chess.Board) -> chess.Move:
        """
        Get the best move for the current position.
        
        Args:
            board: Current board position
            
        Returns:
            Best move according to the bot's evaluation
        """
        self.nodes_evaluated = 0
        start_time = time.time()
        
        # Update position history for repetition detection
        current_fen = board.fen().split(' ')[0]  # Just the position part
        self.position_history.append(current_fen)
        
        # Keep only recent history to avoid memory issues
        if len(self.position_history) > 50:
            self.position_history = self.position_history[-50:]
        
        # Use minimax to find the best move
        maximizing = board.turn == chess.WHITE
        _, best_move = self.minimax(board, self.depth, float('-inf'), float('inf'), maximizing)
        
        end_time = time.time()
        
        if best_move is None:
            # Fallback to random move if no move found
            legal_moves = list(board.legal_moves)
            best_move = random.choice(legal_moves) if legal_moves else None
        
        # Update move history
        if best_move:
            self.move_history.append(best_move)
            if len(self.move_history) > 50:
                self.move_history = self.move_history[-50:]
        
        # Print performance statistics
        tt_stats = ""
        if self.transposition_table:
            stats = self.transposition_table.get_stats()
            tt_stats = f" (TT: {stats['hit_rate']:.1f}% hit rate, {stats['size']} entries)"
        
        print(f"{self.name} evaluated {self.nodes_evaluated} positions in {end_time - start_time:.2f} seconds{tt_stats}")
        
        return best_move
    
    def set_depth(self, depth: int):
        """Set the search depth for the bot."""
        self.depth = depth
        print(f"{self.name} search depth set to {depth}")
    
    def clear_transposition_table(self):
        """Clear the transposition table."""
        if self.transposition_table:
            self.transposition_table.clear()
            print(f"{self.name} transposition table cleared")
    
    def get_transposition_table_stats(self) -> Dict[str, int]:
        """Get transposition table statistics."""
        if self.transposition_table:
            return self.transposition_table.get_stats()
        return {}
    
    def set_transposition_table_size(self, max_size: int):
        """Set the maximum size of the transposition table."""
        if self.transposition_table:
            self.transposition_table.max_size = max_size
            # If current size exceeds new max, clear the table
            if len(self.transposition_table.table) > max_size:
                self.transposition_table.clear()
            print(f"{self.name} transposition table max size set to {max_size}")
    
    def enable_transposition_table(self, enable: bool = True):
        """Enable or disable the transposition table."""
        if enable and not self.transposition_table:
            self.transposition_table = TranspositionTable()
            self.use_transposition_table = True
            print(f"{self.name} transposition table enabled")
        elif not enable and self.transposition_table:
            self.transposition_table = None
            self.use_transposition_table = False
            print(f"{self.name} transposition table disabled")
    
    def reset_for_new_game(self):
        """Reset bot state for a new game."""
        self.move_history.clear()
        self.position_history.clear()
        if self.transposition_table:
            self.transposition_table.clear()
        print(f"{self.name} reset for new game")