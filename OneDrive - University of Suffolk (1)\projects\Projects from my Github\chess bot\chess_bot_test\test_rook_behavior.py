#!/usr/bin/env python3
"""
Test script to analyze rook behavior and demonstrate improvements.
"""

import chess
from chess_bot_transposition import ChessBot

def test_rook_shuffling():
    """Test the bot's behavior in a position where rook shuffling might occur."""
    
    # Create the position from your example
    board = chess.Board()
    moves = [
        "d4", "Nc6", "e4", "Nf6", "d5", "Nb4", "c3", "Nxe4", 
        "f3", "Nxa2", "Rxa2", "Nf6", "Bc4", "Rg8", "Bg5", "Rh8", 
        "Bxf6", "gxf6", "f4", "Rg8", "g3"
    ]
    
    for move_str in moves:
        try:
            move = board.parse_san(move_str)
            board.push(move)
        except ValueError as e:
            print(f"Error parsing move {move_str}: {e}")
            break
    
    print("Current position:")
    print(board)
    print(f"FEN: {board.fen()}")
    print()
    
    # Test with different bot configurations
    print("Testing bot behavior:")
    
    # Bot with transposition table
    bot_with_tt = ChessBot(depth=3, name="Bot_with_TT", use_transposition_table=True)
    
    # Bot without transposition table
    bot_without_tt = ChessBot(depth=3, name="Bot_without_TT", use_transposition_table=False)
    
    print("\n=== Bot with Transposition Table ===")
    move1 = bot_with_tt.get_best_move(board.copy())
    print(f"Chosen move: {move1}")
    
    # Simulate the move and get the next move to see if it shuffles
    test_board = board.copy()
    test_board.push(move1)
    move2 = bot_with_tt.get_best_move(test_board)
    print(f"Next move: {move2}")
    
    print("\n=== Bot without Transposition Table ===")
    move3 = bot_without_tt.get_best_move(board.copy())
    print(f"Chosen move: {move3}")
    
    # Check if moves are rook shuffling
    def is_rook_shuffle(move, board):
        piece = board.piece_at(move.from_square)
        if piece and piece.piece_type == chess.ROOK:
            from_rank = chess.square_rank(move.from_square)
            to_rank = chess.square_rank(move.to_square)
            # Check if it's moving on the back rank
            if ((piece.color == chess.WHITE and from_rank == 0 and to_rank == 0) or
                (piece.color == chess.BLACK and from_rank == 7 and to_rank == 7)):
                return True
        return False
    
    print(f"\nMove analysis:")
    print(f"Move 1 is rook shuffle: {is_rook_shuffle(move1, board)}")
    print(f"Move 3 is rook shuffle: {is_rook_shuffle(move3, board)}")
    
    # Show evaluation scores
    print(f"\nPosition evaluation:")
    print(f"Bot with TT evaluation: {bot_with_tt.evaluate_board(board)}")
    print(f"Bot without TT evaluation: {bot_without_tt.evaluate_board(board)}")
    
    # Test transposition table stats
    if bot_with_tt.transposition_table:
        stats = bot_with_tt.get_transposition_table_stats()
        print(f"\nTransposition table stats: {stats}")

def test_alternative_moves():
    """Test what moves the bot considers in the problematic position."""
    
    board = chess.Board()
    moves = [
        "d4", "Nc6", "e4", "Nf6", "d5", "Nb4", "c3", "Nxe4", 
        "f3", "Nxa2", "Rxa2", "Nf6", "Bc4", "Rg8", "Bg5", "Rh8", 
        "Bxf6", "gxf6", "f4", "Rg8", "g3"
    ]
    
    for move_str in moves:
        move = board.parse_san(move_str)
        board.push(move)
    
    print("\n=== Alternative Move Analysis ===")
    print("Legal moves in current position:")
    
    bot = ChessBot(depth=2, name="Analyzer")
    
    # Get all legal moves and their evaluations
    legal_moves = list(board.legal_moves)
    move_evaluations = []
    
    for move in legal_moves:
        board.push(move)
        eval_score = bot.evaluate_board(board)
        board.pop()
        
        piece = board.piece_at(move.from_square)
        piece_name = piece.symbol() if piece else "?"
        
        move_evaluations.append((move, eval_score, piece_name))
    
    # Sort by evaluation score
    move_evaluations.sort(key=lambda x: x[1], reverse=board.turn == chess.WHITE)
    
    print("Top 10 moves by evaluation:")
    for i, (move, score, piece) in enumerate(move_evaluations[:10]):
        print(f"{i+1:2d}. {move} ({piece}) - Score: {score:+4d}")

if __name__ == "__main__":
    test_rook_shuffling()
    test_alternative_moves()