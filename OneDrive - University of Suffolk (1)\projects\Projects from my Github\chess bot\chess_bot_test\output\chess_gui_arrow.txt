PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_arrow.py
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
Enabling undo button: human_color=True, moves=1, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 743 positions in 0.19 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=3, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1399 positions in 0.37 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=5, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2024 positions in 0.44 seconds
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 6 moves in move stack
Current board state: r1bqkb1r/pppppppp/5n2/8/3nP3/3P1P2/PPP3PP/RNBQKBNR w KQkq - 1 4
Undoing 2 moves
New board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=4, turn=True
Enabling redo button: 2 moves available to redo
Starting undo with 4 moves in move stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Undoing 2 moves
New board state: rnbqkb1r/pppppppp/5n2/8/8/3P4/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 4 moves available to redo
Starting redo with 4 moves in redo stack
Current board state: rnbqkb1r/pppppppp/5n2/8/8/3P4/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Redoing 1 moves
New board state: rnbqkb1r/pppppppp/5n2/8/4P3/3P4/PPP2PPP/RNBQKBNR b KQkq - 0 2
Enabling undo button: human_color=True, moves=3, turn=False
Enabling redo button: 3 moves available to redo
Starting redo with 3 moves in redo stack
Current board state: rnbqkb1r/pppppppp/5n2/8/4P3/3P4/PPP2PPP/RNBQKBNR b KQkq - 0 2
Redoing 1 moves
New board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Enabling undo button: human_color=True, moves=4, turn=True
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Redoing 1 moves
New board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P1P2/PPP3PP/RNBQKBNR b KQkq - 0 3
Enabling undo button: human_color=True, moves=5, turn=False
Enabling redo button: 1 moves available to redo
Starting redo with 1 moves in redo stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P1P2/PPP3PP/RNBQKBNR b KQkq - 0 3
Redoing 1 moves
New board state: r1bqkb1r/pppppppp/5n2/8/3nP3/3P1P2/PPP3PP/RNBQKBNR w KQkq - 1 4
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
Starting undo with 6 moves in move stack
Current board state: r1bqkb1r/pppppppp/5n2/8/3nP3/3P1P2/PPP3PP/RNBQKBNR w KQkq - 1 4
Undoing 2 moves
New board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=4, turn=True
Enabling redo button: 2 moves available to redo
Enabling undo button: human_color=True, moves=5, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2024 positions in 0.45 seconds
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=7, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2785 positions in 0.74 seconds
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=9, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 4343 positions in 0.94 seconds
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 10 moves in move stack
Current board state: r1bqkb1r/ppp1pppp/3p1n2/8/4P3/3P1P2/PPP1B1PP/RNBQK2R w KQkq - 0 6
Undoing 2 moves
New board state: r1bqkb1r/pppppppp/5n2/8/4P3/3P1P2/PPP1n1PP/RNBQKB1R w KQkq - 0 5
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=8, turn=True
Enabling redo button: 2 moves available to redo
Starting undo with 8 moves in move stack
Current board state: r1bqkb1r/pppppppp/5n2/8/4P3/3P1P2/PPP1n1PP/RNBQKB1R w KQkq - 0 5
Undoing 2 moves
New board state: r1bqkb1r/pppppppp/5n2/8/3nP3/3P1P2/PPP3PP/RNBQKBNR w KQkq - 1 4
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=True, moves=6, turn=True
Enabling redo button: 4 moves available to redo
Starting redo with 4 moves in redo stack
Current board state: r1bqkb1r/pppppppp/5n2/8/3nP3/3P1P2/PPP3PP/RNBQKBNR w KQkq - 1 4
Redoing 1 moves
New board state: r1bqkb1r/pppppppp/5n2/8/3nP3/3P1P2/PPP1N1PP/RNBQKB1R b KQkq - 2 4
Enabling undo button: human_color=True, moves=7, turn=False
Enabling redo button: 3 moves available to redo
Starting redo with 3 moves in redo stack
Current board state: r1bqkb1r/pppppppp/5n2/8/3nP3/3P1P2/PPP1N1PP/RNBQKB1R b KQkq - 2 4
Redoing 1 moves
New board state: r1bqkb1r/pppppppp/5n2/8/4P3/3P1P2/PPP1n1PP/RNBQKB1R w KQkq - 0 5
Enabling undo button: human_color=True, moves=8, turn=True
Enabling redo button: 2 moves available to redo
ChessBot search depth set to 5
Starting redo with 2 moves in redo stack
Current board state: r1bqkb1r/pppppppp/5n2/8/4P3/3P1P2/PPP1n1PP/RNBQKB1R w KQkq - 0 5
Redoing 1 moves
New board state: r1bqkb1r/pppppppp/5n2/8/4P3/3P1P2/PPP1B1PP/RNBQK2R b KQkq - 0 5
Enabling undo button: human_color=True, moves=9, turn=False
Enabling redo button: 1 moves available to redo
Starting redo with 1 moves in redo stack
Current board state: r1bqkb1r/pppppppp/5n2/8/4P3/3P1P2/PPP1B1PP/RNBQK2R b KQkq - 0 5
Redoing 1 moves
New board state: r1bqkb1r/ppp1pppp/3p1n2/8/4P3/3P1P2/PPP1B1PP/RNBQK2R w KQkq - 0 6
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
Enabling undo button: human_color=True, moves=11, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled












=====================================================================


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_arrow.py
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
Enabling undo button: human_color=True, moves=1, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 743 positions in 0.17 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=3, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1328 positions in 0.36 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 4 moves in move stack
Current board state: rnbqkb1r/pppppppp/8/3n4/8/3PP3/PPP2PPP/RNBQKBNR w KQkq - 1 3
Undoing 2 moves
New board state: rnbqkb1r/pppppppp/5n2/8/8/3P4/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Enabling undo button: human_color=True, moves=3, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1227 positions in 0.30 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 4 moves in move stack
Current board state: rnbqkb1r/pppppppp/8/3n4/8/3P1P2/PPP1P1PP/RNBQKBNR w KQkq - 1 3
Undoing 2 moves
New board state: rnbqkb1r/pppppppp/5n2/8/8/3P4/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Enabling undo button: human_color=True, moves=3, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1399 positions in 0.39 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=5, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1864 positions in 0.49 seconds
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=7, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1881 positions in 0.51 seconds
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=9, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2693 positions in 0.74 seconds
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=11, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1545 positions in 0.35 seconds
Enabling undo button: human_color=True, moves=12, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=12, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=13, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1167 positions in 0.33 seconds
Enabling undo button: human_color=True, moves=14, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=14, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 14 moves in move stack
Current board state: r1bqkb1r/pppppppp/8/8/4PP2/2KP4/PP4PP/RNB2BNn w kq - 0 8
Undoing 2 moves
New board state: r1bqkb1r/pppppppp/8/8/4PP2/3P4/PPK2nPP/RNB2BNR w kq - 1 7
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=12, turn=True
Enabling redo button: 2 moves available to redo
Starting undo with 12 moves in move stack
Current board state: r1bqkb1r/pppppppp/8/8/4PP2/3P4/PPK2nPP/RNB2BNR w kq - 1 7
Undoing 2 moves
New board state: r1bqkb1r/pppppppp/8/8/4PPn1/3P4/PPnK2PP/RNB2BNR w kq - 0 6
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=True, moves=10, turn=True
Enabling redo button: 4 moves available to redo
Starting undo with 10 moves in move stack
Current board state: r1bqkb1r/pppppppp/8/8/4PPn1/3P4/PPnK2PP/RNB2BNR w kq - 0 6
Undoing 2 moves
New board state: r1bqkb1r/pppppppp/5n2/8/4PPQ1/3P4/PPn3PP/RNB1KBNR w KQkq - 0 5
Added 2 moves to redo stack, now has 6 moves
Enabling undo button: human_color=True, moves=8, turn=True
Enabling redo button: 6 moves available to redo
Enabling undo button: human_color=True, moves=9, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1511 positions in 0.28 seconds
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 10 moves in move stack
Current board state: r1bqkb1r/pppppppp/8/8/4PPn1/3P4/PPn2KPP/RNB2BNR w kq - 0 6
Undoing 2 moves
New board state: r1bqkb1r/pppppppp/5n2/8/4PPQ1/3P4/PPn3PP/RNB1KBNR w KQkq - 0 5
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=8, turn=True
Enabling redo button: 2 moves available to redo
Starting undo with 8 moves in move stack
Current board state: r1bqkb1r/pppppppp/5n2/8/4PPQ1/3P4/PPn3PP/RNB1KBNR w KQkq - 0 5
Undoing 2 moves
New board state: r1bqkb1r/pppppppp/5n2/8/3nPP2/3P4/PPP3PP/RNBQKBNR w KQkq - 1 4
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=True, moves=6, turn=True
Enabling redo button: 4 moves available to redo
Starting undo with 6 moves in move stack
Current board state: r1bqkb1r/pppppppp/5n2/8/3nPP2/3P4/PPP3PP/RNBQKBNR w KQkq - 1 4
Undoing 2 moves
New board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Added 2 moves to redo stack, now has 6 moves
Enabling undo button: human_color=True, moves=4, turn=True
Enabling redo button: 6 moves available to redo
Starting undo with 4 moves in move stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Undoing 2 moves
New board state: rnbqkb1r/pppppppp/5n2/8/8/3P4/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Added 2 moves to redo stack, now has 8 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 8 moves available to redo
Starting redo with 8 moves in redo stack
Current board state: rnbqkb1r/pppppppp/5n2/8/8/3P4/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Redoing 1 move
New board state: rnbqkb1r/pppppppp/5n2/8/4P3/3P4/PPP2PPP/RNBQKBNR b KQkq - 0 2
Enabling undo button: human_color=True, moves=3, turn=False
Enabling redo button: 7 moves available to redo
Starting redo with 7 moves in redo stack
Current board state: rnbqkb1r/pppppppp/5n2/8/4P3/3P4/PPP2PPP/RNBQKBNR b KQkq - 0 2
Redoing 1 move
New board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Enabling undo button: human_color=True, moves=4, turn=True
Enabling redo button: 6 moves available to redo
Starting redo with 6 moves in redo stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Redoing 1 move
New board state: r1bqkb1r/pppppppp/2n2n2/8/4PP2/3P4/PPP3PP/RNBQKBNR b KQkq - 0 3
Enabling undo button: human_color=True, moves=5, turn=False
Enabling redo button: 5 moves available to redo
Starting redo with 5 moves in redo stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/4PP2/3P4/PPP3PP/RNBQKBNR b KQkq - 0 3
Redoing 1 move
New board state: r1bqkb1r/pppppppp/5n2/8/3nPP2/3P4/PPP3PP/RNBQKBNR w KQkq - 1 4
Enabling undo button: human_color=True, moves=6, turn=True
Enabling redo button: 4 moves available to redo
Enabling undo button: human_color=True, moves=7, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 3586 positions in 0.75 seconds
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 8 moves in move stack
Current board state: r1bqkb1r/pppppppp/5n2/8/4PP2/3P1n2/PPP3PP/RNBQKB1R w KQkq - 0 5
Undoing 2 moves
New board state: r1bqkb1r/pppppppp/5n2/8/3nPP2/3P4/PPP3PP/RNBQKBNR w KQkq - 1 4
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=6, turn=True
Enabling redo button: 2 moves available to redo
Starting undo with 6 moves in move stack
Current board state: r1bqkb1r/pppppppp/5n2/8/3nPP2/3P4/PPP3PP/RNBQKBNR w KQkq - 1 4
Undoing 2 moves
New board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=True, moves=4, turn=True
Enabling redo button: 4 moves available to redo
Starting undo with 4 moves in move stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Undoing 2 moves
New board state: rnbqkb1r/pppppppp/5n2/8/8/3P4/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Added 2 moves to redo stack, now has 6 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 6 moves available to redo
Starting redo with 6 moves in redo stack
Current board state: rnbqkb1r/pppppppp/5n2/8/8/3P4/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Redoing 1 move
New board state: rnbqkb1r/pppppppp/5n2/8/4P3/3P4/PPP2PPP/RNBQKBNR b KQkq - 0 2
Enabling undo button: human_color=True, moves=3, turn=False
Enabling redo button: 5 moves available to redo
Starting redo with 5 moves in redo stack
Current board state: rnbqkb1r/pppppppp/5n2/8/4P3/3P4/PPP2PPP/RNBQKBNR b KQkq - 0 2
Redoing 1 move
New board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Enabling undo button: human_color=True, moves=4, turn=True
Enabling redo button: 4 moves available to redo
Starting redo with 4 moves in redo stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Redoing 1 move
New board state: r1bqkb1r/pppppppp/2n2n2/8/4PP2/3P4/PPP3PP/RNBQKBNR b KQkq - 0 3
Enabling undo button: human_color=True, moves=5, turn=False
Enabling redo button: 3 moves available to redo
Starting redo with 3 moves in redo stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/4PP2/3P4/PPP3PP/RNBQKBNR b KQkq - 0 3
Redoing 1 move
New board state: r1bqkb1r/pppppppp/5n2/8/3nPP2/3P4/PPP3PP/RNBQKBNR w KQkq - 1 4
Enabling undo button: human_color=True, moves=6, turn=True
Enabling redo button: 2 moves available to redo
Enabling undo button: human_color=True, moves=7, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1854 positions in 0.47 seconds
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=9, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 4319 positions in 0.95 seconds
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
Colors switched: human_color=False, board.turn=True
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 715 positions in 0.18 seconds
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling redo button: No moves to redo
Board interaction re-enabled
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling redo button: No moves to redo
Enabling undo button: human_color=False, moves=2, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 759 positions in 0.20 seconds
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=4, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2304 positions in 0.63 seconds
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=6, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1374 positions in 0.26 seconds
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 7 moves in move stack
Current board state: rnbqkbnr/ppp3pp/3p4/5p2/2N5/2N5/PPPPPPPP/R1BQKB1R b KQkq - 1 4
Undoing 2 moves
New board state: rnbqkbnr/ppp2ppp/3p4/8/2N5/8/PPPPPPPP/RNBQKB1R b KQkq - 1 3
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=False, moves=5, turn=False
Enabling redo button: 2 moves available to redo
Starting undo with 5 moves in move stack
Current board state: rnbqkbnr/ppp2ppp/3p4/8/2N5/8/PPPPPPPP/RNBQKB1R b KQkq - 1 3
Undoing 2 moves
New board state: rnbqkbnr/pppp1ppp/8/4N3/8/8/PPPPPPPP/RNBQKB1R b KQkq - 0 2
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=False, moves=3, turn=False
Enabling redo button: 4 moves available to redo
Enabling undo button: human_color=False, moves=4, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2304 positions in 0.52 seconds
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 5 moves in move stack
Current board state: rnbqkbnr/ppp2ppp/3p4/8/2N5/8/PPPPPPPP/RNBQKB1R b KQkq - 1 3
Undoing 2 moves
New board state: rnbqkbnr/pppp1ppp/8/4N3/8/8/PPPPPPPP/RNBQKB1R b KQkq - 0 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=False, moves=3, turn=False
Enabling redo button: 2 moves available to redo
Starting undo with 3 moves in move stack
Current board state: rnbqkbnr/pppp1ppp/8/4N3/8/8/PPPPPPPP/RNBQKB1R b KQkq - 0 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Added 2 moves to redo stack, now has 4 moves
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling redo button: 4 moves available to redo
Starting redo with 4 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Redoing 1 move
New board state: rnbqkbnr/pppp1ppp/8/4p3/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Enabling undo button: human_color=False, moves=2, turn=True
Enabling redo button: 3 moves available to redo
Starting redo with 3 moves in redo stack
Current board state: rnbqkbnr/pppp1ppp/8/4p3/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Redoing 1 move
New board state: rnbqkbnr/pppp1ppp/8/4N3/8/8/PPPPPPPP/RNBQKB1R b KQkq - 0 2
Enabling undo button: human_color=False, moves=3, turn=False
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pppp1ppp/8/4N3/8/8/PPPPPPPP/RNBQKB1R b KQkq - 0 2
Redoing 1 move
New board state: rnbqkbnr/ppp2ppp/3p4/4N3/8/8/PPPPPPPP/RNBQKB1R w KQkq - 0 3
Enabling undo button: human_color=False, moves=4, turn=True
Enabling redo button: 1 moves available to redo
Starting redo with 1 moves in redo stack
Current board state: rnbqkbnr/ppp2ppp/3p4/4N3/8/8/PPPPPPPP/RNBQKB1R w KQkq - 0 3
Redoing 1 move
New board state: rnbqkbnr/ppp2ppp/3p4/8/2N5/8/PPPPPPPP/RNBQKB1R b KQkq - 1 3
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
Enabling undo button: human_color=False, moves=6, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1588 positions in 0.45 seconds
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=8, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2265 positions in 0.41 seconds
Enabling undo button: human_color=False, moves=9, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=9, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=10, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1548 positions in 0.30 seconds
Enabling undo button: human_color=False, moves=11, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=11, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=12, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 3383 positions in 0.57 seconds
Enabling undo button: human_color=False, moves=13, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=13, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=14, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 3212 positions in 0.54 seconds
Enabling undo button: human_color=False, moves=15, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=15, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 15 moves in move stack
Current board state: rnb1k2r/pp2qp1p/2pb1n2/N5p1/8/8/PPPPPPPP/1RBQKB1R b Kkq - 3 8
Undoing 2 moves
New board state: rnbqk2r/pp3p1p/2pb1n2/6p1/2N5/8/PPPPPPPP/1RBQKB1R b Kkq - 1 7
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=False, moves=13, turn=False
Enabling redo button: 2 moves available to redo
Starting undo with 13 moves in move stack
Current board state: rnbqk2r/pp3p1p/2pb1n2/6p1/2N5/8/PPPPPPPP/1RBQKB1R b Kkq - 1 7
Undoing 2 moves
New board state: rnbqkb1r/pp3p1p/2pN1n2/6p1/2N5/8/PPPPPPPP/R1BQKB1R b KQkq - 0 6
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=False, moves=11, turn=False
Enabling redo button: 4 moves available to redo
Enabling undo button: human_color=False, moves=12, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 3383 positions in 0.61 seconds
Enabling undo button: human_color=False, moves=13, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=13, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 13 moves in move stack
Current board state: rnbqk2r/pp3p1p/2pb1n2/6p1/2N5/8/PPPPPPPP/1RBQKB1R b Kkq - 1 7
Undoing 2 moves
New board state: rnbqkb1r/pp3p1p/2pN1n2/6p1/2N5/8/PPPPPPPP/R1BQKB1R b KQkq - 0 6
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=False, moves=11, turn=False
Enabling redo button: 2 moves available to redo
Enabling undo button: human_color=False, moves=12, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2200 positions in 0.38 seconds
Enabling undo button: human_color=False, moves=13, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=13, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 13 moves in move stack
Current board state: rnb1kb1r/pp3p1p/2pN1n2/6p1/8/8/PPPPPPPP/R1BQKB1R b KQkq - 0 7
Undoing 2 moves
New board state: rnbqkb1r/pp3p1p/2pN1n2/6p1/2N5/8/PPPPPPPP/R1BQKB1R b KQkq - 0 6
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=False, moves=11, turn=False
Enabling redo button: 2 moves available to redo
Starting undo with 11 moves in move stack
Current board state: rnbqkb1r/pp3p1p/2pN1n2/6p1/2N5/8/PPPPPPPP/R1BQKB1R b KQkq - 0 6
Undoing 2 moves
New board state: rnbqkb1r/ppp2p1p/3p1n2/1N4p1/2N5/8/PPPPPPPP/R1BQKB1R b KQkq - 1 5
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=False, moves=9, turn=False
Enabling redo button: 4 moves available to redo
Starting undo with 9 moves in move stack
Current board state: rnbqkb1r/ppp2p1p/3p1n2/1N4p1/2N5/8/PPPPPPPP/R1BQKB1R b KQkq - 1 5
Undoing 2 moves
New board state: rnbqkb1r/ppp2ppp/3p1n2/8/2N5/2N5/PPPPPPPP/R1BQKB1R b KQkq - 3 4
Added 2 moves to redo stack, now has 6 moves
Enabling undo button: human_color=False, moves=7, turn=False
Enabling redo button: 6 moves available to redo
Starting undo with 7 moves in move stack
Current board state: rnbqkb1r/ppp2ppp/3p1n2/8/2N5/2N5/PPPPPPPP/R1BQKB1R b KQkq - 3 4
Undoing 2 moves
New board state: rnbqkbnr/ppp2ppp/3p4/8/2N5/8/PPPPPPPP/RNBQKB1R b KQkq - 1 3
Added 2 moves to redo stack, now has 8 moves
Enabling undo button: human_color=False, moves=5, turn=False
Enabling redo button: 8 moves available to redo
Enabling undo button: human_color=False, moves=6, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1245 positions in 0.22 seconds
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 7 moves in move stack
Current board state: rnbqkbnr/ppp3pp/3p1p2/8/2N5/2N5/PPPPPPPP/R1BQKB1R b KQkq - 1 4
Undoing 2 moves
New board state: rnbqkbnr/ppp2ppp/3p4/8/2N5/8/PPPPPPPP/RNBQKB1R b KQkq - 1 3
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=False, moves=5, turn=False
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/ppp2ppp/3p4/8/2N5/8/PPPPPPPP/RNBQKB1R b KQkq - 1 3
Redoing 1 move
New board state: rnbqkbnr/ppp3pp/3p1p2/8/2N5/8/PPPPPPPP/RNBQKB1R w KQkq - 0 4
Enabling undo button: human_color=False, moves=6, turn=True
Enabling redo button: 1 moves available to redo
Starting redo with 1 moves in redo stack
Current board state: rnbqkbnr/ppp3pp/3p1p2/8/2N5/8/PPPPPPPP/RNBQKB1R w KQkq - 0 4
Redoing 1 move
New board state: rnbqkbnr/ppp3pp/3p1p2/8/2N5/2N5/PPPPPPPP/R1BQKB1R b KQkq - 1 4
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo






==========================================================
==========================================================





PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_arrow.py
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
Colors switched: human_color=False, board.turn=True
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 715 positions in 0.11 seconds
Board interaction disabled
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling redo button: No moves to redo
Board interaction re-enabled
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling redo button: No moves to redo
Enabling undo button: human_color=False, moves=2, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 918 positions in 0.18 seconds
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 3 moves in move stack
Current board state: rnbqkbnr/ppppp1pp/5p2/8/3N4/8/PPPPPPPP/RNBQKB1R b KQkq - 1 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Added 2 moves to redo stack, now has 2 moves
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Redoing 1 move
New board state: rnbqkbnr/ppppp1pp/5p2/8/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Enabling undo button: human_color=False, moves=2, turn=True
Enabling redo button: 1 moves available to redo
Starting redo with 1 moves in redo stack
Current board state: rnbqkbnr/ppppp1pp/5p2/8/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Redoing 1 move
New board state: rnbqkbnr/ppppp1pp/5p2/8/3N4/8/PPPPPPPP/RNBQKB1R b KQkq - 1 2
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Starting undo with 3 moves in move stack
Current board state: rnbqkbnr/ppppp1pp/5p2/8/3N4/8/PPPPPPPP/RNBQKB1R b KQkq - 1 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Added 2 moves to redo stack, now has 2 moves
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Redoing 1 move
New board state: rnbqkbnr/ppppp1pp/5p2/8/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Enabling undo button: human_color=False, moves=2, turn=True
Enabling redo button: 1 moves available to redo
Starting redo with 1 moves in redo stack
Current board state: rnbqkbnr/ppppp1pp/5p2/8/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Redoing 1 move
New board state: rnbqkbnr/ppppp1pp/5p2/8/3N4/8/PPPPPPPP/RNBQKB1R b KQkq - 1 2
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Colors switched: human_color=True, board.turn=False
Enabling undo button: human_color=True, moves=3, turn=False
Disabling redo button: No moves to redo
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1405 positions in 0.27 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Starting undo with 4 moves in move stack
Current board state: r1bqkbnr/ppppp1pp/2n2p2/8/3N4/8/PPPPPPPP/RNBQKB1R w KQkq - 2 3
Undoing 2 moves
New board state: rnbqkbnr/ppppp1pp/5p2/8/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Starting undo with 2 moves in move stack
Current board state: rnbqkbnr/ppppp1pp/5p2/8/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves to redo stack, now has 4 moves
Disabling undo button: No moves in stack
Enabling redo button: 4 moves available to redo
Starting redo with 4 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Redoing 1 move
New board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Enabling undo button: human_color=True, moves=1, turn=False
Enabling redo button: 3 moves available to redo
Starting redo with 3 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Redoing 1 move
New board state: rnbqkbnr/ppppp1pp/5p2/8/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Enabling undo button: human_color=True, moves=3, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 772 positions in 0.19 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=5, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 976 positions in 0.23 seconds
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=7, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2454 positions in 0.61 seconds
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
ChessBot search depth set to 2
Enabling undo button: human_color=True, moves=1, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 100 positions in 0.02 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 2 moves in move stack
Current board state: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves to redo stack, now has 2 moves
Disabling undo button: No moves in stack
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Redoing 1 move
New board state: rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq - 0 1
Enabling undo button: human_color=True, moves=1, turn=False
Enabling redo button: 1 moves available to redo
Colors switched: human_color=False, board.turn=False
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling redo button: No moves to redo
Enabling undo button: human_color=False, moves=2, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 137 positions in 0.02 seconds
Board interaction disabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=4, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 360 positions in 0.09 seconds
Board interaction disabled
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=6, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 415 positions in 0.08 seconds
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=8, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 339 positions in 0.07 seconds
Enabling undo button: human_color=False, moves=9, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=9, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=10, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 257 positions in 0.08 seconds
Enabling undo button: human_color=False, moves=11, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=11, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Colors switched: human_color=True, board.turn=False
Enabling undo button: human_color=True, moves=11, turn=False
Disabling redo button: No moves to redo
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 51 positions in 0.01 seconds
Board interaction disabled
Enabling undo button: human_color=True, moves=12, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=12, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=12, turn=True
Disabling redo button: No moves to redo
Enabling undo button: human_color=True, moves=13, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 164 positions in 0.03 seconds
Board interaction disabled
Enabling undo button: human_color=True, moves=14, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=14, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 14 moves in move stack
Current board state: rnbq1b1r/ppppp2k/8/7n/8/8/PPPP1PPP/RNB1KBNR w KQ - 0 8
Undoing 2 moves
New board state: rnbq1b1r/ppppp2k/5n2/6Q1/8/8/PPPP1PPP/RNB1KBNR w KQ - 0 7
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=12, turn=True
Enabling redo button: 2 moves available to redo
Starting undo with 12 moves in move stack
Current board state: rnbq1b1r/ppppp2k/5n2/6Q1/8/8/PPPP1PPP/RNB1KBNR w KQ - 0 7
Undoing 2 moves
New board state: rnbq1b1r/ppppp1kp/5nP1/6Q1/8/8/PPPP1PPP/RNB1KBNR w KQ - 1 6
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=True, moves=10, turn=True
Enabling redo button: 4 moves available to redo
Starting undo with 10 moves in move stack
Current board state: rnbq1b1r/ppppp1kp/5nP1/6Q1/8/8/PPPP1PPP/RNB1KBNR w KQ - 1 6
Undoing 2 moves
New board state: rnbq1b1r/pppppk1p/5np1/5PQ1/8/8/PPPP1PPP/RNB1KBNR w KQ - 1 5
Added 2 moves to redo stack, now has 6 moves
Enabling undo button: human_color=True, moves=8, turn=True
Enabling redo button: 6 moves available to redo
Starting undo with 8 moves in move stack
Current board state: rnbq1b1r/pppppk1p/5np1/5PQ1/8/8/PPPP1PPP/RNB1KBNR w KQ - 1 5
Undoing 2 moves
New board state: rnbqkb1r/ppppp2p/5np1/5pQ1/4P3/8/PPPP1PPP/RNB1KBNR w KQkq - 2 4
Added 2 moves to redo stack, now has 8 moves
Enabling undo button: human_color=True, moves=6, turn=True
Enabling redo button: 8 moves available to redo
Starting undo with 6 moves in move stack
Current board state: rnbqkb1r/ppppp2p/5np1/5pQ1/4P3/8/PPPP1PPP/RNB1KBNR w KQkq - 2 4
Undoing 2 moves
New board state: rnbqkbnr/ppppp2p/6p1/5p1Q/4P3/8/PPPP1PPP/RNB1KBNR w KQkq - 0 3
Added 2 moves to redo stack, now has 10 moves
Enabling undo button: human_color=True, moves=4, turn=True
Enabling redo button: 10 moves available to redo
Starting undo with 4 moves in move stack
Current board state: rnbqkbnr/ppppp2p/6p1/5p1Q/4P3/8/PPPP1PPP/RNB1KBNR w KQkq - 0 3
Undoing 2 moves
New board state: rnbqkbnr/ppppp1pp/8/5p2/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2
Added 2 moves to redo stack, now has 12 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 12 moves available to redo
Starting undo with 2 moves in move stack
Current board state: rnbqkbnr/ppppp1pp/8/5p2/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves to redo stack, now has 14 moves
Disabling undo button: No moves in stack
Enabling redo button: 14 moves available to redo
Colors switched: human_color=False, board.turn=True
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
Bot's turn after color switch, making bot move
ChessBot evaluated 80 positions in 0.01 seconds
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling redo button: No moves to redo
Board interaction re-enabled
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling redo button: No moves to redo
Enabling undo button: human_color=False, moves=2, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 100 positions in 0.03 seconds
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=4, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 299 positions in 0.08 seconds
Board interaction disabled
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=6, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 191 positions in 0.05 seconds
Board interaction disabled
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 7 moves in move stack
Current board state: rnbqkbnr/pp3ppp/3p4/2p5/2N5/2N5/PPPPPPPP/R1BQKB1R b KQkq - 1 4
Undoing 2 moves
New board state: rnbqkbnr/ppp2ppp/3p4/8/2N5/8/PPPPPPPP/RNBQKB1R b KQkq - 1 3
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=False, moves=5, turn=False
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/ppp2ppp/3p4/8/2N5/8/PPPPPPPP/RNBQKB1R b KQkq - 1 3
Redoing 1 move
New board state: rnbqkbnr/pp3ppp/3p4/2p5/2N5/8/PPPPPPPP/RNBQKB1R w KQkq - 0 4
Enabling undo button: human_color=False, moves=6, turn=True
Enabling redo button: 1 moves available to redo
Colors switched: human_color=True, board.turn=True
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
Enabling undo button: human_color=True, moves=7, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 97 positions in 0.03 seconds
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Colors switched: human_color=False, board.turn=True
Enabling undo button: human_color=False, moves=8, turn=True
Disabling redo button: No moves to redo
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 619 positions in 0.15 seconds
Board interaction disabled
Enabling undo button: human_color=False, moves=9, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=9, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=9, turn=False
Disabling redo button: No moves to redo
Enabling undo button: human_color=False, moves=10, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 759 positions in 0.16 seconds
Enabling undo button: human_color=False, moves=11, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=11, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=12, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 483 positions in 0.12 seconds
Enabling undo button: human_color=False, moves=13, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=13, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 13 moves in move stack
Current board state: rnb1kb1r/pp3p1p/3q1n2/2p3B1/8/2NP4/PPP1PPPP/R2QKB1R b KQkq - 0 7
Undoing 2 moves
New board state: rnb1kb1r/pp3ppp/3q1n2/2p5/8/2NP4/PPP1PPPP/R1BQKB1R b KQkq - 0 6
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=False, moves=11, turn=False
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnb1kb1r/pp3ppp/3q1n2/2p5/8/2NP4/PPP1PPPP/R1BQKB1R b KQkq - 0 6
Redoing 1 move
New board state: rnb1kb1r/pp3p1p/3q1n2/2p3p1/8/2NP4/PPP1PPPP/R1BQKB1R w KQkq - 0 7
Enabling undo button: human_color=False, moves=12, turn=True
Enabling redo button: 1 moves available to redo
Starting redo with 1 moves in redo stack
Current board state: rnb1kb1r/pp3p1p/3q1n2/2p3p1/8/2NP4/PPP1PPPP/R1BQKB1R w KQkq - 0 7
Redoing 1 move
New board state: rnb1kb1r/pp3p1p/3q1n2/2p3B1/8/2NP4/PPP1PPPP/R2QKB1R b KQkq - 0 7
Enabling undo button: human_color=False, moves=13, turn=False
Disabling redo button: No moves to redo
Colors switched: human_color=True, board.turn=False
Enabling undo button: human_color=True, moves=13, turn=False
Disabling redo button: No moves to redo
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 328 positions in 0.08 seconds
Board interaction disabled
Enabling undo button: human_color=True, moves=14, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=14, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=14, turn=True
Disabling redo button: No moves to redo
Colors switched: human_color=False, board.turn=True
Enabling undo button: human_color=False, moves=14, turn=True
Disabling redo button: No moves to redo
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 967 positions in 0.21 seconds
Enabling undo button: human_color=False, moves=15, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=15, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=15, turn=False
Disabling redo button: No moves to redo
Starting undo with 15 moves in move stack
Current board state: rnb1kb1r/pp3p1p/3q4/2p3B1/4N1n1/3P4/PPP1PPPP/R2QKB1R b KQkq - 2 8
Undoing 2 moves
New board state: rnb1kb1r/pp3p1p/3q1n2/2p3B1/8/2NP4/PPP1PPPP/R2QKB1R b KQkq - 0 7
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=False, moves=13, turn=False
Enabling redo button: 2 moves available to redo
Starting undo with 13 moves in move stack
Current board state: rnb1kb1r/pp3p1p/3q1n2/2p3B1/8/2NP4/PPP1PPPP/R2QKB1R b KQkq - 0 7
Undoing 2 moves
New board state: rnb1kb1r/pp3ppp/3q1n2/2p5/8/2NP4/PPP1PPPP/R1BQKB1R b KQkq - 0 6
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=False, moves=11, turn=False
Enabling redo button: 4 moves available to redo
Starting undo with 11 moves in move stack
Current board state: rnb1kb1r/pp3ppp/3q1n2/2p5/8/2NP4/PPP1PPPP/R1BQKB1R b KQkq - 0 6
Undoing 2 moves
New board state: rnb1kbnr/pp3ppp/3q4/2p5/8/2N5/PPPPPPPP/R1BQKB1R b KQkq - 1 5
Added 2 moves to redo stack, now has 6 moves
Enabling undo button: human_color=False, moves=9, turn=False
Enabling redo button: 6 moves available to redo
Starting redo with 6 moves in redo stack
Current board state: rnb1kbnr/pp3ppp/3q4/2p5/8/2N5/PPPPPPPP/R1BQKB1R b KQkq - 1 5
Redoing 1 move
New board state: rnb1kb1r/pp3ppp/3q1n2/2p5/8/2N5/PPPPPPPP/R1BQKB1R w KQkq - 2 6
Enabling undo button: human_color=False, moves=10, turn=True
Enabling redo button: 5 moves available to redo
Starting redo with 5 moves in redo stack
Current board state: rnb1kb1r/pp3ppp/3q1n2/2p5/8/2N5/PPPPPPPP/R1BQKB1R w KQkq - 2 6
Redoing 1 move
New board state: rnb1kb1r/pp3ppp/3q1n2/2p5/8/2NP4/PPP1PPPP/R1BQKB1R b KQkq - 0 6
Enabling undo button: human_color=False, moves=11, turn=False
Enabling redo button: 4 moves available to redo
Starting undo with 11 moves in move stack
Current board state: rnb1kb1r/pp3ppp/3q1n2/2p5/8/2NP4/PPP1PPPP/R1BQKB1R b KQkq - 0 6
Undoing 2 moves
New board state: rnb1kbnr/pp3ppp/3q4/2p5/8/2N5/PPPPPPPP/R1BQKB1R b KQkq - 1 5
Added 2 moves to redo stack, now has 6 moves
Enabling undo button: human_color=False, moves=9, turn=False
Enabling redo button: 6 moves available to redo
Starting redo with 6 moves in redo stack
Current board state: rnb1kbnr/pp3ppp/3q4/2p5/8/2N5/PPPPPPPP/R1BQKB1R b KQkq - 1 5
Redoing 1 move
New board state: rnb1kb1r/pp3ppp/3q1n2/2p5/8/2N5/PPPPPPPP/R1BQKB1R w KQkq - 2 6
Enabling undo button: human_color=False, moves=10, turn=True
Enabling redo button: 5 moves available to redo
Colors switched: human_color=True, board.turn=True
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
Enabling undo button: human_color=True, moves=11, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 240 positions in 0.05 seconds
Enabling undo button: human_color=True, moves=12, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=12, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=13, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 260 positions in 0.05 seconds
Enabling undo button: human_color=True, moves=14, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=14, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 14 moves in move stack
Current board state: r3kb1r/pp3ppp/2nqbn2/2p5/4P3/2N2P2/PPPP2PP/R1BQKB1R w KQkq - 1 8
Undoing 2 moves
New board state: r1b1kb1r/pp3ppp/2nq1n2/2p5/4P3/2N5/PPPP1PPP/R1BQKB1R w KQkq - 1 7
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=12, turn=True
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: r1b1kb1r/pp3ppp/2nq1n2/2p5/4P3/2N5/PPPP1PPP/R1BQKB1R w KQkq - 1 7
Redoing 1 move
New board state: r1b1kb1r/pp3ppp/2nq1n2/2p5/4P3/2N2P2/PPPP2PP/R1BQKB1R b KQkq - 0 7
Enabling undo button: human_color=True, moves=13, turn=False
Enabling redo button: 1 moves available to redo
Colors switched: human_color=False, board.turn=False
Enabling undo button: human_color=False, moves=13, turn=False
Disabling redo button: No moves to redo
Enabling undo button: human_color=False, moves=14, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 275 positions in 0.04 seconds
Enabling undo button: human_color=False, moves=15, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=15, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 15 moves in move stack
Current board state: r1b1kb1r/pp3ppp/2n2n2/2p5/4P3/2N2P2/PPPP2PR/R1BQKB2 b Qkq - 0 8
Undoing 2 moves
New board state: r1b1kb1r/pp3ppp/2nq1n2/2p5/4P3/2N2P2/PPPP2PP/R1BQKB1R b KQkq - 0 7
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=False, moves=13, turn=False
Enabling redo button: 2 moves available to redo
Starting undo with 13 moves in move stack
Current board state: r1b1kb1r/pp3ppp/2nq1n2/2p5/4P3/2N2P2/PPPP2PP/R1BQKB1R b KQkq - 0 7
Undoing 2 moves
New board state: rnb1kb1r/pp3ppp/3q1n2/2p5/4P3/2N5/PPPP1PPP/R1BQKB1R b KQkq - 0 6
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=False, moves=11, turn=False
Enabling redo button: 4 moves available to redo
Starting undo with 11 moves in move stack
Current board state: rnb1kb1r/pp3ppp/3q1n2/2p5/4P3/2N5/PPPP1PPP/R1BQKB1R b KQkq - 0 6
Undoing 2 moves
New board state: rnb1kbnr/pp3ppp/3q4/2p5/8/2N5/PPPPPPPP/R1BQKB1R b KQkq - 1 5
Added 2 moves to redo stack, now has 6 moves
Enabling undo button: human_color=False, moves=9, turn=False
Enabling redo button: 6 moves available to redo
Starting undo with 9 moves in move stack
Current board state: rnb1kbnr/pp3ppp/3q4/2p5/8/2N5/PPPPPPPP/R1BQKB1R b KQkq - 1 5
Undoing 2 moves
New board state: rnbqkbnr/pp3ppp/3N4/2p5/8/8/PPPPPPPP/RNBQKB1R b KQkq - 0 4
Added 2 moves to redo stack, now has 8 moves
Enabling undo button: human_color=False, moves=7, turn=False
Enabling redo button: 8 moves available to redo
Starting redo with 8 moves in redo stack
Current board state: rnbqkbnr/pp3ppp/3N4/2p5/8/8/PPPPPPPP/RNBQKB1R b KQkq - 0 4
Redoing 1 move
New board state: rnb1kbnr/pp3ppp/3q4/2p5/8/8/PPPPPPPP/RNBQKB1R w KQkq - 0 5
Enabling undo button: human_color=False, moves=8, turn=True
Enabling redo button: 7 moves available to redo
Colors switched: human_color=True, board.turn=True
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
Enabling undo button: human_color=True, moves=9, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 156 positions in 0.02 seconds
Board interaction disabled
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled





------------------------------------------


fixing redo functionality (redo 2 moves at a time - human and bot):


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_arrow.py
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
Enabling undo button: human_color=True, moves=1, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 811 positions in 0.23 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 2 moves in move stack
Current board state: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves to redo stack, now has 2 moves
Disabling undo button: No moves in stack
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Redoing 2 moves
Redoing move 1/2: e4
Redoing move 2/2: Nf6
New board state: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Enabling undo button: human_color=True, moves=3, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1125 positions in 0.50 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 4 moves in move stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Undoing 2 moves
New board state: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Redoing 2 moves
Redoing move 1/2: d3
Redoing move 2/2: Nc6
New board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Starting undo with 4 moves in move stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Undoing 2 moves
New board state: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Starting undo with 2 moves in move stack
Current board state: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves to redo stack, now has 4 moves
Disabling undo button: No moves in stack
Enabling redo button: 4 moves available to redo
Starting redo with 4 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Redoing 2 moves
Redoing move 1/2: e4
Redoing move 2/2: Nf6
New board state: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Redoing 2 moves
Redoing move 1/2: d3
Redoing move 2/2: Nc6
New board state: r1bqkb1r/pppppppp/2n2n2/8/4P3/3P4/PPP2PPP/RNBQKBNR w KQkq - 1 3
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Enabling undo button: human_color=True, moves=5, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1734 positions in 0.45 seconds
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=7, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 939 positions in 0.38 seconds
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=8, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=9, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 3727 positions in 1.01 seconds
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=10, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=11, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1918 positions in 0.58 seconds
Enabling undo button: human_color=True, moves=12, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=12, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=13, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 962 positions in 0.40 seconds
Enabling undo button: human_color=True, moves=14, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=14, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=15, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 893 positions in 0.27 seconds
Enabling undo button: human_color=True, moves=16, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=16, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=17, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 951 positions in 0.28 seconds
Enabling undo button: human_color=True, moves=18, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=18, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=19, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2754 positions in 0.78 seconds
Enabling undo button: human_color=True, moves=20, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=20, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=21, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2879 positions in 0.76 seconds
Enabling undo button: human_color=True, moves=22, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=22, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=23, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 782 positions in 0.20 seconds
Enabling undo button: human_color=True, moves=24, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=24, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=25, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1353 positions in 0.35 seconds
Enabling undo button: human_color=True, moves=26, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=26, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=27, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1654 positions in 0.60 seconds
Enabling undo button: human_color=True, moves=28, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=28, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=29, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 631 positions in 0.15 seconds
Board interaction disabled
Enabling undo button: human_color=True, moves=30, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=30, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=31, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2851 positions in 0.72 seconds
Enabling undo button: human_color=True, moves=32, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=32, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=33, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 5382 positions in 1.33 seconds
Enabling undo button: human_color=True, moves=34, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=34, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=35, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 3559 positions in 0.87 seconds
Enabling undo button: human_color=True, moves=36, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=36, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=37, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 3285 positions in 0.82 seconds
Enabling undo button: human_color=True, moves=38, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=38, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 38 moves in move stack
Current board state: 2kr4/pp2qp2/2p1b2Q/4p3/4r3/2P5/P1P2P1P/R4R1K w - - 0 20
Undoing 2 moves
New board state: 2kr4/pp2qp2/2p1b2Q/4p3/4P1r1/2P5/P1P2P1P/R4RK1 w - - 0 19
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=36, turn=True
Enabling redo button: 2 moves available to redo
Starting undo with 36 moves in move stack
Current board state: 2kr4/pp2qp2/2p1b2Q/4p3/4P1r1/2P5/P1P2P1P/R4RK1 w - - 0 19
Undoing 2 moves
New board state: 2kr4/pp2qpQ1/2p1b3/4p1r1/4P1P1/2P5/P1P2P1P/R4RK1 w - - 1 18
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=True, moves=34, turn=True
Enabling redo button: 4 moves available to redo
Starting redo with 4 moves in redo stack
Current board state: 2kr4/pp2qpQ1/2p1b3/4p1r1/4P1P1/2P5/P1P2P1P/R4RK1 w - - 1 18
Redoing 2 moves
Redoing move 1/2: Qh6
Redoing move 2/2: Rxg4+
New board state: 2kr4/pp2qp2/2p1b2Q/4p3/4P1r1/2P5/P1P2P1P/R4RK1 w - - 0 19
Enabling undo button: human_color=True, moves=36, turn=True
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: 2kr4/pp2qp2/2p1b2Q/4p3/4P1r1/2P5/P1P2P1P/R4RK1 w - - 0 19
Redoing 2 moves
Redoing move 1/2: Kh1
Redoing move 2/2: Rxe4
New board state: 2kr4/pp2qp2/2p1b2Q/4p3/4r3/2P5/P1P2P1P/R4R1K w - - 0 20
Enabling undo button: human_color=True, moves=38, turn=True
Disabling redo button: No moves to redo
Starting undo with 38 moves in move stack
Current board state: 2kr4/pp2qp2/2p1b2Q/4p3/4r3/2P5/P1P2P1P/R4R1K w - - 0 20
Undoing 2 moves
New board state: 2kr4/pp2qp2/2p1b2Q/4p3/4P1r1/2P5/P1P2P1P/R4RK1 w - - 0 19
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=36, turn=True
Enabling redo button: 2 moves available to redo
Starting undo with 36 moves in move stack
Current board state: 2kr4/pp2qp2/2p1b2Q/4p3/4P1r1/2P5/P1P2P1P/R4RK1 w - - 0 19
Undoing 2 moves
New board state: 2kr4/pp2qpQ1/2p1b3/4p1r1/4P1P1/2P5/P1P2P1P/R4RK1 w - - 1 18
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=True, moves=34, turn=True
Enabling redo button: 4 moves available to redo
Starting undo with 34 moves in move stack
Current board state: 2kr4/pp2qpQ1/2p1b3/4p1r1/4P1P1/2P5/P1P2P1P/R4RK1 w - - 1 18
Undoing 2 moves
New board state: 2kr4/pp2qpQ1/2p1b3/4p2r/4P3/2P5/P1P2PPP/R4RK1 w - - 1 17
Added 2 moves to redo stack, now has 6 moves
Enabling undo button: human_color=True, moves=32, turn=True
Enabling redo button: 6 moves available to redo
Starting redo with 6 moves in redo stack
Current board state: 2kr4/pp2qpQ1/2p1b3/4p2r/4P3/2P5/P1P2PPP/R4RK1 w - - 1 17
Redoing 2 moves
Redoing move 1/2: g4
Redoing move 2/2: Rg5
New board state: 2kr4/pp2qpQ1/2p1b3/4p1r1/4P1P1/2P5/P1P2P1P/R4RK1 w - - 1 18
Enabling undo button: human_color=True, moves=34, turn=True
Enabling redo button: 4 moves available to redo
Starting undo with 34 moves in move stack
Current board state: 2kr4/pp2qpQ1/2p1b3/4p1r1/4P1P1/2P5/P1P2P1P/R4RK1 w - - 1 18
Undoing 2 moves
New board state: 2kr4/pp2qpQ1/2p1b3/4p2r/4P3/2P5/P1P2PPP/R4RK1 w - - 1 17
Added 2 moves to redo stack, now has 6 moves
Enabling undo button: human_color=True, moves=32, turn=True
Enabling redo button: 6 moves available to redo
Starting undo with 32 moves in move stack
Current board state: 2kr4/pp2qpQ1/2p1b3/4p2r/4P3/2P5/P1P2PPP/R4RK1 w - - 1 17
Undoing 2 moves
New board state: r3k3/pp2qpp1/2p1b3/4p2r/4P3/2P3Q1/P1P2PPP/R4RK1 w q - 0 16
Added 2 moves to redo stack, now has 8 moves
Enabling undo button: human_color=True, moves=30, turn=True
Enabling redo button: 8 moves available to redo
Starting undo with 30 moves in move stack
Current board state: r3k3/pp2qpp1/2p1b3/4p2r/4P3/2P3Q1/P1P2PPP/R4RK1 w q - 0 16
Undoing 2 moves
New board state: r3k2r/pp2qpp1/2p1b3/4p2B/4P3/2P2Q2/P1P2PPP/R4RK1 w kq - 2 15
Added 2 moves to redo stack, now has 10 moves
Enabling undo button: human_color=True, moves=28, turn=True
Enabling redo button: 10 moves available to redo
Starting undo with 28 moves in move stack
Current board state: r3k2r/pp2qpp1/2p1b3/4p2B/4P3/2P2Q2/P1P2PPP/R4RK1 w kq - 2 15
Undoing 2 moves
New board state: r3k2r/pp3pp1/2p1bq2/4p2B/4P3/2P5/P1P1QPPP/R4RK1 w kq - 0 14
Added 2 moves to redo stack, now has 12 moves
Enabling undo button: human_color=True, moves=26, turn=True
Enabling redo button: 12 moves available to redo
Starting undo with 26 moves in move stack
Current board state: r3k2r/pp3pp1/2p1bq2/4p2B/4P3/2P5/P1P1QPPP/R4RK1 w kq - 0 14
Undoing 2 moves
New board state: r3k2r/ppp2pp1/4bq2/4p2B/4p3/2PP4/P1P1QPPP/R4RK1 w kq - 0 13
Added 2 moves to redo stack, now has 14 moves
Enabling undo button: human_color=True, moves=24, turn=True
Enabling redo button: 14 moves available to redo
Starting undo with 24 moves in move stack
Current board state: r3k2r/ppp2pp1/4bq2/4p2B/4p3/2PP4/P1P1QPPP/R4RK1 w kq - 0 13
Undoing 2 moves
New board state: r3k2r/ppp2pp1/4bq2/3pp2p/4P3/2PP1B2/P1P1QPPP/R4RK1 w kq - 2 12
Added 2 moves to redo stack, now has 16 moves
Enabling undo button: human_color=True, moves=22, turn=True
Enabling redo button: 16 moves available to redo
Starting undo with 22 moves in move stack
Current board state: r3k2r/ppp2pp1/4bq2/3pp2p/4P3/2PP1B2/P1P1QPPP/R4RK1 w kq - 2 12
Undoing 2 moves
New board state: r1b1k2r/ppp2pp1/5q2/3pp2p/4P3/2PP1B2/P1P1QPPP/R3K2R w KQkq - 0 11
Added 2 moves to redo stack, now has 18 moves
Enabling undo button: human_color=True, moves=20, turn=True
Enabling redo button: 18 moves available to redo
Starting redo with 18 moves in redo stack
Current board state: r1b1k2r/ppp2pp1/5q2/3pp2p/4P3/2PP1B2/P1P1QPPP/R3K2R w KQkq - 0 11
Redoing 2 moves
Redoing move 1/2: O-O
Redoing move 2/2: Be6
New board state: r3k2r/ppp2pp1/4bq2/3pp2p/4P3/2PP1B2/P1P1QPPP/R4RK1 w kq - 2 12
Enabling undo button: human_color=True, moves=22, turn=True
Enabling redo button: 16 moves available to redo
Starting redo with 16 moves in redo stack
Current board state: r3k2r/ppp2pp1/4bq2/3pp2p/4P3/2PP1B2/P1P1QPPP/R4RK1 w kq - 2 12
Redoing 2 moves
Redoing move 1/2: Bxh5
Redoing move 2/2: dxe4
New board state: r3k2r/ppp2pp1/4bq2/4p2B/4p3/2PP4/P1P1QPPP/R4RK1 w kq - 0 13
Enabling undo button: human_color=True, moves=24, turn=True
Enabling redo button: 14 moves available to redo
Starting redo with 14 moves in redo stack
Current board state: r3k2r/ppp2pp1/4bq2/4p2B/4p3/2PP4/P1P1QPPP/R4RK1 w kq - 0 13
Redoing 2 moves
Redoing move 1/2: dxe4
Redoing move 2/2: c6
New board state: r3k2r/pp3pp1/2p1bq2/4p2B/4P3/2P5/P1P1QPPP/R4RK1 w kq - 0 14
Enabling undo button: human_color=True, moves=26, turn=True
Enabling redo button: 12 moves available to redo
Starting redo with 12 moves in redo stack
Current board state: r3k2r/pp3pp1/2p1bq2/4p2B/4P3/2P5/P1P1QPPP/R4RK1 w kq - 0 14
Redoing 2 moves
Redoing move 1/2: Qf3
Redoing move 2/2: Qe7
New board state: r3k2r/pp2qpp1/2p1b3/4p2B/4P3/2P2Q2/P1P2PPP/R4RK1 w kq - 2 15
Enabling undo button: human_color=True, moves=28, turn=True
Enabling redo button: 10 moves available to redo
Starting redo with 10 moves in redo stack
Current board state: r3k2r/pp2qpp1/2p1b3/4p2B/4P3/2P2Q2/P1P2PPP/R4RK1 w kq - 2 15
Redoing 2 moves
Redoing move 1/2: Qg3
Redoing move 2/2: Rxh5
New board state: r3k3/pp2qpp1/2p1b3/4p2r/4P3/2P3Q1/P1P2PPP/R4RK1 w q - 0 16
Enabling undo button: human_color=True, moves=30, turn=True
Enabling redo button: 8 moves available to redo
Starting undo with 30 moves in move stack
Current board state: r3k3/pp2qpp1/2p1b3/4p2r/4P3/2P3Q1/P1P2PPP/R4RK1 w q - 0 16
Undoing 2 moves
New board state: r3k2r/pp2qpp1/2p1b3/4p2B/4P3/2P2Q2/P1P2PPP/R4RK1 w kq - 2 15
Added 2 moves to redo stack, now has 10 moves
Enabling undo button: human_color=True, moves=28, turn=True
Enabling redo button: 10 moves available to redo
Starting redo with 10 moves in redo stack
Current board state: r3k2r/pp2qpp1/2p1b3/4p2B/4P3/2P2Q2/P1P2PPP/R4RK1 w kq - 2 15
Redoing 2 moves
Redoing move 1/2: Qg3
Redoing move 2/2: Rxh5
New board state: r3k3/pp2qpp1/2p1b3/4p2r/4P3/2P3Q1/P1P2PPP/R4RK1 w q - 0 16
Enabling undo button: human_color=True, moves=30, turn=True
Enabling redo button: 8 moves available to redo
Starting undo with 30 moves in move stack
Current board state: r3k3/pp2qpp1/2p1b3/4p2r/4P3/2P3Q1/P1P2PPP/R4RK1 w q - 0 16
Undoing 2 moves
New board state: r3k2r/pp2qpp1/2p1b3/4p2B/4P3/2P2Q2/P1P2PPP/R4RK1 w kq - 2 15
Added 2 moves to redo stack, now has 10 moves
Enabling undo button: human_color=True, moves=28, turn=True
Enabling redo button: 10 moves available to redo
Starting redo with 10 moves in redo stack
Current board state: r3k2r/pp2qpp1/2p1b3/4p2B/4P3/2P2Q2/P1P2PPP/R4RK1 w kq - 2 15
Redoing 2 moves
Redoing move 1/2: Qg3
Redoing move 2/2: Rxh5
New board state: r3k3/pp2qpp1/2p1b3/4p2r/4P3/2P3Q1/P1P2PPP/R4RK1 w q - 0 16
Enabling undo button: human_color=True, moves=30, turn=True
Enabling redo button: 8 moves available to redo