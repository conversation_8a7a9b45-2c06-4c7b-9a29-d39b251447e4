#!/usr/bin/env python3
"""
Chess Bot GUI - A desktop chess game with AI opponent using tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import chess
import threading
import re
from chess_bot3_QS import ChessBot
from utils import save_game_pgn, analyze_position, format_analysis, get_opening_name, get_move_quality_description
from tactical_evaluation4 import TacticalEvaluator, MoveQuality # tactical_evaluation2 ; tactical_evaluation3 ; tactical_evaluation4

class ChessGUI:
    """
    GUI Chess game using tkinter with drag-and-drop functionality.
    """
    
    def __init__(self):
        """Initialize the chess GUI."""
        self.root = tk.Tk()
        self.root.title("Chess Bot - GUI Version")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        self.board = chess.Board()
        self.bot = ChessBot(depth=3, name="ChessBot")
        self.tactical_evaluator = TacticalEvaluator()
        self.game_history = []
        self.move_quality_history = []
        self.human_color = chess.WHITE
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        self.bot_thinking = False
        
        self.light_square_color = "#F0D9B5"
        self.dark_square_color = "#B58863"
        self.highlight_color = "#FFFF00"
        self.selected_color = "#FF6B6B"
        self.legal_move_color = "#90EE90"
        
        self.piece_symbols = {
            'P': '♙', 'N': '♘', 'B': '♗', 'R': '♖', 'Q': '♕', 'K': '♔',
            'p': '♟', 'n': '♞', 'b': '♝', 'r': '♜', 'q': '♛', 'k': '♚'
        }
        
        self.setup_ui()
        self.update_board_display()
        self.update_status()
        self.root.after(100, self.update_evaluation)
    
    def setup_ui(self):
        """Set up the user interface."""
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        board_frame = ttk.Frame(main_frame)
        board_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        self.canvas = tk.Canvas(board_frame, width=480, height=480, bg="white")
        self.canvas.pack()
        self.canvas.bind("<Button-1>", self.on_square_click)
        
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        controls_group = ttk.LabelFrame(control_frame, text="Game Controls", padding=10)
        controls_group.pack(fill=tk.X, pady=(0, 10))
        
        self.new_game_btn = ttk.Button(controls_group, text="New Game", command=self.new_game)
        self.new_game_btn.pack(fill=tk.X, pady=2)
        self.switch_colors_btn = ttk.Button(controls_group, text="Switch Colors", command=self.switch_colors)
        self.switch_colors_btn.pack(fill=tk.X, pady=2)
        self.save_game_btn = ttk.Button(controls_group, text="Save Game", command=self.save_game)
        self.save_game_btn.pack(fill=tk.X, pady=2)
        self.load_game_btn = ttk.Button(controls_group, text="Load Game", command=self.load_game)
        self.load_game_btn.pack(fill=tk.X, pady=2)
        
        difficulty_group = ttk.LabelFrame(control_frame, text="Bot Difficulty", padding=10)
        difficulty_group.pack(fill=tk.X, pady=(0, 10))
        
        self.difficulty_var = tk.StringVar(value="Medium")
        difficulties = [("Easy", 2), ("Medium", 3), ("Hard", 4), ("Expert", 5)]
        
        self.difficulty_buttons = []
        for name, depth in difficulties:
            btn = ttk.Radiobutton(difficulty_group, text=f"{name} (depth {depth})", 
                           variable=self.difficulty_var, value=name,
                           command=lambda d=depth: self.set_difficulty(d))
            btn.pack(anchor=tk.W)
            self.difficulty_buttons.append(btn)
        
        status_group = ttk.LabelFrame(control_frame, text="Game Status", padding=10)
        status_group.pack(fill=tk.X, pady=(0, 10))

        self.status_label = ttk.Label(status_group, text="White to move", font=("Arial", 12))
        self.status_label.pack()

        self.opening_label = ttk.Label(status_group, text="Opening: Starting Position", font=("Arial", 10))
        self.opening_label.pack()

        eval_frame = ttk.Frame(status_group)
        eval_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(eval_frame, text="Evaluation:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.eval_label = ttk.Label(eval_frame, text="0.00", font=("Arial", 10))
        self.eval_label.pack(side=tk.LEFT, padx=(5, 0))

        self.eval_bar_frame = ttk.Frame(status_group)
        self.eval_bar_frame.pack(fill=tk.X, pady=(2, 0))

        self.eval_canvas = tk.Canvas(self.eval_bar_frame, height=20, bg="white")
        self.eval_canvas.pack(fill=tk.X, expand=True)
        self.eval_canvas.bind('<Configure>', self.on_canvas_resize)

        self.eval_description_label = ttk.Label(status_group, text="Equal position",
                                              font=("Arial", 9), foreground="gray")
        self.eval_description_label.pack()
        
        history_group = ttk.LabelFrame(control_frame, text="Move History", padding=10)
        history_group.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        history_frame = ttk.Frame(history_group)
        history_frame.pack(fill=tk.BOTH, expand=True)
        
        self.history_text = tk.Text(history_frame, height=8, width=25, wrap=tk.WORD)
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_text.yview)
        self.history_text.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        analysis_frame = ttk.Frame(control_frame)
        analysis_frame.pack(fill=tk.X, pady=(5, 0))

        self.analyze_btn = ttk.Button(analysis_frame, text="Analyze Position", command=self.show_analysis)
        self.analyze_btn.pack(fill=tk.X, pady=2)
        self.hint_btn = ttk.Button(analysis_frame, text="Show Hint", command=self.show_hint)
        self.hint_btn.pack(fill=tk.X, pady=2)
        self.move_quality_btn = ttk.Button(analysis_frame, text="Show Move Quality History", command=self.show_move_quality_history)
        self.move_quality_btn.pack(fill=tk.X, pady=2)
        self.play_from_history_btn = ttk.Button(analysis_frame, text="Play from History", command=self.play_from_history)
        self.play_from_history_btn.pack(fill=tk.X, pady=2)
    
    def draw_board(self):
        self.canvas.delete("all")
        square_size = 60
        
        for row in range(8):
            for col in range(8):
                x1, y1 = col * square_size, row * square_size
                x2, y2 = x1 + square_size, y1 + square_size
                square = chess.square(col, 7 - row)
                if square == self.selected_square: color = self.selected_color
                elif square in self.highlighted_squares: color = self.legal_move_color
                else: color = self.light_square_color if (row + col) % 2 == 0 else self.dark_square_color
                self.canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline="black")
                if col == 0: self.canvas.create_text(x1 + 5, y1 + 10, text=str(8 - row), font=("Arial", 8))
                if row == 7: self.canvas.create_text(x2 - 10, y2 - 5, text=chr(ord('a') + col), font=("Arial", 8))
    
    def draw_pieces(self):
        square_size = 60
        for row in range(8):
            for col in range(8):
                square = chess.square(col, 7 - row)
                piece = self.board.piece_at(square)
                if piece:
                    x = col * square_size + square_size // 2
                    y = row * square_size + square_size // 2
                    self.canvas.create_text(x, y, text=self.piece_symbols.get(piece.symbol()), font=("Arial", 36))
    
    def update_board_display(self):
        self.draw_board()
        self.draw_pieces()
    
    def on_square_click(self, event):
        if self.game_over or self.board.turn != self.human_color or self.bot_thinking: return
        square_size = 60
        col, row = event.x // square_size, event.y // square_size
        if 0 <= col < 8 and 0 <= row < 8:
            clicked_square = chess.square(col, 7 - row)
            if self.selected_square is None:
                piece = self.board.piece_at(clicked_square)
                if piece and piece.color == self.human_color:
                    self.selected_square = clicked_square
                    self.highlight_legal_moves(clicked_square)
            else:
                move = chess.Move(self.selected_square, clicked_square)
                piece = self.board.piece_at(self.selected_square)
                if (piece and piece.piece_type == chess.PAWN and chess.square_rank(clicked_square) in [0, 7]):
                    move.promotion = chess.QUEEN
                if move in self.board.legal_moves: self.make_move(move)
                self.selected_square = None
                self.highlighted_squares = []
            self.update_board_display()

    def highlight_legal_moves(self, square):
        self.highlighted_squares = [move.to_square for move in self.board.legal_moves if move.from_square == square]
    
    def disable_ui(self, is_thinking=True):
        state = 'disabled' if is_thinking else 'normal'
        cursor = "wait" if is_thinking else ""
        for btn in [self.new_game_btn, self.switch_colors_btn, self.save_game_btn, self.load_game_btn, 
                    self.analyze_btn, self.hint_btn, self.move_quality_btn, self.play_from_history_btn] + self.difficulty_buttons:
            btn.config(state=state)
        self.root.config(cursor=cursor)
        self.canvas.config(cursor=cursor)

    def enable_ui(self):
        self.disable_ui(is_thinking=False)
    
    def make_move(self, move: chess.Move, is_auto_play=False):
        try:
            board_before = self.board.copy()
            eval_depth = 2 
            eval_score_before, _ = self.bot.minimax(board_before, depth=eval_depth, alpha=float('-inf'), beta=float('inf'), maximizing_player=board_before.turn)
            san_move = self.board.san(move)
            self.board.push(move)
            self.game_history.append(san_move)
            move_number = len(self.game_history)
            eval_score_after, _ = self.bot.minimax(self.board, depth=eval_depth, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            quality_enum, quality_explanation = self.tactical_evaluator.evaluate_move_quality(
                board_before, move, eval_score_before, eval_score_after, move_number
            )
            eval_change = eval_score_after - eval_score_before
            move_info = {
                'move': san_move, 'player': "White" if board_before.turn == chess.WHITE else "Black",
                'eval_before': eval_score_before / 100.0, 'eval_after': eval_score_after / 100.0,
                'eval_change': eval_change / 100.0, 'quality': quality_enum.value,
                'explanation': quality_explanation, 'move_number': move_number
            }
            self.move_quality_history.append(move_info)
            self.update_history_display()
            self.update_status()
            self.update_evaluation()
            self.update_board_display()
            if not is_auto_play and self.board.turn != self.human_color:
                feedback = f"Move: {san_move} - {quality_enum.value} ({quality_explanation})"
                self.status_label.config(text=feedback)
                self.root.after(4000, lambda: self.update_status() if not self.bot_thinking else None)
            if self.board.is_game_over(): self.handle_game_over()
            elif not is_auto_play and self.board.turn != self.human_color:
                self.root.after(500, self.make_bot_move)
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred while making a move: {e}")
            self.update_status()
    
    def make_bot_move(self):
        self.bot_thinking = True
        self.disable_ui()
        self.update_status("Bot is thinking...")
        def bot_move_thread():
            try:
                move = self.bot.get_best_move(self.board)
                self.root.after(0, lambda: self.finish_bot_move(move))
            except Exception as e:
                self.root.after(0, lambda: self.finish_bot_move_error(f"Bot error: {e}"))
        threading.Thread(target=bot_move_thread, daemon=True).start()
    
    def finish_bot_move(self, move):
        self.bot_thinking = False
        self.enable_ui()
        if move: self.make_move(move)
        else: self.handle_game_over()
    
    def finish_bot_move_error(self, error_message):
        self.bot_thinking = False
        self.enable_ui()
        messagebox.showerror("Error", error_message)
        self.update_status()
    
    def update_status(self, custom_message=None):
        if custom_message:
            self.status_label.config(text=custom_message)
            return
        if self.bot_thinking and not self.board.is_game_over(): return
        if self.board.is_checkmate():
            winner = "Black" if self.board.turn == chess.WHITE else "White"
            self.status_label.config(text=f"Checkmate! {winner} wins!")
        elif self.board.is_stalemate() or self.board.is_insufficient_material() or self.board.can_claim_threefold_repetition():
            self.status_label.config(text="Draw!")
        elif self.board.is_check():
            self.status_label.config(text=f"{'White' if self.board.turn else 'Black'} in check!")
        else:
            self.status_label.config(text=f"{'White' if self.board.turn else 'Black'} to move")
        if len(self.game_history) <= 12:
            self.opening_label.config(text=f"Opening: {get_opening_name(self.board, self.game_history)}")
    
    def update_evaluation(self):
        try:
            eval_score, _ = self.bot.minimax(self.board, 2, float('-inf'), float('inf'), self.board.turn)
            self.eval_label.config(text=f"{eval_score/100.0:+.2f}")
            self.eval_description_label.config(text=get_move_quality_description(eval_score))
            self.update_evaluation_bar(eval_score)
        except Exception: self.eval_label.config(text="N/A")

    def update_evaluation_bar(self, evaluation):
        self.eval_canvas.delete("all")
        w, h = self.eval_canvas.winfo_width(), self.eval_canvas.winfo_height()
        if w <= 1: w = 250
        max_eval = 800
        norm_eval = max(-max_eval, min(max_eval, evaluation))
        split_point = w * (1 - (norm_eval + max_eval) / (2 * max_eval))
        self.eval_canvas.create_rectangle(0, 0, split_point, h, fill=self.dark_square_color, outline="")
        self.eval_canvas.create_rectangle(split_point, 0, w, h, fill=self.light_square_color, outline="")

    def on_canvas_resize(self, event):
        self.update_evaluation()

    def update_history_display(self):
        self.history_text.config(state=tk.NORMAL)
        self.history_text.delete(1.0, tk.END)
        for i in range(0, len(self.game_history), 2):
            move_num = (i // 2) + 1
            white_move = self.game_history[i]
            black_move = self.game_history[i+1] if i + 1 < len(self.game_history) else ""
            wq = self.get_quality_symbol(self.move_quality_history[i]['quality']) if i < len(self.move_quality_history) else ""
            bq = self.get_quality_symbol(self.move_quality_history[i+1]['quality']) if i + 1 < len(self.move_quality_history) else ""
            line = f"{move_num}. {white_move}{wq} {black_move}{bq}\n"
            self.history_text.insert(tk.END, line)
        self.history_text.config(state=tk.DISABLED)
        self.history_text.see(tk.END)
    
    def get_quality_symbol(self, quality: str) -> str:
        return {'Brilliant': '!!', 'Excellent': '!', 'Good': '!', 'Okay': '', 'Inaccurate': '?', 'Poor': '??', 'Blunder': '??'}.get(quality, '')
    
    def handle_game_over(self):
        self.game_over = True
        message = f"Checkmate! {'You' if self.board.turn != self.human_color else 'ChessBot'} win!" if self.board.is_checkmate() else "Game is a draw."
        if messagebox.askyesno("Game Over", f"{message}\n\nWould you like to save this game?"): self.save_game()
    
    def new_game(self):
        if self.bot_thinking: return
        self.board.reset()
        self.game_history.clear()
        self.move_quality_history.clear()
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        self.enable_ui()
        self.update_board_display()
        self.update_status()
        self.update_evaluation()
        self.update_history_display()
    
    def switch_colors(self):
        if self.bot_thinking: return
        self.human_color = not self.human_color
        messagebox.showinfo("Colors Switched", f"You are now playing as {'White' if self.human_color else 'Black'}")
        self.update_status()
        if not self.game_over and self.board.turn != self.human_color: self.make_bot_move()
    
    def set_difficulty(self, depth):
        if self.bot_thinking: return
        self.bot.set_depth(depth)
        messagebox.showinfo("Difficulty Changed", f"Bot difficulty set to {self.difficulty_var.get()} (depth {depth})")
    
    def save_game(self):
        if self.bot_thinking: return
        filename = filedialog.asksaveasfilename(defaultextension=".pgn", filetypes=[("PGN files", "*.pgn")])
        if filename:
            try:
                save_game_pgn(self.board, self.game_history, filename, 
                              "Human" if self.human_color else "ChessBot", 
                              "ChessBot" if self.human_color else "Human")
                messagebox.showinfo("Game Saved", f"Game saved to {filename}")
            except Exception as e: messagebox.showerror("Error", f"Failed to save game: {e}")
    
    def load_game(self):
        if self.bot_thinking: return
        filename = filedialog.askopenfilename(filetypes=[("PGN files", "*.pgn")])
        if filename:
            try:
                from utils import load_game_pgn
                self.board, self.game_history = load_game_pgn(filename)
                self.move_quality_history.clear() 
                self.selected_square = None
                self.highlighted_squares = []
                self.game_over = self.board.is_game_over()
                self.update_board_display()
                self.update_status()
                self.update_evaluation()
                self.update_history_display()
            except Exception as e: messagebox.showerror("Error", f"Failed to load game: {e}")

    def show_analysis(self):
        if self.bot_thinking: return
        try:
            analysis_text = format_analysis(analyze_position(self.board))
            popup = tk.Toplevel(self.root)
            popup.title("Position Analysis")
            text_widget = tk.Text(popup, wrap=tk.WORD, font=("Courier", 10), state=tk.NORMAL)
            text_widget.insert(1.0, analysis_text)
            text_widget.config(state=tk.DISABLED)
            text_widget.pack(padx=10, pady=10, expand=True, fill=tk.BOTH)
        except Exception as e: messagebox.showerror("Error", f"Failed to analyze position: {e}")

    def show_hint(self):
        if self.bot_thinking or self.game_over or self.board.turn != self.human_color: return
        self.update_status("Calculating hint...")
        threading.Thread(target=self._calculate_hint_thread, daemon=True).start()

    def _calculate_hint_thread(self):
        try:
            best_move = self.bot.get_best_move(self.board)
            if best_move:
                san_move = self.board.san(best_move)
                self.root.after(0, lambda: self._show_hint_ui(best_move, san_move))
        except Exception as e: self.root.after(0, lambda: messagebox.showerror("Error", f"Hint error: {e}"))
        finally: self.root.after(0, self.update_status)

    def _show_hint_ui(self, move, san):
        messagebox.showinfo("Hint", f"Suggested move: {san}")
        self.selected_square = move.from_square
        self.highlighted_squares = [move.to_square]
        self.update_board_display()

    def show_move_quality_history(self):
        if self.bot_thinking: return
        if not self.move_quality_history:
            messagebox.showinfo("Move Quality History", "No moves have been made yet.")
            return
        popup = tk.Toplevel(self.root)
        popup.title("Move Quality History")
        popup.geometry("800x600")
        text_widget = tk.Text(popup, wrap=tk.WORD, font=("Courier", 10))
        text_widget.pack(padx=10, pady=10, expand=True, fill=tk.BOTH)
        history_text = self.generate_detailed_history()
        text_widget.insert(1.0, history_text)
        text_widget.config(state=tk.DISABLED)
    
    def generate_detailed_history(self):
        if not self.move_quality_history: return "No moves recorded yet."
        lines = ["MOVE QUALITY ANALYSIS REPORT\n" + "="*80]
        lines.append("\nNOTE: All evaluation scores are from White's perspective.")
        lines.append("Positive values favor White, negative values favor Black.\n")
        lines.append("\nDETAILED MOVE ANALYSIS:\n" + "-"*80)
        for info in self.move_quality_history:
            move_num_str = f"{info['move_number'] // 2 + 1 if info['player'] == 'White' else (info['move_number']+1)//2}{'. ' if info['player'] == 'White' else '... '}"
            lines.append(f"Move {move_num_str}{info['move']} ({info['player']})")
            lines.append(f"  Quality:         {info['quality']} {self.get_quality_symbol(info['quality'])}")
            lines.append(f"  Eval (before):   {info['eval_before']:+.2f}")
            lines.append(f"  Eval (after):    {info['eval_after']:+.2f}")
            lines.append(f"  Eval Change:     {info['eval_change']:+.2f}")
            lines.append(f"  Interpretation:  {info['explanation']}")
            lines.append("")
        return "\n".join(lines)

    def play_from_history(self):
        """Correctly parses and plays moves from the move history text widget."""
        if self.bot_thinking:
            return
        history_text = self.history_text.get(1.0, tk.END).strip()
        if not history_text:
            messagebox.showwarning("No History", "The move history is empty.")
            return

        # *** KEY CHANGE: Replace faulty regex with simpler, correct parsing ***
        # 1. Clean the text by removing move numbers, annotations, and results.
        clean_text = re.sub(r'\d+\.+\s*|[!?#+]\s*|1-0|0-1|1/2-1/2', ' ', history_text)

        # 2. Split the cleaned text by whitespace to get a list of moves.
        moves_san = [move for move in clean_text.split() if move]
        
        if not moves_san:
            messagebox.showwarning("Parse Error", "Could not parse any valid moves from the text.")
            return
            
        if messagebox.askyesno("Play from History", f"Found {len(moves_san)} moves to replay. This will start a new game.\n\nContinue?"):
            self.new_game()
            self._auto_play_moves(moves_san)
    
    def _auto_play_moves(self, moves_san):
        self.disable_ui()
        def play_next(index):
            if index >= len(moves_san):
                self.enable_ui()
                messagebox.showinfo("Auto-play Complete", "All moves replayed.")
                return
            try:
                move = self.board.parse_san(moves_san[index])
                self.make_move(move, is_auto_play=True)
                self.root.after(250, play_next, index + 1)
            except (ValueError, chess.InvalidMoveError, chess.AmbiguousMoveError):
                self.enable_ui()
                messagebox.showerror("Invalid Move", f"Could not play move: '{moves_san[index]}' in the current position.")
        self.root.after(100, play_next, 0)
    
    def run(self):
        """Start the GUI application."""
        self.root.mainloop()

def main():
    """Main entry point for the GUI version."""
    try:
        app = ChessGUI()
        app.run()
    except Exception as e:
        import traceback
        messagebox.showerror("Fatal Error", f"An unexpected error occurred:\n{e}\n\n{traceback.format_exc()}")

if __name__ == "__main__":
    main()