import chess
import chess.engine
import random
from typing import Op<PERSON>, Tuple, List
import time

class ChessBot:
    """
    A chess bot that uses minimax algorithm with alpha-beta pruning
    to make intelligent moves.
    """
    
    def __init__(self, depth: int = 3, name: str = "ChessBot"):
        """
        Initialize the chess bot.
        
        Args:
            depth: Search depth for minimax algorithm
            name: Name of the bot
        """
        self.depth = depth
        self.name = name
        self.nodes_evaluated = 0
        
        # Piece values for evaluation
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
        
        # --- NEW: COMPLETE PIECE-SQUARE TABLES (PSTs) ---
        # These tables give bonuses or penalties to pieces based on their square.
        # The board is represented as a list of 64 squares, from A1 to H8.
        # All tables are from <PERSON>'s perspective. They are mirrored for Black.

        self.pawn_table = [
            0,  0,  0,  0,  0,  0,  0,  0,   # Rank 1: starting position
            5,  5,  5,  5,  5,  5,  5,  5,   # Rank 2: slight bonus for moving
            10, 10, 20, 30, 30, 20, 10, 10,  # Rank 3: encourage central control
            15, 15, 25, 35, 35, 25, 15, 15,  # Rank 4: good central advancement
            20, 20, 30, 40, 40, 30, 20, 20,  # Rank 5: strong central presence
            35, 35, 40, 50, 50, 40, 35, 35,  # Rank 6: approaching promotion
            50, 50, 60, 70, 70, 60, 50, 50,  # Rank 7: about to promote!
            0,  0,  0,  0,  0,  0,  0,  0    # Rank 8: promotion (shouldn't happen)
        ]
        
        self.knight_table = [
            -50,-40,-30,-30,-30,-30,-40,-50,
            -40,-20,  0,  0,  0,  0,-20,-40,
            -30,  0, 10, 15, 15, 10,  0,-30,
            -30,  5, 15, 20, 20, 15,  5,-30,
            -30,  0, 15, 20, 20, 15,  0,-30,
            -30,  5, 10, 15, 15, 10,  5,-30,
            -40,-20,  0,  5,  5,  0,-20,-40,
            -50,-40,-30,-30,-30,-30,-40,-50
        ]

        self.bishop_table = [
            -20,-10,-10,-10,-10,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5, 10, 10,  5,  0,-10,
            -10,  5,  5, 10, 10,  5,  5,-10,
            -10,  0, 10, 10, 10, 10,  0,-10,
            -10, 10, 10, 10, 10, 10, 10,-10,
            -10,  5,  0,  0,  0,  0,  5,-10,
            -20,-10,-10,-10,-10,-10,-10,-20
        ]

        self.rook_table = [
             0,  0,  0,  0,  0,  0,  0,  0,
             5, 10, 10, 10, 10, 10, 10,  5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
             0,  0,  0,  5,  5,  0,  0,  0
        ]

        self.queen_table = [
            -20,-10,-10, -5, -5,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5,  5,  5,  5,  0,-10,
             -5,  0,  5,  5,  5,  5,  0, -5,
              0,  0,  5,  5,  5,  5,  0, -5,
            -10,  5,  5,  5,  5,  5,  0,-10,
            -10,  0,  5,  0,  0,  0,  0,-10,
            -20,-10,-10, -5, -5,-10,-10,-20
        ]

        # King safety is critical. We need two tables: one for the middlegame
        # where the king should be castled, and one for the endgame where the
        # king should be active and centralized.
        self.king_mg_table = [
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -20,-30,-30,-40,-40,-30,-30,-20,
            -10,-20,-20,-20,-20,-20,-20,-10,
             20, 20,  0,  0,  0,  0, 20, 20,
             20, 30, 10,  0,  0, 10, 30, 20
        ]
        
        self.king_eg_table = [
            -50,-40,-30,-20,-20,-30,-40,-50,
            -30,-20,-10,  0,  0,-10,-20,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-30,  0,  0,  0,  0,-30,-30,
            -50,-30,-30,-30,-30,-30,-30,-50
        ]

    def _is_endgame(self, board: chess.Board) -> bool:
        """
        A simple heuristic to determine if the game is in an endgame phase.
        This is typically when queens are off the board or material is low.
        """
        # No queens on the board is a strong indicator of endgame.
        if not board.pieces(chess.QUEEN, chess.WHITE) and not board.pieces(chess.QUEEN, chess.BLACK):
            return True
        
        # Check if each side with a queen has less than two minor pieces.
        white_material = sum(len(board.pieces(pt, chess.WHITE)) for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])
        black_material = sum(len(board.pieces(pt, chess.BLACK)) for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])

        if board.pieces(chess.QUEEN, chess.WHITE) and white_material < 2:
            return True
        if board.pieces(chess.QUEEN, chess.BLACK) and black_material < 2:
            return True
            
        return False

    def evaluate_board(self, board: chess.Board) -> int:
        """
        Comprehensive evaluation of the current board position.
        
        Args:
            board: Chess board to evaluate
            
        Returns:
            Evaluation score (positive for white advantage, negative for black)
        """
        if board.is_checkmate():
            # If the current player is checkmated, it's a win for the other side.
            return -20000 if board.turn == chess.WHITE else 20000
        
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        is_endgame = self._is_endgame(board)

        # 1. Material and basic positional evaluation
        score += self._evaluate_material_and_position(board, is_endgame)
        
        # 2. Pawn structure evaluation
        score += self._evaluate_pawn_structure(board)
        
        # 3. Piece activity and coordination
        score += self._evaluate_piece_activity(board)
        
        # 4. King safety evaluation
        score += self._evaluate_king_safety(board, is_endgame)
        
        # 5. Tactical threats evaluation
        score += self._evaluate_tactical_threats(board)
        
        # 6. Control of key squares and files
        score += self._evaluate_control_and_files(board)
        
        # 7. Mobility evaluation
        score += self._evaluate_mobility(board)
        
        return score
    
    def _evaluate_material_and_position(self, board: chess.Board, is_endgame: bool) -> int:
        """Evaluate material and basic positional factors."""
        score = 0
        
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = self.piece_values[piece.piece_type]
                
                # Add positional bonuses
                if piece.piece_type == chess.PAWN:
                    table = self.pawn_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.KNIGHT:
                    table = self.knight_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.BISHOP:
                    table = self.bishop_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.ROOK:
                    table = self.rook_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.QUEEN:
                    table = self.queen_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.KING:
                    table = self.king_eg_table if is_endgame else self.king_mg_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                
                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value
        
        return score
    
    def _evaluate_pawn_structure(self, board: chess.Board) -> int:
        """Evaluate pawn structure: doubled, isolated, passed pawns, pawn chains."""
        score = 0
        
        white_pawns = board.pieces(chess.PAWN, chess.WHITE)
        black_pawns = board.pieces(chess.PAWN, chess.BLACK)
        
        # Evaluate for both colors
        score += self._evaluate_pawn_structure_for_color(board, chess.WHITE, white_pawns, black_pawns)
        score -= self._evaluate_pawn_structure_for_color(board, chess.BLACK, black_pawns, white_pawns)
        
        return score
    
    def _evaluate_pawn_structure_for_color(self, board: chess.Board, color: chess.Color, 
                                         own_pawns: chess.SquareSet, enemy_pawns: chess.SquareSet) -> int:
        """Evaluate pawn structure for a specific color."""
        score = 0
        
        for square in own_pawns:
            file = chess.square_file(square)
            rank = chess.square_rank(square)
            
            # Check for doubled pawns
            file_pawns = [s for s in own_pawns if chess.square_file(s) == file]
            if len(file_pawns) > 1:
                score -= 25  # Penalty for doubled pawns
            
            # Check for isolated pawns
            adjacent_files = [file - 1, file + 1]
            has_pawn_support = any(
                any(chess.square_file(s) == adj_file for s in own_pawns)
                for adj_file in adjacent_files if 0 <= adj_file <= 7
            )
            if not has_pawn_support:
                score -= 20  # Penalty for isolated pawns
            
            # Check for passed pawns
            if self._is_passed_pawn(square, color, own_pawns, enemy_pawns):
                distance_to_promotion = (7 - rank) if color == chess.WHITE else rank
                passed_bonus = 20 + (7 - distance_to_promotion) * 10
                score += passed_bonus
            
            # Check for pawn chains (pawns protecting each other)
            if self._is_pawn_in_chain(square, color, own_pawns):
                score += 10  # Bonus for pawn chains
        
        return score
    
    def _is_passed_pawn(self, square: chess.Square, color: chess.Color, 
                       own_pawns: chess.SquareSet, enemy_pawns: chess.SquareSet) -> bool:
        """Check if a pawn is passed (no enemy pawns can block its advance)."""
        file = chess.square_file(square)
        rank = chess.square_rank(square)
        
        # Check files that can block this pawn
        blocking_files = [file - 1, file, file + 1]
        blocking_files = [f for f in blocking_files if 0 <= f <= 7]
        
        # Check if there are enemy pawns ahead of this pawn
        for enemy_square in enemy_pawns:
            enemy_file = chess.square_file(enemy_square)
            enemy_rank = chess.square_rank(enemy_square)
            
            if enemy_file in blocking_files:
                if color == chess.WHITE and enemy_rank > rank:
                    return False
                elif color == chess.BLACK and enemy_rank < rank:
                    return False
        
        return True
    
    def _is_pawn_in_chain(self, square: chess.Square, color: chess.Color, pawns: chess.SquareSet) -> bool:
        """Check if a pawn is part of a pawn chain."""
        file = chess.square_file(square)
        rank = chess.square_rank(square)
        
        # Check for supporting pawns
        support_squares = []
        if color == chess.WHITE:
            support_squares = [chess.square(file - 1, rank - 1), chess.square(file + 1, rank - 1)]
        else:
            support_squares = [chess.square(file - 1, rank + 1), chess.square(file + 1, rank + 1)]
        
        support_squares = [s for s in support_squares if 0 <= chess.square_file(s) <= 7 and 0 <= chess.square_rank(s) <= 7]
        
        return any(s in pawns for s in support_squares)
    
    def _evaluate_piece_activity(self, board: chess.Board) -> int:
        """Evaluate piece activity and coordination."""
        score = 0
        
        # Bishop pair bonus
        white_bishops = len(board.pieces(chess.BISHOP, chess.WHITE))
        black_bishops = len(board.pieces(chess.BISHOP, chess.BLACK))
        
        if white_bishops >= 2:
            score += 30  # Bishop pair bonus
        if black_bishops >= 2:
            score -= 30
        
        # Rook on open files
        score += self._evaluate_rook_activity(board, chess.WHITE)
        score -= self._evaluate_rook_activity(board, chess.BLACK)
        
        # Knight outposts
        score += self._evaluate_knight_outposts(board, chess.WHITE)
        score -= self._evaluate_knight_outposts(board, chess.BLACK)
        
        return score
    
    def _evaluate_rook_activity(self, board: chess.Board, color: chess.Color) -> int:
        """Evaluate rook activity on open and semi-open files."""
        score = 0
        rooks = board.pieces(chess.ROOK, color)
        
        for rook_square in rooks:
            file = chess.square_file(rook_square)
            
            # Check if file is open (no pawns) or semi-open (no own pawns)
            own_pawns_on_file = any(
                chess.square_file(s) == file 
                for s in board.pieces(chess.PAWN, color)
            )
            enemy_pawns_on_file = any(
                chess.square_file(s) == file 
                for s in board.pieces(chess.PAWN, not color)
            )
            
            if not own_pawns_on_file and not enemy_pawns_on_file:
                score += 25  # Open file bonus
            elif not own_pawns_on_file:
                score += 15  # Semi-open file bonus
            
            # Bonus for rook on 7th rank
            rank = chess.square_rank(rook_square)
            if (color == chess.WHITE and rank == 6) or (color == chess.BLACK and rank == 1):
                score += 20
        
        return score
    
    def _evaluate_knight_outposts(self, board: chess.Board, color: chess.Color) -> int:
        """Evaluate knight outposts (knights on strong squares)."""
        score = 0
        knights = board.pieces(chess.KNIGHT, color)
        enemy_pawns = board.pieces(chess.PAWN, not color)
        
        for knight_square in knights:
            # Check if knight is on an outpost square
            if self._is_outpost_square(knight_square, color, enemy_pawns):
                score += 25
        
        return score
    
    def _is_outpost_square(self, square: chess.Square, color: chess.Color, enemy_pawns: chess.SquareSet) -> bool:
        """Check if a square is an outpost for the given color."""
        file = chess.square_file(square)
        rank = chess.square_rank(square)
        
        # Outpost squares are typically in the opponent's half
        if color == chess.WHITE and rank < 4:
            return False
        if color == chess.BLACK and rank > 3:
            return False
        
        # Check if enemy pawns can attack this square
        attack_files = [file - 1, file + 1]
        for enemy_square in enemy_pawns:
            enemy_file = chess.square_file(enemy_square)
            enemy_rank = chess.square_rank(enemy_square)
            
            if enemy_file in attack_files:
                if color == chess.WHITE and enemy_rank > rank:
                    return False
                elif color == chess.BLACK and enemy_rank < rank:
                    return False
        
        return True
    
    def _evaluate_king_safety(self, board: chess.Board, is_endgame: bool) -> int:
        """Advanced king safety evaluation."""
        if is_endgame:
            return 0  # King safety less important in endgame
        
        score = 0
        
        # Evaluate king safety for both colors
        score += self._evaluate_king_safety_for_color(board, chess.WHITE)
        score -= self._evaluate_king_safety_for_color(board, chess.BLACK)
        
        return score
    
    def _evaluate_king_safety_for_color(self, board: chess.Board, color: chess.Color) -> int:
        """Evaluate king safety for a specific color."""
        score = 0
        king_square = board.king(color)
        
        if king_square is None:
            return 0
        
        # Penalty for king in center
        king_file = chess.square_file(king_square)
        king_rank = chess.square_rank(king_square)
        
        if 2 <= king_file <= 5:  # King in center files
            score -= 20
        
        # Check pawn shield
        pawn_shield_bonus = self._evaluate_pawn_shield(board, king_square, color)
        score += pawn_shield_bonus
        
        # Penalty for open files near king
        open_files_penalty = self._evaluate_open_files_near_king(board, king_square, color)
        score -= open_files_penalty
        
        return score
    
    def _evaluate_pawn_shield(self, board: chess.Board, king_square: chess.Square, color: chess.Color) -> int:
        """Evaluate pawn shield in front of king."""
        score = 0
        king_file = chess.square_file(king_square)
        king_rank = chess.square_rank(king_square)
        
        # Check files around king
        shield_files = [king_file - 1, king_file, king_file + 1]
        shield_files = [f for f in shield_files if 0 <= f <= 7]
        
        for file in shield_files:
            # Look for pawns in front of king
            shield_ranks = []
            if color == chess.WHITE:
                shield_ranks = [king_rank + 1, king_rank + 2]
            else:
                shield_ranks = [king_rank - 1, king_rank - 2]
            
            shield_ranks = [r for r in shield_ranks if 0 <= r <= 7]
            
            has_pawn_shield = any(
                board.piece_at(chess.square(file, rank)) == chess.Piece(chess.PAWN, color)
                for rank in shield_ranks
            )
            
            if has_pawn_shield:
                score += 15
            else:
                score -= 10  # Penalty for missing pawn shield
        
        return score
    
    def _evaluate_open_files_near_king(self, board: chess.Board, king_square: chess.Square, color: chess.Color) -> int:
        """Evaluate open files near king."""
        penalty = 0
        king_file = chess.square_file(king_square)
        
        # Check files near king
        nearby_files = [king_file - 1, king_file, king_file + 1]
        nearby_files = [f for f in nearby_files if 0 <= f <= 7]
        
        for file in nearby_files:
            # Check if file is open
            pawns_on_file = any(
                chess.square_file(s) == file 
                for s in board.pieces(chess.PAWN, chess.WHITE) | board.pieces(chess.PAWN, chess.BLACK)
            )
            
            if not pawns_on_file:
                penalty += 15  # Penalty for open file near king
        
        return penalty
    
    def _evaluate_tactical_threats(self, board: chess.Board) -> int:
        """Evaluate tactical threats like pins, forks, skewers."""
        score = 0
        
        # Evaluate pins
        score += self._evaluate_pins(board, chess.WHITE)
        score -= self._evaluate_pins(board, chess.BLACK)
        
        # Evaluate forks and multiple attacks
        score += self._evaluate_forks(board, chess.WHITE)
        score -= self._evaluate_forks(board, chess.BLACK)
        
        return score
    
    def _evaluate_pins(self, board: chess.Board, color: chess.Color) -> int:
        """Evaluate pins created by the given color."""
        score = 0
        
        # Check for pins by bishops and queens on diagonals
        bishops_and_queens = board.pieces(chess.BISHOP, color) | board.pieces(chess.QUEEN, color)
        
        for piece_square in bishops_and_queens:
            # Check diagonal attacks
            for direction in [(1, 1), (1, -1), (-1, 1), (-1, -1)]:
                pin_bonus = self._check_pin_on_ray(board, piece_square, direction, color)
                score += pin_bonus
        
        # Check for pins by rooks and queens on ranks/files
        rooks_and_queens = board.pieces(chess.ROOK, color) | board.pieces(chess.QUEEN, color)
        
        for piece_square in rooks_and_queens:
            # Check rank and file attacks
            for direction in [(1, 0), (-1, 0), (0, 1), (0, -1)]:
                pin_bonus = self._check_pin_on_ray(board, piece_square, direction, color)
                score += pin_bonus
        
        return score
    
    def _check_pin_on_ray(self, board: chess.Board, start_square: chess.Square, direction: tuple, color: chess.Color) -> int:
        """Check for pins along a ray from a piece."""
        score = 0
        file_start = chess.square_file(start_square)
        rank_start = chess.square_rank(start_square)
        
        df, dr = direction
        pieces_found = []
        
        # Cast ray from the attacking piece
        for i in range(1, 8):
            new_file = file_start + df * i
            new_rank = rank_start + dr * i
            
            if not (0 <= new_file <= 7 and 0 <= new_rank <= 7):
                break
                
            square = chess.square(new_file, new_rank)
            piece = board.piece_at(square)
            
            if piece:
                pieces_found.append((square, piece))
                
                # If we found 2 pieces and they're both enemy pieces,
                # and the second one is valuable, we have a pin
                if len(pieces_found) == 2:
                    first_piece = pieces_found[0][1]
                    second_piece = pieces_found[1][1]
                    
                    if (first_piece.color != color and second_piece.color != color and
                        second_piece.piece_type in [chess.KING, chess.QUEEN, chess.ROOK]):
                        score += 25  # Pin bonus
                    break
                elif len(pieces_found) > 2:
                    break
        
        return score
    
    def _evaluate_forks(self, board: chess.Board, color: chess.Color) -> int:
        """Evaluate potential forks and multiple attacks."""
        score = 0
        
        # Check knight forks
        knights = board.pieces(chess.KNIGHT, color)
        for knight_square in knights:
            fork_bonus = self._evaluate_knight_fork_potential(board, knight_square, color)
            score += fork_bonus
        
        return score
    
    def _evaluate_knight_fork_potential(self, board: chess.Board, knight_square: chess.Square, color: chess.Color) -> int:
        """Evaluate knight fork potential."""
        score = 0
        
        # Get all squares the knight can move to
        knight_moves = []
        for move in board.legal_moves:
            if move.from_square == knight_square:
                knight_moves.append(move.to_square)
        
        # Check if knight can fork valuable pieces
        for target_square in knight_moves:
            # Create a temporary board to test the fork
            temp_board = board.copy()
            temp_board.set_piece_at(target_square, chess.Piece(chess.KNIGHT, color))
            temp_board.remove_piece_at(knight_square)
            
            # Count valuable pieces the knight would attack
            attacked_pieces = 0
            for square in chess.SQUARES:
                piece = temp_board.piece_at(square)
                if piece and piece.color != color:
                    if temp_board.is_attacked_by(color, square):
                        if piece.piece_type in [chess.QUEEN, chess.ROOK, chess.KING]:
                            attacked_pieces += 1
            
            if attacked_pieces >= 2:
                score += 15  # Bonus for potential fork
        
        return score
    
    def _evaluate_control_and_files(self, board: chess.Board) -> int:
        """Evaluate control of key squares and files."""
        score = 0
        
        # Control of center squares
        center_squares = [chess.D4, chess.D5, chess.E4, chess.E5]
        extended_center = [chess.C3, chess.C4, chess.C5, chess.C6,
                          chess.D3, chess.D6, chess.E3, chess.E6,
                          chess.F3, chess.F4, chess.F5, chess.F6]
        
        for square in center_squares:
            if board.is_attacked_by(chess.WHITE, square):
                score += 10
            if board.is_attacked_by(chess.BLACK, square):
                score -= 10
        
        for square in extended_center:
            if board.is_attacked_by(chess.WHITE, square):
                score += 5
            if board.is_attacked_by(chess.BLACK, square):
                score -= 5
        
        return score
    
    def _evaluate_mobility(self, board: chess.Board) -> int:
        """Evaluate piece mobility."""
        original_turn = board.turn
        
        board.turn = chess.WHITE
        white_mobility = len(list(board.legal_moves))
        board.turn = chess.BLACK
        black_mobility = len(list(board.legal_moves))
        board.turn = original_turn

        mobility_score = (white_mobility - black_mobility) * 2
        return mobility_score
    
    def minimax(self, board: chess.Board, depth: int, alpha: int, beta: int, 
                maximizing_player: bool) -> Tuple[int, Optional[chess.Move]]:
        """
        Minimax algorithm with alpha-beta pruning.
        
        Args:
            board: Current board position
            depth: Remaining search depth
            alpha: Alpha value for pruning
            beta: Beta value for pruning
            maximizing_player: True if maximizing player's turn
            
        Returns:
            Tuple of (evaluation_score, best_move)
        """
        self.nodes_evaluated += 1
        
        if depth == 0 or board.is_game_over():
            return self.evaluate_board(board), None
        
        best_move = None
        
        # Simple move ordering: captures first, then others
        moves = sorted(list(board.legal_moves), key=board.is_capture, reverse=True)

        if maximizing_player:
            max_eval = float('-inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            return max_eval, best_move
        else: # Minimizing player
            min_eval = float('inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            return min_eval, best_move
    
    def get_best_move(self, board: chess.Board) -> chess.Move:
        """
        Get the best move for the current position.
        
        Args:
            board: Current board position
            
        Returns:
            Best move according to the bot's evaluation
        """
        self.nodes_evaluated = 0
        start_time = time.time()
        
        # Use minimax to find the best move
        maximizing = board.turn == chess.WHITE
        _, best_move = self.minimax(board, self.depth, float('-inf'), float('inf'), maximizing)
        
        end_time = time.time()
        
        if best_move is None:
            # Fallback to random move if no move found
            try:
                best_move = random.choice(list(board.legal_moves))
            except IndexError:
                return None # No legal moves
        
        print(f"{self.name} evaluated {self.nodes_evaluated} positions in {end_time - start_time:.2f} seconds")
        
        return best_move
    
    def set_depth(self, depth: int):
        """Set the search depth for the bot."""
        self.depth = depth
        print(f"{self.name} search depth set to {depth}")