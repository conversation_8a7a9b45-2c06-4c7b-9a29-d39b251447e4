PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_chess_bot_QS.py
Running Chess Bot QS Unit Tests...
test_board_evaluation_checkmate (__main__.TestChessBotQS.test_board_evaluation_checkmate)
Test board evaluation in checkmate position. ... ok
test_board_evaluation_starting_position (__main__.TestChessBotQS.test_board_evaluation_starting_position)
Test board evaluation at starting position. ... ok
test_bot_initialization (__main__.TestChessBotQS.test_bot_initialization)
Test bot initialization. ... ok
test_bot_prefers_captures (__main__.TestChessBotQS.test_bot_prefers_captures)
Test that bot prefers capturing moves when beneficial. ... TestBotQS evaluated 180 positions in 0.05 seconds
ok
test_get_best_move_returns_legal_move (__main__.TestChessBotQS.test_get_best_move_returns_legal_move)
Test that get_best_move returns a legal move. ... TestBotQS evaluated 143 positions in 0.04 seconds
ok
test_minimax_depth_zero (__main__.TestChessBotQS.test_minimax_depth_zero)
Test minimax with depth 0 (should use quiescence search). ... ok
test_move_ordering (__main__.TestChessBotQS.test_move_ordering)
Test that move ordering prioritizes captures and checks. ... ok
test_piece_values (__main__.TestChessBotQS.test_piece_values)
Test that piece values are reasonable. ... ok
test_quiescence_search_basic (__main__.TestChessBotQS.test_quiescence_search_basic)
Test basic functionality of quiescence search. ... ok
test_quiescence_search_capture_position (__main__.TestChessBotQS.test_quiescence_search_capture_position)
Test quiescence search in a position with captures. ... ok
test_quiescence_search_depth_limit (__main__.TestChessBotQS.test_quiescence_search_depth_limit)
Test that quiescence search respects depth limit. ... ok
test_quiescence_search_improves_evaluation (__main__.TestChessBotQS.test_quiescence_search_improves_evaluation)     
Test that quiescence search improves evaluation in tactical positions. ... WithQS evaluated 596 positions in 0.13 seconds
WithoutQS evaluated 206 positions in 0.08 seconds
ok
test_set_depth (__main__.TestChessBotQS.test_set_depth)
Test setting bot depth. ... TestBotQS search depth set to 4
ok
test_nodes_evaluated_tracking (__main__.TestChessBotQSPerformance.test_nodes_evaluated_tracking)
Test that nodes_evaluated is tracked correctly. ... NodeTracker evaluated 143 positions in 0.04 seconds
ok
test_quiescence_search_performance (__main__.TestChessBotQSPerformance.test_quiescence_search_performance)
Test performance of quiescence search. ... ok

----------------------------------------------------------------------
Ran 15 tests in 0.353s

OK

Running performance test for ChessBot with Quiescence Search...
PerfBotQS evaluated 1379 positions in 0.29 seconds
PerfBotQS evaluated 1560 positions in 0.24 seconds
PerfBotQS evaluated 2783 positions in 0.47 seconds
PerfBotQS evaluated 3466 positions in 0.61 seconds
PerfBotQS evaluated 1124 positions in 0.16 seconds
Performance test completed:
- 5 moves calculated in 1.77 seconds
- Average time per move: 0.35 seconds
- Total nodes evaluated: 1124