#!/usr/bin/env python3
"""
Comprehensive demonstration of the hybrid chess bot that combines 
minimax with alpha-beta pruning and Monte Carlo Tree Search.
"""

import chess
import chess.pgn
from chess_bot_hybrid import ChessBot
import time
import io

def compare_approaches():
    """Compare pure minimax vs hybrid approach."""
    print("=== Comparing Pure Minimax vs Hybrid Approach ===\n")
    
    # Create bots with different approaches
    minimax_bot = ChessBot(depth=3, name="PureMinimax", use_hybrid=False)
    hybrid_bot = ChessBot(depth=3, name="HybridBot", mcts_simulations=800, 
                         top_moves_count=4, use_hybrid=True)
    
    # Test positions
    test_positions = [
        ("Opening", chess.Board()),
        ("Sicilian Defense", chess.Board("rnbqkbnr/pp1ppppp/8/2p5/4P3/8/PPPP1PPP/RNBQKBNR w KQkq c6 0 2")),
        ("Middle Game", chess.Board("r1bq1rk1/ppp2ppp/2n1bn2/2bpp3/2B1P3/3P1N2/PPP1NPPP/R1BQ1RK1 w - - 0 8")),
        ("Tactical", chess.Board("r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4"))
    ]
    
    for pos_name, board in test_positions:
        print(f"\n{'='*60}")
        print(f"Position: {pos_name}")
        print(f"FEN: {board.fen()}")
        print(f"{'='*60}")
        
        # Test both approaches
        for bot in [minimax_bot, hybrid_bot]:
            print(f"\n--- {bot.name} Analysis ---")
            start_time = time.time()
            
            try:
                move = bot.get_best_move(board.copy())
                end_time = time.time()
                
                if move:
                    temp_board = board.copy()
                    san_move = temp_board.san(move)
                    print(f"Selected move: {move} ({san_move})")
                    print(f"Time taken: {end_time - start_time:.2f} seconds")
                    print(f"Nodes evaluated: {bot.nodes_evaluated}")
                else:
                    print("No move found!")
                    
            except Exception as e:
                print(f"Error: {e}")
            
            print("-" * 40)

def play_hybrid_vs_minimax():
    """Play a game between hybrid bot and pure minimax bot."""
    print("\n=== Hybrid Bot vs Pure Minimax Bot ===\n")
    
    # Create bots
    white_bot = ChessBot(depth=3, name="HybridWhite", mcts_simulations=600, 
                        top_moves_count=3, use_hybrid=True)
    black_bot = ChessBot(depth=3, name="MinimaxBlack", use_hybrid=False)
    
    board = chess.Board()
    move_count = 0
    max_moves = 20  # Limit for demonstration
    
    print("Starting game: HybridWhite vs MinimaxBlack")
    print(f"Initial position:\n{board}\n")
    
    game_pgn = chess.pgn.Game()
    game_pgn.headers["White"] = white_bot.name
    game_pgn.headers["Black"] = black_bot.name
    game_pgn.headers["Event"] = "Hybrid vs Minimax Demo"
    
    node = game_pgn
    
    while not board.is_game_over() and move_count < max_moves:
        move_count += 1
        current_bot = white_bot if board.turn == chess.WHITE else black_bot
        
        print(f"Move {move_count}: {current_bot.name} to move")
        
        try:
            move = current_bot.get_best_move(board)
            
            if move and move in board.legal_moves:
                san_move = board.san(move)
                board.push(move)
                node = node.add_variation(move)
                
                print(f"Played: {san_move}")
                print(f"Position after move:\n{board}\n")
                
                # Check for special conditions
                if board.is_check():
                    print("Check!")
                if board.is_checkmate():
                    print("Checkmate!")
                    break
                if board.is_stalemate():
                    print("Stalemate!")
                    break
                    
            else:
                print("Invalid move generated!")
                break
                
        except Exception as e:
            print(f"Error during move generation: {e}")
            break
    
    # Print game result
    print(f"\nGame completed after {move_count} moves")
    print(f"Final position:\n{board}")
    
    if board.is_checkmate():
        winner = "White" if board.turn == chess.BLACK else "Black"
        print(f"Result: {winner} wins by checkmate!")
    elif board.is_stalemate():
        print("Result: Draw by stalemate")
    elif board.is_insufficient_material():
        print("Result: Draw by insufficient material")
    else:
        print("Game ended early for demonstration")
    
    # Print PGN
    print(f"\nGame PGN:")
    print(game_pgn)

def analyze_hybrid_components():
    """Analyze how the hybrid approach works step by step."""
    print("\n=== Analyzing Hybrid Components ===\n")
    
    bot = ChessBot(depth=3, name="AnalysisBot", mcts_simulations=500, 
                  top_moves_count=4, use_hybrid=True)
    
    # Use a tactical position
    board = chess.Board("r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4")
    
    print("Analyzing tactical position:")
    print(f"FEN: {board.fen()}")
    print(f"Position:\n{board}\n")
    
    print("Step-by-step analysis:")
    print("1. Getting top moves from minimax...")
    
    # Get top moves manually to show the process
    top_moves = bot.get_top_moves_minimax(board, bot.top_moves_count)
    
    print(f"Top {len(top_moves)} moves from minimax:")
    for i, (move, score) in enumerate(top_moves, 1):
        temp_board = board.copy()
        san_move = temp_board.san(move)
        print(f"  {i}. {san_move} (score: {score})")
    
    print(f"\n2. Running MCTS on these {len(top_moves)} candidates...")
    candidate_moves = [move for move, score in top_moves]
    
    # This will show the MCTS process
    final_move = bot.mcts_on_candidates(board, candidate_moves)
    
    if final_move:
        temp_board = board.copy()
        san_final = temp_board.san(final_move)
        print(f"\n3. Final selection: {san_final}")
        
        # Show if MCTS changed the minimax choice
        minimax_best = top_moves[0][0] if top_moves else None
        if minimax_best and final_move != minimax_best:
            temp_board = board.copy()
            san_minimax = temp_board.san(minimax_best)
            print(f"   Note: MCTS chose {san_final} over minimax's top choice {san_minimax}")
        else:
            print(f"   Note: MCTS confirmed minimax's top choice")

def performance_comparison():
    """Compare performance metrics between approaches."""
    print("\n=== Performance Comparison ===\n")
    
    # Different configurations
    configs = [
        ("Pure Minimax D3", {"depth": 3, "use_hybrid": False}),
        ("Pure Minimax D4", {"depth": 4, "use_hybrid": False}),
        ("Hybrid D3 + 300 MCTS", {"depth": 3, "use_hybrid": True, "mcts_simulations": 300, "top_moves_count": 3}),
        ("Hybrid D3 + 600 MCTS", {"depth": 3, "use_hybrid": True, "mcts_simulations": 600, "top_moves_count": 4}),
        ("Hybrid D4 + 400 MCTS", {"depth": 4, "use_hybrid": True, "mcts_simulations": 400, "top_moves_count": 3}),
    ]
    
    # Test position
    board = chess.Board("r1bq1rk1/ppp2ppp/2n1bn2/2bpp3/2B1P3/3P1N2/PPP1NPPP/R1BQ1RK1 w - - 0 8")
    
    print("Testing performance on middle game position:")
    print(f"FEN: {board.fen()}\n")
    
    results = []
    
    for config_name, config in configs:
        print(f"Testing {config_name}...")
        
        bot = ChessBot(name=config_name, **config)
        
        start_time = time.time()
        move = bot.get_best_move(board.copy())
        end_time = time.time()
        
        time_taken = end_time - start_time
        nodes_evaluated = bot.nodes_evaluated
        
        if move:
            temp_board = board.copy()
            san_move = temp_board.san(move)
            
            results.append({
                'config': config_name,
                'move': san_move,
                'time': time_taken,
                'nodes': nodes_evaluated,
                'nps': nodes_evaluated / time_taken if time_taken > 0 else 0
            })
            
            print(f"  Move: {san_move}")
            print(f"  Time: {time_taken:.2f}s")
            print(f"  Nodes: {nodes_evaluated}")
            print(f"  NPS: {nodes_evaluated / time_taken:.0f}\n")
    
    # Summary
    print("Performance Summary:")
    print("-" * 70)
    print(f"{'Configuration':<25} {'Move':<8} {'Time':<8} {'Nodes':<8} {'NPS':<8}")
    print("-" * 70)
    
    for result in results:
        print(f"{result['config']:<25} {result['move']:<8} {result['time']:<8.2f} {result['nodes']:<8} {result['nps']:<8.0f}")

if __name__ == "__main__":
    print("Hybrid Chess Bot Demonstration")
    print("Combining Minimax + Alpha-Beta Pruning + Monte Carlo Tree Search")
    print("=" * 80)
    
    try:
        # Run all demonstrations
        compare_approaches()
        analyze_hybrid_components()
        performance_comparison()
        play_hybrid_vs_minimax()
        
        print("\n" + "=" * 80)
        print("Demonstration completed successfully!")
        print("\nKey Benefits of the Hybrid Approach:")
        print("1. Minimax provides quick evaluation of all moves")
        print("2. Alpha-beta pruning reduces search space efficiently")
        print("3. MCTS explores promising moves more deeply")
        print("4. Combination leverages strengths of both approaches")
        print("5. Configurable balance between speed and depth")
        
    except KeyboardInterrupt:
        print("\nDemonstration interrupted by user.")
    except Exception as e:
        print(f"\nError during demonstration: {e}")