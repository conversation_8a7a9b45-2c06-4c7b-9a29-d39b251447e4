PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_exchange_detection.py
Testing Improved Exchange Detection
==================================================
Position after a3 (attacking the knight):
r . b q k b n r
p p p . . p p p
. . . . p . . .
. . . p N . . .
. n . P . . . .
P . N . . . . .
. P P . P P P P
R . B Q K B . R

Hanging piece evaluation: 330.0

Manual analysis of knight on b4:
Knight value: 320
Attackers: ['a3']
Defenders: ['f8']
  Attacker a3: P (value 100)
Minimum attacker value: 100
Knight value: 320
Exchange loss: 220
Penalty for Black: 330.0
✅ UNFAVORABLE EXCHANGE DETECTED!

==================================================
Bot's Move Choice:
==================================================
Overall position evaluation: 377.0
MediumBot statistics:
  - Time: 12.45s
  - Nodes evaluated: 3497
  - Quiescence nodes: 6842
  - Transposition hits: 0
  - Table size: 313
Bot's chosen move: d8g5
Move in notation: Qg5
❌ Bot chose different move: Qg5
  From: d8
  To: g5