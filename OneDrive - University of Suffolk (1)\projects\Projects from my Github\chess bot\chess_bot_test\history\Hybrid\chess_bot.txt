import chess
import chess.engine
import random
import math
from typing import Optional, Tuple, List, Dict
import time

class MCTSNode:
    """
    Node for Monte Carlo Tree Search.
    """
    
    def __init__(self, board: chess.Board, move: Optional[chess.Move] = None, parent: Optional['MCTSNode'] = None):
        self.board = board.copy()
        self.move = move
        self.parent = parent
        self.children: List['MCTSNode'] = []
        self.visits = 0
        self.wins = 0.0
        self.untried_moves = list(board.legal_moves)
        self.is_terminal = board.is_game_over()
    
    def is_fully_expanded(self) -> bool:
        """Check if all possible moves have been tried."""
        return len(self.untried_moves) == 0
    
    def best_child(self, c_param: float = 1.4) -> 'MCTSNode':
        """Select the best child using UCB1 formula."""
        choices_weights = [
            (child.wins / child.visits) + c_param * math.sqrt((2 * math.log(self.visits) / child.visits))
            for child in self.children
        ]
        return self.children[choices_weights.index(max(choices_weights))]
    
    def expand(self) -> 'MCTSNode':
        """Expand the node by adding a new child."""
        move = self.untried_moves.pop()
        new_board = self.board.copy()
        new_board.push(move)
        child_node = MCTSNode(new_board, move, self)
        self.children.append(child_node)
        return child_node
    
    def rollout(self) -> float:
        """Perform a random rollout from this position."""
        current_board = self.board.copy()
        
        # Limit rollout depth to avoid infinite games
        max_rollout_depth = 50
        depth = 0
        
        while not current_board.is_game_over() and depth < max_rollout_depth:
            legal_moves = list(current_board.legal_moves)
            if not legal_moves:
                break
            move = random.choice(legal_moves)
            current_board.push(move)
            depth += 1
        
        # Evaluate the final position
        if current_board.is_checkmate():
            # If it's checkmate, the player to move lost
            return 0.0 if current_board.turn == self.board.turn else 1.0
        elif current_board.is_stalemate() or current_board.is_insufficient_material():
            return 0.5  # Draw
        else:
            # Use a simple evaluation for incomplete rollouts
            return 0.5  # Neutral evaluation for incomplete games
    
    def backpropagate(self, result: float):
        """Backpropagate the result up the tree."""
        self.visits += 1
        self.wins += result
        if self.parent:
            # Flip the result for the parent (opponent's perspective)
            self.parent.backpropagate(1.0 - result)

class ChessBot:
    """
    A chess bot that combines minimax algorithm with alpha-beta pruning
    and Monte Carlo Tree Search to make intelligent moves.
    """
    
    def __init__(self, depth: int = 3, name: str = "HybridChessBot", 
                 mcts_simulations: int = 1000, top_moves_count: int = 5,
                 use_hybrid: bool = True):
        """
        Initialize the chess bot.
        
        Args:
            depth: Search depth for minimax algorithm
            name: Name of the bot
            mcts_simulations: Number of MCTS simulations to run
            top_moves_count: Number of top moves from minimax to explore with MCTS
            use_hybrid: Whether to use hybrid approach or just minimax
        """
        self.depth = depth
        self.name = name
        self.nodes_evaluated = 0
        self.mcts_simulations = mcts_simulations
        self.top_moves_count = top_moves_count
        self.use_hybrid = use_hybrid
        
        # Piece values for evaluation
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
        
        # Position tables for piece placement evaluation
        self.pawn_table = [
            0,  0,  0,  0,  0,  0,  0,  0,
            50, 50, 50, 50, 50, 50, 50, 50,
            10, 10, 20, 30, 30, 20, 10, 10,
            5,  5, 10, 25, 25, 10,  5,  5,
            0,  0,  0, 20, 20,  0,  0,  0,
            5, -5,-10,  0,  0,-10, -5,  5,
            5, 10, 10,-20,-20, 10, 10,  5,
            0,  0,  0,  0,  0,  0,  0,  0
        ]
        
        self.knight_table = [
            -50,-40,-30,-30,-30,-30,-40,-50,
            -40,-20,  0,  0,  0,  0,-20,-40,
            -30,  0, 10, 15, 15, 10,  0,-30,
            -30,  5, 15, 20, 20, 15,  5,-30,
            -30,  0, 15, 20, 20, 15,  0,-30,
            -30,  5, 10, 15, 15, 10,  5,-30,
            -40,-20,  0,  5,  5,  0,-20,-40,
            -50,-40,-30,-30,-30,-30,-40,-50
        ]
    
    def evaluate_board(self, board: chess.Board) -> int:
        """
        Evaluate the current board position.
        
        Args:
            board: Chess board to evaluate
            
        Returns:
            Evaluation score (positive for white advantage, negative for black)
        """
        if board.is_checkmate():
            return -20000 if board.turn else 20000
        
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        
        # Material evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = self.piece_values[piece.piece_type]
                
                # Add positional bonuses
                if piece.piece_type == chess.PAWN:
                    if piece.color == chess.WHITE:
                        value += self.pawn_table[square]
                    else:
                        value += self.pawn_table[chess.square_mirror(square)]
                elif piece.piece_type == chess.KNIGHT:
                    if piece.color == chess.WHITE:
                        value += self.knight_table[square]
                    else:
                        value += self.knight_table[chess.square_mirror(square)]
                
                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value
        
        # Mobility bonus
        legal_moves = len(list(board.legal_moves))
        if board.turn == chess.WHITE:
            score += legal_moves * 2
        else:
            score -= legal_moves * 2
        
        return score
    
    def minimax(self, board: chess.Board, depth: int, alpha: int, beta: int, 
                maximizing_player: bool) -> Tuple[int, Optional[chess.Move]]:
        """
        Minimax algorithm with alpha-beta pruning.
        
        Args:
            board: Current board position
            depth: Remaining search depth
            alpha: Alpha value for pruning
            beta: Beta value for pruning
            maximizing_player: True if maximizing player's turn
            
        Returns:
            Tuple of (evaluation_score, best_move)
        """
        self.nodes_evaluated += 1
        
        if depth == 0 or board.is_game_over():
            return self.evaluate_board(board), None
        
        best_move = None
        
        if maximizing_player:
            max_eval = float('-inf')
            for move in board.legal_moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            return max_eval, best_move
        else:
            min_eval = float('inf')
            for move in board.legal_moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            return min_eval, best_move
    
    def mcts_search(self, board: chess.Board, simulations: int) -> chess.Move:
        """
        Perform Monte Carlo Tree Search to find the best move.
        
        Args:
            board: Current board position
            simulations: Number of simulations to run
            
        Returns:
            Best move according to MCTS
        """
        root = MCTSNode(board)
        
        for _ in range(simulations):
            # Selection
            node = root
            while not node.is_terminal and node.is_fully_expanded():
                node = node.best_child()
            
            # Expansion
            if not node.is_terminal and not node.is_fully_expanded():
                node = node.expand()
            
            # Simulation (Rollout)
            result = node.rollout()
            
            # Backpropagation
            node.backpropagate(result)
        
        # Return the move with the highest visit count (most explored)
        if root.children:
            best_child = max(root.children, key=lambda c: c.visits)
            return best_child.move
        else:
            # Fallback to random move
            legal_moves = list(board.legal_moves)
            return random.choice(legal_moves) if legal_moves else None
    
    def get_top_moves_minimax(self, board: chess.Board, count: int) -> List[Tuple[chess.Move, int]]:
        """
        Get the top moves according to minimax evaluation.
        
        Args:
            board: Current board position
            count: Number of top moves to return
            
        Returns:
            List of (move, score) tuples sorted by score
        """
        moves_scores = []
        maximizing = board.turn == chess.WHITE
        
        for move in board.legal_moves:
            board.push(move)
            score, _ = self.minimax(board, self.depth - 1, float('-inf'), float('inf'), not maximizing)
            board.pop()
            moves_scores.append((move, score))
        
        # Sort by score (descending for white, ascending for black)
        moves_scores.sort(key=lambda x: x[1], reverse=maximizing)
        
        return moves_scores[:count]
    
    def mcts_on_candidates(self, board: chess.Board, candidate_moves: List[chess.Move]) -> chess.Move:
        """
        Run MCTS only on the candidate moves.
        
        Args:
            board: Current board position
            candidate_moves: List of candidate moves to explore
            
        Returns:
            Best move according to MCTS exploration of candidates
        """
        if not candidate_moves:
            return None
        
        move_scores = {}
        simulations_per_move = max(1, self.mcts_simulations // len(candidate_moves))
        
        for move in candidate_moves:
            # Create a board with the candidate move played
            test_board = board.copy()
            test_board.push(move)
            
            # Run MCTS from this position
            root = MCTSNode(test_board)
            
            for _ in range(simulations_per_move):
                # Selection
                node = root
                while not node.is_terminal and node.is_fully_expanded():
                    node = node.best_child()
                
                # Expansion
                if not node.is_terminal and not node.is_fully_expanded():
                    node = node.expand()
                
                # Simulation (Rollout)
                result = node.rollout()
                
                # Backpropagation
                node.backpropagate(result)
            
            # Score is the win rate from the perspective of the current player
            # Since we made the move, we want 1 - win_rate (opponent's perspective becomes ours)
            if root.visits > 0:
                move_scores[move] = 1.0 - (root.wins / root.visits)
            else:
                move_scores[move] = 0.5
        
        # Return the move with the highest score
        best_move = max(move_scores.keys(), key=lambda m: move_scores[m])
        return best_move
    
    def get_best_move(self, board: chess.Board) -> chess.Move:
        """
        Get the best move for the current position using hybrid approach.
        
        Args:
            board: Current board position
            
        Returns:
            Best move according to the bot's evaluation
        """
        self.nodes_evaluated = 0
        start_time = time.time()
        
        if not self.use_hybrid:
            # Use only minimax with alpha-beta pruning
            maximizing = board.turn == chess.WHITE
            _, best_move = self.minimax(board, self.depth, float('-inf'), float('inf'), maximizing)
        else:
            # Hybrid approach: Minimax + MCTS
            print(f"{self.name} using hybrid approach (Minimax + MCTS)")
            
            # Step 1: Use minimax to get top candidate moves
            print(f"Step 1: Finding top {self.top_moves_count} moves with minimax...")
            top_moves = self.get_top_moves_minimax(board, self.top_moves_count)
            
            if not top_moves:
                # Fallback to random move if no moves found
                legal_moves = list(board.legal_moves)
                best_move = random.choice(legal_moves) if legal_moves else None
            else:
                candidate_moves = [move for move, score in top_moves]
                print(f"Top moves from minimax: {[str(move) for move in candidate_moves]}")
                print(f"Minimax scores: {[score for move, score in top_moves]}")
                
                # Step 2: Use MCTS to explore these candidate moves more deeply
                print(f"Step 2: Exploring candidates with {self.mcts_simulations} MCTS simulations...")
                best_move = self.mcts_on_candidates(board, candidate_moves)
        
        end_time = time.time()
        
        if best_move is None:
            # Final fallback to random move
            legal_moves = list(board.legal_moves)
            best_move = random.choice(legal_moves) if legal_moves else None
        
        print(f"{self.name} evaluated {self.nodes_evaluated} positions in {end_time - start_time:.2f} seconds")
        print(f"Selected move: {best_move}")
        
        return best_move
    
    def set_depth(self, depth: int):
        """Set the search depth for the bot."""
        self.depth = depth
        print(f"{self.name} search depth set to {depth}")
    
    def set_mcts_simulations(self, simulations: int):
        """Set the number of MCTS simulations."""
        self.mcts_simulations = simulations
        print(f"{self.name} MCTS simulations set to {simulations}")
    
    def set_top_moves_count(self, count: int):
        """Set the number of top moves to explore with MCTS."""
        self.top_moves_count = count
        print(f"{self.name} top moves count set to {count}")
    
    def set_hybrid_mode(self, use_hybrid: bool):
        """Enable or disable hybrid mode."""
        self.use_hybrid = use_hybrid
        mode = "hybrid (Minimax + MCTS)" if use_hybrid else "minimax only"
        print(f"{self.name} mode set to {mode}")
    
    def get_config(self) -> Dict:
        """Get current bot configuration."""
        return {
            "name": self.name,
            "depth": self.depth,
            "mcts_simulations": self.mcts_simulations,
            "top_moves_count": self.top_moves_count,
            "use_hybrid": self.use_hybrid
        }