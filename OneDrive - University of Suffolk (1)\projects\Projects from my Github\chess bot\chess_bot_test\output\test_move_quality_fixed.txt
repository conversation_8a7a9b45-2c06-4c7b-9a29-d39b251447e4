PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_move_quality_fixed.py
=== Testing Move Quality Evaluation ===

=== Testing with Static Mode ===
Human move evaluation: Static evaluation mode (fastest)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
Quality: Good
Score difference: 44
✓ PASS - Quality rating is as expected

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
Quality: Good
Score difference: 114
✓ PASS - Quality rating is as expected

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
Quality: Good
Score difference: 45
✗ FAIL - Expected ['Poor', 'Questionable'], got Good

==================================================
=== Testing with Shallow Search Mode ===
Human move evaluation: Shallow search mode (balanced)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
Quality: Questionable
Score difference: -70
✗ FAIL - Expected ['Normal', 'Good'], got Questionable

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
Quality: Normal
Score difference: -13
✗ FAIL - Expected ['Good', 'Excellent'], got Normal

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
Quality: Blunder
Score difference: -917
✗ FAIL - Expected ['Poor', 'Questionable'], got Blunder

==================================================
=== Testing with Full Search Mode ===
Human move evaluation: Full depth-2 search mode (slowest but most accurate)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
Quality: Good
Score difference: 47
✓ PASS - Quality rating is as expected

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
Quality: Good
Score difference: 40
✓ PASS - Quality rating is as expected

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
Quality: Blunder
Score difference: -800
✗ FAIL - Expected ['Poor', 'Questionable'], got Blunder




=======================================================
=======================================================


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_move_quality_fixed.py
=== Testing Move Quality Evaluation ===

=== Testing with Static Mode ===
Human move evaluation: Static evaluation mode (fastest)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
Quality: Good
Score difference: 44
✓ PASS - Quality rating is as expected

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
Quality: Excellent
Score difference: 114
✓ PASS - Quality rating is as expected

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
Quality: Questionable
Score difference: -55
✗ FAIL - Expected ['Blunder', 'Poor'], got Questionable

==================================================
=== Testing with Shallow Search Mode ===
Human move evaluation: Shallow search mode (balanced)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
Quality: Questionable
Score difference: -70
✗ FAIL - Expected ['Normal', 'Good'], got Questionable

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
Quality: Normal
Score difference: -13
✗ FAIL - Expected ['Good', 'Excellent'], got Normal

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
Quality: Blunder
Score difference: -917
✓ PASS - Quality rating is as expected

==================================================
=== Testing with Full Search Mode ===
Human move evaluation: Full depth-2 search mode (slowest but most accurate)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
Quality: Good
Score difference: 47
✓ PASS - Quality rating is as expected

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
Quality: Good
Score difference: 40
✓ PASS - Quality rating is as expected

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
Quality: Blunder
Score difference: -800
✓ PASS - Quality rating is as expected




===============================================================
===============================================================


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_move_quality_fixed.py
=== Testing Move Quality Evaluation ===

=== Testing with Static Mode ===
Human move evaluation: Static evaluation mode (fastest)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
Quality: Good
Score difference: 44
✓ PASS - Quality rating is as expected

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
Quality: Excellent
Score difference: 114
✓ PASS - Quality rating is as expected

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
Quality: Blunder
Score difference: -155
✓ PASS - Quality rating is as expected

==================================================
=== Testing with Shallow Search Mode ===
Human move evaluation: Shallow search mode (balanced)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
Quality: Normal
Score difference: -70
✓ PASS - Quality rating is as expected

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
Quality: Normal
Score difference: -13
✗ FAIL - Expected ['Good', 'Excellent'], got Normal

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
Quality: Blunder
Score difference: -917
✓ PASS - Quality rating is as expected

==================================================
=== Testing with Full Search Mode ===
Human move evaluation: Full depth-2 search mode (slowest but most accurate)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
Quality: Good
Score difference: 47
✓ PASS - Quality rating is as expected

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
Quality: Good
Score difference: 40
✓ PASS - Quality rating is as expected
Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
Quality: Blunder
Score difference: -800
✓ PASS - Quality rating is as expected

==================================================








==================================================================================
==================================================================================




PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_move_quality.py      
=== Test Case 1: Opening move d4 ===
Position before: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Static score before: 0
Move: d4
Quality: Normal
Score difference: -70

=== Test Case 2: Blunder move Qd2 ===
Position before: rnbqk2r/pppp1ppp/3bp3/8/4n3/8/PPP2PPP/RNBQKBNR w KQkq - 0 5
Static score before: -331
Move: Qd2
Quality: Blunder
Score difference: -570

=== Test Case 3: Good move dxe6 ===
Position before: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Static score before: -24
Move: dxe6
Quality: Good
Score difference: -13
PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_move_quality_fixed.py
=== Testing Move Quality Evaluation ===

=== Testing with Static Mode ===
Human move evaluation: Static evaluation mode (fastest)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
Quality: Good
Score difference: 44
✓ PASS - Quality rating is as expected

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
Quality: Excellent
Score difference: 114
✓ PASS - Quality rating is as expected

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
Quality: Blunder
Score difference: -155
✓ PASS - Quality rating is as expected

==================================================
=== Testing with Shallow Search Mode ===
Human move evaluation: Shallow search mode (balanced)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
Quality: Normal
Score difference: -70
✓ PASS - Quality rating is as expected

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
Quality: Good
Score difference: -13
✓ PASS - Quality rating is as expected

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
Quality: Blunder
Score difference: -917
✓ PASS - Quality rating is as expected

==================================================
=== Testing with Full Search Mode ===
Human move evaluation: Full depth-2 search mode (slowest but most accurate)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
Quality: Good
Score difference: 47
✓ PASS - Quality rating is as expected

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
Quality: Good
Score difference: 40
✓ PASS - Quality rating is as expected

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
Quality: Blunder
Score difference: -800
✓ PASS - Quality rating is as expected

==================================================
















==========================================================================================
==========================================================================================



PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_move_quality_fixed.py
=== Testing Move Quality Evaluation ===

=== Testing with Static Mode ===
Human move evaluation: Static evaluation mode (fastest)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
DEBUG Human Move Quality: d4
  Board turn: True (White)
  Search score (passed): 0
  Static score (passed): 0
  Eval before (calculated): 0
  Eval after (calculated): 44
  Score difference: 44
  Evaluation mode: depth=0, lightweight=True
Quality: Good
Score difference: 44
✓ PASS - Quality rating is as expected

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
DEBUG Human Move Quality: dxe6
  Board turn: True (White)
  Search score (passed): 0
  Static score (passed): -24
  Eval before (calculated): 21
  Eval after (calculated): 135
  Score difference: 114
  Evaluation mode: depth=0, lightweight=True
Quality: Excellent
Score difference: 114
✓ PASS - Quality rating is as expected

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
DEBUG Human Move Quality: Qh5
  Board turn: True (White)
  Search score (passed): 0
  Static score (passed): 14
  Eval before (calculated): 39
  Eval after (calculated): -116
  Score difference: -155
  Evaluation mode: depth=0, lightweight=True
Quality: Blunder
Score difference: -155
✓ PASS - Quality rating is as expected

==================================================
=== Testing with Shallow Search Mode ===
Human move evaluation: Shallow search mode (balanced)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
DEBUG Human Move Quality: d4
  Board turn: True (White)
  Search score (passed): 0
  Static score (passed): 0
  Eval before (calculated): 70
  Eval after (calculated): 0
  Score difference: -70
  Evaluation mode: depth=1, lightweight=True
Quality: Normal
Score difference: -70
✓ PASS - Quality rating is as expected

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
DEBUG Human Move Quality: dxe6
  Board turn: True (White)
  Search score (passed): 0
  Static score (passed): -24
  Eval before (calculated): -77
  Eval after (calculated): -90
  Score difference: -13
  Evaluation mode: depth=1, lightweight=True
Quality: Good
Score difference: -13
✓ PASS - Quality rating is as expected

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
DEBUG Human Move Quality: Qh5
  Board turn: True (White)
  Search score (passed): 0
  Static score (passed): 14
  Eval before (calculated): 70
  Eval after (calculated): -847
  Score difference: -917
  Evaluation mode: depth=1, lightweight=True
Quality: Blunder
Score difference: -917
✓ PASS - Quality rating is as expected

==================================================
=== Testing with Full Search Mode ===
Human move evaluation: Full depth-2 search mode (slowest but most accurate)

Test: Opening move d4
Position: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Move: d2d4
DEBUG Human Move Quality: d4
  Board turn: True (White)
  Search score (passed): 0
  Static score (passed): 0
  Eval before (calculated): 0
  Eval after (calculated): 47
  Score difference: 47
  Evaluation mode: depth=2, lightweight=False
Quality: Good
Score difference: 47
✓ PASS - Quality rating is as expected

Test: Good tactical move
Position: rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3
Move: d5e6
DEBUG Human Move Quality: dxe6
  Board turn: True (White)
  Search score (passed): 0
  Static score (passed): -24
  Eval before (calculated): -77
  Eval after (calculated): -37
  Score difference: 40
  Evaluation mode: depth=2, lightweight=False
Quality: Good
Score difference: 40
✓ PASS - Quality rating is as expected

Test: Blunder move
Position: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Move: d1h5
DEBUG Human Move Quality: Qh5
  Board turn: True (White)
  Search score (passed): 0
  Static score (passed): 14
  Eval before (calculated): 12
  Eval after (calculated): -788
  Score difference: -800
  Evaluation mode: depth=2, lightweight=False
Quality: Blunder
Score difference: -800
✓ PASS - Quality rating is as expected

==================================================