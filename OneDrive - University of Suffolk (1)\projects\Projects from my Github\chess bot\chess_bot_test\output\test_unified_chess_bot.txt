PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_unified_chess_bot.py
Running Unified Chess Bot Unit Tests...
============================================================
test_board_evaluation_checkmate (__main__.TestUnifiedChessBotBasic.test_board_evaluation_checkmate)
Test board evaluation in checkmate position. ... ok
test_board_evaluation_stalemate (__main__.TestUnifiedChessBotBasic.test_board_evaluation_stalemate)
Test board evaluation in stalemate position. ... ok
test_board_evaluation_starting_position (__main__.TestUnifiedChessBotBasic.test_board_evaluation_starting_position)
Test board evaluation at starting position. ... ok
test_bot_initialization (__main__.TestUnifiedChessBotBasic.test_bot_initialization)
Test bot initialization with default parameters. ... ok
test_bot_initialization_custom (__main__.TestUnifiedChessBotBasic.test_bot_initialization_custom)     
Test bot initialization with custom parameters. ... ok
test_get_best_move_no_legal_moves (__main__.TestUnifiedChessBotBasic.test_get_best_move_no_legal_moves)
Test get_best_move when no legal moves are available. ... TestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 2
  - Quiescence nodes: 0
  - Transposition hits: 0
  - Table size: 0
ok
test_get_best_move_returns_legal_move (__main__.TestUnifiedChessBotBasic.test_get_best_move_returns_legal_move)
Test that get_best_move returns a legal move. ... TestBot statistics:
  - Time: 0.11s
  - Nodes evaluated: 108
  - Quiescence nodes: 89
  - Transposition hits: 0
  - Table size: 21
ok
test_piece_values (__main__.TestUnifiedChessBotBasic.test_piece_values)
Test that piece values are reasonable. ... ok
test_set_depth (__main__.TestUnifiedChessBotBasic.test_set_depth)
Test setting bot depth. ... TestBot depth set to 5
ok
test_set_features (__main__.TestUnifiedChessBotBasic.test_set_features)
Test enabling/disabling bot features. ... TestBot features updated:
  - Iterative deepening: False
  - Quiescence search: False
  - Transposition table: False
TestBot features updated:
  - Iterative deepening: True
  - Quiescence search: True
  - Transposition table: True
ok
test_endgame_detection (__main__.TestUnifiedChessBotAdvanced.test_endgame_detection)
Test endgame detection. ... ok
test_iterative_deepening (__main__.TestUnifiedChessBotAdvanced.test_iterative_deepening)
Test iterative deepening functionality. ... AdvancedTestBot statistics:
  - Time: 22.99s
  - Nodes evaluated: 4537
  - Quiescence nodes: 17967
  - Transposition hits: 0
  - Table size: 253
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 1
  - Quiescence nodes: 0
  - Transposition hits: 1
  - Table size: 253
FAIL
test_move_ordering (__main__.TestUnifiedChessBotAdvanced.test_move_ordering)
Test move ordering for alpha-beta pruning efficiency. ... ok
test_opening_book (__main__.TestUnifiedChessBotAdvanced.test_opening_book)
Test opening book functionality. ... AdvancedTestBot played from opening book: g1f3
ok
test_quiescence_search (__main__.TestUnifiedChessBotAdvanced.test_quiescence_search)
Test quiescence search functionality. ... AdvancedTestBot statistics:
  - Time: 14.50s
  - Nodes evaluated: 2901
  - Quiescence nodes: 9420
  - Transposition hits: 0
  - Table size: 209
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 3
  - Quiescence nodes: 0
  - Transposition hits: 3
  - Table size: 209
ok
test_transposition_table (__main__.TestUnifiedChessBotAdvanced.test_transposition_table)
Test transposition table functionality. ... AdvancedTestBot statistics:
  - Time: 44.50s
  - Nodes evaluated: 4537
  - Quiescence nodes: 17967
  - Transposition hits: 0
  - Table size: 253
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 3
  - Quiescence nodes: 0
  - Transposition hits: 3
  - Table size: 253
ok
test_all_presets_work (__main__.TestUnifiedChessBotPresets.test_all_presets_work)
Test that all preset bots can find moves. ... EasyBot played from opening book: d2d4
MediumBot played from opening book: d2d4
HardBot played from opening book: g1f3
ExpertBot played from opening book: c2c4
ok
test_easy_bot (__main__.TestUnifiedChessBotPresets.test_easy_bot)
Test EasyBot preset. ... ok
test_expert_bot (__main__.TestUnifiedChessBotPresets.test_expert_bot)
Test ExpertBot preset. ... ok
test_hard_bot (__main__.TestUnifiedChessBotPresets.test_hard_bot)
Test HardBot preset. ... ok
test_medium_bot (__main__.TestUnifiedChessBotPresets.test_medium_bot)
Test MediumBot preset. ... ok
test_depth_scaling (__main__.TestUnifiedChessBotPerformance.test_depth_scaling)
Test that higher depth takes longer but evaluates more nodes. ... UnifiedChessBot statistics:
  - Time: 0.39s
  - Nodes evaluated: 69
  - Quiescence nodes: 150
  - Transposition hits: 0
  - Table size: 34
UnifiedChessBot statistics:
  - Time: 148.69s
  - Nodes evaluated: 13176
  - Quiescence nodes: 108062
  - Transposition hits: 279
  - Table size: 3037
FAIL
test_search_speed (__main__.TestUnifiedChessBotPerformance.test_search_speed)
Test that bot can find moves in reasonable time. ... MediumBot statistics:
  - Time: 20.34s
  - Nodes evaluated: 4537
  - Quiescence nodes: 17967
  - Transposition hits: 0
  - Table size: 253
FAIL
test_transposition_table_efficiency (__main__.TestUnifiedChessBotPerformance.test_transposition_table_efficiency)
Test that transposition table improves efficiency. ... UnifiedChessBot statistics:
  - Time: 18.98s
  - Nodes evaluated: 4459
  - Quiescence nodes: 17731
UnifiedChessBot statistics:
  - Time: 19.67s
  - Nodes evaluated: 4537
  - Quiescence nodes: 17967
  - Transposition hits: 0
  - Table size: 253
FAIL
test_avoid_blunders (__main__.TestUnifiedChessBotTactical.test_avoid_blunders)
Test that bot doesn't hang pieces unnecessarily. ... HardBot statistics:
  - Time: 10.88s
  - Nodes evaluated: 2901
  - Quiescence nodes: 9420
  - Transposition hits: 0
  - Table size: 209
HardBot statistics:
  - Time: 143.66s
  - Nodes evaluated: 30519
  - Quiescence nodes: 110669
  - Transposition hits: 318
  - Table size: 3396
ok
test_checkmate_in_one (__main__.TestUnifiedChessBotTactical.test_checkmate_in_one)
Test that bot finds checkmate in one move. ... HardBot statistics:
  - Time: 12.41s
  - Nodes evaluated: 3183
  - Quiescence nodes: 10948
  - Transposition hits: 0
  - Table size: 217
FAIL
test_simple_capture (__main__.TestUnifiedChessBotTactical.test_simple_capture)
Test that bot captures undefended pieces. ... HardBot statistics:
  - Time: 11.08s
  - Nodes evaluated: 2635
  - Quiescence nodes: 9251
  - Transposition hits: 0
  - Table size: 325
ok

======================================================================
FAIL: test_iterative_deepening (__main__.TestUnifiedChessBotAdvanced.test_iterative_deepening)        
Test iterative deepening functionality.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test\test_unified_chess_bot.py", line 228, in test_iterative_deepening
    self.assertLess(abs(time_with_id - time_without_id), 5.0)
AssertionError: 22.989862203598022 not less than 5.0

======================================================================
FAIL: test_depth_scaling (__main__.TestUnifiedChessBotPerformance.test_depth_scaling)
Test that higher depth takes longer but evaluates more nodes.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test\test_unified_chess_bot.py", line 387, in test_depth_scaling
    self.assertLess(time_deep, 30.0)
AssertionError: 148.68938946723938 not less than 30.0

======================================================================
FAIL: test_search_speed (__main__.TestUnifiedChessBotPerformance.test_search_speed)
Test that bot can find moves in reasonable time.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test\test_unified_chess_bot.py", line 350, in test_search_speed
    self.assertLess(search_time, 10.0)
AssertionError: 20.351800441741943 not less than 10.0

======================================================================
FAIL: test_transposition_table_efficiency (__main__.TestUnifiedChessBotPerformance.test_transposition_table_efficiency)
Test that transposition table improves efficiency.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test\test_unified_chess_bot.py", line 418, in test_transposition_table_efficiency
    self.assertLess(time_no_tt, 15.0)
AssertionError: 18.98495125770569 not less than 15.0

======================================================================
FAIL: test_checkmate_in_one (__main__.TestUnifiedChessBotTactical.test_checkmate_in_one)
Test that bot finds checkmate in one move.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test\test_unified_chess_bot.py", line 498, in test_checkmate_in_one
    self.assertGreater(eval_score, 50)  # At least half a pawn advantage
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: 27 not greater than 50

----------------------------------------------------------------------
Ran 27 tests in 471.289s

FAILED (failures=5)
\nTest Summary:
Tests run: 27
Failures: 5
Errors: 0
Success rate: 81.5%
\n============================================================
UNIFIED CHESS BOT - PERFORMANCE BENCHMARK
============================================================
\nTesting EasyBot:
------------------------------
EasyBot statistics:
  - Time: 0.18s
  - Nodes evaluated: 171
  - Quiescence nodes: 0
Time: 0.18 seconds
Nodes evaluated: 171
Quiescence nodes: 0
Nodes per second: 944
Selected move: e2e4
\nTesting MediumBot:
------------------------------
MediumBot statistics:
  - Time: 2.47s
  - Nodes evaluated: 1301
  - Quiescence nodes: 1488
  - Transposition hits: 0
  - Table size: 98
Time: 2.47 seconds
Nodes evaluated: 1,301
Quiescence nodes: 1,488
Transposition hits: 0
Transposition table size: 98
Nodes per second: 526
Selected move: d2d3
\nTesting HardBot:
------------------------------
HardBot statistics:
  - Time: 10.82s
  - Nodes evaluated: 8804
  - Quiescence nodes: 8356
  - Transposition hits: 173
  - Table size: 1243
Time: 10.82 seconds
Nodes evaluated: 8,804
Quiescence nodes: 8,356
Transposition hits: 173
Transposition table size: 1,243
Nodes per second: 814
Selected move: g1f3
\nTesting ExpertBot:
------------------------------
ExpertBot statistics:
  - Time: 10.59s
  - Nodes evaluated: 8804
  - Quiescence nodes: 8356
  - Transposition hits: 173
  - Table size: 1243
Time: 10.59 seconds
Nodes evaluated: 8,804
Quiescence nodes: 8,356
Transposition hits: 173
Transposition table size: 1,243
Nodes per second: 831
Selected move: g1f3
\n============================================================
\n============================================================
UNIFIED CHESS BOT - TACTICAL TEST SUITE
============================================================
\nTest 1: Open game position
FEN: rnbqkb1r/pppp1ppp/5n2/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3
HardBot statistics:
  - Time: 10.29s
  - Nodes evaluated: 2901
  - Quiescence nodes: 9420
  - Transposition hits: 0
  - Table size: 209
Time: 10.55 seconds
Evaluation: 0.00
Best move: f3d4
Move in range: True
Position after move: rnbqkb1r/pppp1ppp/5n2/4p3/3NP3/8/PPPP1PPP/RNBQKB1R b KQkq - 3 3
\nTest 2: Symmetric position
FEN: r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3
HardBot statistics:
  - Time: 27.20s
  - Nodes evaluated: 4890
  - Quiescence nodes: 10570
  - Transposition hits: 16
  - Table size: 544
Time: 28.98 seconds
Evaluation: -0.12
Best move: f3d4
Move in range: True
Position after move: r1bqkbnr/pppp1ppp/2n5/4p3/3NP3/8/PPPP1PPP/RNBQKB1R b KQkq - 3 3
\nTest 3: Italian Game
FEN: rnbqk2r/pppp1ppp/5n2/2b1p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4
HardBot statistics:
  - Time: 26.45s
  - Nodes evaluated: 1953
  - Quiescence nodes: 9383
  - Transposition hits: 1
  - Table size: 703
Time: 28.71 seconds
Evaluation: 0.00
Best move: f3h4
Move in range: True
Position after move: rnbqk2r/pppp1ppp/5n2/2b1p3/2B1P2N/8/PPPP1PPP/RNBQK2R b KQkq - 5 4
\n============================================================
\nAll tests completed!










==============================================================================
==============================================================================
==============================================================================


latest test results [all 27 tests passed with QS search depth of 4]:



PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_unified_chess_bot.py
Running Unified Chess Bot Unit Tests...
============================================================
test_board_evaluation_checkmate (__main__.TestUnifiedChessBotBasic.test_board_evaluation_checkmate)
Test board evaluation in checkmate position. ... ok
test_board_evaluation_stalemate (__main__.TestUnifiedChessBotBasic.test_board_evaluation_stalemate)
Test board evaluation in stalemate position. ... ok
test_board_evaluation_starting_position (__main__.TestUnifiedChessBotBasic.test_board_evaluation_starting_position)
Test board evaluation at starting position. ... ok
test_bot_initialization (__main__.TestUnifiedChessBotBasic.test_bot_initialization)
Test bot initialization with default parameters. ... ok
test_bot_initialization_custom (__main__.TestUnifiedChessBotBasic.test_bot_initialization_custom)     
Test bot initialization with custom parameters. ... ok
test_get_best_move_no_legal_moves (__main__.TestUnifiedChessBotBasic.test_get_best_move_no_legal_moves)
Test get_best_move when no legal moves are available. ... TestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 2
  - Quiescence nodes: 0
  - Transposition hits: 0
  - Table size: 0
ok
test_get_best_move_returns_legal_move (__main__.TestUnifiedChessBotBasic.test_get_best_move_returns_legal_move)
Test that get_best_move returns a legal move. ... TestBot statistics:
  - Time: 0.13s
  - Nodes evaluated: 108
  - Quiescence nodes: 89
  - Transposition hits: 0
  - Table size: 21
ok
test_piece_values (__main__.TestUnifiedChessBotBasic.test_piece_values)
Test that piece values are reasonable. ... ok
test_set_depth (__main__.TestUnifiedChessBotBasic.test_set_depth)
Test setting bot depth. ... TestBot depth set to 5
ok
test_set_features (__main__.TestUnifiedChessBotBasic.test_set_features)
Test enabling/disabling bot features. ... TestBot features updated:
  - Iterative deepening: False
  - Quiescence search: False
  - Transposition table: False
TestBot features updated:
  - Iterative deepening: True
  - Quiescence search: True
  - Transposition table: True
ok
test_endgame_detection (__main__.TestUnifiedChessBotAdvanced.test_endgame_detection)
Test endgame detection. ... ok
test_iterative_deepening (__main__.TestUnifiedChessBotAdvanced.test_iterative_deepening)
Test iterative deepening functionality. ... AdvancedTestBot statistics:
  - Time: 8.45s
  - Nodes evaluated: 3464
  - Quiescence nodes: 6151
  - Transposition hits: 0
  - Table size: 291
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 1
  - Quiescence nodes: 0
  - Transposition hits: 1
  - Table size: 291
ok
test_move_ordering (__main__.TestUnifiedChessBotAdvanced.test_move_ordering)
Test move ordering for alpha-beta pruning efficiency. ... ok
test_opening_book (__main__.TestUnifiedChessBotAdvanced.test_opening_book)
Test opening book functionality. ... AdvancedTestBot played from opening book: d2d4
ok
test_quiescence_search (__main__.TestUnifiedChessBotAdvanced.test_quiescence_search)
Test quiescence search functionality. ... AdvancedTestBot statistics:
  - Time: 17.75s
  - Nodes evaluated: 2903
  - Quiescence nodes: 8946
  - Transposition hits: 0
  - Table size: 215
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 3
  - Quiescence nodes: 0
  - Transposition hits: 3
  - Table size: 215
ok
test_transposition_table (__main__.TestUnifiedChessBotAdvanced.test_transposition_table)
Test transposition table functionality. ... AdvancedTestBot statistics:
  - Time: 13.16s
  - Nodes evaluated: 3464
  - Quiescence nodes: 6151
  - Transposition hits: 0
  - Table size: 291
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 3
  - Quiescence nodes: 0
  - Transposition hits: 3
  - Table size: 291
ok
test_all_presets_work (__main__.TestUnifiedChessBotPresets.test_all_presets_work)
Test that all preset bots can find moves. ... EasyBot played from opening book: d2d4
MediumBot played from opening book: e2e4
HardBot played from opening book: d2d4
ExpertBot played from opening book: c2c4
ok
test_easy_bot (__main__.TestUnifiedChessBotPresets.test_easy_bot)
Test EasyBot preset. ... ok
test_expert_bot (__main__.TestUnifiedChessBotPresets.test_expert_bot)
Test ExpertBot preset. ... ok
test_hard_bot (__main__.TestUnifiedChessBotPresets.test_hard_bot)
Test HardBot preset. ... ok
test_medium_bot (__main__.TestUnifiedChessBotPresets.test_medium_bot)
Test MediumBot preset. ... ok
test_depth_scaling (__main__.TestUnifiedChessBotPerformance.test_depth_scaling)
Test that higher depth takes longer but evaluates more nodes. ... UnifiedChessBot statistics:
  - Time: 0.42s
  - Nodes evaluated: 243
  - Quiescence nodes: 264
  - Transposition hits: 0
  - Table size: 30
UnifiedChessBot statistics:
  - Time: 9.51s
  - Nodes evaluated: 2665
  - Quiescence nodes: 4685
  - Transposition hits: 0
  - Table size: 268
ok
test_search_speed (__main__.TestUnifiedChessBotPerformance.test_search_speed)
Test that bot can find moves in reasonable time. ... MediumBot statistics:
  - Time: 9.78s
  - Nodes evaluated: 3464
  - Quiescence nodes: 6151
  - Transposition hits: 0
  - Table size: 291
ok
test_transposition_table_efficiency (__main__.TestUnifiedChessBotPerformance.test_transposition_table_efficiency)
Test that transposition table improves efficiency. ... UnifiedChessBot statistics:
  - Time: 0.29s
  - Nodes evaluated: 273
  - Quiescence nodes: 305
UnifiedChessBot statistics:
  - Time: 0.58s
  - Nodes evaluated: 300
  - Quiescence nodes: 332
  - Transposition hits: 0
  - Table size: 30
ok
test_avoid_blunders (__main__.TestUnifiedChessBotTactical.test_avoid_blunders)
Test that bot doesn't hang pieces unnecessarily. ... HardBot statistics:
  - Time: 11.56s
  - Nodes evaluated: 2903
  - Quiescence nodes: 8946
  - Transposition hits: 0
  - Table size: 215
HardBot statistics:
  - Time: 121.93s
  - Nodes evaluated: 26838
  - Quiescence nodes: 94267
  - Transposition hits: 277
  - Table size: 3068
ok
test_checkmate_in_one (__main__.TestUnifiedChessBotTactical.test_checkmate_in_one)
Test that bot finds checkmate in one move. ... HardBot statistics:
  - Time: 38.15s
  - Nodes evaluated: 15991
  - Quiescence nodes: 38036
  - Transposition hits: 207
  - Table size: 2472
ok
test_simple_capture (__main__.TestUnifiedChessBotTactical.test_simple_capture)
Test that bot captures undefended pieces. ... HardBot statistics:
  - Time: 23.00s
  - Nodes evaluated: 17091
  - Quiescence nodes: 24116
  - Transposition hits: 125
  - Table size: 1872
ok

----------------------------------------------------------------------
Ran 27 tests in 256.953s

OK
\nTest Summary:
Tests run: 27
Failures: 0
Errors: 0
Success rate: 100.0%
\n============================================================
UNIFIED CHESS BOT - PERFORMANCE BENCHMARK
============================================================
\nTesting EasyBot:
------------------------------
EasyBot statistics:
  - Time: 0.13s
  - Nodes evaluated: 171
  - Quiescence nodes: 0
Time: 0.13 seconds
Nodes evaluated: 171
Quiescence nodes: 0
Nodes per second: 1,323
Selected move: e2e4
\nTesting MediumBot:
------------------------------
MediumBot statistics:
  - Time: 1.68s
  - Nodes evaluated: 1274
  - Quiescence nodes: 1461
  - Transposition hits: 0
  - Table size: 98
Time: 1.68 seconds
Nodes evaluated: 1,274
Quiescence nodes: 1,461
Transposition hits: 0
Transposition table size: 98
Nodes per second: 760
Selected move: d2d3
\nTesting HardBot:
------------------------------
HardBot statistics:
  - Time: 8.37s
  - Nodes evaluated: 8777
  - Quiescence nodes: 8329
  - Transposition hits: 173
  - Table size: 1243
Time: 8.38 seconds
Nodes evaluated: 8,777
Quiescence nodes: 8,329
Transposition hits: 173
Transposition table size: 1,243
Nodes per second: 1,048
Selected move: g1f3
\nTesting ExpertBot:
------------------------------
ExpertBot statistics:
  - Time: 131.56s
  - Nodes evaluated: 79678
  - Quiescence nodes: 126474
  - Transposition hits: 916
  - Table size: 7455
Time: 131.56 seconds
Nodes evaluated: 79,678
Quiescence nodes: 126,474
Transposition hits: 916
Transposition table size: 7,455
Nodes per second: 606
Selected move: c2c4
\n============================================================
\n============================================================
UNIFIED CHESS BOT - TACTICAL TEST SUITE
============================================================
\nTest 1: Open game position
FEN: rnbqkb1r/pppp1ppp/5n2/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3
HardBot statistics:
  - Time: 35.32s
  - Nodes evaluated: 16407
  - Quiescence nodes: 36598
  - Transposition hits: 219
  - Table size: 2591
Time: 35.32 seconds
Evaluation: 0.00
Best move: b1c3
Move in range: True
Position after move: rnbqkb1r/pppp1ppp/5n2/4p3/4P3/2N2N2/PPPP1PPP/R1BQKB1R b KQkq - 3 3
\nTest 2: Symmetric position
FEN: r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3
HardBot statistics:
  - Time: 23.14s
  - Nodes evaluated: 5610
  - Quiescence nodes: 11551
  - Transposition hits: 89
  - Table size: 3053
Time: 23.48 seconds
Evaluation: -0.12
Best move: e1e2
Move in range: True
Position after move: r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPPKPPP/RNBQ1B1R b kq - 3 3
\nTest 3: Italian Game
FEN: rnbqk2r/pppp1ppp/5n2/2b1p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4
HardBot statistics:
  - Time: 17.37s
  - Nodes evaluated: 2042
  - Quiescence nodes: 8624
  - Transposition hits: 11
  - Table size: 3217
Time: 19.36 seconds
Evaluation: 0.00
Best move: c4e6
Move in range: True
Position after move: rnbqk2r/pppp1ppp/4Bn2/2b1p3/4P3/5N2/PPPP1PPP/RNBQK2R b KQkq - 5 4
\n============================================================
\nAll tests completed!







=======================================


QS search depth of 6:


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_unified_chess_bot.py
Running Unified Chess Bot Unit Tests...
============================================================
test_board_evaluation_checkmate (__main__.TestUnifiedChessBotBasic.test_board_evaluation_checkmate)
Test board evaluation in checkmate position. ... ok
test_board_evaluation_stalemate (__main__.TestUnifiedChessBotBasic.test_board_evaluation_stalemate)
Test board evaluation in stalemate position. ... ok
test_board_evaluation_starting_position (__main__.TestUnifiedChessBotBasic.test_board_evaluation_starting_position)
Test board evaluation at starting position. ... ok
test_bot_initialization (__main__.TestUnifiedChessBotBasic.test_bot_initialization)
Test bot initialization with default parameters. ... ok
test_bot_initialization_custom (__main__.TestUnifiedChessBotBasic.test_bot_initialization_custom)     
Test bot initialization with custom parameters. ... ok
test_get_best_move_no_legal_moves (__main__.TestUnifiedChessBotBasic.test_get_best_move_no_legal_moves)
Test get_best_move when no legal moves are available. ... TestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 2
  - Quiescence nodes: 0
  - Transposition hits: 0
  - Table size: 0
ok
test_get_best_move_returns_legal_move (__main__.TestUnifiedChessBotBasic.test_get_best_move_returns_legal_move)
Test that get_best_move returns a legal move. ... TestBot statistics:
  - Time: 0.14s
  - Nodes evaluated: 108
  - Quiescence nodes: 89
  - Transposition hits: 0
  - Table size: 21
ok
test_piece_values (__main__.TestUnifiedChessBotBasic.test_piece_values)
Test that piece values are reasonable. ... ok
test_set_depth (__main__.TestUnifiedChessBotBasic.test_set_depth)
Test setting bot depth. ... TestBot depth set to 5
ok
test_set_features (__main__.TestUnifiedChessBotBasic.test_set_features)
Test enabling/disabling bot features. ... TestBot features updated:
  - Iterative deepening: False
  - Quiescence search: False
  - Transposition table: False
TestBot features updated:
  - Iterative deepening: True
  - Quiescence search: True
  - Transposition table: True
ok
test_endgame_detection (__main__.TestUnifiedChessBotAdvanced.test_endgame_detection)
Test endgame detection. ... ok
test_iterative_deepening (__main__.TestUnifiedChessBotAdvanced.test_iterative_deepening)
Test iterative deepening functionality. ... AdvancedTestBot statistics:
  - Time: 9.58s
  - Nodes evaluated: 3385
  - Quiescence nodes: 5994
  - Transposition hits: 0
  - Table size: 282
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 1
  - Quiescence nodes: 0
  - Transposition hits: 1
  - Table size: 282
ok
test_move_ordering (__main__.TestUnifiedChessBotAdvanced.test_move_ordering)
Test move ordering for alpha-beta pruning efficiency. ... ok
test_opening_book (__main__.TestUnifiedChessBotAdvanced.test_opening_book)
Test opening book functionality. ... AdvancedTestBot played from opening book: d2d4
ok
test_quiescence_search (__main__.TestUnifiedChessBotAdvanced.test_quiescence_search)
Test quiescence search functionality. ... AdvancedTestBot statistics:
  - Time: 27.03s
  - Nodes evaluated: 2901
  - Quiescence nodes: 9420
  - Transposition hits: 0
  - Table size: 209
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 3
  - Quiescence nodes: 0
  - Transposition hits: 3
  - Table size: 209
ok
test_transposition_table (__main__.TestUnifiedChessBotAdvanced.test_transposition_table)
Test transposition table functionality. ... AdvancedTestBot statistics:
  - Time: 13.37s
  - Nodes evaluated: 3385
  - Quiescence nodes: 5994
  - Transposition hits: 0
  - Table size: 282
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 3
  - Quiescence nodes: 0
  - Transposition hits: 3
  - Table size: 282
ok
test_all_presets_work (__main__.TestUnifiedChessBotPresets.test_all_presets_work)
Test that all preset bots can find moves. ... EasyBot played from opening book: c2c4
MediumBot played from opening book: c2c4
HardBot played from opening book: d2d4
ExpertBot played from opening book: e2e4
ok
test_easy_bot (__main__.TestUnifiedChessBotPresets.test_easy_bot)
Test EasyBot preset. ... ok
test_expert_bot (__main__.TestUnifiedChessBotPresets.test_expert_bot)
Test ExpertBot preset. ... ok
test_hard_bot (__main__.TestUnifiedChessBotPresets.test_hard_bot)
Test HardBot preset. ... ok
test_medium_bot (__main__.TestUnifiedChessBotPresets.test_medium_bot)
Test MediumBot preset. ... ok
test_depth_scaling (__main__.TestUnifiedChessBotPerformance.test_depth_scaling)
Test that higher depth takes longer but evaluates more nodes. ... UnifiedChessBot statistics:
  - Time: 0.43s
  - Nodes evaluated: 243
  - Quiescence nodes: 264
  - Transposition hits: 0
  - Table size: 30
UnifiedChessBot statistics:
  - Time: 10.66s
  - Nodes evaluated: 2576
  - Quiescence nodes: 4449
  - Transposition hits: 0
  - Table size: 259
ok
test_search_speed (__main__.TestUnifiedChessBotPerformance.test_search_speed)
Test that bot can find moves in reasonable time. ... MediumBot statistics:
  - Time: 11.23s
  - Nodes evaluated: 3385
  - Quiescence nodes: 5994
  - Transposition hits: 0
  - Table size: 282
ok
test_transposition_table_efficiency (__main__.TestUnifiedChessBotPerformance.test_transposition_table_efficiency)
Test that transposition table improves efficiency. ... UnifiedChessBot statistics:
  - Time: 0.42s
  - Nodes evaluated: 273
  - Quiescence nodes: 305
UnifiedChessBot statistics:
  - Time: 0.41s
  - Nodes evaluated: 300
  - Quiescence nodes: 332
  - Transposition hits: 0
  - Table size: 30
ok
test_avoid_blunders (__main__.TestUnifiedChessBotTactical.test_avoid_blunders)
Test that bot doesn't hang pieces unnecessarily. ... HardBot statistics:
  - Time: 15.58s
  - Nodes evaluated: 2901
  - Quiescence nodes: 9420
  - Transposition hits: 0
  - Table size: 209
HardBot statistics:
  - Time: 156.68s
  - Nodes evaluated: 30519
  - Quiescence nodes: 110669
  - Transposition hits: 318
  - Table size: 3396
ok
test_checkmate_in_one (__main__.TestUnifiedChessBotTactical.test_checkmate_in_one)
Test that bot finds checkmate in one move. ... HardBot statistics:
  - Time: 16.12s
  - Nodes evaluated: 3183
  - Quiescence nodes: 10948
  - Transposition hits: 0
  - Table size: 217
ok
test_simple_capture (__main__.TestUnifiedChessBotTactical.test_simple_capture)
Test that bot captures undefended pieces. ... HardBot statistics:
  - Time: 10.59s
  - Nodes evaluated: 2635
  - Quiescence nodes: 9251
  - Transposition hits: 0
  - Table size: 325
ok

----------------------------------------------------------------------
Ran 27 tests in 274.775s

OK
\nTest Summary:
Tests run: 27
Failures: 0
Errors: 0
Success rate: 100.0%
\n============================================================
UNIFIED CHESS BOT - PERFORMANCE BENCHMARK
============================================================
\nTesting EasyBot:
------------------------------
EasyBot statistics:
  - Time: 0.12s
  - Nodes evaluated: 171
  - Quiescence nodes: 0
Time: 0.12 seconds
Nodes evaluated: 171
Quiescence nodes: 0
Nodes per second: 1,378
Selected move: e2e4
\nTesting MediumBot:
------------------------------
MediumBot statistics:
  - Time: 2.04s
  - Nodes evaluated: 1301
  - Quiescence nodes: 1488
  - Transposition hits: 0
  - Table size: 98
Time: 2.04 seconds
Nodes evaluated: 1,301
Quiescence nodes: 1,488
Transposition hits: 0
Transposition table size: 98
Nodes per second: 639
Selected move: d2d3
\nTesting HardBot:
------------------------------
HardBot statistics:
  - Time: 8.57s
  - Nodes evaluated: 8804
  - Quiescence nodes: 8356
  - Transposition hits: 173
  - Table size: 1243
Time: 8.58 seconds
Nodes evaluated: 8,804
Quiescence nodes: 8,356
Transposition hits: 173
Transposition table size: 1,243
Nodes per second: 1,027
Selected move: g1f3
\nTesting ExpertBot:
------------------------------
ExpertBot statistics:
  - Time: 141.96s
  - Nodes evaluated: 79678
  - Quiescence nodes: 126474
  - Transposition hits: 916
  - Table size: 7455
Time: 141.96 seconds
Nodes evaluated: 79,678
Quiescence nodes: 126,474
Transposition hits: 916
Transposition table size: 7,455
Nodes per second: 561
Selected move: c2c4
\n============================================================
\n============================================================
UNIFIED CHESS BOT - TACTICAL TEST SUITE
============================================================
\nTest 1: Open game position
FEN: rnbqkb1r/pppp1ppp/5n2/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3
HardBot statistics:
  - Time: 51.61s
  - Nodes evaluated: 15238
  - Quiescence nodes: 54268
  - Transposition hits: 206
  - Table size: 2378
Time: 51.61 seconds
Evaluation: 0.00
Best move: d1e2
Move in range: True
Position after move: rnbqkb1r/pppp1ppp/5n2/4p3/4P3/5N2/PPPPQPPP/RNB1KB1R b KQkq - 3 3
\nTest 2: Symmetric position
FEN: r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3
HardBot statistics:
  - Time: 13.35s
  - Nodes evaluated: 5493
  - Quiescence nodes: 11391
  - Transposition hits: 81
  - Table size: 2826
Time: 13.40 seconds
Evaluation: -0.12
Best move: e1e2
Move in range: True
Position after move: r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPPKPPP/RNBQ1B1R b kq - 3 3
\nTest 3: Italian Game
FEN: rnbqk2r/pppp1ppp/5n2/2b1p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4
HardBot statistics:
  - Time: 10.97s
  - Nodes evaluated: 1947
  - Quiescence nodes: 9329
  - Transposition hits: 9
  - Table size: 2981
Time: 10.97 seconds
Evaluation: 0.00
Best move: f3h4
Move in range: True
Position after move: rnbqk2r/pppp1ppp/5n2/2b1p3/2B1P2N/8/PPPP1PPP/RNBQK2R b KQkq - 5 4
\n============================================================
\nAll tests completed!






==============================================



PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_unified_chess_bot.py
Running Unified Chess Bot Unit Tests...
============================================================
test_board_evaluation_checkmate (__main__.TestUnifiedChessBotBasic.test_board_evaluation_checkmate)
Test board evaluation in checkmate position. ... ok
test_board_evaluation_stalemate (__main__.TestUnifiedChessBotBasic.test_board_evaluation_stalemate)
Test board evaluation in stalemate position. ... ok
test_board_evaluation_starting_position (__main__.TestUnifiedChessBotBasic.test_board_evaluation_starting_position)
Test board evaluation at starting position. ... ok
test_bot_initialization (__main__.TestUnifiedChessBotBasic.test_bot_initialization)
Test bot initialization with default parameters. ... ok
test_bot_initialization_custom (__main__.TestUnifiedChessBotBasic.test_bot_initialization_custom)     
Test bot initialization with custom parameters. ... ok
test_get_best_move_no_legal_moves (__main__.TestUnifiedChessBotBasic.test_get_best_move_no_legal_moves)
Test get_best_move when no legal moves are available. ... TestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 2
  - Quiescence nodes: 0
  - Search evaluation: 0
  - Transposition hits: 0
  - Table size: 0
ok
test_get_best_move_returns_legal_move (__main__.TestUnifiedChessBotBasic.test_get_best_move_returns_legal_move)
Test that get_best_move returns a legal move. ... TestBot statistics:
  - Time: 1.01s
  - Nodes evaluated: 109
  - Quiescence nodes: 93
  - Search evaluation: 0
  - Transposition hits: 0
  - Table size: 21
ok
test_piece_values (__main__.TestUnifiedChessBotBasic.test_piece_values)
Test that piece values are reasonable. ... ok
test_set_depth (__main__.TestUnifiedChessBotBasic.test_set_depth)
Test setting bot depth. ... TestBot depth set to 5
ok
test_set_features (__main__.TestUnifiedChessBotBasic.test_set_features)
Test enabling/disabling bot features. ... TestBot features updated:
  - Iterative deepening: False
  - Quiescence search: False
  - Transposition table: False
TestBot features updated:
  - Iterative deepening: True
  - Quiescence search: True
  - Transposition table: True
ok
test_endgame_detection (__main__.TestUnifiedChessBotAdvanced.test_endgame_detection)
Test endgame detection. ... ok
test_iterative_deepening (__main__.TestUnifiedChessBotAdvanced.test_iterative_deepening)
Test iterative deepening functionality. ... AdvancedTestBot statistics:
  - Time: 5.91s
  - Nodes evaluated: 2069
  - Quiescence nodes: 2853
  - Search evaluation: 44
  - Transposition hits: 0
  - Table size: 156
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 1
  - Quiescence nodes: 0
  - Search evaluation: 44
  - Transposition hits: 1
  - Table size: 156
ok
test_move_ordering (__main__.TestUnifiedChessBotAdvanced.test_move_ordering)
Test move ordering for alpha-beta pruning efficiency. ... ok
test_opening_book (__main__.TestUnifiedChessBotAdvanced.test_opening_book)
Test opening book functionality. ... AdvancedTestBot played from opening book: d2d4
ok
test_quiescence_search (__main__.TestUnifiedChessBotAdvanced.test_quiescence_search)
Test quiescence search functionality. ... AdvancedTestBot statistics:
  - Time: 4.92s
  - Nodes evaluated: 1075
  - Quiescence nodes: 2929
  - Search evaluation: 61
  - Transposition hits: 0
  - Table size: 82
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 3
  - Quiescence nodes: 0
  - Search evaluation: 61
  - Transposition hits: 3
  - Table size: 82
ok
test_transposition_table (__main__.TestUnifiedChessBotAdvanced.test_transposition_table)
Test transposition table functionality. ... AdvancedTestBot statistics:
  - Time: 3.98s
  - Nodes evaluated: 2069
  - Quiescence nodes: 2853
  - Search evaluation: 44
  - Transposition hits: 0
  - Table size: 156
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 3
  - Quiescence nodes: 0
  - Search evaluation: 44
  - Transposition hits: 3
  - Table size: 156
ok
test_all_presets_work (__main__.TestUnifiedChessBotPresets.test_all_presets_work)
Test that all preset bots can find moves. ... EasyBot played from opening book: g1f3
MediumBot played from opening book: d2d4
HardBot played from opening book: c2c4
ExpertBot played from opening book: g1f3
ok
test_easy_bot (__main__.TestUnifiedChessBotPresets.test_easy_bot)
Test EasyBot preset. ... ok
test_expert_bot (__main__.TestUnifiedChessBotPresets.test_expert_bot)
Test ExpertBot preset. ... ok
test_hard_bot (__main__.TestUnifiedChessBotPresets.test_hard_bot)
Test HardBot preset. ... ok
test_medium_bot (__main__.TestUnifiedChessBotPresets.test_medium_bot)
Test MediumBot preset. ... ok
test_depth_scaling (__main__.TestUnifiedChessBotPerformance.test_depth_scaling)
Test that higher depth takes longer but evaluates more nodes. ... UnifiedChessBot statistics:
  - Time: 0.52s
  - Nodes evaluated: 194
  - Quiescence nodes: 314
  - Search evaluation: 0
  - Transposition hits: 0
  - Table size: 30
UnifiedChessBot statistics:
  - Time: 5.69s
  - Nodes evaluated: 2642
  - Quiescence nodes: 3612
  - Search evaluation: 44
  - Transposition hits: 0
  - Table size: 188
ok
test_search_speed (__main__.TestUnifiedChessBotPerformance.test_search_speed)
Test that bot can find moves in reasonable time. ... MediumBot statistics:
  - Time: 4.63s
  - Nodes evaluated: 2069
  - Quiescence nodes: 2853
  - Search evaluation: 44
  - Transposition hits: 0
  - Table size: 156
ok
test_transposition_table_efficiency (__main__.TestUnifiedChessBotPerformance.test_transposition_table_efficiency)
Test that transposition table improves efficiency. ... UnifiedChessBot statistics:
  - Time: 0.63s
  - Nodes evaluated: 224
  - Quiescence nodes: 348
  - Search evaluation: 0
UnifiedChessBot statistics:
  - Time: 0.50s
  - Nodes evaluated: 141
  - Quiescence nodes: 230
  - Search evaluation: 0
  - Transposition hits: 0
  - Table size: 30
ok
test_avoid_blunders (__main__.TestUnifiedChessBotTactical.test_avoid_blunders)
Test that bot doesn't hang pieces unnecessarily. ... HardBot statistics:
  - Time: 31.05s
  - Nodes evaluated: 6834
  - Quiescence nodes: 17193
  - Search evaluation: 30
  - Transposition hits: 94
  - Table size: 1180
HardBot statistics:
  - Time: 33.31s
  - Nodes evaluated: 4260
  - Quiescence nodes: 11664
  - Search evaluation: 30
  - Transposition hits: 128
  - Table size: 2053
ok
test_checkmate_in_one (__main__.TestUnifiedChessBotTactical.test_checkmate_in_one)
Test that bot finds checkmate in one move. ... HardBot statistics:
  - Time: 24.16s
  - Nodes evaluated: 6805
  - Quiescence nodes: 18197
  - Search evaluation: 28
  - Transposition hits: 104
  - Table size: 1128
ok
test_simple_capture (__main__.TestUnifiedChessBotTactical.test_simple_capture)
Test that bot captures undefended pieces. ... HardBot statistics:
  - Time: 20.97s
  - Nodes evaluated: 4451
  - Quiescence nodes: 7116
  - Search evaluation: 0
  - Transposition hits: 21
  - Table size: 623
ok

----------------------------------------------------------------------
Ran 27 tests in 141.346s

OK
\nTest Summary:
Tests run: 27
Failures: 0
Errors: 0
Success rate: 100.0%
\n============================================================
UNIFIED CHESS BOT - PERFORMANCE BENCHMARK
============================================================
\nTesting EasyBot:
------------------------------
EasyBot statistics:
  - Time: 0.16s
  - Nodes evaluated: 171
  - Quiescence nodes: 0
  - Search evaluation: 0
Time: 0.16 seconds
Nodes evaluated: 171
Quiescence nodes: 0
Nodes per second: 1,061
Selected move: e2e4
\nTesting MediumBot:
------------------------------
MediumBot statistics:
  - Time: 1.39s
  - Nodes evaluated: 1093
  - Quiescence nodes: 1011
  - Search evaluation: 56
  - Transposition hits: 0
  - Table size: 112
Time: 1.40 seconds
Nodes evaluated: 1,093
Quiescence nodes: 1,011
Transposition hits: 0
Transposition table size: 112
Nodes per second: 783
Selected move: e2e4
\nTesting HardBot:
------------------------------
HardBot statistics:
  - Time: 5.66s
  - Nodes evaluated: 3847
  - Quiescence nodes: 3933
  - Search evaluation: 3
  - Transposition hits: 114
  - Table size: 626
Time: 5.66 seconds
Nodes evaluated: 3,847
Quiescence nodes: 3,933
Transposition hits: 114
Transposition table size: 626
Nodes per second: 680
Selected move: d2d4
\nTesting ExpertBot:
------------------------------
ExpertBot statistics:
  - Time: 31.00s
  - Nodes evaluated: 22212
  - Quiescence nodes: 26471
  - Search evaluation: 47
  - Transposition hits: 369
  - Table size: 2066
Time: 31.00 seconds
Nodes evaluated: 22,212
Quiescence nodes: 26,471
Transposition hits: 369
Transposition table size: 2,066
Nodes per second: 717
Selected move: d2d4
\n============================================================
\n============================================================
UNIFIED CHESS BOT - TACTICAL TEST SUITE
============================================================
\nTest 1: Open game position
FEN: rnbqkb1r/pppp1ppp/5n2/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3
HardBot statistics:
  - Time: 24.27s
  - Nodes evaluated: 6834
  - Quiescence nodes: 17193
  - Search evaluation: 30
  - Transposition hits: 94
  - Table size: 1180
Time: 24.27 seconds
Evaluation: 0.00
Best move: f3e5
Move in range: True
Position after move: rnbqkb1r/pppp1ppp/5n2/4N3/4P3/8/PPPP1PPP/RNBQKB1R b KQkq - 0 3
\nTest 2: Symmetric position
FEN: r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3
HardBot statistics:
  - Time: 17.27s
  - Nodes evaluated: 7467
  - Quiescence nodes: 11258
  - Search evaluation: 11
  - Transposition hits: 222
  - Table size: 2233
Time: 17.27 seconds
Evaluation: -0.12
Best move: b1c3
Move in range: True
Position after move: r1bqkbnr/pppp1ppp/2n5/4p3/4P3/2N2N2/PPPP1PPP/R1BQKB1R b KQkq - 3 3
\nTest 3: Italian Game
FEN: rnbqk2r/pppp1ppp/5n2/2b1p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4
HardBot statistics:
  - Time: 11.08s
  - Nodes evaluated: 2908
  - Quiescence nodes: 7636
  - Search evaluation: 105
  - Transposition hits: 14
Evaluation: 0.00
Best move: f3e5
Move in range: True
Position after move: rnbqk2r/pppp1ppp/5n2/2b1N3/2B1P3/8/PPPP1PPP/RNBQK2R b KQkq - 0 4
\n============================================================
\nAll tests completed!













=============================================================
=============================================================
=============================================================

new pull request - hanging piece problem:


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_unified_chess_bot.py
Running Unified Chess Bot Unit Tests...
============================================================
test_board_evaluation_checkmate (__main__.TestUnifiedChessBotBasic.test_board_evaluation_checkmate)
Test board evaluation in checkmate position. ... ok
test_board_evaluation_stalemate (__main__.TestUnifiedChessBotBasic.test_board_evaluation_stalemate)
Test board evaluation in stalemate position. ... ok
test_board_evaluation_starting_position (__main__.TestUnifiedChessBotBasic.test_board_evaluation_starting_position)
Test board evaluation at starting position. ... ok
test_bot_initialization (__main__.TestUnifiedChessBotBasic.test_bot_initialization)
Test bot initialization with default parameters. ... ok
test_bot_initialization_custom (__main__.TestUnifiedChessBotBasic.test_bot_initialization_custom)     
Test bot initialization with custom parameters. ... ok
test_get_best_move_no_legal_moves (__main__.TestUnifiedChessBotBasic.test_get_best_move_no_legal_moves)
Test get_best_move when no legal moves are available. ... TestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 2
  - Quiescence nodes: 0
  - Transposition hits: 0
  - Table size: 0
ok
test_get_best_move_returns_legal_move (__main__.TestUnifiedChessBotBasic.test_get_best_move_returns_legal_move)
Test that get_best_move returns a legal move. ... TestBot statistics:
  - Time: 0.14s
  - Nodes evaluated: 101
  - Quiescence nodes: 86
  - Transposition hits: 0
  - Table size: 21
ok
test_piece_values (__main__.TestUnifiedChessBotBasic.test_piece_values)
Test that piece values are reasonable. ... ok
test_set_depth (__main__.TestUnifiedChessBotBasic.test_set_depth)
Test setting bot depth. ... TestBot depth set to 5
ok
test_set_features (__main__.TestUnifiedChessBotBasic.test_set_features)
Test enabling/disabling bot features. ... TestBot features updated:
  - Iterative deepening: False
  - Quiescence search: False
  - Transposition table: False
TestBot features updated:
  - Iterative deepening: True
  - Quiescence search: True
  - Transposition table: True
ok
test_endgame_detection (__main__.TestUnifiedChessBotAdvanced.test_endgame_detection)
Test endgame detection. ... ok
test_iterative_deepening (__main__.TestUnifiedChessBotAdvanced.test_iterative_deepening)
Test iterative deepening functionality. ... AdvancedTestBot statistics:
  - Time: 8.28s
  - Nodes evaluated: 2708
  - Quiescence nodes: 5264
  - Transposition hits: 0
  - Table size: 215
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 1
  - Quiescence nodes: 0
  - Transposition hits: 1
  - Table size: 215
ok
test_move_ordering (__main__.TestUnifiedChessBotAdvanced.test_move_ordering)
Test move ordering for alpha-beta pruning efficiency. ... ok
test_opening_book (__main__.TestUnifiedChessBotAdvanced.test_opening_book)
Test opening book functionality. ... AdvancedTestBot played from opening book: g1f3
ok
test_quiescence_search (__main__.TestUnifiedChessBotAdvanced.test_quiescence_search)
Test quiescence search functionality. ... AdvancedTestBot statistics:
  - Time: 14.90s
  - Nodes evaluated: 2990
  - Quiescence nodes: 10443
  - Transposition hits: 0
  - Table size: 198
AdvancedTestBot statistics:
  - Time: 0.02s
  - Nodes evaluated: 3
  - Quiescence nodes: 0
  - Transposition hits: 3
  - Table size: 198
ok
test_transposition_table (__main__.TestUnifiedChessBotAdvanced.test_transposition_table)
Test transposition table functionality. ... AdvancedTestBot statistics:
  - Time: 10.79s
  - Nodes evaluated: 2708
  - Quiescence nodes: 5264
  - Transposition hits: 0
  - Table size: 215
AdvancedTestBot statistics:
  - Time: 0.00s
  - Nodes evaluated: 3
  - Quiescence nodes: 0
  - Transposition hits: 3
  - Table size: 215
ok
test_all_presets_work (__main__.TestUnifiedChessBotPresets.test_all_presets_work)
Test that all preset bots can find moves. ... EasyBot played from opening book: g1f3
MediumBot played from opening book: c2c4
HardBot played from opening book: d2d4
ExpertBot played from opening book: c2c4
ok
test_easy_bot (__main__.TestUnifiedChessBotPresets.test_easy_bot)
Test EasyBot preset. ... ok
test_expert_bot (__main__.TestUnifiedChessBotPresets.test_expert_bot)
Test ExpertBot preset. ... ok
test_hard_bot (__main__.TestUnifiedChessBotPresets.test_hard_bot)
Test HardBot preset. ... ok
test_medium_bot (__main__.TestUnifiedChessBotPresets.test_medium_bot)
Test MediumBot preset. ... ok
test_depth_scaling (__main__.TestUnifiedChessBotPerformance.test_depth_scaling)
Test that higher depth takes longer but evaluates more nodes. ... UnifiedChessBot statistics:
  - Time: 0.84s
  - Nodes evaluated: 170
  - Quiescence nodes: 380
  - Transposition hits: 0
  - Table size: 30
UnifiedChessBot statistics:
  - Time: 9.38s
  - Nodes evaluated: 2330
  - Quiescence nodes: 4876
  - Transposition hits: 0
  - Table size: 206
ok
test_search_speed (__main__.TestUnifiedChessBotPerformance.test_search_speed)
Test that bot can find moves in reasonable time. ... MediumBot statistics:
  - Time: 9.23s
  - Nodes evaluated: 2708
  - Quiescence nodes: 5264
  - Transposition hits: 0
  - Table size: 215
ok
test_transposition_table_efficiency (__main__.TestUnifiedChessBotPerformance.test_transposition_table_efficiency)
Test that transposition table improves efficiency. ... UnifiedChessBot statistics:
  - Time: 0.56s
  - Nodes evaluated: 200
  - Quiescence nodes: 421
UnifiedChessBot statistics:
  - Time: 0.65s
  - Nodes evaluated: 227
  - Quiescence nodes: 480
  - Transposition hits: 0
  - Table size: 30
ok
test_avoid_blunders (__main__.TestUnifiedChessBotTactical.test_avoid_blunders)
Test that bot doesn't hang pieces unnecessarily. ... HardBot statistics:
  - Time: 13.11s
  - Nodes evaluated: 2990
  - Quiescence nodes: 10443
  - Transposition hits: 0
  - Table size: 198
HardBot statistics:
  - Time: 663.27s
  - Nodes evaluated: 73807
  - Quiescence nodes: 327926
  - Transposition hits: 820
  - Table size: 6275
ok
test_checkmate_in_one (__main__.TestUnifiedChessBotTactical.test_checkmate_in_one)
Test that bot finds checkmate in one move. ... HardBot statistics:
  - Time: 14.60s
  - Nodes evaluated: 2500
  - Quiescence nodes: 9148
  - Transposition hits: 0
  - Table size: 165
FAIL
test_simple_capture (__main__.TestUnifiedChessBotTactical.test_simple_capture)
Test that bot captures undefended pieces. ... HardBot statistics:
  - Time: 33.60s
  - Nodes evaluated: 3261
  - Quiescence nodes: 10859
  - Transposition hits: 0
  - Table size: 375
ok

======================================================================
FAIL: test_checkmate_in_one (__main__.TestUnifiedChessBotTactical.test_checkmate_in_one)
Test that bot finds checkmate in one move.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test\test_unified_chess_bot.py", line 498, in test_checkmate_in_one
    self.assertGreater(eval_score, 10)  # Any positive advantage
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: -918 not greater than 10

----------------------------------------------------------------------
Ran 27 tests in 779.448s

FAILED (failures=1)
\nTest Summary:
Tests run: 27
Failures: 1
Errors: 0
Success rate: 96.3%
\n============================================================
UNIFIED CHESS BOT - PERFORMANCE BENCHMARK
============================================================
\nTesting EasyBot:
------------------------------
EasyBot statistics:
  - Time: 0.11s
  - Nodes evaluated: 80
  - Quiescence nodes: 0
Time: 0.11 seconds
Nodes evaluated: 80
Quiescence nodes: 0
Nodes per second: 753
Selected move: d2d4
\nTesting MediumBot:
------------------------------
MediumBot statistics:
  - Time: 5.61s
  - Nodes evaluated: 2320
  - Quiescence nodes: 2871
  - Transposition hits: 0
  - Table size: 153
Time: 5.62 seconds
Nodes evaluated: 2,320
Quiescence nodes: 2,871
Transposition hits: 0
Transposition table size: 153
Nodes per second: 413
Selected move: d2d4
\nTesting HardBot:
------------------------------
HardBot statistics:
  - Time: 31.14s
  - Nodes evaluated: 7670
  - Quiescence nodes: 8978
  - Transposition hits: 111
  - Table size: 1184
Time: 31.14 seconds
Nodes evaluated: 7,670
Quiescence nodes: 8,978
Transposition hits: 111
Transposition table size: 1,184
Nodes per second: 246
Selected move: d2d4
\nTesting ExpertBot:
------------------------------
ExpertBot statistics:
  - Time: 17.26s
  - Nodes evaluated: 7670
  - Quiescence nodes: 9198
  - Transposition hits: 111
  - Table size: 1184
Time: 17.26 seconds
Nodes evaluated: 7,670
Quiescence nodes: 9,198
Transposition hits: 111
Transposition table size: 1,184
Nodes per second: 444
Selected move: d2d4
\n============================================================
\n============================================================
UNIFIED CHESS BOT - TACTICAL TEST SUITE
============================================================
\nTest 1: Open game position
FEN: rnbqkb1r/pppp1ppp/5n2/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3
HardBot statistics:
  - Time: 16.55s
  - Nodes evaluated: 2990
  - Quiescence nodes: 10443
  - Transposition hits: 0
  - Table size: 198
Time: 16.55 seconds
Evaluation: 0.25
Best move: f3d4
Move in range: True
Position after move: rnbqkb1r/pppp1ppp/5n2/4p3/3NP3/8/PPPP1PPP/RNBQKB1R b KQkq - 3 3
\nTest 2: Symmetric position
FEN: r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3
HardBot statistics:
  - Time: 15.96s
  - Nodes evaluated: 3333
  - Quiescence nodes: 9745
  - Transposition hits: 17
  - Table size: 410
Time: 15.96 seconds
Evaluation: 0.13
Best move: f1a6
Move in range: True
Position after move: r1bqkbnr/pppp1ppp/B1n5/4p3/4P3/5N2/PPPP1PPP/RNBQK2R b KQkq - 3 3
\nTest 3: Italian Game
FEN: rnbqk2r/pppp1ppp/5n2/2b1p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4
HardBot statistics:
  - Time: 30.85s
  - Nodes evaluated: 4491
  - Quiescence nodes: 18700
  - Transposition hits: 0
  - Table size: 648
Time: 30.85 seconds
Best move: c4f7
Move in range: True
Position after move: rnbqk2r/pppp1Bpp/5n2/2b1p3/4P3/5N2/PPPP1PPP/RNBQK2R b KQkq - 0 4
\n============================================================
\nAll tests completed!