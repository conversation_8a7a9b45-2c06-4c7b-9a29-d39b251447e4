#!/usr/bin/env python3
"""
Debug the move ordering to see why knight moves aren't prioritized
"""

import chess
from unified_chess_bot import MediumBot

def debug_move_ordering():
    print("Debugging Move Ordering")
    print("="*50)
    
    # Create the problematic position
    board = chess.Board()
    moves = ["Nc3", "d5", "Nf3", "Nc6", "d4", "e6", "Ne5", "Nb4", "a3"]
    for move in moves:
        board.push_san(move)
    
    print("Position:")
    print(board)
    print()
    
    bot = MediumBot()
    
    # Get all legal moves
    legal_moves = list(board.legal_moves)
    print(f"Total legal moves: {len(legal_moves)}")
    
    # Test the move ordering function
    ordered_moves = bot._order_moves(board, legal_moves, None)
    
    print("\nMove ordering analysis:")
    print("-" * 40)
    
    # Calculate scores for each move manually
    move_scores = []
    
    for move in legal_moves:
        score = 0
        move_san = board.san(move)
        
        # Check if this move escapes from an attack
        piece = board.piece_at(move.from_square)
        if piece:
            # Check if piece is currently under attack
            attackers = board.attackers(not piece.color, move.from_square)
            if attackers:
                score += 5000
                print(f"ESCAPE BONUS: {move_san} - piece on {chess.square_name(move.from_square)} is under attack!")
                
                # Extra bonus if escaping to a safe square
                board.push(move)
                new_attackers = board.attackers(not piece.color, move.to_square)
                if not new_attackers:
                    score += 1000
                    print(f"  SAFE ESCAPE: Moving to {chess.square_name(move.to_square)} is safe!")
                else:
                    print(f"  RISKY ESCAPE: Moving to {chess.square_name(move.to_square)} is still attacked!")
                board.pop()
        
        # Check captures
        if board.is_capture(move):
            victim = board.piece_at(move.to_square)
            attacker = board.piece_at(move.from_square)
            if victim and attacker:
                capture_score = bot.piece_values[victim.piece_type] * 10 - bot.piece_values[attacker.piece_type]
                score += capture_score
                print(f"CAPTURE: {move_san} - score {capture_score}")
        
        # Check for checks
        board.push(move)
        if board.is_check():
            score += 500
            print(f"CHECK: {move_san}")
        board.pop()
        
        # Center control
        if move.to_square in [chess.E4, chess.E5, chess.D4, chess.D5]:
            score += 50
        
        move_scores.append((move, move_san, score))
    
    # Sort by score
    move_scores.sort(key=lambda x: x[2], reverse=True)
    
    print(f"\nTop 10 moves by scoring:")
    print("-" * 40)
    for i, (move, san, score) in enumerate(move_scores[:10]):
        print(f"{i+1:2d}. {san:8s} - Score: {score:6.0f}")
        if move.from_square == chess.B4:
            print(f"    ^^^ KNIGHT MOVE! ^^^")
    
    # Check if ordered moves match our calculation
    print(f"\nActual ordered moves (first 5):")
    for i, move in enumerate(ordered_moves[:5]):
        san = board.san(move)
        # Find the score we calculated
        calculated_score = next((score for m, s, score in move_scores if m == move), 0)
        print(f"{i+1}. {san:8s} - Score: {calculated_score:6.0f}")
        if move.from_square == chess.B4:
            print(f"    ^^^ KNIGHT MOVE! ^^^")

if __name__ == "__main__":
    debug_move_ordering()