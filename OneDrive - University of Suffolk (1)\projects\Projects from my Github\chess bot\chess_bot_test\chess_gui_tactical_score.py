#!/usr/bin/env python3
"""
Chess Bot GUI - A desktop chess game with AI opponent using tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import chess
import threading
from chess_bot import ChessBot # chess_bot | chess_bot2 for the enhanced bot (stronger evaluation function) | chess_bot_tactical 
from utils import save_game_pgn, analyze_position, format_analysis, get_opening_name, get_move_quality_description
from tactical_evaluation_test import TacticalEvaluator, ThreatType, MoveQuality # tactical_evaluation_test | tactical_evaluation | tactical_evaluation0 | tactical_evaluation00 | tactical_evaluation1

class ChessGUI:
    """
    GUI Chess game using tkinter with drag-and-drop functionality.
    """
    
    def __init__(self):
        """Initialize the chess GUI."""
        self.root = tk.Tk()
        self.root.title("Chess Bot - GUI Version")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # Game state
        self.board = chess.Board()
        self.bot = ChessBot(depth=3, name="ChessBot") # can change from 3 to 5 to see if it helps (update: it's really slow)
        self.tactical_evaluator = TacticalEvaluator()  # New tactical evaluation system
        self.game_history = []
        self.move_quality_history = []  # Store move quality ratings
        self.human_color = chess.WHITE
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        self.bot_thinking = False  # Track if bot is currently thinking
        
        # Colors and styling
        self.light_square_color = "#F0D9B5"
        self.dark_square_color = "#B58863"
        self.highlight_color = "#FFFF00"
        self.selected_color = "#FF6B6B"
        self.legal_move_color = "#90EE90"
        
        # Unicode chess pieces
        self.piece_symbols = {
            'P': '♙', 'N': '♘', 'B': '♗', 'R': '♖', 'Q': '♕', 'K': '♔',  # White
            'p': '♟', 'n': '♞', 'b': '♝', 'r': '♜', 'q': '♛', 'k': '♚'   # Black
        }
        
        self.setup_ui()
        self.update_board_display()
        self.update_status()

        # Delay evaluation update to ensure GUI is fully rendered
        self.root.after(100, self.update_evaluation)
    
    def setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left panel for the chess board
        board_frame = ttk.Frame(main_frame)
        board_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        # Chess board canvas
        self.canvas = tk.Canvas(board_frame, width=480, height=480, bg="white")
        self.canvas.pack()
        self.canvas.bind("<Button-1>", self.on_square_click)
        
        # Right panel for controls and information
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Game controls
        controls_group = ttk.LabelFrame(control_frame, text="Game Controls", padding=10)
        controls_group.pack(fill=tk.X, pady=(0, 10))
        
        self.new_game_btn = ttk.Button(controls_group, text="New Game", command=self.new_game)
        self.new_game_btn.pack(fill=tk.X, pady=2)
        self.switch_colors_btn = ttk.Button(controls_group, text="Switch Colors", command=self.switch_colors)
        self.switch_colors_btn.pack(fill=tk.X, pady=2)
        self.save_game_btn = ttk.Button(controls_group, text="Save Game", command=self.save_game)
        self.save_game_btn.pack(fill=tk.X, pady=2)
        self.load_game_btn = ttk.Button(controls_group, text="Load Game", command=self.load_game)
        self.load_game_btn.pack(fill=tk.X, pady=2)
        
        # Bot difficulty
        difficulty_group = ttk.LabelFrame(control_frame, text="Bot Difficulty", padding=10)
        difficulty_group.pack(fill=tk.X, pady=(0, 10))
        
        self.difficulty_var = tk.StringVar(value="Medium")
        difficulties = [("Easy", 2), ("Medium", 3), ("Hard", 4), ("Expert", 5)]
        
        self.difficulty_buttons = []
        for name, depth in difficulties:
            btn = ttk.Radiobutton(difficulty_group, text=f"{name} (depth {depth})", 
                           variable=self.difficulty_var, value=name,
                           command=lambda d=depth: self.set_difficulty(d))
            btn.pack(anchor=tk.W)
            self.difficulty_buttons.append(btn)
        
        # Game status
        status_group = ttk.LabelFrame(control_frame, text="Game Status", padding=10)
        status_group.pack(fill=tk.X, pady=(0, 10))

        self.status_label = ttk.Label(status_group, text="White to move", font=("Arial", 12))
        self.status_label.pack()

        self.opening_label = ttk.Label(status_group, text="Opening: Starting Position", font=("Arial", 10))
        self.opening_label.pack()

        # Position evaluation
        eval_frame = ttk.Frame(status_group)
        eval_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(eval_frame, text="Evaluation:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.eval_label = ttk.Label(eval_frame, text="0.00", font=("Arial", 10))
        self.eval_label.pack(side=tk.LEFT, padx=(5, 0))

        self.eval_bar_frame = ttk.Frame(status_group)
        self.eval_bar_frame.pack(fill=tk.X, pady=(2, 0))

        # Evaluation bar (visual representation)
        self.eval_canvas = tk.Canvas(self.eval_bar_frame, height=20, bg="white")
        self.eval_canvas.pack(fill=tk.X, expand=True)

        # Bind canvas resize event to redraw the evaluation bar
        self.eval_canvas.bind('<Configure>', self.on_canvas_resize)

        self.eval_description_label = ttk.Label(status_group, text="Equal position",
                                              font=("Arial", 9), foreground="gray")
        self.eval_description_label.pack()
        
        # Move history
        history_group = ttk.LabelFrame(control_frame, text="Move History", padding=10)
        history_group.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Scrollable text widget for move history
        history_frame = ttk.Frame(history_group)
        history_frame.pack(fill=tk.BOTH, expand=True)
        
        self.history_text = tk.Text(history_frame, height=8, width=25, wrap=tk.WORD)
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_text.yview)
        self.history_text.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Analysis and hint buttons
        analysis_frame = ttk.Frame(control_frame)
        analysis_frame.pack(fill=tk.X, pady=(5, 0))

        self.analyze_btn = ttk.Button(analysis_frame, text="Analyze Position", command=self.show_analysis)
        self.analyze_btn.pack(fill=tk.X, pady=2)
        self.hint_btn = ttk.Button(analysis_frame, text="Show Hint", command=self.show_hint)
        self.hint_btn.pack(fill=tk.X, pady=2)
        self.tactical_threats_btn = ttk.Button(analysis_frame, text="Show Tactical Threats", command=self.show_tactical_threats)
        self.tactical_threats_btn.pack(fill=tk.X, pady=2)
        self.move_quality_btn = ttk.Button(analysis_frame, text="Show Move Quality History", command=self.show_move_quality_history)
        self.move_quality_btn.pack(fill=tk.X, pady=2)
        self.play_from_history_btn = ttk.Button(analysis_frame, text="Play from History", command=self.play_from_history)
        self.play_from_history_btn.pack(fill=tk.X, pady=2)
    
    def draw_board(self):
        """Draw the chess board."""
        self.canvas.delete("all")
        square_size = 60
        
        for row in range(8):
            for col in range(8):
                x1 = col * square_size
                y1 = row * square_size
                x2 = x1 + square_size
                y2 = y1 + square_size
                
                # Determine square color
                is_light = (row + col) % 2 == 0
                square = chess.square(col, 7 - row)
                
                # Choose color based on square state
                if square == self.selected_square:
                    color = self.selected_color
                elif square in self.highlighted_squares:
                    color = self.legal_move_color
                else:
                    color = self.light_square_color if is_light else self.dark_square_color
                
                # Draw square
                self.canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline="black")
                
                # Add coordinates
                if col == 0:  # Rank labels
                    self.canvas.create_text(x1 + 5, y1 + 10, text=str(8 - row), 
                                          font=("Arial", 8), fill="black")
                if row == 7:  # File labels
                    self.canvas.create_text(x2 - 10, y2 - 5, text=chr(ord('a') + col), 
                                          font=("Arial", 8), fill="black")
    
    def draw_pieces(self):
        """Draw the chess pieces on the board."""
        square_size = 60
        
        for row in range(8):
            for col in range(8):
                square = chess.square(col, 7 - row)
                piece = self.board.piece_at(square)
                
                if piece:
                    x = col * square_size + square_size // 2
                    y = row * square_size + square_size // 2
                    
                    piece_symbol = self.piece_symbols.get(piece.symbol(), piece.symbol())
                    self.canvas.create_text(x, y, text=piece_symbol, 
                                          font=("Arial", 36), fill="black")
    
    def update_board_display(self):
        """Update the visual board display."""
        self.draw_board()
        self.draw_pieces()
    
    def on_square_click(self, event):
        """Handle mouse clicks on the board."""
        if self.game_over or self.board.turn != self.human_color or self.bot_thinking:
            return
        
        square_size = 60
        col = event.x // square_size
        row = event.y // square_size
        
        if 0 <= col < 8 and 0 <= row < 8:
            clicked_square = chess.square(col, 7 - row)
            
            if self.selected_square is None:
                # Select a piece
                piece = self.board.piece_at(clicked_square)
                if piece and piece.color == self.human_color:
                    self.selected_square = clicked_square
                    self.highlight_legal_moves(clicked_square)
                    self.update_board_display()
            else:
                # Try to make a move
                move = chess.Move(self.selected_square, clicked_square)
                
                # Check for promotion
                piece = self.board.piece_at(self.selected_square)
                if (piece and piece.piece_type == chess.PAWN and 
                    ((piece.color == chess.WHITE and chess.square_rank(clicked_square) == 7) or
                     (piece.color == chess.BLACK and chess.square_rank(clicked_square) == 0))):
                    # Default to queen promotion for simplicity
                    move = chess.Move(self.selected_square, clicked_square, promotion=chess.QUEEN)
                
                if move in self.board.legal_moves:
                    self.make_move(move)
                
                # Clear selection
                self.selected_square = None
                self.highlighted_squares = []
                self.update_board_display()
    
    def highlight_legal_moves(self, square):
        """Highlight legal moves for the selected piece."""
        self.highlighted_squares = []
        for move in self.board.legal_moves:
            if move.from_square == square:
                self.highlighted_squares.append(move.to_square)
    
    def disable_ui(self):
        """Disable all UI controls while bot is thinking."""
        # Disable all game control buttons
        self.new_game_btn.config(state='disabled')
        self.switch_colors_btn.config(state='disabled')
        self.save_game_btn.config(state='disabled')
        self.load_game_btn.config(state='disabled')
        
        # Disable difficulty selection buttons
        for btn in self.difficulty_buttons:
            btn.config(state='disabled')
        
        # Disable analysis buttons
        self.analyze_btn.config(state='disabled')
        self.hint_btn.config(state='disabled')
        self.tactical_threats_btn.config(state='disabled')
        self.move_quality_btn.config(state='disabled')
        self.play_from_history_btn.config(state='disabled')
        
        # Change cursor to indicate waiting
        self.root.config(cursor="wait")
        self.canvas.config(cursor="wait")
    
    def enable_ui(self):
        """Re-enable all UI controls after bot finishes thinking."""
        # Re-enable all game control buttons
        self.new_game_btn.config(state='normal')
        self.switch_colors_btn.config(state='normal')
        self.save_game_btn.config(state='normal')
        self.load_game_btn.config(state='normal')
        
        # Re-enable difficulty selection buttons
        for btn in self.difficulty_buttons:
            btn.config(state='normal')
        
        # Re-enable analysis buttons
        self.analyze_btn.config(state='normal')
        self.hint_btn.config(state='normal')
        self.tactical_threats_btn.config(state='normal')
        self.move_quality_btn.config(state='normal')
        self.play_from_history_btn.config(state='normal')
        
        # Reset cursor to normal
        self.root.config(cursor="")
        self.canvas.config(cursor="")
    
    def make_move(self, move):
        """Make a move on the board."""
        try:
            # Get evaluation before move for comparison (using search for more accuracy) - note: can make depth higher but this slows performance (lagging)
            eval_score_before, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn) # can change depth to 3 or 4
            
            # Normalize evaluation relative to starting position
            if not hasattr(self, 'starting_eval'):
                self.starting_eval = eval_score_before
            
            eval_before = eval_score_before - self.starting_eval

            san_move = self.board.san(move)
            board_before = self.board.copy()  # Save board state before move
            self.board.push(move)
            self.game_history.append(san_move)

            # Get evaluation after move (using search for more accuracy) - note: can make depth higher but this slows performance (lagging)
            eval_score_after, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn) # can change to depth 3 or 4
            eval_after = eval_score_after - self.starting_eval
            eval_change = eval_after - eval_before

            # Debug print to understand evaluation changes
            print(f"Move: {san_move}, Eval before: {eval_before}, Eval after: {eval_after}, Change: {eval_change}")

            # Update displays
            self.update_history_display()
            self.update_status()
            self.update_evaluation()
            self.update_board_display()

            # Show evaluation change for moves
            change_text = f"{eval_change/100.0:+.2f}"

            # Use tactical evaluation system for move quality
            current_player = "White" if not self.board.turn else "Black"  # Player who just moved
            move_number = len(self.game_history)

            # Use tactical evaluator to determine move quality
            move_quality_enum, explanation = self.tactical_evaluator.evaluate_move_quality(
                board_before, move, eval_before, eval_after, move_number
            )
            move_quality = move_quality_enum.value

            # Store move quality information
            move_info = {
                'move': san_move,
                'player': current_player,
                'eval_before': eval_before / 100.0,
                'eval_after': eval_after / 100.0,
                'eval_change': eval_change / 100.0,
                'quality': move_quality,
                'explanation': explanation,
                'move_number': move_number
            }
            self.move_quality_history.append(move_info)

            # Always show move evaluation for human moves
            if self.board.turn != self.human_color:  # Human just moved (turn switched to bot)
                # Update status temporarily to show move evaluation
                self.status_label.config(text=f"Move: {san_move} ({change_text}) - {move_quality}")
                self.root.after(3000, lambda: self.update_status() if not self.bot_thinking else None)  # Reset after 3 seconds, but only if bot isn't thinking

            # MODIFIED: Check for game over using the self.game_over flag
            if self.game_over:
                self.handle_game_over()
            elif self.board.turn != self.human_color:
                # Bot's turn
                self.root.after(500, self.make_bot_move)  # Small delay for better UX

        except Exception as e:
            messagebox.showerror("Error", f"Invalid move: {e}")
    
    def make_bot_move(self):
        """Make a move for the bot in a separate thread."""
        def bot_move_thread():
            try:
                move = self.bot.get_best_move(self.board)
                
                if move:
                    # Update UI in main thread
                    self.root.after(0, lambda: self.finish_bot_move(move))
                else:
                    self.root.after(0, lambda: self.finish_bot_move_error("Bot couldn't find a move!"))
            except Exception as e:
                self.root.after(0, lambda: self.finish_bot_move_error(f"Bot error: {e}"))
        
        # Set bot thinking state and disable UI
        self.bot_thinking = True
        self.disable_ui()
        self.update_status("Bot is thinking...")
        
        threading.Thread(target=bot_move_thread, daemon=True).start()
    
    def finish_bot_move(self, move):
        """Complete the bot move in the main thread."""
        self.bot_thinking = False
        self.enable_ui()
        self.make_move(move)
    
    def finish_bot_move_error(self, error_message):
        """Handle bot move errors in the main thread."""
        self.bot_thinking = False
        self.enable_ui()
        messagebox.showerror("Error", error_message)
        self.update_status()  # Reset status after error
    
    def update_status(self, custom_message=None):
        """Update the game status display."""
        if custom_message:
            self.status_label.config(text=custom_message)
            return
        
        # Don't update status if bot is thinking (unless it's a game over condition)
        if self.bot_thinking and not self.board.is_game_over():
            return
        
        if self.board.is_checkmate():
            winner = "Black" if self.board.turn == chess.WHITE else "White"
            self.status_label.config(text=f"Checkmate! {winner} wins!")
            self.game_over = True
        elif self.board.is_stalemate():
            self.status_label.config(text="Stalemate! Draw!")
            self.game_over = True
        elif self.board.is_insufficient_material():
            self.status_label.config(text="Insufficient material! Draw!")
            self.game_over = True
        # ADDED: Check for threefold repetition
        elif self.board.can_claim_threefold_repetition():
            self.status_label.config(text="Draw by threefold repetition!")
            self.game_over = True
        elif self.board.is_check():
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} in check!")
        else:
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} to move")
        
        # Update opening name
        if len(self.game_history) <= 8:
            opening = get_opening_name(self.board, self.game_history)
            self.opening_label.config(text=f"Opening: {opening}")

    def update_evaluation(self):
        """Update the position evaluation display."""
        try:
            # Get evaluation from the bot using the same method as in make_move (minimax with depth=2)
            raw_evaluation, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)

            # Normalize evaluation - subtract starting position bias
            # The starting position should be close to 0
            if len(self.game_history) == 0:
                # Store the starting position evaluation as baseline
                if not hasattr(self, 'starting_eval'):
                    self.starting_eval = raw_evaluation
                evaluation = 0  # Force starting position to be 0
            else:
                # Adjust evaluation relative to starting position
                if hasattr(self, 'starting_eval'):
                    evaluation = raw_evaluation - self.starting_eval
                else:
                    evaluation = raw_evaluation

            # Convert to centipawns and format
            eval_score = evaluation / 100.0

            # Format the score display
            if abs(evaluation) >= 9900:  # Very high evaluation indicates mate
                if evaluation > 0:
                    eval_text = "+M"  # Mate for white
                else:
                    eval_text = "-M"  # Mate for black
            else:
                eval_text = f"{eval_score:+.2f}"

            self.eval_label.config(text=eval_text)

            # Update evaluation description
            description = get_move_quality_description(evaluation)
            self.eval_description_label.config(text=description)

            # Update evaluation bar
            self.update_evaluation_bar(evaluation)

        except Exception as e:
            self.eval_label.config(text="N/A")
            self.eval_description_label.config(text="Evaluation unavailable")

    def update_evaluation_bar(self, evaluation):
        """Update the visual evaluation bar."""
        try:
            self.eval_canvas.delete("all")

            # Force canvas to update its size
            self.eval_canvas.update_idletasks()
            canvas_width = self.eval_canvas.winfo_width()
            if canvas_width <= 1:  # Canvas not yet rendered
                # Use the parent frame width as reference
                parent_width = self.eval_bar_frame.winfo_width()
                if parent_width > 1:
                    canvas_width = parent_width - 10  # Account for padding
                else:
                    canvas_width = 250  # Default width

            canvas_height = 20

            # Normalize evaluation to -500 to +500 range for better display
            max_eval = 500
            normalized_eval = max(-max_eval, min(max_eval, evaluation))

            # Calculate bar position (0 = left edge, 1 = right edge)
            bar_position = (normalized_eval + max_eval) / (2 * max_eval)
            center_x = canvas_width * bar_position

            # Draw background (neutral)
            self.eval_canvas.create_rectangle(0, 0, canvas_width, canvas_height,
                                            fill="#E8E8E8", outline="#CCCCCC", width=1)

            # Draw evaluation indicator
            center_pos = canvas_width // 2

            if evaluation > 25:
                # White advantage - green on right side
                color = "#4CAF50"  # Green
                width = center_x - center_pos
                if width > 0:
                    self.eval_canvas.create_rectangle(center_pos, 2, center_x, canvas_height-2,
                                                    fill=color, outline="")
            elif evaluation < -25:
                # Black advantage - red on left side
                color = "#F44336"  # Red
                width = center_pos - center_x
                if width > 0:
                    self.eval_canvas.create_rectangle(center_x, 2, center_pos, canvas_height-2,
                                                    fill=color, outline="")

            # Draw center line
            self.eval_canvas.create_line(center_pos, 0, center_pos, canvas_height,
                                       fill="#666666", width=2)

            # Draw current position marker
            marker_size = 4
            self.eval_canvas.create_oval(center_x - marker_size, canvas_height//2 - marker_size,
                                       center_x + marker_size, canvas_height//2 + marker_size,
                                       fill="#333333", outline="white", width=2)

            # Add labels
            self.eval_canvas.create_text(5, canvas_height//2, text="Black",
                                       font=("Arial", 8), fill="#666666", anchor="w")
            self.eval_canvas.create_text(canvas_width-5, canvas_height//2, text="White",
                                       font=("Arial", 8), fill="#666666", anchor="e")

        except Exception as e:
            pass  # Silently handle canvas errors

    def on_canvas_resize(self, event):
        """Handle canvas resize events to redraw the evaluation bar."""
        # Redraw the evaluation bar when canvas is resized
        if hasattr(self, 'board'):
            try:
                raw_evaluation, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
                if len(self.game_history) == 0:
                    evaluation = 0
                else:
                    if hasattr(self, 'starting_eval'):
                        evaluation = raw_evaluation - self.starting_eval
                    else:
                        evaluation = raw_evaluation
                self.update_evaluation_bar(evaluation)
            except:
                pass

    def update_history_display(self):
        """Update the move history display with move quality ratings."""
        self.history_text.delete(1.0, tk.END)
        
        # Format moves in pairs (White, Black) with quality ratings
        for i in range(0, len(self.game_history), 2):
            move_num = (i // 2) + 1
            white_move = self.game_history[i] if i < len(self.game_history) else ""
            black_move = self.game_history[i + 1] if i + 1 < len(self.game_history) else ""
            
            line = f"{move_num}. {white_move}"
            
            # Add quality rating for white move if available
            if i < len(self.move_quality_history):
                quality_info = self.move_quality_history[i]
                quality_symbol = self.get_quality_symbol(quality_info['quality'])
                line += f" {quality_symbol}"
            
            if black_move:
                line += f" {black_move}"
                # Add quality rating for black move if available
                if i + 1 < len(self.move_quality_history):
                    quality_info = self.move_quality_history[i + 1]
                    quality_symbol = self.get_quality_symbol(quality_info['quality'])
                    line += f" {quality_symbol}"
            
            line += "\n"
            self.history_text.insert(tk.END, line)
        
        # Scroll to bottom
        self.history_text.see(tk.END)
    
    def get_quality_symbol(self, quality):
        """Get a symbol representing move quality."""
        quality_symbols = {
            'Excellent': '!!',
            'Good': '!',
            'Okay': '',
            'Inaccurate': '?',
            'Poor': '??'
        }
        return quality_symbols.get(quality, '')
    
    def handle_game_over(self):
        """Handle game over situation."""
        self.game_over = True
        
        if self.board.is_checkmate():
            winner = "You" if self.board.turn != self.human_color else "ChessBot"
            message = f"Checkmate! {winner} win!"
        elif self.board.is_stalemate():
            message = "Stalemate! The game is a draw."
        elif self.board.is_insufficient_material():
            message = "Insufficient material! The game is a draw."
        # ADDED: Handle threefold repetition message
        elif self.board.can_claim_threefold_repetition():
            message = "The game is a draw by threefold repetition."
        else:
            message = "Game over!"
        
        result = messagebox.askyesno("Game Over", f"{message}\n\nWould you like to save this game?")
        if result:
            self.save_game()
    
    def new_game(self):
        """Start a new game."""
        if self.bot_thinking:
            messagebox.showinfo("New Game", "Please wait - bot is currently thinking.")
            return
        
        self.board = chess.Board()
        self.game_history = []
        self.move_quality_history = []
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        self.bot_thinking = False  # Reset bot thinking state
        
        # Ensure UI is enabled
        self.enable_ui()

        # Reset evaluation baseline
        if hasattr(self, 'starting_eval'):
            delattr(self, 'starting_eval')

        self.update_board_display()
        self.update_status()
        self.update_evaluation()
        self.update_history_display()
    
    def switch_colors(self):
        """Switch human and bot colors."""
        if self.bot_thinking:
            messagebox.showinfo("Switch Colors", "Please wait - bot is currently thinking.")
            return
        
        self.bot_thinking = False  # Reset bot thinking state when switching colors
        self.enable_ui()  # Make sure UI is enabled
        self.human_color = not self.human_color
        color_name = "White" if self.human_color == chess.WHITE else "Black"
        messagebox.showinfo("Colors Switched", f"You are now playing as {color_name}")
        
        # Update status immediately
        self.update_status()
        
        # If it's now bot's turn, make a move
        if not self.game_over and self.board.turn != self.human_color:
            self.make_bot_move()
    
    def set_difficulty(self, depth):
        """Set the bot difficulty."""
        if self.bot_thinking:
            messagebox.showinfo("Difficulty Change", "Please wait - bot is currently thinking.")
            return
        
        self.bot.set_depth(depth)
        difficulty_name = self.difficulty_var.get()
        messagebox.showinfo("Difficulty Changed", f"Bot difficulty set to {difficulty_name} (depth {depth})")
    
    def save_game(self):
        """Save the current game to a PGN file."""
        if self.bot_thinking:
            messagebox.showinfo("Save Game", "Please wait - bot is currently thinking.")
            return
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".pgn",
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
                title="Save Chess Game"
            )
            
            if filename:
                white_player = "Human" if self.human_color == chess.WHITE else "ChessBot"
                black_player = "ChessBot" if self.human_color == chess.WHITE else "Human"
                
                save_game_pgn(self.board, self.game_history, filename, white_player, black_player)
                messagebox.showinfo("Game Saved", f"Game saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save game: {e}")
    
    def load_game(self):
        """Load a game from a PGN file."""
        if self.bot_thinking:
            messagebox.showinfo("Load Game", "Please wait - bot is currently thinking.")
            return
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
                title="Load Chess Game"
            )
            
            if filename:
                from utils import load_game_pgn
                board, history = load_game_pgn(filename)
                
                self.board = board
                self.game_history = history
                self.move_quality_history = []  # Reset quality history when loading
                self.selected_square = None
                self.highlighted_squares = []
                self.game_over = self.board.is_game_over()
                
                self.update_board_display()
                self.update_status()
                self.update_evaluation()
                self.update_history_display()

                messagebox.showinfo("Game Loaded", f"Game loaded from {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load game: {e}")
    
    def show_analysis(self):
        """Show position analysis in a popup window."""
        if self.bot_thinking:
            messagebox.showinfo("Analysis", "Please wait - bot is currently thinking.")
            return
        try:
            analysis = analyze_position(self.board)
            analysis_text = format_analysis(analysis)
            
            # Create popup window
            popup = tk.Toplevel(self.root)
            popup.title("Position Analysis")
            popup.geometry("400x500")
            
            # Text widget with scrollbar
            frame = ttk.Frame(popup)
            frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(frame, wrap=tk.WORD, font=("Courier", 10))
            scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            text_widget.insert(1.0, analysis_text)
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to analyze position: {e}")

    def show_hint(self):
        """Show the best move suggestion."""
        if self.bot_thinking:
            messagebox.showinfo("Hint", "Please wait - bot is currently thinking.")
            return
        if self.game_over or self.board.turn != self.human_color:
            messagebox.showinfo("Hint", "No hint available - not your turn or game is over.")
            return

        try:
            # Get the best move from the bot
            self.update_status("Calculating best move...")

            def calculate_hint():
                try:
                    best_move = self.bot.get_best_move(self.board)
                    if best_move:
                        san_move = self.board.san(best_move)

                        # Get evaluation of the move using same method as elsewhere
                        self.board.push(best_move)
                        eval_after, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
                        self.board.pop()

                        eval_before, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
                        eval_change = eval_after - eval_before

                        hint_text = f"Suggested move: {san_move}\n"
                        #hint_text += f"Evaluation change: {eval_change/100.0:+.2f}\n"
                        #hint_text += f"Quality: {get_move_quality_description(eval_after)}"

                        # Highlight the suggested move
                        self.selected_square = best_move.from_square
                        self.highlighted_squares = [best_move.to_square]

                        self.root.after(0, lambda: [
                            messagebox.showinfo("Hint", hint_text),
                            self.update_board_display(),
                            self.update_status()
                        ])
                    else:
                        self.root.after(0, lambda: [
                            messagebox.showinfo("Hint", "No good move found."),
                            self.update_status()
                        ])
                except Exception as e:
                    self.root.after(0, lambda: [
                        messagebox.showerror("Error", f"Failed to calculate hint: {e}"),
                        self.update_status()
                    ])

            # Calculate hint in separate thread
            threading.Thread(target=calculate_hint, daemon=True).start()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to show hint: {e}")
            self.update_status()

    def show_move_quality_history(self):
        """Show detailed move quality history in a popup window."""
        if self.bot_thinking:
            messagebox.showinfo("Move Quality History", "Please wait - bot is currently thinking.")
            return
        if not self.move_quality_history:
            messagebox.showinfo("Move Quality History", "No moves have been made yet.")
            return
        
        try:
            # Create popup window
            popup = tk.Toplevel(self.root)
            popup.title("Move Quality History")
            popup.geometry("800x600")
            
            # Main frame
            main_frame = ttk.Frame(popup)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # Title
            title_label = ttk.Label(main_frame, text="Detailed Move Quality Analysis", 
                                  font=("Arial", 14, "bold"))
            title_label.pack(pady=(0, 10))
            
            # Text widget with scrollbar
            text_frame = ttk.Frame(main_frame)
            text_frame.pack(fill=tk.BOTH, expand=True)
            
            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Courier", 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # Generate detailed history text
            history_text = self.generate_detailed_history()
            text_widget.insert(1.0, history_text)
            text_widget.config(state=tk.DISABLED)
            
            # Close button
            close_button = ttk.Button(main_frame, text="Close", command=popup.destroy)
            close_button.pack(pady=(10, 0))
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to show move quality history: {e}")
    
    def generate_detailed_history(self):
        """Generate detailed text for move quality history."""
        if not self.move_quality_history:
            return "No moves recorded yet."
        
        lines = []
        lines.append("=" * 80)
        lines.append("MOVE QUALITY ANALYSIS REPORT")
        lines.append("=" * 80)
        lines.append("")
        
        # Summary statistics
        quality_counts = {}
        total_moves = len(self.move_quality_history)
        
        for move_info in self.move_quality_history:
            quality = move_info['quality']
            quality_counts[quality] = quality_counts.get(quality, 0) + 1
        
        lines.append("SUMMARY:")
        lines.append(f"Total moves analyzed: {total_moves}")
        lines.append("")
        lines.append("NOTE: Opening moves are judged leniently:")
        lines.append("  - Moves 1-6: Most moves within 1 pawn are 'Okay'")
        lines.append("  - Moves 7-10: Extended opening with moderate standards")
        lines.append("  - Moves 11+: Standard evaluation criteria")
        lines.append("")
        
        for quality in ['Excellent', 'Good', 'Okay', 'Inaccurate', 'Poor']:
            count = quality_counts.get(quality, 0)
            percentage = (count / total_moves * 100) if total_moves > 0 else 0
            symbol = self.get_quality_symbol(quality)
            lines.append(f"{quality:12} {symbol:2}: {count:3} moves ({percentage:5.1f}%)")
        
        lines.append("")
        lines.append("-" * 80)
        lines.append("DETAILED MOVE ANALYSIS:")
        lines.append("-" * 80)
        lines.append("")
        
        # Detailed move-by-move analysis
        for i, move_info in enumerate(self.move_quality_history):
            move_num = (i // 2) + 1
            color = "White" if i % 2 == 0 else "Black"
            
            lines.append(f"Move {move_num}{'. ' if color == 'White' else '... '}"
                        f"{move_info['move']} ({color})")
            lines.append(f"  Quality:        {move_info['quality']} {self.get_quality_symbol(move_info['quality'])}")
            lines.append(f"  Eval before:    {move_info['eval_before']:+.2f}")
            lines.append(f"  Eval after:     {move_info['eval_after']:+.2f}")
            lines.append(f"  Eval change:    {move_info['eval_change']:+.2f}")
            
            # Add interpretation with more realistic thresholds
            move_num = move_info['move_number']
            
            if color == "White":
                if move_info['eval_change'] > 0.3:
                    interpretation = "Improved White's position"
                elif move_info['eval_change'] > 0.1:
                    interpretation = "Slightly improved White's position"
                elif move_info['eval_change'] > -0.1:
                    interpretation = "Maintained position balance"
                elif move_info['eval_change'] > -0.3:
                    interpretation = "Slightly favored Black"
                else:
                    interpretation = "Favored Black significantly"
            else:
                if move_info['eval_change'] < -0.3:
                    interpretation = "Improved Black's position"
                elif move_info['eval_change'] < -0.1:
                    interpretation = "Slightly improved Black's position"
                elif move_info['eval_change'] < 0.1:
                    interpretation = "Maintained position balance"
                elif move_info['eval_change'] < 0.3:
                    interpretation = "Slightly favored White"
                else:
                    interpretation = "Favored White significantly"
            
            # Special note for opening moves
            if move_num <= 6:
                interpretation += " (opening development)"
            
            lines.append(f"  Interpretation: {interpretation}")
            lines.append("")
        
        lines.append("=" * 80)
        lines.append("Legend:")
        lines.append("!! = Excellent move    ! = Good move    (blank) = Okay move")
        lines.append("?  = Inaccurate move   ?? = Poor move")
        lines.append("")
        lines.append("Evaluation is from White's perspective:")
        lines.append("Positive values favor White, negative values favor Black")
        lines.append("=" * 80)
        
        return "\n".join(lines)

    def play_from_history(self):
        """Parse and play moves from the move history text widget."""
        if self.bot_thinking:
            messagebox.showinfo("Play from History", "Please wait - bot is currently thinking.")
            return
        
        try:
            # Get the text from the history widget
            history_text = self.history_text.get(1.0, tk.END).strip()
            
            if not history_text:
                messagebox.showwarning("No History", "The move history is empty. Please paste some moves first.")
                return
            
            # Parse the moves from the text
            moves = self.parse_history_text(history_text)
            
            if not moves:
                messagebox.showwarning("No Valid Moves", "No valid moves found in the history text.")
                return
            
            # Confirm with user
            result = messagebox.askyesno("Play from History", 
                                       f"Found {len(moves)} moves to play. This will start a new game and replay these moves.\n\n"
                                       f"Continue?")
            if not result:
                return
            
            # Start a new game
            self.new_game()
            
            # Play the moves automatically
            self.auto_play_moves(moves)
            
        except Exception as e:
            messagebox.showerror("Error", f"Error playing from history: {str(e)}")
    
    def parse_history_text(self, text):
        """Parse move history text and extract moves in SAN format."""
        moves = []
        
        # Split text into lines and process each line
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Remove move numbers and quality symbols
            # Pattern: "1. e4 !! e5 ?" -> extract "e4" and "e5"
            import re
            
            # Remove move numbers (1., 2., etc.)
            line = re.sub(r'\d+\.', '', line)
            
            # Remove quality symbols (!, !!, ?, ??)
            line = re.sub(r'[!?]+', '', line)
            
            # Split by whitespace and filter out empty strings
            parts = [part.strip() for part in line.split() if part.strip()]
            
            # Each part should be a move
            for part in parts:
                if part and len(part) >= 2:  # Basic sanity check for move length
                    moves.append(part)
        
        return moves
    
    def auto_play_moves(self, moves):
        """Automatically play a list of moves with a delay between each move."""
        self.auto_play_list = moves
        self.auto_play_index = 0
        self.auto_playing = True
        
        # Disable UI during auto-play
        self.disable_ui()
        
        # Start playing moves
        self.play_next_auto_move()
    
    def play_next_auto_move(self):
        """Play the next move in the auto-play sequence."""
        if not hasattr(self, 'auto_playing') or not self.auto_playing:
            return
        
        if self.auto_play_index >= len(self.auto_play_list):
            # Finished playing all moves
            self.auto_playing = False
            self.enable_ui()
            messagebox.showinfo("Auto-play Complete", "All moves have been played!")
            return
        
        try:
            move_san = self.auto_play_list[self.auto_play_index]
            
            # Try to parse the move
            try:
                move = self.board.parse_san(move_san)
                
                # Make the move
                self.make_move_without_bot_response(move)
                
                self.auto_play_index += 1
                
                # Schedule the next move (delay of 500ms)
                self.root.after(500, self.play_next_auto_move)
                
            except chess.InvalidMoveError:
                # Invalid move, try to continue with next move
                print(f"Invalid move: {move_san}")
                self.auto_play_index += 1
                self.root.after(100, self.play_next_auto_move)
                
        except Exception as e:
            self.auto_playing = False
            self.enable_ui()
            messagebox.showerror("Auto-play Error", f"Error playing move: {str(e)}")
    
    def make_move_without_bot_response(self, move):
        """Make a move without triggering a bot response."""
        try:
            # Similar to make_move but without bot response
            eval_score_before, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            
            if not hasattr(self, 'starting_eval'):
                self.starting_eval = eval_score_before
            
            eval_before = eval_score_before - self.starting_eval
            
            san_move = self.board.san(move)
            self.board.push(move)
            self.game_history.append(san_move)
            
            # Get evaluation after move
            eval_score_after, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            eval_after = eval_score_after - self.starting_eval
            eval_change = eval_after - eval_before
            
            # Update displays
            self.update_history_display()
            self.update_status()
            self.update_evaluation()
            self.update_board_display()
            
            # Store move quality for analysis (EXACT SAME LOGIC AS MANUAL PLAY)
            current_player = "White" if not self.board.turn else "Black"
            move_number = len(self.game_history)
            
            # Adjusted thresholds based on actual evaluation behavior (SAME AS MANUAL)
            if move_number <= 15:  # Opening moves (first 15 moves)
                excellent_threshold = 300  # 3.0 pawn (truly exceptional opening moves)
                good_threshold = 150       # 1.5 pawns  
                inaccurate_threshold = 120 # 1.2 pawns
                poor_threshold = 200       # 2.0 pawns
            elif move_number <= 30:  # Middle game
                excellent_threshold = 250  # 2.5 pawns (major tactical/positional gains)
                good_threshold = 120       # 1.2 pawns
                inaccurate_threshold = 150 # 1.5 pawns
                poor_threshold = 250       # 2.5 pawns
            else:  # End game
                excellent_threshold = 200  # 2.0 pawns (endgame precision is crucial)
                good_threshold = 100       # 1.0 pawns
                inaccurate_threshold = 150 # 1.5 pawn
                poor_threshold = 250       # 2.5 pawns

            # Special handling for very early opening moves (first 6 moves)
            # Adjusted thresholds based on actual evaluation behavior (2-3 pawn swings are normal)
            if move_number <= 6:
                if current_player == "White":
                    if eval_change >= 400:  # **** pawn or better (truly exceptional)
                        move_quality = "Excellent"
                    elif eval_change >= 200:   # **** pawn (good improvement)
                        move_quality = "Good"
                    elif eval_change >= -50:  # Down to -0.5 pawns is still okay  
                        move_quality = "Okay"
                    elif eval_change >= -150:  # Down to -1.5 pawn
                        move_quality = "Inaccurate"
                    else:
                        move_quality = "Poor"  # Losing more than 1.5 pawn is poor
                else:
                    if eval_change <= -400:  # -4.0 pawn or better for Black (truly exceptional)
                        move_quality = "Excellent"
                    elif eval_change <= -200:   # -2.0 pawn (good improvement)
                        move_quality = "Good"
                    elif eval_change <= 50:   # Up to +0.5 pawns is still okay
                        move_quality = "Okay"
                    elif eval_change <= 150:   # Up to **** pawn
                        move_quality = "Inaccurate"
                    else:
                        move_quality = "Poor"  # Losing more than 1.5 pawn is poor
            elif move_number <= 10:
                # Extended opening (moves 7-10) - moderate standards
                if current_player == "White":
                    if eval_change >= 350:  # **** pawn or better (exceptional tactical sequences)
                        move_quality = "Excellent"
                    elif eval_change >= 180:   # **** pawn (good improvement)
                        move_quality = "Good"
                    elif eval_change >= -60:  # Down to -0.6 pawns is still okay
                        move_quality = "Okay"
                    elif eval_change >= -140: # Down to -1.4 pawn
                        move_quality = "Inaccurate"
                    else:
                        move_quality = "Poor"
                else:
                    if eval_change <= -350:  # -3.5 pawn or better for Black
                        move_quality = "Excellent"
                    elif eval_change <= -180:   # -1.8 pawn
                        move_quality = "Good"
                    elif eval_change <= 60:   # Up to +0.6 pawns is still okay
                        move_quality = "Okay"
                    elif eval_change <= 140:  # Up to **** pawn
                        move_quality = "Inaccurate"
                    else:
                        move_quality = "Poor"
            else:
                # Middle and end game - use normal thresholds
                if current_player == "White":
                    # White just moved - positive change is good for White
                    if eval_change >= excellent_threshold:
                        move_quality = "Excellent"
                    elif eval_change >= good_threshold:
                        move_quality = "Good"
                    elif eval_change >= -good_threshold:  # Allow small losses
                        move_quality = "Okay"
                    elif eval_change >= -inaccurate_threshold:
                        move_quality = "Inaccurate"
                    elif eval_change >= -poor_threshold:
                        move_quality = "Poor"
                    else:
                        move_quality = "Poor"
                else:
                    # Black just moved - negative change is good for Black
                    if eval_change <= -excellent_threshold:
                        move_quality = "Excellent"
                    elif eval_change <= -good_threshold:
                        move_quality = "Good"
                    elif eval_change <= good_threshold:  # Allow small gains for White
                        move_quality = "Okay"
                    elif eval_change <= inaccurate_threshold:
                        move_quality = "Inaccurate"
                    elif eval_change <= poor_threshold:
                        move_quality = "Poor"
                    else:
                        move_quality = "Poor"
            
            # Store move quality information (EXACT SAME STRUCTURE AS MANUAL)
            move_info = {
                'move': san_move,
                'player': current_player,
                'eval_before': eval_before / 100.0,
                'eval_after': eval_after / 100.0,
                'eval_change': eval_change / 100.0,
                'quality': move_quality,
                'move_number': move_number
            }
            
            self.move_quality_history.append(move_info)
            
            # Check for game over
            if self.board.is_game_over():
                self.handle_game_over()
                if hasattr(self, 'auto_playing'):
                    self.auto_playing = False
                    self.enable_ui()
                    
        except Exception as e:
            print(f"Error making move {move}: {e}")
            raise

    def show_analysis(self):
        """Show position analysis."""
        try:
            # Get current evaluation using the same method as move quality analysis
            eval_score, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            
            # Apply the same normalization as in move quality analysis
            if hasattr(self, 'starting_eval'):
                eval_score = eval_score - self.starting_eval
            
            # Get opening name
            opening_name = get_opening_name(self.board, self.game_history)
            
            # Analyze position
            analysis = analyze_position(self.board)
            
            # Format the analysis with additional information
            formatted_analysis = self.format_comprehensive_analysis(analysis, opening_name, eval_score)
            
            # Create analysis window
            analysis_window = tk.Toplevel(self.root)
            analysis_window.title("Position Analysis")
            analysis_window.geometry("600x400")
            
            # Text widget with scrollbar
            text_frame = ttk.Frame(analysis_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 11))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # Insert analysis
            text_widget.insert(tk.END, formatted_analysis)
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Analysis Error", f"Error analyzing position: {str(e)}")
    
    def show_hint(self):
        """Show best move hint."""
        try:
            if self.board.turn != self.human_color:
                messagebox.showinfo("Hint", "It's not your turn!")
                return
                
            # Get best move
            best_move = self.bot.get_best_move(self.board)
            if best_move:
                hint_text = f"Best move: {self.board.san(best_move)}"
                messagebox.showinfo("Hint", hint_text)
            else:
                messagebox.showinfo("Hint", "No legal moves available!")
                
        except Exception as e:
            messagebox.showerror("Hint Error", f"Error getting hint: {str(e)}")
    
    def show_tactical_threats(self):
        """Show tactical threats analysis."""
        try:
            # Get tactical threats
            threats = self.tactical_evaluator.evaluate_threats(self.board)
            
            # Create threats window
            threats_window = tk.Toplevel(self.root)
            threats_window.title("Tactical Threats Analysis")
            threats_window.geometry("700x500")
            
            # Text widget with scrollbar
            text_frame = ttk.Frame(threats_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # Use the new balanced threat analysis
            analysis_summary = self.tactical_evaluator.get_threat_analysis_summary(threats, self.board)
            
            # Add current position info at the top
            position_info = []
            position_info.append(f"Position: {self.board.fen()}")
            position_info.append(f"Turn: {'White' if self.board.turn == chess.WHITE else 'Black'}")
            position_info.append(f"Move: {len(self.game_history) // 2 + 1}")
            position_info.append("")
            
            # Combine position info with analysis
            full_content = "\n".join(position_info) + analysis_summary
            
            text_widget.insert(tk.END, full_content)
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Tactical Analysis Error", f"Error analyzing threats: {str(e)}")
    
    def show_move_quality_history(self):
        """Show move quality history analysis."""
        try:
            if not self.move_quality_history:
                messagebox.showinfo("Move Quality", "No moves have been played yet.")
                return
            
            # Create move quality window
            quality_window = tk.Toplevel(self.root)
            quality_window.title("Move Quality Analysis")
            quality_window.geometry("800x600")
            
            # Text widget with scrollbar
            text_frame = ttk.Frame(quality_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # Generate quality analysis
            analysis = self.generate_move_quality_analysis()
            text_widget.insert(tk.END, analysis)
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Move Quality Error", f"Error showing move quality: {str(e)}")
    
    def generate_move_quality_analysis(self):
        """Generate a detailed move quality analysis report."""
        lines = []
        lines.append("=" * 80)
        lines.append("MOVE QUALITY ANALYSIS REPORT")
        lines.append("=" * 80)
        lines.append("")
        
        # Summary statistics
        quality_counts = {}
        total_moves = len(self.move_quality_history)
        
        for move_info in self.move_quality_history:
            quality = move_info['quality']
            quality_counts[quality] = quality_counts.get(quality, 0) + 1
        
        lines.append("SUMMARY:")
        lines.append(f"Total moves analyzed: {total_moves}")
        lines.append("")
        lines.append("Move Quality Distribution:")
        lines.append("")
        
        # Order qualities from best to worst
        quality_order = ['Brilliant', 'Excellent', 'Good', 'Okay', 'Inaccurate', 'Poor', 'Blunder']
        
        for quality in quality_order:
            count = quality_counts.get(quality, 0)
            percentage = (count / total_moves * 100) if total_moves > 0 else 0
            symbol = self.get_quality_symbol(quality)
            lines.append(f"{quality:12} {symbol:3}: {count:3} moves ({percentage:5.1f}%)")
        
        lines.append("")
        lines.append("-" * 80)
        lines.append("DETAILED MOVE ANALYSIS:")
        lines.append("-" * 80)
        lines.append("")
        
        # Detailed move-by-move analysis
        for i, move_info in enumerate(self.move_quality_history):
            move_num = (i // 2) + 1
            color = "White" if i % 2 == 0 else "Black"
            
            lines.append(f"Move {move_num}{'. ' if color == 'White' else '... '}"
                        f"{move_info['move']} ({color})")
            lines.append(f"  Quality:        {move_info['quality']} {self.get_quality_symbol(move_info['quality'])}")
            lines.append(f"  Eval before:    {move_info['eval_before']:+.2f}")
            lines.append(f"  Eval after:     {move_info['eval_after']:+.2f}")
            lines.append(f"  Eval change:    {move_info['eval_change']:+.2f}")
            
            # Add explanation if available
            if 'explanation' in move_info and move_info['explanation']:
                lines.append(f"  Explanation:    {move_info['explanation']}")
            
            lines.append("")
        
        lines.append("=" * 80)
        lines.append("Legend:")
        lines.append("!!! = Brilliant move   !! = Excellent move   ! = Good move")
        lines.append("(blank) = Okay move    ? = Inaccurate move   ?? = Poor move   ??? = Blunder")
        lines.append("")
        lines.append("Evaluation is from White's perspective:")
        lines.append("Positive values favor White, negative values favor Black")
        lines.append("=" * 80)
        
        return "\n".join(lines)
    
    def get_quality_symbol(self, quality):
        """Get the symbol for a move quality."""
        quality_symbols = {
            'Brilliant': '!!!',
            'Excellent': '!!',
            'Good': '!',
            'Okay': '',
            'Inaccurate': '?',
            'Poor': '??',
            'Blunder': '???'
        }
        return quality_symbols.get(quality, '')
    
    def format_comprehensive_analysis(self, analysis, opening_name, eval_score):
        """Format comprehensive position analysis for display."""
        lines = []
        lines.append("COMPREHENSIVE POSITION ANALYSIS")
        lines.append("=" * 50)
        lines.append("")
        
        # Opening information
        lines.append(f"Opening: {opening_name}")
        lines.append("")
        
        # Current position FEN
        lines.append("Position FEN:")
        lines.append(f"{self.board.fen()}")
        lines.append("")
        
        # Position evaluation
        eval_pawns = eval_score / 100.0
        if eval_pawns > 0:
            lines.append(f"Evaluation: +{eval_pawns:.2f} (White advantage)")
        elif eval_pawns < 0:
            lines.append(f"Evaluation: {eval_pawns:.2f} (Black advantage)")
        else:
            lines.append("Evaluation: 0.00 (Equal position)")
        
        # Add note about evaluation consistency
        lines.append("(Normalized relative to starting position)")
        lines.append("")
        
        # Material balance
        material = analysis['material_balance']
        if material > 0:
            lines.append(f"Material: White +{material} points")
        elif material < 0:
            lines.append(f"Material: Black +{abs(material)} points")
        else:
            lines.append("Material: Equal")
        lines.append("")
        
        # Piece counts
        white_pieces = analysis['piece_count']['white']
        black_pieces = analysis['piece_count']['black']
        
        piece_names = {
            chess.PAWN: 'Pawns', chess.KNIGHT: 'Knights', chess.BISHOP: 'Bishops',
            chess.ROOK: 'Rooks', chess.QUEEN: 'Queens', chess.KING: 'Kings'
        }
        
        lines.append("Piece Count:")
        for piece_type, name in piece_names.items():
            white_count = white_pieces.get(piece_type, 0)
            black_count = black_pieces.get(piece_type, 0)
            lines.append(f"  {name}: White {white_count}, Black {black_count}")
        lines.append("")
        
        # Mobility
        white_mobility = analysis['mobility']['white']
        black_mobility = analysis['mobility']['black']
        lines.append(f"Mobility (legal moves): White {white_mobility}, Black {black_mobility}")
        lines.append("")
        
        # Center control
        white_center = analysis['center_control']['white']
        black_center = analysis['center_control']['black']
        lines.append(f"Center Control: White {white_center}, Black {black_center}")
        lines.append("")
        
        # Game phase
        total_pieces = sum(white_pieces.values()) + sum(black_pieces.values())
        if total_pieces > 20:
            phase = "Opening"
        elif total_pieces > 12:
            phase = "Middlegame"
        else:
            phase = "Endgame"
        lines.append(f"Game Phase: {phase}")
        lines.append("")
        
        # Position summary
        lines.append("Position Summary:")
        lines.append("-" * 20)
        
        if abs(eval_pawns) < 0.5:
            lines.append("• Balanced position")
        elif eval_pawns > 0.5:
            lines.append("• White has an advantage")
        else:
            lines.append("• Black has an advantage")
        
        if white_mobility > black_mobility + 5:
            lines.append("• White has superior mobility")
        elif black_mobility > white_mobility + 5:
            lines.append("• Black has superior mobility")
        
        if white_center > black_center + 2:
            lines.append("• White controls the center")
        elif black_center > white_center + 2:
            lines.append("• Black controls the center")
        
        return "\n".join(lines)
    
    def run(self):
        """Start the GUI application."""
        self.root.mainloop()

def main():
    """Main entry point for the GUI version."""
    try:
        app = ChessGUI()
        app.run()
    except Exception as e:
        print(f"Error starting GUI: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()