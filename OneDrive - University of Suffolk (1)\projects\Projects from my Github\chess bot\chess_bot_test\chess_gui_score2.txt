#!/usr/bin/env python3
"""
Chess Bot GUI - A desktop chess game with AI opponent using tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import chess
import threading
from chess_bot import ChessBot # chess_bot2 for the enhanced bot (stronger evaluation function)
from utils import save_game_pgn, analyze_position, format_analysis, get_opening_name, get_move_quality_description

class ChessGUI:
    """
    GUI Chess game using tkinter with drag-and-drop functionality.
    """
    
    def __init__(self):
        """Initialize the chess GUI."""
        self.root = tk.Tk()
        self.root.title("Chess Bot - GUI Version")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # Game state
        self.board = chess.Board()
        self.bot = ChessBot(depth=3, name="ChessBot")
        self.game_history = []
        self.move_quality_history = []  # Store move quality ratings
        self.human_color = chess.WHITE
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        self.bot_thinking = False  # Track if bot is currently thinking
        
        # Colors and styling
        self.light_square_color = "#F0D9B5"
        self.dark_square_color = "#B58863"
        self.highlight_color = "#FFFF00"
        self.selected_color = "#FF6B6B"
        self.legal_move_color = "#90EE90"
        
        # Unicode chess pieces
        self.piece_symbols = {
            'P': '♙', 'N': '♘', 'B': '♗', 'R': '♖', 'Q': '♕', 'K': '♔',  # White
            'p': '♟', 'n': '♞', 'b': '♝', 'r': '♜', 'q': '♛', 'k': '♚'   # Black
        }
        
        self.setup_ui()
        self.update_board_display()
        self.update_status()

        # Delay evaluation update to ensure GUI is fully rendered
        self.root.after(100, self.update_evaluation)
    
    def setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left panel for the chess board
        board_frame = ttk.Frame(main_frame)
        board_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        # Chess board canvas
        self.canvas = tk.Canvas(board_frame, width=480, height=480, bg="white")
        self.canvas.pack()
        self.canvas.bind("<Button-1>", self.on_square_click)
        
        # Right panel for controls and information
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Game controls
        controls_group = ttk.LabelFrame(control_frame, text="Game Controls", padding=10)
        controls_group.pack(fill=tk.X, pady=(0, 10))
        
        self.new_game_btn = ttk.Button(controls_group, text="New Game", command=self.new_game)
        self.new_game_btn.pack(fill=tk.X, pady=2)
        self.switch_colors_btn = ttk.Button(controls_group, text="Switch Colors", command=self.switch_colors)
        self.switch_colors_btn.pack(fill=tk.X, pady=2)
        self.save_game_btn = ttk.Button(controls_group, text="Save Game", command=self.save_game)
        self.save_game_btn.pack(fill=tk.X, pady=2)
        self.load_game_btn = ttk.Button(controls_group, text="Load Game", command=self.load_game)
        self.load_game_btn.pack(fill=tk.X, pady=2)
        
        # Bot difficulty
        difficulty_group = ttk.LabelFrame(control_frame, text="Bot Difficulty", padding=10)
        difficulty_group.pack(fill=tk.X, pady=(0, 10))
        
        self.difficulty_var = tk.StringVar(value="Medium")
        difficulties = [("Easy", 2), ("Medium", 3), ("Hard", 4), ("Expert", 5)]
        
        self.difficulty_buttons = []
        for name, depth in difficulties:
            btn = ttk.Radiobutton(difficulty_group, text=f"{name} (depth {depth})", 
                           variable=self.difficulty_var, value=name,
                           command=lambda d=depth: self.set_difficulty(d))
            btn.pack(anchor=tk.W)
            self.difficulty_buttons.append(btn)
        
        # Game status
        status_group = ttk.LabelFrame(control_frame, text="Game Status", padding=10)
        status_group.pack(fill=tk.X, pady=(0, 10))

        self.status_label = ttk.Label(status_group, text="White to move", font=("Arial", 12))
        self.status_label.pack()

        self.opening_label = ttk.Label(status_group, text="Opening: Starting Position", font=("Arial", 10))
        self.opening_label.pack()

        # Position evaluation
        eval_frame = ttk.Frame(status_group)
        eval_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(eval_frame, text="Evaluation:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.eval_label = ttk.Label(eval_frame, text="0.00", font=("Arial", 10))
        self.eval_label.pack(side=tk.LEFT, padx=(5, 0))

        self.eval_bar_frame = ttk.Frame(status_group)
        self.eval_bar_frame.pack(fill=tk.X, pady=(2, 0))

        # Evaluation bar (visual representation)
        self.eval_canvas = tk.Canvas(self.eval_bar_frame, height=20, bg="white")
        self.eval_canvas.pack(fill=tk.X, expand=True)

        # Bind canvas resize event to redraw the evaluation bar
        self.eval_canvas.bind('<Configure>', self.on_canvas_resize)

        self.eval_description_label = ttk.Label(status_group, text="Equal position",
                                              font=("Arial", 9), foreground="gray")
        self.eval_description_label.pack()
        
        # Move history
        history_group = ttk.LabelFrame(control_frame, text="Move History", padding=10)
        history_group.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Scrollable text widget for move history
        history_frame = ttk.Frame(history_group)
        history_frame.pack(fill=tk.BOTH, expand=True)
        
        self.history_text = tk.Text(history_frame, height=8, width=25, wrap=tk.WORD)
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_text.yview)
        self.history_text.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Analysis and hint buttons
        analysis_frame = ttk.Frame(control_frame)
        analysis_frame.pack(fill=tk.X, pady=(5, 0))

        self.analyze_btn = ttk.Button(analysis_frame, text="Analyze Position", command=self.show_analysis)
        self.analyze_btn.pack(fill=tk.X, pady=2)
        self.hint_btn = ttk.Button(analysis_frame, text="Show Hint", command=self.show_hint)
        self.hint_btn.pack(fill=tk.X, pady=2)
        self.move_quality_btn = ttk.Button(analysis_frame, text="Show Move Quality History", command=self.show_move_quality_history)
        self.move_quality_btn.pack(fill=tk.X, pady=2)
        self.detailed_analysis_btn = ttk.Button(analysis_frame, text="Show Detailed Analysis", command=self.show_detailed_analysis)
        self.detailed_analysis_btn.pack(fill=tk.X, pady=2)
    
    def draw_board(self):
        """Draw the chess board."""
        self.canvas.delete("all")
        square_size = 60
        
        for row in range(8):
            for col in range(8):
                x1 = col * square_size
                y1 = row * square_size
                x2 = x1 + square_size
                y2 = y1 + square_size
                
                # Determine square color
                is_light = (row + col) % 2 == 0
                square = chess.square(col, 7 - row)
                
                # Choose color based on square state
                if square == self.selected_square:
                    color = self.selected_color
                elif square in self.highlighted_squares:
                    color = self.legal_move_color
                else:
                    color = self.light_square_color if is_light else self.dark_square_color
                
                # Draw square
                self.canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline="black")
                
                # Add coordinates
                if col == 0:  # Rank labels
                    self.canvas.create_text(x1 + 5, y1 + 10, text=str(8 - row), 
                                          font=("Arial", 8), fill="black")
                if row == 7:  # File labels
                    self.canvas.create_text(x2 - 10, y2 - 5, text=chr(ord('a') + col), 
                                          font=("Arial", 8), fill="black")
    
    def draw_pieces(self):
        """Draw the chess pieces on the board."""
        square_size = 60
        
        for row in range(8):
            for col in range(8):
                square = chess.square(col, 7 - row)
                piece = self.board.piece_at(square)
                
                if piece:
                    x = col * square_size + square_size // 2
                    y = row * square_size + square_size // 2
                    
                    piece_symbol = self.piece_symbols.get(piece.symbol(), piece.symbol())
                    self.canvas.create_text(x, y, text=piece_symbol, 
                                          font=("Arial", 36), fill="black")
    
    def update_board_display(self):
        """Update the visual board display."""
        self.draw_board()
        self.draw_pieces()
    
    def on_square_click(self, event):
        """Handle mouse clicks on the board."""
        if self.game_over or self.board.turn != self.human_color or self.bot_thinking:
            return
        
        square_size = 60
        col = event.x // square_size
        row = event.y // square_size
        
        if 0 <= col < 8 and 0 <= row < 8:
            clicked_square = chess.square(col, 7 - row)
            
            if self.selected_square is None:
                # Select a piece
                piece = self.board.piece_at(clicked_square)
                if piece and piece.color == self.human_color:
                    self.selected_square = clicked_square
                    self.highlight_legal_moves(clicked_square)
                    self.update_board_display()
            else:
                # Try to make a move
                move = chess.Move(self.selected_square, clicked_square)
                
                # Check for promotion
                piece = self.board.piece_at(self.selected_square)
                if (piece and piece.piece_type == chess.PAWN and 
                    ((piece.color == chess.WHITE and chess.square_rank(clicked_square) == 7) or
                     (piece.color == chess.BLACK and chess.square_rank(clicked_square) == 0))):
                    # Default to queen promotion for simplicity
                    move = chess.Move(self.selected_square, clicked_square, promotion=chess.QUEEN)
                
                if move in self.board.legal_moves:
                    self.make_move(move)
                
                # Clear selection
                self.selected_square = None
                self.highlighted_squares = []
                self.update_board_display()
    
    def highlight_legal_moves(self, square):
        """Highlight legal moves for the selected piece."""
        self.highlighted_squares = []
        for move in self.board.legal_moves:
            if move.from_square == square:
                self.highlighted_squares.append(move.to_square)
    
    def disable_ui(self):
        """Disable all UI controls while bot is thinking."""
        # Disable all game control buttons
        self.new_game_btn.config(state='disabled')
        self.switch_colors_btn.config(state='disabled')
        self.save_game_btn.config(state='disabled')
        self.load_game_btn.config(state='disabled')
        
        # Disable difficulty selection buttons
        for btn in self.difficulty_buttons:
            btn.config(state='disabled')
        
        # Disable analysis buttons
        self.analyze_btn.config(state='disabled')
        self.hint_btn.config(state='disabled')
        self.move_quality_btn.config(state='disabled')
        self.detailed_analysis_btn.config(state='disabled')
        
        # Change cursor to indicate waiting
        self.root.config(cursor="wait")
        self.canvas.config(cursor="wait")
    
    def enable_ui(self):
        """Re-enable all UI controls after bot finishes thinking."""
        # Re-enable all game control buttons
        self.new_game_btn.config(state='normal')
        self.switch_colors_btn.config(state='normal')
        self.save_game_btn.config(state='normal')
        self.load_game_btn.config(state='normal')
        
        # Re-enable difficulty selection buttons
        for btn in self.difficulty_buttons:
            btn.config(state='normal')
        
        # Re-enable analysis buttons
        self.analyze_btn.config(state='normal')
        self.hint_btn.config(state='normal')
        self.move_quality_btn.config(state='normal')
        self.detailed_analysis_btn.config(state='normal')
        
        # Reset cursor to normal
        self.root.config(cursor="")
        self.canvas.config(cursor="")
    
    def make_move(self, move):
        """Make a move on the board."""
        try:
            # Get multi-depth evaluation before move for comprehensive analysis
            short_before, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            medium_before, _ = self.bot.minimax(self.board, depth=4, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            
            # Determine appropriate long-term depth based on game phase
            piece_count = len(list(self.board.piece_map()))
            if piece_count <= 12:  # Endgame
                long_term_depth = 6
            elif piece_count <= 20:  # Late middlegame
                long_term_depth = 5
            else:  # Opening/early middlegame
                long_term_depth = 4
                
            long_before, _ = self.bot.minimax(self.board, depth=long_term_depth, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            
            # Normalize evaluations relative to starting position
            if not hasattr(self, 'starting_eval_short'):
                self.starting_eval_short = short_before
                self.starting_eval_medium = medium_before
                self.starting_eval_long = long_before
            
            short_norm_before = short_before - self.starting_eval_short
            medium_norm_before = medium_before - self.starting_eval_medium
            long_norm_before = long_before - self.starting_eval_long
            
            # Calculate trend before move
            trend_before = long_norm_before - short_norm_before
            
            # Use medium-term evaluation as primary, adjusted by trend
            eval_before = medium_norm_before + (trend_before * 0.3)

            san_move = self.board.san(move)
            self.board.push(move)
            self.game_history.append(san_move)

            # Get multi-depth evaluation after move
            short_after, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            medium_after, _ = self.bot.minimax(self.board, depth=4, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            long_after, _ = self.bot.minimax(self.board, depth=long_term_depth, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            
            short_norm_after = short_after - self.starting_eval_short
            medium_norm_after = medium_after - self.starting_eval_medium
            long_norm_after = long_after - self.starting_eval_long
            
            # Calculate trend after move
            trend_after = long_norm_after - short_norm_after
            
            # Use medium-term evaluation as primary, adjusted by trend
            eval_after = medium_norm_after + (trend_after * 0.3)
            eval_change = eval_after - eval_before

            # Enhanced debug print with multi-depth information
            print(f"Move: {san_move}")
            print(f"  Short-term: {short_norm_before/100:.2f} -> {short_norm_after/100:.2f}")
            print(f"  Medium-term: {medium_norm_before/100:.2f} -> {medium_norm_after/100:.2f}")
            print(f"  Long-term: {long_norm_before/100:.2f} -> {long_norm_after/100:.2f}")
            print(f"  Trend: {trend_before/100:.2f} -> {trend_after/100:.2f}")
            print(f"  Adjusted change: {eval_change/100:.2f}")
            
            # Store additional evaluation data for move quality assessment
            move_eval_data = {
                'short_change': short_norm_after - short_norm_before,
                'medium_change': medium_norm_after - medium_norm_before,
                'long_change': long_norm_after - long_norm_before,
                'trend_change': trend_after - trend_before
            }

            # Update displays
            self.update_history_display()
            self.update_status()
            self.update_evaluation()
            self.update_board_display()

            # Show evaluation change for moves
            change_text = f"{eval_change/100.0:+.2f}"

            # Enhanced move quality assessment using multi-depth evaluation
            current_player = "White" if not self.board.turn else "Black"  # Player who just moved
            move_number = len(self.game_history)
            
            # Get multi-depth changes for comprehensive assessment
            short_change = move_eval_data['short_change']
            medium_change = move_eval_data['medium_change']
            long_change = move_eval_data['long_change']
            trend_change = move_eval_data['trend_change']
            
            # Determine if this is a "trap move" (looks good short-term, bad long-term)
            # or a "sacrifice move" (looks bad short-term, good long-term)
            is_trap_move = False
            is_sacrifice_move = False
            
            if current_player == "White":
                # For white: positive changes are good
                if short_change > 100 and long_change < -150:  # Looks good short-term, bad long-term
                    is_trap_move = True
                elif short_change < -100 and long_change > 150:  # Sacrifice for long-term gain
                    is_sacrifice_move = True
            else:
                # For black: negative changes are good
                if short_change < -100 and long_change > 150:  # Looks good short-term, bad long-term
                    is_trap_move = True
                elif short_change > 100 and long_change < -150:  # Sacrifice for long-term gain
                    is_sacrifice_move = True
            
            # Use long-term evaluation as primary for move quality if there's a significant difference
            # between short and long term, otherwise use the adjusted evaluation
            if abs(long_change - short_change) > 200:  # Significant difference (2 pawns)
                primary_eval_change = long_change  # Prioritize long-term consequences
                quality_modifier = " (deep)"
            else:
                primary_eval_change = eval_change  # Use adjusted evaluation
                quality_modifier = ""

            # Adjusted thresholds based on game phase
            if move_number <= 15:  # Opening moves (first 15 moves)
                excellent_threshold = 300  # 3.0 pawn (truly exceptional opening moves)
                good_threshold = 150       # 1.5 pawns  
                inaccurate_threshold = 120 # 1.2 pawns
                poor_threshold = 200       # 2.0 pawns
            elif move_number <= 30:  # Middle game
                excellent_threshold = 250  # 2.5 pawns (major tactical/positional gains)
                good_threshold = 120       # 1.2 pawns
                inaccurate_threshold = 150 # 1.5 pawns
                poor_threshold = 250       # 2.5 pawns
            else:  # End game
                excellent_threshold = 200  # 2.0 pawns (endgame precision is crucial)
                good_threshold = 100       # 1.0 pawns
                inaccurate_threshold = 150 # 1.5 pawn
                poor_threshold = 250       # 2.5 pawns

            # Enhanced move quality assessment using primary_eval_change
            # Special modifiers for trap moves and sacrifices
            if is_trap_move:
                move_quality = "Poor"
                quality_modifier = " (trap!)"
            elif is_sacrifice_move:
                # Assess sacrifice quality based on long-term gain
                if current_player == "White":
                    if long_change >= excellent_threshold:
                        move_quality = "Excellent"
                        quality_modifier = " (sacrifice)"
                    elif long_change >= good_threshold:
                        move_quality = "Good"
                        quality_modifier = " (sacrifice)"
                    else:
                        move_quality = "Inaccurate"
                        quality_modifier = " (dubious sacrifice)"
                else:
                    if long_change <= -excellent_threshold:
                        move_quality = "Excellent"
                        quality_modifier = " (sacrifice)"
                    elif long_change <= -good_threshold:
                        move_quality = "Good"
                        quality_modifier = " (sacrifice)"
                    else:
                        move_quality = "Inaccurate"
                        quality_modifier = " (dubious sacrifice)"
            else:
                # Standard move quality assessment using primary evaluation
                if move_number <= 6:
                    # Very early opening moves
                    if current_player == "White":
                        if primary_eval_change >= 400:
                            move_quality = "Excellent"
                        elif primary_eval_change >= 200:
                            move_quality = "Good"
                        elif primary_eval_change >= -50:
                            move_quality = "Okay"
                        elif primary_eval_change >= -150:
                            move_quality = "Inaccurate"
                        else:
                            move_quality = "Poor"
                    else:
                        if primary_eval_change <= -400:
                            move_quality = "Excellent"
                        elif primary_eval_change <= -200:
                            move_quality = "Good"
                        elif primary_eval_change <= 50:
                            move_quality = "Okay"
                        elif primary_eval_change <= 150:
                            move_quality = "Inaccurate"
                        else:
                            move_quality = "Poor"
                elif move_number <= 10:
                    # Extended opening (moves 7-10) - moderate standards
                    if current_player == "White":
                        if primary_eval_change >= 350:
                            move_quality = "Excellent"
                        elif primary_eval_change >= 180:
                            move_quality = "Good"
                        elif primary_eval_change >= -60:
                            move_quality = "Okay"
                        elif primary_eval_change >= -140:
                            move_quality = "Inaccurate"
                        else:
                            move_quality = "Poor"
                    else:
                        if primary_eval_change <= -350:
                            move_quality = "Excellent"
                        elif primary_eval_change <= -180:
                            move_quality = "Good"
                        elif primary_eval_change <= 60:
                            move_quality = "Okay"
                        elif primary_eval_change <= 140:
                            move_quality = "Inaccurate"
                        else:
                            move_quality = "Poor"
                else:
                    # Middle and end game - use enhanced thresholds
                    if current_player == "White":
                        if primary_eval_change >= excellent_threshold:
                            move_quality = "Excellent"
                        elif primary_eval_change >= good_threshold:
                            move_quality = "Good"
                        elif primary_eval_change >= -good_threshold:
                            move_quality = "Okay"
                        elif primary_eval_change >= -inaccurate_threshold:
                            move_quality = "Inaccurate"
                        elif primary_eval_change >= -poor_threshold:
                            move_quality = "Poor"
                        else:
                            move_quality = "Poor"
                    else:
                        # Black just moved - negative change is good for Black
                        if primary_eval_change <= -excellent_threshold:
                            move_quality = "Excellent"
                        elif primary_eval_change <= -good_threshold:
                            move_quality = "Good"
                        elif primary_eval_change <= good_threshold:
                            move_quality = "Okay"
                        elif primary_eval_change <= inaccurate_threshold:
                            move_quality = "Inaccurate"
                        elif primary_eval_change <= poor_threshold:
                            move_quality = "Poor"
                        else:
                            move_quality = "Poor"

            # Store enhanced move quality information with multi-depth data
            move_info = {
                'move': san_move,
                'player': current_player,
                'eval_before': eval_before / 100.0,
                'eval_after': eval_after / 100.0,
                'eval_change': eval_change / 100.0,
                'short_change': short_change / 100.0,
                'medium_change': medium_change / 100.0,
                'long_change': long_change / 100.0,
                'trend_change': trend_change / 100.0,
                'primary_change': primary_eval_change / 100.0,
                'quality': move_quality,
                'quality_modifier': quality_modifier,
                'move_number': move_number,
                'is_trap': is_trap_move,
                'is_sacrifice': is_sacrifice_move
            }
            self.move_quality_history.append(move_info)

            # Enhanced status display for human moves
            if self.board.turn != self.human_color:  # Human just moved (turn switched to bot)
                # Show comprehensive move evaluation
                if quality_modifier:
                    status_text = f"Move: {san_move} ({change_text}) - {move_quality}{quality_modifier}"
                else:
                    status_text = f"Move: {san_move} ({change_text}) - {move_quality}"
                
                # Add trend information for significant differences
                if abs(long_change - short_change) > 100:  # 1+ pawn difference
                    trend_text = f" [Short: {short_change/100:+.1f}, Long: {long_change/100:+.1f}]"
                    status_text += trend_text
                
                self.status_label.config(text=status_text)
                self.root.after(4000, lambda: self.update_status() if not self.bot_thinking else None)  # Reset after 4 seconds

            # Check for game over
            if self.board.is_game_over():
                self.handle_game_over()
            elif self.board.turn != self.human_color:
                # Bot's turn
                self.root.after(500, self.make_bot_move)  # Small delay for better UX

        except Exception as e:
            messagebox.showerror("Error", f"Invalid move: {e}")
    
    def make_bot_move(self):
        """Make a move for the bot in a separate thread."""
        def bot_move_thread():
            try:
                move = self.bot.get_best_move(self.board)
                
                if move:
                    # Update UI in main thread
                    self.root.after(0, lambda: self.finish_bot_move(move))
                else:
                    self.root.after(0, lambda: self.finish_bot_move_error("Bot couldn't find a move!"))
            except Exception as e:
                self.root.after(0, lambda: self.finish_bot_move_error(f"Bot error: {e}"))
        
        # Set bot thinking state and disable UI
        self.bot_thinking = True
        self.disable_ui()
        self.update_status("Bot is thinking...")
        
        threading.Thread(target=bot_move_thread, daemon=True).start()
    
    def finish_bot_move(self, move):
        """Complete the bot move in the main thread."""
        self.bot_thinking = False
        self.enable_ui()
        self.make_move(move)
    
    def finish_bot_move_error(self, error_message):
        """Handle bot move errors in the main thread."""
        self.bot_thinking = False
        self.enable_ui()
        messagebox.showerror("Error", error_message)
        self.update_status()  # Reset status after error
    
    def update_status(self, custom_message=None):
        """Update the game status display."""
        if custom_message:
            self.status_label.config(text=custom_message)
            return
        
        # Don't update status if bot is thinking (unless it's a game over condition)
        if self.bot_thinking and not self.board.is_game_over():
            return
        
        if self.board.is_checkmate():
            winner = "Black" if self.board.turn == chess.WHITE else "White"
            self.status_label.config(text=f"Checkmate! {winner} wins!")
            self.game_over = True
        elif self.board.is_stalemate():
            self.status_label.config(text="Stalemate! Draw!")
            self.game_over = True
        elif self.board.is_insufficient_material():
            self.status_label.config(text="Insufficient material! Draw!")
            self.game_over = True
        elif self.board.is_check():
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} in check!")
        else:
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} to move")
        
        # Update opening name
        if len(self.game_history) <= 8:
            opening = get_opening_name(self.board, self.game_history)
            self.opening_label.config(text=f"Opening: {opening}")

    def update_evaluation(self):
        """Update the position evaluation display with multi-depth analysis."""
        try:
            # Get evaluations at different depths for better future possibility assessment
            short_term_eval, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            medium_term_eval, _ = self.bot.minimax(self.board, depth=4, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            
            # Get long-term evaluation if not too computationally expensive
            # For positions with few pieces (endgame), we can afford deeper search
            piece_count = len(list(self.board.piece_map()))
            if piece_count <= 12:  # Endgame
                long_term_depth = 6
            elif piece_count <= 20:  # Late middlegame
                long_term_depth = 5
            else:  # Opening/early middlegame
                long_term_depth = 4
            
            long_term_eval, _ = self.bot.minimax(self.board, depth=long_term_depth, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)

            # Normalize evaluations - subtract starting position bias
            if len(self.game_history) == 0:
                # Store baseline evaluations
                if not hasattr(self, 'starting_eval_short'):
                    self.starting_eval_short = short_term_eval
                    self.starting_eval_medium = medium_term_eval
                    self.starting_eval_long = long_term_eval
                
                short_term_normalized = 0
                medium_term_normalized = 0
                long_term_normalized = 0
            else:
                # Adjust evaluations relative to starting position
                if hasattr(self, 'starting_eval_short'):
                    short_term_normalized = short_term_eval - self.starting_eval_short
                    medium_term_normalized = medium_term_eval - self.starting_eval_medium
                    long_term_normalized = long_term_eval - self.starting_eval_long
                else:
                    short_term_normalized = short_term_eval
                    medium_term_normalized = medium_term_eval
                    long_term_normalized = long_term_eval

            # Calculate evaluation trend (how evaluation changes with deeper search)
            short_to_medium_trend = medium_term_normalized - short_term_normalized
            medium_to_long_trend = long_term_normalized - medium_term_normalized
            overall_trend = long_term_normalized - short_term_normalized

            # Use the medium-term evaluation as the primary display value
            # but factor in the trends for a more comprehensive assessment
            primary_evaluation = medium_term_normalized
            
            # Apply trend weighting: if deeper search shows deteriorating position, adjust evaluation
            trend_weight = 0.3  # How much to weight the trend
            adjusted_evaluation = primary_evaluation + (overall_trend * trend_weight)

            # Convert to centipawns and format
            eval_score = adjusted_evaluation / 100.0
            short_score = short_term_normalized / 100.0
            long_score = long_term_normalized / 100.0

            # Format the score display with trend information
            if abs(adjusted_evaluation) >= 9900:  # Very high evaluation indicates mate
                if adjusted_evaluation > 0:
                    eval_text = "+M"  # Mate for white
                else:
                    eval_text = "-M"  # Mate for black
            else:
                eval_text = f"{eval_score:+.2f}"
                
                # Add trend indicator
                if abs(overall_trend) > 50:  # Significant trend (0.5 pawn difference)
                    if overall_trend > 0:
                        eval_text += " ↗"  # Improving with depth
                    else:
                        eval_text += " ↘"  # Worsening with depth

            self.eval_label.config(text=eval_text)

            # Enhanced evaluation description that considers future possibilities
            description = self.get_enhanced_position_description(
                short_term_normalized, medium_term_normalized, long_term_normalized,
                short_to_medium_trend, medium_to_long_trend, overall_trend
            )
            self.eval_description_label.config(text=description)

            # Update evaluation bar with the adjusted evaluation
            self.update_evaluation_bar(adjusted_evaluation)

            # Store evaluation components for move analysis
            self.current_eval_components = {
                'short_term': short_term_normalized,
                'medium_term': medium_term_normalized,
                'long_term': long_term_normalized,
                'adjusted': adjusted_evaluation,
                'trend': overall_trend
            }

        except Exception as e:
            self.eval_label.config(text="N/A")
            self.eval_description_label.config(text="Evaluation unavailable")
            
    def get_enhanced_position_description(self, short_eval, medium_eval, long_eval, 
                                        short_to_medium_trend, medium_to_long_trend, overall_trend):
        """Get enhanced position description considering future possibilities."""
        
        # Determine primary evaluation level
        primary_eval = medium_eval
        abs_eval = abs(primary_eval)
        
        if abs_eval >= 900:  # 9+ pawns
            base_desc = "Decisive advantage" if primary_eval > 0 else "Decisive disadvantage"
        elif abs_eval >= 500:  # 5+ pawns
            base_desc = "Winning position" if primary_eval > 0 else "Losing position"
        elif abs_eval >= 300:  # 3+ pawns
            base_desc = "Large advantage" if primary_eval > 0 else "Large disadvantage"
        elif abs_eval >= 150:  # 1.5+ pawns
            base_desc = "Clear advantage" if primary_eval > 0 else "Clear disadvantage"
        elif abs_eval >= 50:   # 0.5+ pawns
            base_desc = "Slight advantage" if primary_eval > 0 else "Slight disadvantage"
        else:
            base_desc = "Equal position"
        
        # Add trend analysis
        if abs(overall_trend) > 100:  # Significant trend (1+ pawn)
            if overall_trend > 0:
                trend_desc = " (improving prospects)"
            else:
                trend_desc = " (deteriorating prospects)"
        elif abs(overall_trend) > 50:  # Moderate trend (0.5+ pawn)
            if overall_trend > 0:
                trend_desc = " (slightly improving)"
            else:
                trend_desc = " (slightly worsening)"
        else:
            trend_desc = ""
        
        # Special case: position looks good short-term but bad long-term
        if short_eval > 100 and long_eval < -100:
            trend_desc = " (tactical trap ahead!)"
        elif short_eval < -100 and long_eval > 100:
            trend_desc = " (hidden resources!)"
        
        return base_desc + trend_desc

    def update_evaluation_bar(self, evaluation):
        """Update the visual evaluation bar."""
        try:
            self.eval_canvas.delete("all")

            # Force canvas to update its size
            self.eval_canvas.update_idletasks()
            canvas_width = self.eval_canvas.winfo_width()
            if canvas_width <= 1:  # Canvas not yet rendered
                # Use the parent frame width as reference
                parent_width = self.eval_bar_frame.winfo_width()
                if parent_width > 1:
                    canvas_width = parent_width - 10  # Account for padding
                else:
                    canvas_width = 250  # Default width

            canvas_height = 20

            # Normalize evaluation to -500 to +500 range for better display
            max_eval = 500
            normalized_eval = max(-max_eval, min(max_eval, evaluation))

            # Calculate bar position (0 = left edge, 1 = right edge)
            bar_position = (normalized_eval + max_eval) / (2 * max_eval)
            center_x = canvas_width * bar_position

            # Draw background (neutral)
            self.eval_canvas.create_rectangle(0, 0, canvas_width, canvas_height,
                                            fill="#E8E8E8", outline="#CCCCCC", width=1)

            # Draw evaluation indicator
            center_pos = canvas_width // 2

            if evaluation > 25:
                # White advantage - green on right side
                color = "#4CAF50"  # Green
                width = center_x - center_pos
                if width > 0:
                    self.eval_canvas.create_rectangle(center_pos, 2, center_x, canvas_height-2,
                                                    fill=color, outline="")
            elif evaluation < -25:
                # Black advantage - red on left side
                color = "#F44336"  # Red
                width = center_pos - center_x
                if width > 0:
                    self.eval_canvas.create_rectangle(center_x, 2, center_pos, canvas_height-2,
                                                    fill=color, outline="")

            # Draw center line
            self.eval_canvas.create_line(center_pos, 0, center_pos, canvas_height,
                                       fill="#666666", width=2)

            # Draw current position marker
            marker_size = 4
            self.eval_canvas.create_oval(center_x - marker_size, canvas_height//2 - marker_size,
                                       center_x + marker_size, canvas_height//2 + marker_size,
                                       fill="#333333", outline="white", width=2)

            # Add labels
            self.eval_canvas.create_text(5, canvas_height//2, text="Black",
                                       font=("Arial", 8), fill="#666666", anchor="w")
            self.eval_canvas.create_text(canvas_width-5, canvas_height//2, text="White",
                                       font=("Arial", 8), fill="#666666", anchor="e")

        except Exception as e:
            pass  # Silently handle canvas errors

    def on_canvas_resize(self, event):
        """Handle canvas resize events to redraw the evaluation bar."""
        # Redraw the evaluation bar when canvas is resized
        if hasattr(self, 'board') and hasattr(self, 'current_eval_components'):
            try:
                # Use the stored evaluation components to avoid recalculation
                evaluation = self.current_eval_components['adjusted']
                self.update_evaluation_bar(evaluation)
            except:
                # Fallback to quick evaluation
                try:
                    raw_evaluation, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
                    if len(self.game_history) == 0:
                        evaluation = 0
                    else:
                        if hasattr(self, 'starting_eval_short'):
                            evaluation = raw_evaluation - self.starting_eval_short
                        else:
                            evaluation = raw_evaluation
                    self.update_evaluation_bar(evaluation)
                except:
                    pass

    def update_history_display(self):
        """Update the move history display with move quality ratings."""
        self.history_text.delete(1.0, tk.END)
        
        # Format moves in pairs (White, Black) with quality ratings
        for i in range(0, len(self.game_history), 2):
            move_num = (i // 2) + 1
            white_move = self.game_history[i] if i < len(self.game_history) else ""
            black_move = self.game_history[i + 1] if i + 1 < len(self.game_history) else ""
            
            line = f"{move_num}. {white_move}"
            
            # Add quality rating for white move if available
            if i < len(self.move_quality_history):
                quality_info = self.move_quality_history[i]
                quality_symbol = self.get_quality_symbol(quality_info['quality'])
                line += f" {quality_symbol}"
            
            if black_move:
                line += f" {black_move}"
                # Add quality rating for black move if available
                if i + 1 < len(self.move_quality_history):
                    quality_info = self.move_quality_history[i + 1]
                    quality_symbol = self.get_quality_symbol(quality_info['quality'])
                    line += f" {quality_symbol}"
            
            line += "\n"
            self.history_text.insert(tk.END, line)
        
        # Scroll to bottom
        self.history_text.see(tk.END)
    
    def get_quality_symbol(self, quality):
        """Get a symbol representing move quality."""
        quality_symbols = {
            'Excellent': '!!',
            'Good': '!',
            'Okay': '',
            'Inaccurate': '?',
            'Poor': '??'
        }
        return quality_symbols.get(quality, '')
    
    def handle_game_over(self):
        """Handle game over situation."""
        self.game_over = True
        
        if self.board.is_checkmate():
            winner = "You" if self.board.turn != self.human_color else "ChessBot"
            message = f"Checkmate! {winner} win!"
        elif self.board.is_stalemate():
            message = "Stalemate! The game is a draw."
        elif self.board.is_insufficient_material():
            message = "Insufficient material! The game is a draw."
        else:
            message = "Game over!"
        
        result = messagebox.askyesno("Game Over", f"{message}\n\nWould you like to save this game?")
        if result:
            self.save_game()
    
    def new_game(self):
        """Start a new game."""
        if self.bot_thinking:
            messagebox.showinfo("New Game", "Please wait - bot is currently thinking.")
            return
        
        self.board = chess.Board()
        self.game_history = []
        self.move_quality_history = []
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        self.bot_thinking = False  # Reset bot thinking state
        
        # Ensure UI is enabled
        self.enable_ui()

        # Reset enhanced evaluation baselines
        for attr in ['starting_eval', 'starting_eval_short', 'starting_eval_medium', 'starting_eval_long']:
            if hasattr(self, attr):
                delattr(self, attr)

        self.update_board_display()
        self.update_status()
        self.update_evaluation()
        self.update_history_display()
    
    def switch_colors(self):
        """Switch human and bot colors."""
        if self.bot_thinking:
            messagebox.showinfo("Switch Colors", "Please wait - bot is currently thinking.")
            return
        
        self.bot_thinking = False  # Reset bot thinking state when switching colors
        self.enable_ui()  # Make sure UI is enabled
        self.human_color = not self.human_color
        color_name = "White" if self.human_color == chess.WHITE else "Black"
        messagebox.showinfo("Colors Switched", f"You are now playing as {color_name}")
        
        # Update status immediately
        self.update_status()
        
        # If it's now bot's turn, make a move
        if not self.game_over and self.board.turn != self.human_color:
            self.make_bot_move()
    
    def set_difficulty(self, depth):
        """Set the bot difficulty."""
        if self.bot_thinking:
            messagebox.showinfo("Difficulty Change", "Please wait - bot is currently thinking.")
            return
        
        self.bot.set_depth(depth)
        difficulty_name = self.difficulty_var.get()
        messagebox.showinfo("Difficulty Changed", f"Bot difficulty set to {difficulty_name} (depth {depth})")
    
    def save_game(self):
        """Save the current game to a PGN file."""
        if self.bot_thinking:
            messagebox.showinfo("Save Game", "Please wait - bot is currently thinking.")
            return
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".pgn",
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
                title="Save Chess Game"
            )
            
            if filename:
                white_player = "Human" if self.human_color == chess.WHITE else "ChessBot"
                black_player = "ChessBot" if self.human_color == chess.WHITE else "Human"
                
                save_game_pgn(self.board, self.game_history, filename, white_player, black_player)
                messagebox.showinfo("Game Saved", f"Game saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save game: {e}")
    
    def load_game(self):
        """Load a game from a PGN file."""
        if self.bot_thinking:
            messagebox.showinfo("Load Game", "Please wait - bot is currently thinking.")
            return
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
                title="Load Chess Game"
            )
            
            if filename:
                from utils import load_game_pgn
                board, history = load_game_pgn(filename)
                
                self.board = board
                self.game_history = history
                self.move_quality_history = []  # Reset quality history when loading
                self.selected_square = None
                self.highlighted_squares = []
                self.game_over = self.board.is_game_over()
                
                self.update_board_display()
                self.update_status()
                self.update_evaluation()
                self.update_history_display()

                messagebox.showinfo("Game Loaded", f"Game loaded from {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load game: {e}")
    
    def show_analysis(self):
        """Show position analysis in a popup window."""
        if self.bot_thinking:
            messagebox.showinfo("Analysis", "Please wait - bot is currently thinking.")
            return
        try:
            analysis = analyze_position(self.board)
            analysis_text = format_analysis(analysis)
            
            # Create popup window
            popup = tk.Toplevel(self.root)
            popup.title("Position Analysis")
            popup.geometry("400x500")
            
            # Text widget with scrollbar
            frame = ttk.Frame(popup)
            frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(frame, wrap=tk.WORD, font=("Courier", 10))
            scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            text_widget.insert(1.0, analysis_text)
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to analyze position: {e}")

    def show_hint(self):
        """Show the best move suggestion."""
        if self.bot_thinking:
            messagebox.showinfo("Hint", "Please wait - bot is currently thinking.")
            return
        if self.game_over or self.board.turn != self.human_color:
            messagebox.showinfo("Hint", "No hint available - not your turn or game is over.")
            return

        try:
            # Get the best move from the bot
            self.update_status("Calculating best move...")

            def calculate_hint():
                try:
                    best_move = self.bot.get_best_move(self.board)
                    if best_move:
                        san_move = self.board.san(best_move)

                        # Get evaluation of the move using same method as elsewhere
                        self.board.push(best_move)
                        eval_after, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
                        self.board.pop()

                        eval_before, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
                        eval_change = eval_after - eval_before

                        hint_text = f"Suggested move: {san_move}\n"
                        #hint_text += f"Evaluation change: {eval_change/100.0:+.2f}\n"
                        #hint_text += f"Quality: {get_move_quality_description(eval_after)}"

                        # Highlight the suggested move
                        self.selected_square = best_move.from_square
                        self.highlighted_squares = [best_move.to_square]

                        self.root.after(0, lambda: [
                            messagebox.showinfo("Hint", hint_text),
                            self.update_board_display(),
                            self.update_status()
                        ])
                    else:
                        self.root.after(0, lambda: [
                            messagebox.showinfo("Hint", "No good move found."),
                            self.update_status()
                        ])
                except Exception as e:
                    self.root.after(0, lambda: [
                        messagebox.showerror("Error", f"Failed to calculate hint: {e}"),
                        self.update_status()
                    ])

            # Calculate hint in separate thread
            threading.Thread(target=calculate_hint, daemon=True).start()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to show hint: {e}")
            self.update_status()

    def show_move_quality_history(self):
        """Show detailed move quality history in a popup window."""
        if self.bot_thinking:
            messagebox.showinfo("Move Quality History", "Please wait - bot is currently thinking.")
            return
        if not self.move_quality_history:
            messagebox.showinfo("Move Quality History", "No moves have been made yet.")
            return
        
        try:
            # Create popup window
            popup = tk.Toplevel(self.root)
            popup.title("Move Quality History")
            popup.geometry("800x600")
            
            # Main frame
            main_frame = ttk.Frame(popup)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # Title
            title_label = ttk.Label(main_frame, text="Detailed Move Quality Analysis", 
                                  font=("Arial", 14, "bold"))
            title_label.pack(pady=(0, 10))
            
            # Text widget with scrollbar
            text_frame = ttk.Frame(main_frame)
            text_frame.pack(fill=tk.BOTH, expand=True)
            
            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Courier", 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # Generate detailed history text
            history_text = self.generate_detailed_history()
            text_widget.insert(1.0, history_text)
            text_widget.config(state=tk.DISABLED)
            
            # Close button
            close_button = ttk.Button(main_frame, text="Close", command=popup.destroy)
            close_button.pack(pady=(10, 0))
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to show move quality history: {e}")
    
    def generate_detailed_history(self):
        """Generate detailed text for move quality history."""
        if not self.move_quality_history:
            return "No moves recorded yet."
        
        lines = []
        lines.append("=" * 80)
        lines.append("MOVE QUALITY ANALYSIS REPORT")
        lines.append("=" * 80)
        lines.append("")
        
        # Summary statistics
        quality_counts = {}
        total_moves = len(self.move_quality_history)
        
        for move_info in self.move_quality_history:
            quality = move_info['quality']
            quality_counts[quality] = quality_counts.get(quality, 0) + 1
        
        lines.append("SUMMARY:")
        lines.append(f"Total moves analyzed: {total_moves}")
        lines.append("")
        lines.append("NOTE: Opening moves are judged leniently:")
        lines.append("  - Moves 1-6: Most moves within 1 pawn are 'Okay'")
        lines.append("  - Moves 7-10: Extended opening with moderate standards")
        lines.append("  - Moves 11+: Standard evaluation criteria")
        lines.append("")
        
        for quality in ['Excellent', 'Good', 'Okay', 'Inaccurate', 'Poor']:
            count = quality_counts.get(quality, 0)
            percentage = (count / total_moves * 100) if total_moves > 0 else 0
            symbol = self.get_quality_symbol(quality)
            lines.append(f"{quality:12} {symbol:2}: {count:3} moves ({percentage:5.1f}%)")
        
        lines.append("")
        lines.append("-" * 80)
        lines.append("DETAILED MOVE ANALYSIS:")
        lines.append("-" * 80)
        lines.append("")
        
        # Detailed move-by-move analysis
        for i, move_info in enumerate(self.move_quality_history):
            move_num = (i // 2) + 1
            color = "White" if i % 2 == 0 else "Black"
            
            lines.append(f"Move {move_num}{'. ' if color == 'White' else '... '}"
                        f"{move_info['move']} ({color})")
            lines.append(f"  Quality:        {move_info['quality']} {self.get_quality_symbol(move_info['quality'])}")
            lines.append(f"  Eval before:    {move_info['eval_before']:+.2f}")
            lines.append(f"  Eval after:     {move_info['eval_after']:+.2f}")
            lines.append(f"  Eval change:    {move_info['eval_change']:+.2f}")
            
            # Add interpretation with more realistic thresholds
            move_num = move_info['move_number']
            
            if color == "White":
                if move_info['eval_change'] > 0.3:
                    interpretation = "Improved White's position"
                elif move_info['eval_change'] > 0.1:
                    interpretation = "Slightly improved White's position"
                elif move_info['eval_change'] > -0.1:
                    interpretation = "Maintained position balance"
                elif move_info['eval_change'] > -0.3:
                    interpretation = "Slightly favored Black"
                else:
                    interpretation = "Favored Black significantly"
            else:
                if move_info['eval_change'] < -0.3:
                    interpretation = "Improved Black's position"
                elif move_info['eval_change'] < -0.1:
                    interpretation = "Slightly improved Black's position"
                elif move_info['eval_change'] < 0.1:
                    interpretation = "Maintained position balance"
                elif move_info['eval_change'] < 0.3:
                    interpretation = "Slightly favored White"
                else:
                    interpretation = "Favored White significantly"
            
            # Special note for opening moves
            if move_num <= 6:
                interpretation += " (opening development)"
            
            lines.append(f"  Interpretation: {interpretation}")
            lines.append("")
        
        lines.append("=" * 80)
        lines.append("Legend:")
        lines.append("!! = Excellent move    ! = Good move    (blank) = Okay move")
        lines.append("?  = Inaccurate move   ?? = Poor move")
        lines.append("")
        lines.append("Evaluation is from White's perspective:")
        lines.append("Positive values favor White, negative values favor Black")
        lines.append("=" * 80)
        
        return "\n".join(lines)

    def show_detailed_analysis(self):
        """Show detailed multi-depth position analysis."""
        if self.bot_thinking:
            messagebox.showinfo("Detailed Analysis", "Please wait - bot is currently thinking.")
            return
            
        try:
            # Create popup window
            popup = tk.Toplevel(self.root)
            popup.title("Multi-Depth Position Analysis")
            popup.geometry("900x700")
            
            # Main frame
            main_frame = ttk.Frame(popup)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # Title
            title_label = ttk.Label(main_frame, text="Multi-Depth Position Analysis", 
                                  font=("Arial", 14, "bold"))
            title_label.pack(pady=(0, 10))
            
            # Create notebook for tabs
            notebook = ttk.Notebook(main_frame)
            notebook.pack(fill=tk.BOTH, expand=True)
            
            # Current position analysis tab
            current_tab = ttk.Frame(notebook)
            notebook.add(current_tab, text="Current Position")
            
            # Text widget for current position
            current_text_frame = ttk.Frame(current_tab)
            current_text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            current_text = tk.Text(current_text_frame, wrap=tk.WORD, font=("Courier", 10))
            current_scrollbar = ttk.Scrollbar(current_text_frame, orient=tk.VERTICAL, command=current_text.yview)
            current_text.configure(yscrollcommand=current_scrollbar.set)
            
            current_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            current_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # Move comparison tab (if there's move history)
            if self.move_quality_history:
                comparison_tab = ttk.Frame(notebook)
                notebook.add(comparison_tab, text="Recent Moves")
                
                comparison_text_frame = ttk.Frame(comparison_tab)
                comparison_text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
                
                comparison_text = tk.Text(comparison_text_frame, wrap=tk.WORD, font=("Courier", 10))
                comparison_scrollbar = ttk.Scrollbar(comparison_text_frame, orient=tk.VERTICAL, command=comparison_text.yview)
                comparison_text.configure(yscrollcommand=comparison_scrollbar.set)
                
                comparison_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                comparison_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # Generate analysis in a separate thread
            def generate_analysis():
                try:
                    # Current position analysis
                    current_analysis = self.generate_current_position_analysis()
                    
                    # Recent moves analysis (if available)
                    if self.move_quality_history:
                        recent_analysis = self.generate_recent_moves_analysis()
                    
                    # Update UI in main thread
                    self.root.after(0, lambda: [
                        current_text.insert(1.0, current_analysis),
                        current_text.config(state=tk.DISABLED),
                        comparison_text.insert(1.0, recent_analysis) if self.move_quality_history else None,
                        comparison_text.config(state=tk.DISABLED) if self.move_quality_history else None
                    ])
                    
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to generate analysis: {e}"))
            
            # Show loading message
            current_text.insert(1.0, "Generating detailed analysis...\nThis may take a few moments.")
            if self.move_quality_history:
                comparison_text.insert(1.0, "Analyzing recent moves...")
            
            # Start analysis in background
            threading.Thread(target=generate_analysis, daemon=True).start()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to show detailed analysis: {e}")
    
    def generate_current_position_analysis(self):
        """Generate comprehensive analysis of the current position."""
        lines = []
        lines.append("=" * 90)
        lines.append("COMPREHENSIVE POSITION ANALYSIS")
        lines.append("=" * 90)
        lines.append("")
        
        # Basic position info
        turn = "White" if self.board.turn == chess.WHITE else "Black"
        lines.append(f"Turn to move: {turn}")
        lines.append(f"Move number: {len(self.game_history) + 1}")
        lines.append(f"Pieces on board: {len(self.board.piece_map())}")
        lines.append("")
        
        # Multi-depth evaluation
        lines.append("MULTI-DEPTH EVALUATION ANALYSIS:")
        lines.append("-" * 50)
        
        try:
            # Calculate evaluations at multiple depths
            short_eval, _ = self.bot.minimax(self.board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            medium_eval, _ = self.bot.minimax(self.board, depth=4, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            
            piece_count = len(self.board.piece_map())
            if piece_count <= 12:
                long_depth = 6
            elif piece_count <= 20:
                long_depth = 5
            else:
                long_depth = 4
            
            long_eval, _ = self.bot.minimax(self.board, depth=long_depth, alpha=float('-inf'), beta=float('inf'), maximizing_player=self.board.turn)
            
            # Normalize evaluations
            if hasattr(self, 'starting_eval_short'):
                short_norm = (short_eval - self.starting_eval_short) / 100.0
                medium_norm = (medium_eval - self.starting_eval_medium) / 100.0
                long_norm = (long_eval - self.starting_eval_long) / 100.0
            else:
                short_norm = short_eval / 100.0
                medium_norm = medium_eval / 100.0
                long_norm = long_eval / 100.0
            
            lines.append(f"Depth 2 (short-term):   {short_norm:+.2f}")
            lines.append(f"Depth 4 (medium-term):  {medium_norm:+.2f}")
            lines.append(f"Depth {long_depth} (long-term):     {long_norm:+.2f}")
            lines.append("")
            
            # Trend analysis
            short_to_medium = medium_norm - short_norm
            medium_to_long = long_norm - medium_norm
            overall_trend = long_norm - short_norm
            
            lines.append("EVALUATION TREND ANALYSIS:")
            lines.append(f"Short to medium change: {short_to_medium:+.2f}")
            lines.append(f"Medium to long change:  {medium_to_long:+.2f}")
            lines.append(f"Overall trend:          {overall_trend:+.2f}")
            lines.append("")
            
            # Trend interpretation
            if abs(overall_trend) > 0.5:
                if overall_trend > 0:
                    trend_desc = "Position IMPROVES significantly with deeper analysis"
                else:
                    trend_desc = "Position DETERIORATES significantly with deeper analysis"
            elif abs(overall_trend) > 0.2:
                if overall_trend > 0:
                    trend_desc = "Position shows modest improvement with deeper analysis"
                else:
                    trend_desc = "Position shows modest deterioration with deeper analysis"
            else:
                trend_desc = "Position remains stable across all search depths"
            
            lines.append(f"Trend interpretation: {trend_desc}")
            lines.append("")
            
            # Tactical vs positional assessment
            if abs(short_to_medium) > 0.3:
                lines.append("⚠️  TACTICAL ALERT: Significant evaluation change between depths 2-4")
                lines.append("   This suggests immediate tactical considerations are important.")
            elif abs(medium_to_long) > 0.3:
                lines.append("🔍 STRATEGIC FACTORS: Evaluation changes significantly at deeper depths")
                lines.append("   This suggests long-term positional factors are crucial.")
            else:
                lines.append("✅ STABLE POSITION: Evaluation is consistent across all depths")
            
            lines.append("")
            
        except Exception as e:
            lines.append(f"Error calculating multi-depth evaluation: {e}")
            lines.append("")
        
        # Game phase assessment
        lines.append("GAME PHASE ASSESSMENT:")
        lines.append("-" * 30)
        
        piece_count = len(self.board.piece_map())
        queens_on_board = len(self.board.pieces(chess.QUEEN, chess.WHITE)) + len(self.board.pieces(chess.QUEEN, chess.BLACK))
        
        if len(self.game_history) <= 15:
            phase = "Opening"
        elif piece_count <= 12 or queens_on_board == 0:
            phase = "Endgame"
        else:
            phase = "Middlegame"
        
        lines.append(f"Current phase: {phase}")
        lines.append(f"Pieces remaining: {piece_count}")
        lines.append(f"Queens on board: {queens_on_board}")
        lines.append("")
        
        # Position characteristics
        lines.append("POSITION CHARACTERISTICS:")
        lines.append("-" * 35)
        
        if self.board.is_check():
            lines.append("⚔️  King in check")
        if self.board.is_checkmate():
            lines.append("🏁 Checkmate")
        elif self.board.is_stalemate():
            lines.append("🤝 Stalemate")
        elif self.board.is_insufficient_material():
            lines.append("🤝 Insufficient material for checkmate")
        
        # Count legal moves
        legal_moves = list(self.board.legal_moves)
        lines.append(f"Legal moves available: {len(legal_moves)}")
        
        if len(legal_moves) < 5:
            lines.append("⚠️  Limited mobility")
        elif len(legal_moves) > 30:
            lines.append("✅ High mobility")
        
        lines.append("")
        
        # Material balance
        lines.append("MATERIAL ANALYSIS:")
        lines.append("-" * 25)
        
        white_material = 0
        black_material = 0
        
        for square in chess.SQUARES:
            piece = self.board.piece_at(square)
            if piece:
                value = self.bot.piece_values[piece.piece_type]
                if piece.color == chess.WHITE:
                    white_material += value
                else:
                    black_material += value
        
        material_diff = white_material - black_material
        lines.append(f"White material: {white_material/100:.1f} points")
        lines.append(f"Black material: {black_material/100:.1f} points")
        lines.append(f"Material balance: {material_diff/100:+.1f} (White perspective)")
        
        if abs(material_diff) >= 500:
            lines.append("💥 Significant material imbalance")
        elif abs(material_diff) >= 200:
            lines.append("⚖️  Moderate material imbalance")
        else:
            lines.append("⚖️  Material roughly equal")
        
        lines.append("")
        lines.append("=" * 90)
        
        return "\n".join(lines)
    
    def generate_recent_moves_analysis(self):
        """Generate analysis of recent moves with multi-depth data."""
        lines = []
        lines.append("=" * 90)
        lines.append("RECENT MOVES MULTI-DEPTH ANALYSIS")
        lines.append("=" * 90)
        lines.append("")
        
        # Show last 5 moves with detailed analysis
        recent_moves = self.move_quality_history[-10:]  # Last 10 moves (5 for each side)
        
        for move_info in recent_moves:
            move_num = move_info['move_number']
            color = move_info['player']
            
            lines.append(f"Move {(move_num + 1) // 2}{'. ' if color == 'White' else '... '}"
                        f"{move_info['move']} ({color})")
            
            # Enhanced quality assessment if available
            if 'quality_modifier' in move_info and move_info['quality_modifier']:
                quality_text = f"{move_info['quality']}{move_info['quality_modifier']}"
            else:
                quality_text = move_info['quality']
            
            lines.append(f"  Quality: {quality_text} {self.get_quality_symbol(move_info['quality'])}")
            
            # Multi-depth information if available
            if 'short_change' in move_info:
                lines.append(f"  Short-term change:  {move_info['short_change']:+.2f}")
                lines.append(f"  Medium-term change: {move_info['medium_change']:+.2f}")
                lines.append(f"  Long-term change:   {move_info['long_change']:+.2f}")
                
                # Highlight discrepancies
                short_long_diff = abs(move_info['long_change'] - move_info['short_change'])
                if short_long_diff > 1.0:
                    if move_info['short_change'] > 0 > move_info['long_change']:
                        lines.append("  ⚠️  WARNING: Move looks good short-term but bad long-term!")
                    elif move_info['short_change'] < 0 < move_info['long_change']:
                        lines.append("  💡 INSIGHT: Short-term sacrifice for long-term gain")
                    else:
                        lines.append("  🔍 NOTE: Significant evaluation difference between depths")
                
                # Special annotations
                if 'is_trap' in move_info and move_info['is_trap']:
                    lines.append("  🎭 TACTICAL TRAP: Looks good but has hidden problems")
                elif 'is_sacrifice' in move_info and move_info['is_sacrifice']:
                    lines.append("  ⚔️  POSITIONAL SACRIFICE: Short-term loss for long-term gain")
            
            lines.append("")
        
        lines.append("=" * 90)
        lines.append("ANALYSIS LEGEND:")
        lines.append("Short-term = 2 ply deep  |  Medium-term = 4 ply deep  |  Long-term = 4-6 ply deep")
        lines.append("Positive values favor White  |  Negative values favor Black")
        lines.append("=" * 90)
        
        return "\n".join(lines)

    def run(self):
        """Start the GUI application."""
        self.root.mainloop()

def main():
    """Main entry point for the GUI version."""
    try:
        app = ChessGUI()
        app.run()
    except Exception as e:
        print(f"Error starting GUI: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()