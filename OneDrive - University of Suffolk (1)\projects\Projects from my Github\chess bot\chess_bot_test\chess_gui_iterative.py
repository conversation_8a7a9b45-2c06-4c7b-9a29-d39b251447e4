import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import chess
import chess.pgn
from chess_bot_iterative import ChessBotIterative
import threading
import time
import io

class ChessGUIIterative:
    def __init__(self, root):
        self.root = root
        self.root.title("Chess Game with Iterative Deepening Bot")
        self.root.geometry("800x700")
        
        # Game state
        self.board = chess.Board()
        self.selected_square = None
        self.bot = ChessBotIterative(max_depth=6, time_limit=3.0, name="IterativeBot")
        self.player_color = chess.WHITE
        self.game_active = True
        self.bot_thinking = False
        
        # GUI components
        self.create_widgets()
        self.update_board_display()
        
        # If bot plays white, make first move
        if self.player_color == chess.BLACK:
            self.root.after(500, self.bot_move)
    
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Board frame
        board_frame = ttk.Frame(main_frame)
        board_frame.grid(row=0, column=0, padx=(0, 10))
        
        # Create chess board
        self.board_buttons = []
        for row in range(8):
            button_row = []
            for col in range(8):
                square = chess.square(col, 7-row)
                is_light = (row + col) % 2 == 0
                color = "#F0D9B5" if is_light else "#B58863"
                
                btn = tk.Button(board_frame, 
                               width=8, height=4, 
                               bg=color,
                               font=("Arial", 12, "bold"),
                               command=lambda s=square: self.on_square_click(s))
                btn.grid(row=row, column=col, padx=1, pady=1)
                button_row.append(btn)
            self.board_buttons.append(button_row)
        
        # Control panel
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Bot settings
        settings_frame = ttk.LabelFrame(control_frame, text="Bot Settings", padding="10")
        settings_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Time limit setting
        ttk.Label(settings_frame, text="Time Limit (seconds):").grid(row=0, column=0, sticky=tk.W)
        self.time_var = tk.StringVar(value="3.0")
        time_spinbox = ttk.Spinbox(settings_frame, from_=0.5, to=10.0, increment=0.5, 
                                  textvariable=self.time_var, width=10)
        time_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(5, 0))
        
        # Max depth setting
        ttk.Label(settings_frame, text="Max Depth:").grid(row=1, column=0, sticky=tk.W)
        self.depth_var = tk.StringVar(value="6")
        depth_spinbox = ttk.Spinbox(settings_frame, from_=3, to=10, increment=1, 
                                   textvariable=self.depth_var, width=10)
        depth_spinbox.grid(row=1, column=1, sticky=tk.W, padx=(5, 0))
        
        # Apply settings button
        ttk.Button(settings_frame, text="Apply Settings", 
                  command=self.apply_settings).grid(row=2, column=0, columnspan=2, pady=(10, 0))
        
        # Player color selection
        color_frame = ttk.LabelFrame(control_frame, text="Player Color", padding="10")
        color_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.color_var = tk.StringVar(value="white")
        ttk.Radiobutton(color_frame, text="White", variable=self.color_var, 
                       value="white", command=self.change_player_color).grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(color_frame, text="Black", variable=self.color_var, 
                       value="black", command=self.change_player_color).grid(row=0, column=1, sticky=tk.W)
        
        # Game controls
        game_frame = ttk.LabelFrame(control_frame, text="Game Controls", padding="10")
        game_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(game_frame, text="New Game", command=self.new_game).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(game_frame, text="Save Game", command=self.save_game).grid(row=1, column=0, sticky=(tk.W, tk.E))
        ttk.Button(game_frame, text="Load Game", command=self.load_game).grid(row=2, column=0, sticky=(tk.W, tk.E))
        
        # Bot status
        status_frame = ttk.LabelFrame(control_frame, text="Bot Status", padding="10")
        status_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame, text="Ready")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.progress_var = tk.StringVar()
        self.progress_label = ttk.Label(status_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=1, column=0, sticky=tk.W)
        
        # Search info
        info_frame = ttk.LabelFrame(control_frame, text="Search Information", padding="10")
        info_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.info_text = tk.Text(info_frame, height=8, width=30, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=scrollbar.set)
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Move history
        history_frame = ttk.LabelFrame(control_frame, text="Move History", padding="10")
        history_frame.grid(row=5, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.history_text = tk.Text(history_frame, height=6, width=30, wrap=tk.WORD)
        hist_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_text.yview)
        self.history_text.configure(yscrollcommand=hist_scrollbar.set)
        self.history_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        hist_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Configure grid weights
        main_frame.columnconfigure(1, weight=1)
        control_frame.rowconfigure(5, weight=1)
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(0, weight=1)
        history_frame.columnconfigure(0, weight=1)
        history_frame.rowconfigure(0, weight=1)
    
    def apply_settings(self):
        """Apply the bot settings."""
        try:
            time_limit = float(self.time_var.get())
            max_depth = int(self.depth_var.get())
            
            self.bot.set_time_limit(time_limit)
            self.bot.set_max_depth(max_depth)
            
            self.update_info_text(f"Settings applied: Time limit = {time_limit}s, Max depth = {max_depth}")
            
        except ValueError:
            messagebox.showerror("Error", "Invalid settings values")
    
    def change_player_color(self):
        """Change player color and restart game."""
        if self.bot_thinking:
            return
        
        self.player_color = chess.WHITE if self.color_var.get() == "white" else chess.BLACK
        self.new_game()
    
    def new_game(self):
        """Start a new game."""
        if self.bot_thinking:
            return
            
        self.board = chess.Board()
        self.selected_square = None
        self.game_active = True
        self.bot_thinking = False
        self.update_board_display()
        self.history_text.delete(1.0, tk.END)
        self.info_text.delete(1.0, tk.END)
        self.status_label.config(text="New game started")
        self.progress_var.set("")
        
        # If bot plays white, make first move
        if self.player_color == chess.BLACK:
            self.root.after(500, self.bot_move)
    
    def save_game(self):
        """Save current game to PGN file."""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".pgn",
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")]
            )
            if filename:
                game = chess.pgn.Game()
                game.setup(chess.Board())
                node = game
                
                # Replay moves to create PGN
                temp_board = chess.Board()
                for move in self.board.move_stack:
                    node = node.add_variation(move)
                    temp_board.push(move)
                
                with open(filename, 'w') as f:
                    print(game, file=f)
                
                messagebox.showinfo("Success", f"Game saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save game: {str(e)}")
    
    def load_game(self):
        """Load game from PGN file."""
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")]
            )
            if filename:
                with open(filename, 'r') as f:
                    game = chess.pgn.read_game(f)
                
                if game:
                    self.board = chess.Board()
                    for move in game.mainline_moves():
                        self.board.push(move)
                    
                    self.update_board_display()
                    self.update_move_history()
                    messagebox.showinfo("Success", f"Game loaded from {filename}")
                else:
                    messagebox.showerror("Error", "No valid game found in file")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load game: {str(e)}")
    
    def update_board_display(self):
        """Update the visual board display."""
        for row in range(8):
            for col in range(8):
                square = chess.square(col, 7-row)
                piece = self.board.piece_at(square)
                
                # Reset button color
                is_light = (row + col) % 2 == 0
                if square == self.selected_square:
                    color = "#FFD700"  # Gold for selected square
                else:
                    color = "#F0D9B5" if is_light else "#B58863"
                
                # Set piece symbol
                if piece:
                    symbol = self.get_piece_symbol(piece)
                else:
                    symbol = ""
                
                self.board_buttons[row][col].config(text=symbol, bg=color)
    
    def get_piece_symbol(self, piece):
        """Get Unicode symbol for chess piece."""
        symbols = {
            chess.PAWN: "♙♟", chess.ROOK: "♖♜", chess.KNIGHT: "♘♞",
            chess.BISHOP: "♗♝", chess.QUEEN: "♕♛", chess.KING: "♔♚"
        }
        return symbols[piece.piece_type][0 if piece.color == chess.WHITE else 1]
    
    def on_square_click(self, square):
        """Handle square click."""
        if not self.game_active or self.bot_thinking:
            return
        
        if self.board.turn != self.player_color:
            return
        
        if self.selected_square is None:
            # Select piece
            piece = self.board.piece_at(square)
            if piece and piece.color == self.player_color:
                self.selected_square = square
                self.update_board_display()
        else:
            # Make move
            move = chess.Move(self.selected_square, square)
            
            # Check for promotion
            if (self.board.piece_at(self.selected_square) and 
                self.board.piece_at(self.selected_square).piece_type == chess.PAWN and 
                chess.square_rank(square) in [0, 7]):
                move = chess.Move(self.selected_square, square, promotion=chess.QUEEN)
            
            if move in self.board.legal_moves:
                self.make_move(move)
                self.selected_square = None
                self.update_board_display()
                
                # Check game state
                if self.check_game_over():
                    return
                
                # Bot's turn
                self.root.after(100, self.bot_move)
            else:
                self.selected_square = None
                self.update_board_display()
    
    def make_move(self, move):
        """Make a move and update display."""
        self.board.push(move)
        self.update_move_history()
    
    def update_move_history(self):
        """Update the move history display."""
        self.history_text.delete(1.0, tk.END)
        moves = []
        temp_board = chess.Board()
        
        for i, move in enumerate(self.board.move_stack):
            if i % 2 == 0:
                move_number = i // 2 + 1
                moves.append(f"{move_number}. {temp_board.san(move)}")
            else:
                moves.append(f"{temp_board.san(move)}")
            temp_board.push(move)
        
        self.history_text.insert(tk.END, " ".join(moves))
        self.history_text.see(tk.END)
    
    def bot_move(self):
        """Make bot move in separate thread."""
        if not self.game_active or self.board.turn == self.player_color:
            return
        
        self.bot_thinking = True
        self.status_label.config(text="Bot thinking...")
        self.progress_var.set("Starting search...")
        
        # Start bot thinking in separate thread
        thread = threading.Thread(target=self.bot_think)
        thread.daemon = True
        thread.start()
    
    def bot_think(self):
        """Bot thinking process."""
        try:
            # Update search progress
            def update_progress():
                if self.bot_thinking:
                    elapsed = time.time() - start_time
                    info = self.bot.get_search_info()
                    progress_text = f"Time: {elapsed:.1f}s, Nodes: {info['nodes_evaluated']}"
                    self.root.after(0, lambda: self.progress_var.set(progress_text))
                    self.root.after(100, update_progress)
            
            start_time = time.time()
            self.root.after(100, update_progress)
            
            # Get bot move
            move = self.bot.get_best_move(self.board)
            
            # Update GUI in main thread
            self.root.after(0, lambda: self.bot_move_complete(move))
            
        except Exception as e:
            self.root.after(0, lambda: self.bot_error(str(e)))
    
    def bot_move_complete(self, move):
        """Handle bot move completion."""
        self.bot_thinking = False
        
        if move and move in self.board.legal_moves:
            self.make_move(move)
            self.update_board_display()
            
            # Display search information
            info = self.bot.get_search_info()
            info_text = f"Bot played: {move}\n"
            info_text += f"Nodes evaluated: {info['nodes_evaluated']}\n"
            info_text += f"Time limit: {info['time_limit']}s\n"
            info_text += f"Max depth: {info['max_depth']}\n"
            info_text += f"Time: {time.time() - self.bot.start_time:.2f}s\n"
            info_text += "-" * 30 + "\n"
            
            self.update_info_text(info_text)
            
            self.status_label.config(text="Bot move completed")
            self.progress_var.set("")
            
            # Check game state
            self.check_game_over()
        else:
            self.status_label.config(text="Bot failed to find move")
            self.progress_var.set("")
    
    def bot_error(self, error_msg):
        """Handle bot error."""
        self.bot_thinking = False
        self.status_label.config(text=f"Bot error: {error_msg}")
        self.progress_var.set("")
    
    def update_info_text(self, text):
        """Update info text display."""
        self.info_text.insert(tk.END, text)
        self.info_text.see(tk.END)
    
    def check_game_over(self):
        """Check if game is over."""
        if self.board.is_checkmate():
            winner = "White" if self.board.turn == chess.BLACK else "Black"
            self.status_label.config(text=f"Checkmate! {winner} wins!")
            self.game_active = False
            return True
        elif self.board.is_stalemate():
            self.status_label.config(text="Stalemate! It's a draw!")
            self.game_active = False
            return True
        elif self.board.is_insufficient_material():
            self.status_label.config(text="Draw by insufficient material!")
            self.game_active = False
            return True
        elif self.board.is_check():
            self.status_label.config(text="Check!")
        else:
            self.status_label.config(text="Game in progress")
        
        return False

if __name__ == "__main__":
    root = tk.Tk()
    app = ChessGUIIterative(root)
    root.mainloop()