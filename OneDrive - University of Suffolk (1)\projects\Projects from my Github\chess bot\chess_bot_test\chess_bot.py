import chess
import chess.engine
import random
from typing import Op<PERSON>, Tuple, List
import time

class ChessBot:
    """
    A chess bot that uses minimax algorithm with alpha-beta pruning
    to make intelligent moves.
    """
    
    def __init__(self, depth: int = 3, name: str = "ChessBot"):
        """
        Initialize the chess bot.
        
        Args:
            depth: Search depth for minimax algorithm
            name: Name of the bot
        """
        self.depth = depth
        self.name = name
        self.nodes_evaluated = 0
        
        # Piece values for evaluation
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
        
        # --- NEW: COMPLETE PIECE-SQUARE TABLES (PSTs) ---
        # These tables give bonuses or penalties to pieces based on their square.
        # The board is represented as a list of 64 squares, from A1 to H8.
        # All tables are from <PERSON>'s perspective. They are mirrored for Black.

        self.pawn_table = [
            0,  0,  0,  0,  0,  0,  0,  0,   # Rank 1: starting position
            5,  5,  5,  5,  5,  5,  5,  5,   # Rank 2: slight bonus for moving
            10, 10, 20, 30, 30, 20, 10, 10,  # Rank 3: encourage central control
            15, 15, 25, 35, 35, 25, 15, 15,  # Rank 4: good central advancement
            20, 20, 30, 40, 40, 30, 20, 20,  # Rank 5: strong central presence
            35, 35, 40, 50, 50, 40, 35, 35,  # Rank 6: approaching promotion
            50, 50, 60, 70, 70, 60, 50, 50,  # Rank 7: about to promote!
            0,  0,  0,  0,  0,  0,  0,  0    # Rank 8: promotion (shouldn't happen)
        ]
        
        self.knight_table = [
            -50,-40,-30,-30,-30,-30,-40,-50,
            -40,-20,  0,  0,  0,  0,-20,-40,
            -30,  0, 10, 15, 15, 10,  0,-30,
            -30,  5, 15, 20, 20, 15,  5,-30,
            -30,  0, 15, 20, 20, 15,  0,-30,
            -30,  5, 10, 15, 15, 10,  5,-30,
            -40,-20,  0,  5,  5,  0,-20,-40,
            -50,-40,-30,-30,-30,-30,-40,-50
        ]

        self.bishop_table = [
            -20,-10,-10,-10,-10,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5, 10, 10,  5,  0,-10,
            -10,  5,  5, 10, 10,  5,  5,-10,
            -10,  0, 10, 10, 10, 10,  0,-10,
            -10, 10, 10, 10, 10, 10, 10,-10,
            -10,  5,  0,  0,  0,  0,  5,-10,
            -20,-10,-10,-10,-10,-10,-10,-20
        ]

        self.rook_table = [
             0,  0,  0,  0,  0,  0,  0,  0,
             5, 10, 10, 10, 10, 10, 10,  5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
             0,  0,  0,  5,  5,  0,  0,  0
        ]

        self.queen_table = [
            -20,-10,-10, -5, -5,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5,  5,  5,  5,  0,-10,
             -5,  0,  5,  5,  5,  5,  0, -5,
              0,  0,  5,  5,  5,  5,  0, -5,
            -10,  5,  5,  5,  5,  5,  0,-10,
            -10,  0,  5,  0,  0,  0,  0,-10,
            -20,-10,-10, -5, -5,-10,-10,-20
        ]

        # King safety is critical. We need two tables: one for the middlegame
        # where the king should be castled, and one for the endgame where the
        # king should be active and centralized.
        self.king_mg_table = [
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -20,-30,-30,-40,-40,-30,-30,-20,
            -10,-20,-20,-20,-20,-20,-20,-10,
             20, 20,  0,  0,  0,  0, 20, 20,
             20, 30, 10,  0,  0, 10, 30, 20
        ]
        
        self.king_eg_table = [
            -50,-40,-30,-20,-20,-30,-40,-50,
            -30,-20,-10,  0,  0,-10,-20,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-30,  0,  0,  0,  0,-30,-30,
            -50,-30,-30,-30,-30,-30,-30,-50
        ]

    def _is_endgame(self, board: chess.Board) -> bool:
        """
        A simple heuristic to determine if the game is in an endgame phase.
        This is typically when queens are off the board or material is low.
        """
        # No queens on the board is a strong indicator of endgame.
        if not board.pieces(chess.QUEEN, chess.WHITE) and not board.pieces(chess.QUEEN, chess.BLACK):
            return True
        
        # Check if each side with a queen has less than two minor pieces.
        white_material = sum(len(board.pieces(pt, chess.WHITE)) for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])
        black_material = sum(len(board.pieces(pt, chess.BLACK)) for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])

        if board.pieces(chess.QUEEN, chess.WHITE) and white_material < 2:
            return True
        if board.pieces(chess.QUEEN, chess.BLACK) and black_material < 2:
            return True
            
        return False

    def evaluate_board(self, board: chess.Board) -> int:
        """
        Evaluate the current board position.
        
        Args:
            board: Chess board to evaluate
            
        Returns:
            Evaluation score (positive for white advantage, negative for black)
        """
        if board.is_checkmate():
            # If the current player is checkmated, it's a win for the other side.
            return -20000 if board.turn == chess.WHITE else 20000
        
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        
        # --- NEW: Determine if it's the endgame to select the correct King PST ---
        is_endgame = self._is_endgame(board)

        # Material and positional evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = self.piece_values[piece.piece_type]
                
                # --- UPDATED: Add positional bonuses for ALL pieces ---
                if piece.piece_type == chess.PAWN:
                    table = self.pawn_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.KNIGHT:
                    table = self.knight_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.BISHOP:
                    table = self.bishop_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.ROOK:
                    table = self.rook_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.QUEEN:
                    table = self.queen_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.KING:
                    # Use the correct table based on game phase
                    table = self.king_eg_table if is_endgame else self.king_mg_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                
                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value

        # Mobility calculation (remains the same)
        original_turn = board.turn
        board.turn = chess.WHITE
        white_mobility = board.legal_moves.count()
        board.turn = chess.BLACK
        black_mobility = board.legal_moves.count()
        board.turn = original_turn

        mobility_score = (white_mobility - black_mobility) * 2 # Weight of 2
        score += mobility_score
        
        return score
    
    def minimax(self, board: chess.Board, depth: int, alpha: int, beta: int, 
                maximizing_player: bool) -> Tuple[int, Optional[chess.Move]]:
        """
        Minimax algorithm with alpha-beta pruning.
        
        Args:
            board: Current board position
            depth: Remaining search depth
            alpha: Alpha value for pruning
            beta: Beta value for pruning
            maximizing_player: True if maximizing player's turn
            
        Returns:
            Tuple of (evaluation_score, best_move)
        """
        self.nodes_evaluated += 1
        
        if depth == 0 or board.is_game_over():
            return self.evaluate_board(board), None
        
        best_move = None
        
        # Simple move ordering: captures first, then others
        moves = sorted(list(board.legal_moves), key=board.is_capture, reverse=True)

        if maximizing_player:
            max_eval = float('-inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            return max_eval, best_move
        else: # Minimizing player
            min_eval = float('inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            return min_eval, best_move
    
    def get_best_move(self, board: chess.Board) -> chess.Move:
        """
        Get the best move for the current position.
        
        Args:
            board: Current board position
            
        Returns:
            Best move according to the bot's evaluation
        """
        self.nodes_evaluated = 0
        start_time = time.time()
        
        # Use minimax to find the best move
        maximizing = board.turn == chess.WHITE
        _, best_move = self.minimax(board, self.depth, float('-inf'), float('inf'), maximizing)
        
        end_time = time.time()
        
        if best_move is None:
            # Fallback to random move if no move found
            try:
                best_move = random.choice(list(board.legal_moves))
            except IndexError:
                return None # No legal moves
        
        print(f"{self.name} evaluated {self.nodes_evaluated} positions in {end_time - start_time:.2f} seconds")
        
        return best_move
    
    def set_depth(self, depth: int):
        """Set the search depth for the bot."""
        self.depth = depth
        print(f"{self.name} search depth set to {depth}")