"""
Demonstration of Iterative Deepening Benefits in Chess AI

This script demonstrates the key advantages of iterative deepening:
1. Time management
2. Progressive search refinement
3. Better move ordering from previous iterations
4. Anytime algorithm behavior
"""

import chess
import time
from chess_bot import ChessBot
from chess_bot_iterative import ChessBotIterative

def compare_search_strategies():
    """Compare regular minimax vs iterative deepening."""
    print("=== ITERATIVE DEEPENING DEMONSTRATION ===\n")
    
    # Test positions
    positions = [
        ("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1", "Opening Position"),
        ("r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 1", "Italian Game"),
        ("rnbqkb1r/pp1ppppp/5n2/2p5/2B1P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 1", "Italian Game Variation"),
        ("8/8/8/8/8/3k4/3P4/3K4 w - - 0 1", "King and Pawn Endgame"),
    ]
    
    for fen, description in positions:
        print(f"\n--- {description} ---")
        board = chess.Board(fen)
        
        # Test different time controls
        time_controls = [1.0, 2.0, 3.0]
        
        for time_limit in time_controls:
            print(f"\nTime Control: {time_limit}s")
            
            # Regular bot (fixed depth)
            regular_bot = ChessBot(depth=4, name="RegularBot")
            
            # Iterative deepening bot
            iterative_bot = ChessBotIterative(max_depth=8, time_limit=time_limit, name="IterativeBot")
            
            # Test regular bot
            start_time = time.time()
            regular_move = regular_bot.get_best_move(board)
            regular_time = time.time() - start_time
            
            # Test iterative bot
            start_time = time.time()
            iterative_move = iterative_bot.get_best_move(board)
            iterative_time = time.time() - start_time
            
            print(f"  Regular Bot:")
            print(f"    Move: {regular_move}")
            print(f"    Time: {regular_time:.2f}s")
            print(f"    Nodes: {regular_bot.nodes_evaluated}")
            print(f"    Depth: {regular_bot.depth}")
            
            iterative_info = iterative_bot.get_search_info()
            print(f"  Iterative Bot:")
            print(f"    Move: {iterative_move}")
            print(f"    Time: {iterative_time:.2f}s")
            print(f"    Nodes: {iterative_info['nodes_evaluated']}")
            print(f"    Max Depth: {iterative_info['max_depth']}")
            
            # Analysis
            time_efficiency = abs(iterative_time - time_limit) / time_limit
            print(f"  Analysis:")
            print(f"    Time efficiency: {(1-time_efficiency)*100:.1f}%")
            print(f"    Same move: {'Yes' if regular_move == iterative_move else 'No'}")
            
            if regular_move != iterative_move:
                print(f"    Regular: {regular_move}, Iterative: {iterative_move}")

def demonstrate_anytime_behavior():
    """Demonstrate anytime algorithm behavior."""
    print("\n\n=== ANYTIME ALGORITHM DEMONSTRATION ===\n")
    print("This shows how iterative deepening provides increasingly better moves")
    print("as more time is available, making it an 'anytime' algorithm.\n")
    
    board = chess.Board("r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 1")
    print("Position: Italian Game")
    print(board)
    
    # Test different time allocations
    time_allocations = [0.5, 1.0, 2.0, 3.0, 5.0]
    
    print("\nResults with different time allocations:")
    print("Time | Move      | Nodes  | Depth Reached")
    print("-----|-----------|--------|---------------")
    
    for time_limit in time_allocations:
        bot = ChessBotIterative(max_depth=10, time_limit=time_limit, name="AnyTimeBot")
        
        start_time = time.time()
        move = bot.get_best_move(board)
        actual_time = time.time() - start_time
        
        info = bot.get_search_info()
        print(f"{time_limit:4.1f}s | {str(move):9} | {info['nodes_evaluated']:6} | Variable")

def demonstrate_progressive_refinement():
    """Show how iterative deepening refines the search progressively."""
    print("\n\n=== PROGRESSIVE REFINEMENT DEMONSTRATION ===\n")
    print("This shows how the best move can change as search depth increases,")
    print("demonstrating the progressive refinement of iterative deepening.\n")
    
    # Use a tactical position where deeper search might find better moves
    board = chess.Board("r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 1")
    
    print("Position: Italian Game (tactical position)")
    print(board)
    print()
    
    # Create a custom bot that shows intermediate results
    class VerboseIterativeBot(ChessBotIterative):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.depth_moves = {}
        
        def minimax(self, board, depth, alpha, beta, maximizing_player, current_depth=0):
            result = super().minimax(board, depth, alpha, beta, maximizing_player, current_depth)
            
            # Store the best move for each depth at the root level
            if current_depth == 0 and result[1] is not None:
                remaining_depth = depth
                if remaining_depth not in self.depth_moves:
                    self.depth_moves[remaining_depth] = result[1]
            
            return result
        
        def get_depth_progression(self):
            return self.depth_moves
    
    # Run the demonstration
    bot = VerboseIterativeBot(max_depth=6, time_limit=5.0, name="ProgressiveBot")
    
    print("Running search with progressive refinement...")
    move = bot.get_best_move(board)
    
    print(f"\nFinal best move: {move}")
    print(f"Total nodes evaluated: {bot.get_search_info()['nodes_evaluated']}")

def main():
    """Main demonstration function."""
    print("ITERATIVE DEEPENING CHESS AI DEMONSTRATION")
    print("==========================================")
    print()
    print("This demonstration shows the key benefits of iterative deepening:")
    print("1. Precise time management")
    print("2. Anytime algorithm behavior")
    print("3. Progressive search refinement")
    print("4. Better move ordering from previous iterations")
    print()
    
    try:
        compare_search_strategies()
        demonstrate_anytime_behavior()
        demonstrate_progressive_refinement()
        
        print("\n\n=== SUMMARY ===")
        print("Iterative deepening provides:")
        print("• Better time management (stops exactly at time limit)")
        print("• Anytime behavior (always has a move ready)")
        print("• Progressive improvement (deeper search = better moves)")
        print("• Efficient search (good move ordering from previous iterations)")
        print("• Flexibility (can adjust time vs. strength trade-off)")
        
    except KeyboardInterrupt:
        print("\n\nDemonstration interrupted by user.")
    except Exception as e:
        print(f"\nError during demonstration: {e}")

if __name__ == "__main__":
    main()