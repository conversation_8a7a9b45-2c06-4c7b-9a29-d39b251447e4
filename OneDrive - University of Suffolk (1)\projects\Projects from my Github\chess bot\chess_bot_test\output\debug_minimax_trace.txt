PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test>                       python debug_minimax_trace.py
Tracing Minimax Search
==================================================
Position (Black to move):
r . b q k b n r
p p p . . p p p
. . . . p . . .
. . . p N . . .
. n . P . . . .
P . N . . . . .
. P P . P P P P
R . B Q K B . R
Turn: Black

Testing key moves with depth 1:
----------------------------------------
Move: Na6
  After move, turn: Black
  Maximizing: True
  Eval from minimax: 158
  Direct eval: 377.0

Move: Nxc2+
  After move, turn: Black
  Maximizing: True
  Eval from minimax: -306
  Direct eval: 377.0

Move: Qg5
  After move, turn: Black
  Maximizing: True
  Eval from minimax: 2516.0
  Direct eval: 377.0

Move: Qd6
  After move, turn: Black
  Maximizing: True
  Eval from minimax: 519.0
  Direct eval: 377.0

==================================================
FULL MINIMAX SEARCH
==================================================
Root position: Black to move, maximizing = False
Best move: Nd3+
Eval score: -340

Manual trace of top-level moves:
----------------------------------------
  Ne7: 820
  Nh6: 605.0
  Nf6: 488.0
  Be7: 528.0
  Bd6: 509.0
  Bc5: 1256.0
  Ke7: 887
  Qe7: 536.0
  Qd7: 1622.0
  Qf6: 513.0

Best moves for Black (lowest eval):
  Nf6: 488.0
  Bd6: 509.0
  Qf6: 513.0
  Be7: 528.0
  Qe7: 536.0

Worst moves for Black (highest eval):
  Nh6: 605.0
  Ne7: 820
  Ke7: 887
  Bc5: 1256.0
  Qd7: 1622.0