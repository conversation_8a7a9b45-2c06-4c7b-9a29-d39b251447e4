PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_rook_behavior.py
Current position:
r . b q k b r .
p p p p p p . p
. . . . . p . .
. . . P . . . .
. . B . . P . .
. . P . . . P .
R P . . . . . P
. N . Q K . N R
FEN: r1bqkbr1/pppppp1p/5p2/3P4/2B2P2/2P3P1/RP5P/1N1QK1NR b Kq - 0 11

Testing bot behavior:

=== Bot with Transposition Table ===
Bot_with_TT evaluated 1640 positions in 1.34 seconds (TT: 5.1% hit rate, 1556 entries)
Chosen move: g8g7
Bot_with_TT evaluated 3170 positions in 2.09 seconds (TT: 18.4% hit rate, 3825 entries)
Next move: g1f3

=== Bot without Transposition Table ===
Bot_without_TT evaluated 1640 positions in 0.98 seconds
Chosen move: g8g7

Move analysis:
Move 1 is rook shuffle: False
Move 3 is rook shuffle: False

Position evaluation:
Bot with TT evaluation: -242
Bot without TT evaluation: -242

Transposition table stats: {'size': 3825, 'max_size': 1000000, 'hits': 887, 'misses': 3923, 'hit_rate': 18.440748440748443}

=== Alternative Move Analysis ===
Legal moves in current position:
Top 10 moves by evaluation:
 1. g8g3 (r) - Score: -242
 2. g8g4 (r) - Score: -131
 3. g8g7 (r) - Score: -117
 4. g8g6 (r) - Score: -117
 5. g8g5 (r) - Score: -115
 6. f8g7 (b) - Score: -102
 7. f8h6 (b) - Score: -102
 8. a8b8 (r) - Score:  -97
 9. f6f5 (p) - Score:  -94
10. g8h8 (r) - Score:  -87