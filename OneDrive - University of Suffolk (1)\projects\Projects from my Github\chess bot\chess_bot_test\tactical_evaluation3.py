#!/usr/bin/env python3
"""
Enhanced tactical evaluation system for chess bot.
Provides comprehensive analysis of tactical threats, blunders, and move quality.
"""

import chess
from typing import Dict, List, Tuple, Optional, Set
from enum import Enum

class ThreatType(Enum):
    """Types of tactical threats."""
    HANGING_PIECE = "hanging_piece"
    UNDEFENDED_ATTACK = "undefended_attack"
    OVERLOADED_DEFENDER = "overloaded_defender" 
    PINNED_PIECE = "pinned_piece"
    FORK = "fork"
    SKEWER = "skewer"
    DISCOVERED_ATTACK = "discovered_attack"
    BACK_RANK_MATE = "back_rank_mate"
    TACTICAL_SHOT = "tactical_shot"

class MoveQuality(Enum):
    """Move quality classifications."""
    BRILLIANT = "Brilliant"
    EXCELLENT = "Excellent"
    GOOD = "Good"
    OKAY = "Okay"
    INACCURATE = "Inaccurate"
    POOR = "Poor"
    BLUNDER = "Blunder"

class TacticalEvaluator:
    """Enhanced tactical evaluation and threat detection."""
    
    def __init__(self):
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
    
    def evaluate_threats(self, board: chess.Board) -> Dict[chess.Color, List[Dict]]:
        """
        Comprehensive threat evaluation for both sides.
        """
        threats = {chess.WHITE: [], chess.BLACK: []}
        
        for color in [chess.WHITE, chess.BLACK]:
            threats[color].extend(self._find_hanging_pieces(board, color))
            threats[color].extend(self._find_undefended_attacks(board, color))
            threats[color].extend(self._find_overloaded_defenders(board, color))
            threats[color].extend(self._find_pinned_pieces(board, color))
            threats[color].extend(self._find_tactical_shots(board, color))
            threats[color].extend(self._find_back_rank_threats(board, color))
        
        return threats
    
    def _find_hanging_pieces(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        """Find pieces that are hanging (undefended and attacked)."""
        hanging = []
        
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece and piece.color == color:
                if piece.piece_type == chess.PAWN:
                    continue
                
                if board.is_attacked_by(not color, square):
                    attackers = board.attackers(not color, square)
                    defenders = board.attackers(color, square)
                    
                    if len(attackers) > 0:
                        attacker_values = [self.piece_values[board.piece_at(sq).piece_type] 
                                         for sq in attackers if board.piece_at(sq)]
                        
                        min_attacker = min(attacker_values) if attacker_values else 0
                        
                        if len(defenders) == 0 or min_attacker < self.piece_values[piece.piece_type]:
                            hanging.append({
                                'type': ThreatType.HANGING_PIECE,
                                'square': square,
                                'piece': piece,
                                'description': f"{piece.symbol().upper()} on {chess.square_name(square)} is hanging"
                            })
        
        return hanging
    
    def _find_undefended_attacks(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        """Find pieces that can attack undefended enemy pieces."""
        attacks = []
        opponent_color = not color
        
        for from_square in chess.SQUARES:
            piece = board.piece_at(from_square)
            if piece and piece.color == color:
                attacked_squares = board.attacks(from_square)
                
                for to_square in attacked_squares:
                    target = board.piece_at(to_square)
                    if target and target.color == opponent_color:
                        defenders = board.attackers(opponent_color, to_square)
                        
                        if len(defenders) == 0:
                            attacks.append({
                                'type': ThreatType.UNDEFENDED_ATTACK,
                                'from_square': from_square,
                                'to_square': to_square,
                                'attacker': piece,
                                'target': target,
                                'description': f"{piece.symbol().upper()} can capture undefended {target.symbol().upper()}"
                            })
        
        return attacks
    
    def _find_overloaded_defenders(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        """Find pieces that are defending multiple pieces and could be overloaded."""
        overloaded = []
        opponent_color = not color
        
        for defender_square in chess.SQUARES:
            defender = board.piece_at(defender_square)
            if defender and defender.color == opponent_color:
                defended_pieces = []
                
                for protected_square in chess.SQUARES:
                    protected_piece = board.piece_at(protected_square)
                    if (protected_piece and protected_piece.color == opponent_color and 
                        protected_square != defender_square):
                        
                        if defender_square in board.attackers(opponent_color, protected_square):
                            if board.is_attacked_by(color, protected_square):
                                defended_pieces.append((protected_square, protected_piece))
                
                if len(defended_pieces) >= 2:
                    overloaded.append({
                        'type': ThreatType.OVERLOADED_DEFENDER,
                        'defender_square': defender_square,
                        'defender': defender,
                        'defended_pieces': defended_pieces,
                        'description': f"{defender.symbol().upper()} is overloaded defending {len(defended_pieces)} pieces"
                    })
        
        return overloaded
    
    def _find_pinned_pieces(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        """Find pieces that are pinned."""
        pinned = []
        
        king_square = board.king(color)
        if king_square is None:
            return pinned
        
        for pinner_square in board.attackers(not color, king_square):
            if board.is_pin(color, pinner_square):
                # Find the pinned piece
                for pinned_square in chess.SQUARES_BETWEEN[king_square][pinner_square]:
                    piece = board.piece_at(pinned_square)
                    if piece and piece.color == color:
                        pinner = board.piece_at(pinner_square)
                        pinned.append({
                            'type': ThreatType.PINNED_PIECE,
                            'pinned_square': pinned_square,
                            'pinned_piece': piece,
                            'pinner_square': pinner_square,
                            'pinner': pinner,
                            'description': f"{piece.symbol().upper()} is pinned by {pinner.symbol().upper()}"
                        })
                        break
        return pinned

    def _find_tactical_shots(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        """Find tactical opportunities like forks, skewers, etc."""
        tactical_shots = []
        
        for move in board.legal_moves:
            if board.turn == color:
                temp_board = board.copy()
                temp_board.push(move)
                
                if temp_board.is_check():
                    attacked_squares = temp_board.checkers()
                    if len(attacked_squares) > 1:
                         tactical_shots.append({
                            'type': ThreatType.TACTICAL_SHOT, 'description': "Double check opportunity"
                        })
                # Basic fork check
                moving_piece = board.piece_at(move.from_square)
                if moving_piece and moving_piece.piece_type == chess.KNIGHT:
                    attacked_after_move = temp_board.attacks(move.to_square)
                    valuable_targets = []
                    for sq in attacked_after_move:
                        target = temp_board.piece_at(sq)
                        if target and target.color != color and target.piece_type in [chess.QUEEN, chess.ROOK]:
                            valuable_targets.append(target)
                    if len(valuable_targets) >= 2:
                        tactical_shots.append({
                             'type': ThreatType.FORK, 'description': "Knight fork opportunity"
                        })

        return tactical_shots
    
    def _find_back_rank_threats(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        """Find back rank mate threats."""
        threats = []
        opponent_color = not color
        
        king_square = board.king(opponent_color)
        if king_square is None:
            return threats
        
        back_rank = 0 if opponent_color == chess.WHITE else 7
        king_rank = chess.square_rank(king_square)

        if king_rank == back_rank:
            for file in range(8):
                square = chess.square(file, back_rank)
                if board.is_attacked_by(color, square):
                     # Check if it's a rook or queen attacking
                    for attacker_sq in board.attackers(color, square):
                        attacker = board.piece_at(attacker_sq)
                        if attacker and attacker.piece_type in [chess.ROOK, chess.QUEEN]:
                            # Check if the king can escape
                            temp_board = board.copy()
                            try:
                                temp_board.push(chess.Move(attacker_sq, square))
                                if temp_board.is_checkmate():
                                    threats.append({
                                        'type': ThreatType.BACK_RANK_MATE,
                                        'description': f"Back rank mate threat with {attacker.symbol().upper()}"
                                    })
                            except: # Illegal move if piece is pinned etc.
                                continue
        return threats

    def evaluate_move_quality(self, board_before: chess.Board, move: chess.Move, 
                            eval_before: int, eval_after: int, move_number: int) -> Tuple[MoveQuality, str]:
        """
        Optimized move quality evaluation with better performance and calibration.
        """
        eval_change = eval_after - eval_before
        player_color = board_before.turn
        
        if player_color == chess.BLACK:
            eval_change = -eval_change
        
        explanation_parts = []
        
        eval_change = max(-1000, min(1000, eval_change))

        # Check for brilliant moves (significant, unexpected sacrifice for large gain)
        is_sacrifice = False
        moving_piece = board_before.piece_at(move.from_square)
        captured_piece = board_before.piece_at(move.to_square)
        if moving_piece and captured_piece:
            if self.piece_values[moving_piece.piece_type] > self.piece_values[captured_piece.piece_type]:
                is_sacrifice = True
        
        # *** KEY CHANGE: Removed numerical values from text explanations ***
        if is_sacrifice and eval_change > 250:
            quality = MoveQuality.BRILLIANT
            explanation_parts.append("A brilliant sacrifice that creates a winning advantage")
        elif eval_change > 150:
            quality = MoveQuality.EXCELLENT
            explanation_parts.append("An excellent move, creating a strong advantage")
        elif eval_change > 75:
            quality = MoveQuality.GOOD
            explanation_parts.append("A good move that improves the position")
        elif eval_change > -50:
            quality = MoveQuality.OKAY
            explanation_parts.append("A solid and reasonable move")
        elif eval_change > -150:
            quality = MoveQuality.INACCURATE
            explanation_parts.append("An inaccuracy, as a better move was available")
        elif eval_change > -300:
            quality = MoveQuality.POOR
            explanation_parts.append("A poor move that significantly worsens the position")
        else:
            quality = MoveQuality.BLUNDER
            explanation_parts.append("A blunder that makes the position much worse")

        # Check for specific tactical blunders
        board_after = board_before.copy()
        board_after.push(move)
        if quality in [MoveQuality.POOR, MoveQuality.BLUNDER]:
            if board_after.is_checkmate():
                 explanation_parts.append("This move leads to checkmate.")
            else:
                hanging = self._find_hanging_pieces(board_after, player_color)
                if hanging:
                    piece_name = chess.piece_name(hanging[0]['piece'].piece_type)
                    explanation_parts.append(f"It leaves a {piece_name} undefended.")

        explanation = ". ".join(explanation_parts)
        return quality, explanation