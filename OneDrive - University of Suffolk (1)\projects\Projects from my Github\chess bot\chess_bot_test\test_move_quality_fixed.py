#!/usr/bin/env python3
"""
Test script to verify move quality ratings are working correctly
with the improved evaluation system.
"""

import chess
from unified_chess_bot import UnifiedChessBot

def test_move_quality():
    """Test move quality evaluation with different modes."""
    
    print("=== Testing Move Quality Evaluation ===\n")
    
    # Create bot with different evaluation modes
    bot = UnifiedChessBot(depth=3, name="TestBot")
    
    # Test cases: (position_fen, move_uci, expected_quality_range)
    test_cases = [
        {
            "name": "Opening move d4",
            "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
            "move": "d2d4",
            "expected": ["Normal", "Good"]  # Should be reasonable opening move
        },
        {
            "name": "Good tactical move",
            "fen": "rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3",
            "move": "d5e6",  # dxe6, good pawn break
            "expected": ["Good", "Excellent"]
        },
        {
            "name": "Blunder move",
            "fen": "rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2",
            "move": "d1h5",  # Early queen out, should be blunder
            "expected": ["Blunder", "Poor"]  # Blunder is correct for this move
        }
    ]
    
    # Test with different evaluation modes
    modes = [
        ("Static", True, 0),
        ("Shallow Search", True, 1),
        ("Full Search", False, 2)
    ]
    
    for mode_name, lightweight, depth in modes:
        print(f"=== Testing with {mode_name} Mode ===")
        bot.set_human_evaluation_mode(lightweight=lightweight, depth=depth)
        
        for test_case in test_cases:
            board = chess.Board(test_case["fen"])
            move = chess.Move.from_uci(test_case["move"])
            
            print(f"\nTest: {test_case['name']}")
            print(f"Position: {test_case['fen']}")
            print(f"Move: {test_case['move']}")
            
            # Record move quality
            bot._record_move_quality(
                board, move, 
                search_score=0, 
                static_score=bot.get_evaluation(board),
                depth_used=0, 
                time_taken=0.0, 
                move_type="Human"
            )
            
            # Get the last recorded move quality
            if bot.move_quality_history:
                last_move = bot.move_quality_history[-1]
                quality = last_move['quality']
                score_diff = last_move['score_difference']
                
                print(f"Quality: {quality}")
                print(f"Score difference: {score_diff}")
                
                # Check if quality is in expected range
                if quality in test_case['expected']:
                    print("✓ PASS - Quality rating is as expected")
                else:
                    print(f"✗ FAIL - Expected {test_case['expected']}, got {quality}")
            else:
                print("✗ FAIL - No move quality recorded")
        
        print("\n" + "="*50)
        bot.clear_move_quality_history()

if __name__ == "__main__":
    test_move_quality()