#!/usr/bin/env python3
"""
Enhanced tactical evaluation system for chess bot.
Provides comprehensive analysis of tactical threats, blunders, and move quality.
"""

import chess
from typing import Dict, List, Tuple, Optional, Set
from enum import Enum

class ThreatType(Enum):
    HANGING_PIECE = "hanging_piece"
    UNDEFENDED_ATTACK = "undefended_attack"
    OVERLOADED_DEFENDER = "overloaded_defender" 
    PINNED_PIECE = "pinned_piece"
    FORK = "fork"
    SKEWER = "skewer"
    DISCOVERED_ATTACK = "discovered_attack"
    BACK_RANK_MATE = "back_rank_mate"
    TACTICAL_SHOT = "tactical_shot"

class MoveQuality(Enum):
    BRILLIANT = "Brilliant"
    EXCELLENT = "Excellent"
    GOOD = "Good"
    OKAY = "Okay"
    INACCURATE = "Inaccurate"
    POOR = "Poor"
    BLUNDER = "Blunder"

class TacticalEvaluator:
    def __init__(self):
        self.piece_values = {
            chess.PAWN: 100, chess.KNIGHT: 320, chess.BISHOP: 330,
            chess.ROOK: 500, chess.QUEEN: 900, chess.KING: 20000
        }
    
    # Other methods like evaluate_threats, etc. are unchanged and included for completeness.
    
    def evaluate_threats(self, board: chess.Board) -> Dict[chess.Color, List[Dict]]:
        threats = {chess.WHITE: [], chess.BLACK: []}
        for color in [chess.WHITE, chess.BLACK]:
            threats[color].extend(self._find_hanging_pieces(board, color))
            threats[color].extend(self._find_undefended_attacks(board, color))
            threats[color].extend(self._find_overloaded_defenders(board, color))
            threats[color].extend(self._find_pinned_pieces(board, color))
            threats[color].extend(self._find_tactical_shots(board, color))
            threats[color].extend(self._find_back_rank_threats(board, color))
        return threats
    
    def _find_hanging_pieces(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        hanging = []
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece and piece.color == color and piece.piece_type != chess.PAWN:
                if board.is_attacked_by(not color, square):
                    attackers = board.attackers(not color, square)
                    defenders = board.attackers(color, square)
                    if len(attackers) > 0:
                        attacker_values = [self.piece_values[board.piece_at(sq).piece_type] for sq in attackers if board.piece_at(sq)]
                        min_attacker = min(attacker_values) if attacker_values else 0
                        if len(defenders) == 0 or min_attacker < self.piece_values[piece.piece_type]:
                            hanging.append({'type': ThreatType.HANGING_PIECE, 'square': square, 'piece': piece, 'description': f"{piece.symbol().upper()} on {chess.square_name(square)} is hanging"})
        return hanging
    
    def _find_undefended_attacks(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        attacks = []
        opponent_color = not color
        for from_square in chess.SQUARES:
            piece = board.piece_at(from_square)
            if piece and piece.color == color:
                for to_square in board.attacks(from_square):
                    target = board.piece_at(to_square)
                    if target and target.color == opponent_color and not board.attackers(opponent_color, to_square):
                        attacks.append({'type': ThreatType.UNDEFENDED_ATTACK, 'from_square': from_square, 'to_square': to_square, 'attacker': piece, 'target': target, 'description': f"{piece.symbol().upper()} can capture undefended {target.symbol().upper()}"})
        return attacks
    
    def _find_overloaded_defenders(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        overloaded = []
        opponent_color = not color
        for defender_square in chess.SQUARES:
            defender = board.piece_at(defender_square)
            if defender and defender.color == opponent_color:
                defended_pieces = []
                for protected_square in chess.SQUARES:
                    protected_piece = board.piece_at(protected_square)
                    if protected_piece and protected_piece.color == opponent_color and protected_square != defender_square and defender_square in board.attackers(opponent_color, protected_square) and board.is_attacked_by(color, protected_square):
                        defended_pieces.append((protected_square, protected_piece))
                if len(defended_pieces) >= 2:
                    overloaded.append({'type': ThreatType.OVERLOADED_DEFENDER, 'defender_square': defender_square, 'defender': defender, 'defended_pieces': defended_pieces, 'description': f"{defender.symbol().upper()} is overloaded defending {len(defended_pieces)} pieces"})
        return overloaded
    
    def _find_pinned_pieces(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        pinned = []
        king_square = board.king(color)
        if king_square is None: return pinned
        for pinner_square in board.attackers(not color, king_square):
            if board.is_pin(color, pinner_square):
                for pinned_square in chess.SQUARES_BETWEEN[king_square][pinner_square]:
                    piece = board.piece_at(pinned_square)
                    if piece and piece.color == color:
                        pinner = board.piece_at(pinner_square)
                        pinned.append({'type': ThreatType.PINNED_PIECE, 'pinned_square': pinned_square, 'pinned_piece': piece, 'pinner_square': pinner_square, 'pinner': pinner, 'description': f"{piece.symbol().upper()} is pinned by {pinner.symbol().upper()}"})
                        break
        return pinned

    def _find_tactical_shots(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        tactical_shots = []
        for move in board.legal_moves:
            if board.turn == color:
                temp_board = board.copy()
                temp_board.push(move)
                if temp_board.is_check() and len(temp_board.checkers()) > 1:
                    tactical_shots.append({'type': ThreatType.TACTICAL_SHOT, 'description': "Double check opportunity"})
                moving_piece = board.piece_at(move.from_square)
                if moving_piece and moving_piece.piece_type == chess.KNIGHT:
                    valuable_targets = [temp_board.piece_at(sq) for sq in temp_board.attacks(move.to_square) if temp_board.piece_at(sq) and temp_board.piece_at(sq).color != color and temp_board.piece_at(sq).piece_type in [chess.QUEEN, chess.ROOK]]
                    if len(valuable_targets) >= 2:
                        tactical_shots.append({'type': ThreatType.FORK, 'description': "Knight fork opportunity"})
        return tactical_shots
    
    def _find_back_rank_threats(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        threats = []
        opponent_color = not color
        king_square = board.king(opponent_color)
        if king_square is None: return threats
        back_rank = 0 if opponent_color == chess.WHITE else 7
        if chess.square_rank(king_square) == back_rank:
            for file in range(8):
                square = chess.square(file, back_rank)
                if board.is_attacked_by(color, square):
                    for attacker_sq in board.attackers(color, square):
                        attacker = board.piece_at(attacker_sq)
                        if attacker and attacker.piece_type in [chess.ROOK, chess.QUEEN]:
                            temp_board = board.copy()
                            try:
                                temp_board.push(chess.Move(attacker_sq, square))
                                if temp_board.is_checkmate():
                                    threats.append({'type': ThreatType.BACK_RANK_MATE, 'description': f"Back rank mate threat with {attacker.symbol().upper()}"})
                            except: continue
        return threats

    def evaluate_move_quality(self, board_before: chess.Board, move: chess.Move, 
                            eval_before: int, eval_after: int, move_number: int) -> Tuple[MoveQuality, str]:
        """
        Final calibrated move quality evaluation with sensitive thresholds.
        """
        eval_change = eval_after - eval_before
        player_color = board_before.turn
        
        if player_color == chess.BLACK:
            eval_change = -eval_change
        
        explanation_parts = []
        eval_change = max(-1000, min(1000, eval_change))

        is_sacrifice = False
        moving_piece = board_before.piece_at(move.from_square)
        captured_piece = board_before.piece_at(move.to_square)
        if moving_piece and captured_piece and self.piece_values[moving_piece.piece_type] > self.piece_values[captured_piece.piece_type]:
            is_sacrifice = True
        
        # *** KEY CHANGE: Final recalibration of thresholds for sensitive analysis ***
        if is_sacrifice and eval_change >= 200:
            quality = MoveQuality.BRILLIANT
            explanation_parts.append("A brilliant sacrifice that creates a winning advantage")
        elif eval_change >= 100:
            quality = MoveQuality.EXCELLENT
            explanation_parts.append("An excellent move that creates a strong advantage")
        elif eval_change >= 25:
            quality = MoveQuality.GOOD
            explanation_parts.append("A good move that improves the position")
        elif eval_change > -25:
            quality = MoveQuality.OKAY
            explanation_parts.append("A solid and reasonable move")
        elif eval_change > -100:
            quality = MoveQuality.INACCURATE
            explanation_parts.append("An inaccuracy, as a better move was available")
        elif eval_change > -200:
            quality = MoveQuality.POOR
            explanation_parts.append("A poor move that significantly worsens the position")
        else:
            quality = MoveQuality.BLUNDER
            explanation_parts.append("A blunder that makes the position much worse")

        board_after = board_before.copy()
        board_after.push(move)
        if quality in [MoveQuality.POOR, MoveQuality.BLUNDER]:
            if board_after.is_checkmate():
                 explanation_parts.append("This move leads to checkmate.")
            else:
                hanging = self._find_hanging_pieces(board_after, player_color)
                if hanging:
                    piece_name = chess.piece_name(hanging[0]['piece'].piece_type)
                    explanation_parts.append(f"It leaves a {piece_name} undefended.")

        explanation = ". ".join(explanation_parts)
        return quality, explanation