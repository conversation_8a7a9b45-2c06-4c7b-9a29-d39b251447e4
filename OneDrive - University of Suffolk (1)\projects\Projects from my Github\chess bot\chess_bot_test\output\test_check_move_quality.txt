PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_check_move_quality.py
Testing move quality evaluation fixes...
==================================================
Testing check move quality evaluation...
Move Nd3+ gives check: True
Move quality: Excellent
Explanation: outstanding opening (5.0)
Evaluation change (<PERSON>'s perspective): ***** pawns
✅ PASSED: Check move with good evaluation correctly not flagged as poor

Testing non-check hanging piece...
Move Nh3 gives check: False
Move quality: Poor
Explanation: weak opening move (-2.0)
✅ PASSED: Bad non-check move correctly flagged as poor      

==================================================
Results: 2/2 tests passed
✅ Move quality evaluation fixes appear to be working!