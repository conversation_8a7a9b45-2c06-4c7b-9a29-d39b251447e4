
note: 
- use gemini 2.5 pro instead of agents to save queries e.g. as knight opening moves only depend on chess bot file (the AI)
- in output/qs_analysis: only need one test like qs_analysis_test.log as the rest are repeats


file types: bots ; tests ; GUI's ; CLI


files that go together on branches:
tactical_eval_system - chess_bot.py ; chess_gui_score.py ; tactical_evaluation.py   
------------------

aims: [ACTUALLY READ THE FUCKING CODE AI GENERATES FOR YOU, YOU FUCKING CUNT]

--> ask AI to explain, review, and find any issues with the codebase [if need to make changes, create copy of project folder and edit that, in case AI messes up your existing codebase --> also make sure everything you need is committed to github]	

version control - some commits have some features work, others have other features work

- build future chess bots by following YouTube series that teaches you everything from first principles 
[see tabs ; chess AI YT playlist]
- search "chess bot"

- [DONE] create a unified chess bot (bot) that can be tested (test) and played against (GUI/CLI) - unified GUI also

--------------------

Tip for chess bot project (but really any project)

at whatever point you are at, create a copy of the folder that contains your project, get AI to explain how it works and identify any bugs, then focus on fixing them (without breaking the damn code), then only commit afterwards 

--------------------

future:

[while waiting for zencoder limit to reset, explore on the internet for viable chess bot / engine solutions]

- walk me through step by step how to create a chess bot with monte carlo tree search

------------------------

ideas:

- train a neural net on chess moves from video data e.g. GothamChess
- train neural net based on games played against the AI or Human so it improves iteratively, learns from opponent and its mistakes through self-play
[efficiently updatable neural network]

fun:
- create an AI agent that uses an LLM to play chess

-------------------------
-------------------------


Here's a project plan for building a chess bot, structured as a bullet-pointed abstraction hierarchy:

- **Project Goal: Develop a functional chess bot**
  - **Phase 1: Project Setup and Planning**
    - Define project scope and objectives
      - Bot plays legal chess moves
      - Competes at beginner to intermediate level
      - Supports standard chess rules
    - Select programming language (e.g., Python, C++)
    - Choose development tools and libraries
      - Chess library (e.g., python-chess)
      - IDE (e.g., VS Code)
    - Set up version control (e.g., Git, GitHub)
    - Create project timeline
      - Duration: 4-6 weeks
      - Milestones: setup, core logic, AI, testing, deployment

  - **Phase 2: Core Chess Engine Development**
    - Implement chess board representation
      - Use 8x8 array or bitboards
      - Track piece positions and states
    - Develop move generation
      - Generate legal moves for each piece
      - Handle special moves (castling, en passant, promotion)
    - Implement move validation
      - Ensure moves comply with chess rules
      - Check for check/checkmate/stalemate
    - Create game state management
      - Track turn order
      - Maintain move history
      - Handle game outcomes (win/loss/draw)

  - **Phase 3: AI Decision-Making**
    - Design evaluation function
      - Assign values to pieces (e.g., pawn=1, queen=9)
      - Consider positional factors (e.g., control of center)
    - Implement search algorithm
      - Use Minimax with alpha-beta pruning
      - Set search depth (e.g., 3-5 plies)
    - Optimize performance
      - Use transposition tables
      - Implement move ordering
    - Add difficulty levels
      - Adjust search depth
      - Introduce random move selection for lower levels

  - **Phase 4: User Interface**
    - Develop basic UI
      - Command-line interface or simple GUI (e.g., Pygame)
      - Display board and pieces
    - Enable user input
      - Accept moves via notation (e.g., e4, Nf3)
      - Validate user moves
    - Provide game feedback
      - Show bot’s moves
      - Display game status (e.g., check, game over)

  - **Phase 5: Testing and Debugging**
    - Unit test core components
      - Test move generation
      - Test evaluation function
    - Integration testing
      - Simulate full games
      - Verify game state transitions
    - Performance testing
      - Measure response time
      - Optimize bottlenecks
    - Bug fixing
      - Address illegal moves
      - Fix UI issues

  - **Phase 6: Deployment and Enhancements**
    - Package bot for distribution
      - Create executable or script
      - Write setup instructions
    - Add features (optional)
      - Online play via chess APIs
      - Save/load games
      - Customizable AI difficulty
    - Document code and usage
      - Write README
      - Include user guide
    - Deploy to platform (e.g., GitHub, personal server)

  - **Phase 7: Maintenance and Updates**
    - Gather user feedback
    - Fix reported bugs
    - Plan future improvements
      - Advanced AI (e.g., neural networks)
      - Enhanced UI (e.g., 3D board)
    - Monitor performance
      - Update dependencies
      - Ensure compatibility

This hierarchy provides a clear, modular plan to build a chess bot, from setup to deployment, with room for future enhancements.