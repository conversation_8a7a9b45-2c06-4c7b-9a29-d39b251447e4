.editorconfig
.gitignore
.readthedocs.yml
CHANGELOG-OLD.rst
CHANGELOG.rst
LICENSE.txt
MANIFEST.in
README.rst
release.py
setup.cfg
setup.py
test.py
tox.ini
.github/FUNDING.yml
.github/dependabot.yml
.github/workflows/codeql.yml
.github/workflows/setup-ubuntu-latest.sh
.github/workflows/setup-windows-latest.sh
.github/workflows/test.yml
chess/__init__.py
chess/engine.py
chess/gaviota.py
chess/pgn.py
chess/polyglot.py
chess/py.typed
chess/svg.py
chess/syzygy.py
chess/variant.py
chess.egg-info/PKG-INFO
chess.egg-info/SOURCES.txt
chess.egg-info/dependency_links.txt
chess.egg-info/not-zip-safe
chess.egg-info/top_level.txt
data/endgame-dm-4.epd
data/endgame-dm-5.epd
data/endgame.epd
data/suicide-dtm.epd
data/suicide-dtz.epd
data/suicide-stats.epd
data/gaviota/MD5SUMS.txt
data/gaviota/SOURCE.txt
data/gaviota/TEST-SOURCE.txt
data/gaviota/kbk.gtb.cp4
data/gaviota/knk.gtb.cp4
data/gaviota/kpk.gtb.cp4
data/gaviota/kqk.gtb.cp4
data/gaviota/krk.gtb.cp4
data/pgn/anastasian-lewis.pgn
data/pgn/antichess-programfox.pgn
data/pgn/chessbase-empty-line.pgn
data/pgn/cutechess-fischerrandom.pgn
data/pgn/kasparov-deep-blue-1997.pgn
data/pgn/knightvuillaume-jannlee-zh-lichess.pgn
data/pgn/molinari-bordais-1979.pgn
data/pgn/nepomniachtchi-liren-game1.pgn
data/pgn/saturs-jannlee-zh-lichess.pgn
data/pgn/stockfish-learning.pgn
data/pgn/uci-moves.pgn
data/pgn/utf8-bom.pgn
data/polyglot/SOURCE.txt
data/polyglot/lasker-trap.bin
data/polyglot/performance.bin
data/syzygy/atomic/SOURCE-5.txt
data/syzygy/atomic/SOURCE-6-DTZ.txt
data/syzygy/atomic/SOURCE-6-WDL.txt
data/syzygy/giveaway/SOURCE-5.txt
data/syzygy/giveaway/SOURCE-6.txt
data/syzygy/regular/KBBvK.rtbw
data/syzygy/regular/KBBvK.rtbz
data/syzygy/regular/KBNvK.rtbw
data/syzygy/regular/KBNvK.rtbz
data/syzygy/regular/KBPvK.rtbw
data/syzygy/regular/KBPvK.rtbz
data/syzygy/regular/KBvK.rtbw
data/syzygy/regular/KBvK.rtbz
data/syzygy/regular/KBvKB.rtbw
data/syzygy/regular/KBvKB.rtbz
data/syzygy/regular/KBvKN.rtbw
data/syzygy/regular/KBvKN.rtbz
data/syzygy/regular/KBvKP.rtbw
data/syzygy/regular/KBvKP.rtbz
data/syzygy/regular/KNNvK.rtbw
data/syzygy/regular/KNNvK.rtbz
data/syzygy/regular/KNPvK.rtbw
data/syzygy/regular/KNPvK.rtbz
data/syzygy/regular/KNvK.rtbw
data/syzygy/regular/KNvK.rtbz
data/syzygy/regular/KNvKN.rtbw
data/syzygy/regular/KNvKN.rtbz
data/syzygy/regular/KNvKP.rtbw
data/syzygy/regular/KNvKP.rtbz
data/syzygy/regular/KPPvK.rtbw
data/syzygy/regular/KPPvK.rtbz
data/syzygy/regular/KPvK.rtbw
data/syzygy/regular/KPvK.rtbz
data/syzygy/regular/KPvKP.rtbw
data/syzygy/regular/KPvKP.rtbz
data/syzygy/regular/KQBvK.rtbw
data/syzygy/regular/KQBvK.rtbz
data/syzygy/regular/KQNvK.rtbw
data/syzygy/regular/KQNvK.rtbz
data/syzygy/regular/KQPvK.rtbw
data/syzygy/regular/KQPvK.rtbz
data/syzygy/regular/KQQvK.rtbw
data/syzygy/regular/KQQvK.rtbz
data/syzygy/regular/KQRvK.rtbw
data/syzygy/regular/KQRvK.rtbz
data/syzygy/regular/KQvK.rtbw
data/syzygy/regular/KQvK.rtbz
data/syzygy/regular/KQvKB.rtbw
data/syzygy/regular/KQvKB.rtbz
data/syzygy/regular/KQvKN.rtbw
data/syzygy/regular/KQvKN.rtbz
data/syzygy/regular/KQvKP.rtbw
data/syzygy/regular/KQvKP.rtbz
data/syzygy/regular/KQvKQ.rtbw
data/syzygy/regular/KQvKQ.rtbz
data/syzygy/regular/KQvKR.rtbw
data/syzygy/regular/KQvKR.rtbz
data/syzygy/regular/KRBvK.rtbw
data/syzygy/regular/KRBvK.rtbz
data/syzygy/regular/KRNvK.rtbw
data/syzygy/regular/KRNvK.rtbz
data/syzygy/regular/KRPvK.rtbw
data/syzygy/regular/KRPvK.rtbz
data/syzygy/regular/KRRvK.rtbw
data/syzygy/regular/KRRvK.rtbz
data/syzygy/regular/KRvK.rtbw
data/syzygy/regular/KRvK.rtbz
data/syzygy/regular/KRvKB.rtbw
data/syzygy/regular/KRvKB.rtbz
data/syzygy/regular/KRvKN.rtbw
data/syzygy/regular/KRvKN.rtbz
data/syzygy/regular/KRvKP.rtbw
data/syzygy/regular/KRvKP.rtbz
data/syzygy/regular/KRvKR.rtbw
data/syzygy/regular/KRvKR.rtbz
data/syzygy/regular/SOURCE-5.txt
data/syzygy/regular/SOURCE-6.txt
data/syzygy/regular/SOURCE.txt
data/syzygy/suicide/SOURCE-5.txt
data/syzygy/suicide/SOURCE-6-PAWNLESS.txt
data/syzygy/suicide/TEST-SOURCE.txt
docs/Makefile
docs/Ne4.svg
docs/changelog.rst
docs/conf.py
docs/core.rst
docs/engine.rst
docs/gaviota.rst
docs/index.rst
docs/make.bat
docs/pgn.rst
docs/polyglot.rst
docs/requirements.txt
docs/svg.rst
docs/syzygy.rst
docs/variant.rst
docs/wR.svg
docs/images/clente-chess.png
docs/images/cli-chess.png
docs/images/crazyara.png
docs/images/jcchess.png
docs/images/maia.png
docs/images/pettingzoo.png
docs/images/syzygy.png
examples/chess960_pos_list.py
examples/polyglot_tree.py
examples/push_san.py
examples/xray_attacks.py
examples/bratko_kopec/bratko-kopec.epd
examples/bratko_kopec/bratko_kopec.py
examples/bratko_kopec/giveaway-puzzles.epd
examples/perft/3check.perft
examples/perft/atomic.perft
examples/perft/chess960.perft
examples/perft/crazyhouse.perft
examples/perft/giveaway.perft
examples/perft/horde.perft
examples/perft/perft.py
examples/perft/racingkings.perft
examples/perft/random.perft
examples/perft/tricky.perft
fuzz/engine.py
fuzz/epd.py
fuzz/fen.py
fuzz/pgn.py
fuzz/corpus/engine/stockfish-10
fuzz/corpus/engine/stockfish-mv-11
fuzz/corpus/engine/uciok-readyok
fuzz/corpus/epd/bratko-kopec.epd
fuzz/corpus/epd/giveaway-puzzles.epd
fuzz/corpus/epd/pv.epd
fuzz/corpus/epd/string-escaping.epd
fuzz/corpus/fen/empty
fuzz/corpus/fen/endgame
fuzz/corpus/fen/horde
fuzz/corpus/fen/initial
fuzz/corpus/fen/scid-3check
fuzz/corpus/fen/scid-zh
fuzz/corpus/fen/winboard-3check
fuzz/corpus/fen/winboard-zh
fuzz/corpus/pgn/anastasian-lewis.pgn
fuzz/corpus/pgn/antichess-programfox.pgn
fuzz/corpus/pgn/cutechess-fischerrandom.pgn
fuzz/corpus/pgn/kasparov-deep-blue-1997.pgn
fuzz/corpus/pgn/knightvuillaume-jannlee-zh-lichess.pgn
fuzz/corpus/pgn/molinari-bordais-1979.pgn
fuzz/corpus/pgn/saturs-jannlee-zh-lichess.pgn
fuzz/corpus/pgn/stockfish-learning.pgn
python-chess-stub/README.rst
python-chess-stub/setup.py