PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_iterative.py
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.02s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.25s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.00s, nodes=4399
  Depth 5: score=344, move=g1h3, time=1.72s, nodes=9823
IterativeBot completed search: 9823 nodes in 3.00s
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=45, move=h3f4, time=0.00s, nodes=21
  Depth 2: score=-13, move=h3f4, time=0.02s, nodes=92
  Depth 3: score=41, move=h3f4, time=0.27s, nodes=1024
  Depth 4: score=-15, move=b1c3, time=1.70s, nodes=6499
  Depth 5: score=44, move=b1c3, time=1.00s, nodes=9863
IterativeBot completed search: 9863 nodes in 3.00s
IterativeBot time limit set to 3.0 seconds
IterativeBot maximum depth set to 6
IterativeBot time limit set to 3.0 seconds
IterativeBot maximum depth set to 6
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=89, move=h3f4, time=0.01s, nodes=23
  Depth 2: score=35, move=h3f4, time=0.02s, nodes=103
  Depth 3: score=114, move=c3d5, time=0.42s, nodes=1366
  Depth 4: score=22, move=e2e4, time=2.51s, nodes=9616
  Depth 5: score=97, move=e2e4, time=0.04s, nodes=9745
IterativeBot completed search: 9745 nodes in 3.00s
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=156, move=e4f5, time=0.01s, nodes=32
  Depth 2: score=28, move=d1h5, time=0.04s, nodes=125
  Depth 3: score=332, move=d1h5, time=0.33s, nodes=1209
  Depth 4: score=-61, move=a1b1, time=2.61s, nodes=8069
IterativeBot completed search: 8069 nodes in 3.00s
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=132, move=e4f5, time=0.01s, nodes=30
  Depth 2: score=4, move=d1h5, time=0.04s, nodes=125
  Depth 3: score=316, move=d1h5, time=0.24s, nodes=880
  Depth 4: score=-30, move=c3d5, time=1.55s, nodes=4682
  Depth 5: score=222, move=c3d5, time=1.17s, nodes=7884
IterativeBot completed search: 7884 nodes in 3.00s
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=331, move=d5b4, time=0.01s, nodes=34
  Depth 2: score=179, move=d5b4, time=0.04s, nodes=133
  Depth 3: score=340, move=d1h5, time=0.24s, nodes=873
  Depth 4: score=190, move=d5b4, time=1.20s, nodes=3858
  Depth 5: score=365, move=d5b4, time=1.51s, nodes=8109
IterativeBot completed search: 8109 nodes in 3.00s
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.02s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.24s, nodes=996
  Depth 4: score=0, move=g1f3, time=0.95s, nodes=4399
  Depth 5: score=45, move=g1h3, time=1.79s, nodes=10240
IterativeBot completed search: 10240 nodes in 3.00s
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=155, move=h3g5, time=0.01s, nodes=21
  Depth 2: score=101, move=h3g5, time=0.02s, nodes=81
  Depth 3: score=196, move=h3g5, time=0.17s, nodes=703
  Depth 4: score=4, move=h3g5, time=2.27s, nodes=7843
  Depth 5: score=254, move=h3g5, time=0.53s, nodes=9803
IterativeBot completed search: 9803 nodes in 3.00s
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.04s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.26s, nodes=996
  Depth 4: score=0, move=g1f3, time=0.99s, nodes=4399
  Depth 5: score=78, move=g1h3, time=1.71s, nodes=9620
IterativeBot completed search: 9620 nodes in 3.00s
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=-8, move=g8f6, time=0.01s, nodes=21
  Depth 2: score=46, move=g8f6, time=0.03s, nodes=99
  Depth 3: score=-51, move=b8c6, time=0.23s, nodes=879
  Depth 4: score=59, move=e7e6, time=2.47s, nodes=6888
  Depth 5: score=-181, move=e7e6, time=0.26s, nodes=7731
IterativeBot completed search: 7731 nodes in 3.00s
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=-38, move=f8b4, time=0.01s, nodes=31
  Depth 2: score=36, move=b8c6, time=0.07s, nodes=189
  Depth 3: score=-115, move=b8c6, time=0.40s, nodes=1416
  Depth 4: score=38, move=b8c6, time=1.99s, nodes=6546
  Depth 5: score=-344, move=b8c6, time=0.53s, nodes=8158
IterativeBot completed search: 8158 nodes in 3.00s
IterativeBot time limit set to 5.0 seconds
IterativeBot maximum depth set to 10
IterativeBot starting iterative deepening search (max depth: 10, time limit: 5.0s)
  Depth 1: score=-161, move=c6d4, time=0.01s, nodes=34
  Depth 2: score=-6, move=g8f6, time=0.13s, nodes=267
  Depth 3: score=-177, move=f8b4, time=0.88s, nodes=3027
  Depth 4: score=-4, move=d8g5, time=3.87s, nodes=12265
  Depth 5: score=-489, move=d8g5, time=0.10s, nodes=12439
IterativeBot completed search: 12439 nodes in 5.00s
IterativeBot time limit set to 3.0 seconds
IterativeBot maximum depth set to 6
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=-168, move=c6d4, time=0.02s, nodes=49
  Depth 2: score=-26, move=g5g2, time=0.08s, nodes=235
  Depth 3: score=-544, move=g5g2, time=1.34s, nodes=3916
  Depth 4: score=-822, move=f8d6, time=1.56s, nodes=7301
IterativeBot completed search: 7301 nodes in 3.00s
IterativeBot time limit set to 3.0 seconds
IterativeBot maximum depth set to 6