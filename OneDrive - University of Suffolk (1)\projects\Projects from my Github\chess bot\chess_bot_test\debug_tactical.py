#!/usr/bin/env python3
"""
Debug script to test tactical detection for the specific position
"""

import chess
from tactical_evaluation import TacticalEvaluator

def debug_position():
    """Debug the specific position where Nxd5 should be caught as a blunder."""
    
    # Recreate the position from the game
    board = chess.Board()
    moves = [
        'd2d4', 'b8c6', 'b1c3', 'g8f6', 'c1g5', 'f6g4', 'f2f3', 'g4f6', 
        'g5f6', 'e7f6', 'e2e3', 'f8b4', 'f1b5', 'c6e7', 'd1d2', 'e7d5'
    ]
    
    for move_str in moves:
        move = chess.Move.from_uci(move_str)
        board.push(move)
    
    print("Position before Nxd5:")
    print(board)
    print(f"FEN: {board.fen()}")
    print()
    
    # Test the problematic move
    move = chess.Move.from_uci('c3d5')  # Nxd5
    print(f"Testing move: {board.san(move)}")
    
    # Check the position after the move
    board_after = board.copy()
    board_after.push(move)
    
    print("Position after Nxd5:")
    print(board_after)
    print()
    
    # Check where the queen is
    queen_square = None
    for square in chess.SQUARES:
        piece = board_after.piece_at(square)
        if piece and piece.piece_type == chess.QUEEN and piece.color == chess.WHITE:
            queen_square = square
            break
    
    print(f"White queen is on: {chess.square_name(queen_square)}")
    
    # Check if the queen is attacked
    if queen_square:
        is_attacked = board_after.is_attacked_by(chess.BLACK, queen_square)
        print(f"Queen is attacked by Black: {is_attacked}")
        
        if is_attacked:
            attackers = board_after.attackers(chess.BLACK, queen_square)
            print(f"Queen attackers: {[chess.square_name(sq) for sq in attackers]}")
            
            for attacker_square in attackers:
                attacker_piece = board_after.piece_at(attacker_square)
                print(f"  {attacker_piece.symbol().upper()} on {chess.square_name(attacker_square)}")
        
        # Check if the queen is defended
        defenders = board_after.attackers(chess.WHITE, queen_square)
        print(f"Queen defenders: {[chess.square_name(sq) for sq in defenders]}")
    
    print()
    
    # Test the tactical evaluation
    evaluator = TacticalEvaluator()
    major_blunder, note = evaluator._quick_tactical_check(board, move, chess.WHITE)
    print(f"Tactical blunder detected: {major_blunder}")
    print(f"Note: {note}")
    
    # Test full move quality evaluation
    quality, explanation = evaluator.evaluate_move_quality(board, move, 0, 100, 9)
    print(f"Move quality: {quality.value}")
    print(f"Explanation: {explanation}")

if __name__ == "__main__":
    debug_position()