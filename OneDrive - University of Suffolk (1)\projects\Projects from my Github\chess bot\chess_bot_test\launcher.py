#!/usr/bin/env python3
"""
Chess Bot Launcher - Choose between CLI and GUI versions
"""

import sys
import os

def show_launcher_menu():
    """Display the launcher menu."""
    print("\n" + "="*60)
    print("CHESS BOT - VERSION SELECTOR")
    print("="*60)
    print("Welcome to Chess Bot! Choose your preferred interface:")
    print()
    print("1. CLI Version - Command Line Interface")
    print("   • Text-based interface")
    print("   • Lightweight and fast")
    print("   • Works in any terminal")
    print("   • Full chess notation support")
    print()
    print("2. GUI Version - Graphical User Interface")
    print("   • Visual chess board")
    print("   • Drag and drop moves")
    print("   • Real-time game analysis")
    print("   • Save/load games with file dialogs")
    print()
    print("3. Help - About Chess Bot")
    print("4. Quit")
    print("="*60)

def show_help():
    """Display help information about Chess Bot."""
    print("\n" + "="*60)
    print("ABOUT CHESS BOT")
    print("="*60)
    print("Chess Bot is a complete chess game with AI opponent that offers")
    print("both command-line and graphical interfaces.")
    print()
    print("FEATURES:")
    print("• Full chess rules implementation")
    print("• AI opponent using minimax algorithm with alpha-beta pruning")
    print("• Multiple difficulty levels (Easy to Expert)")
    print("• Position analysis and evaluation")
    print("• Game saving/loading in PGN format")
    print("• Opening recognition")
    print("• Move history tracking")
    print("• Both CLI and GUI interfaces")
    print()
    print("CLI VERSION FEATURES:")
    print("• Standard algebraic notation input")
    print("• Unicode chess piece display")
    print("• Interactive help system")
    print("• Bot vs Bot demonstration mode")
    print("• Position analysis on demand")
    print()
    print("GUI VERSION FEATURES:")
    print("• Visual drag-and-drop interface")
    print("• Real-time board highlighting")
    print("• Graphical move history")
    print("• File dialogs for save/load")
    print("• Popup position analysis")
    print("• Difficulty selection via radio buttons")
    print()
    print("SYSTEM REQUIREMENTS:")
    print("• Python 3.7 or higher")
    print("• python-chess library")
    print("• tkinter (usually included with Python)")
    print()
    print("DIFFICULTY LEVELS:")
    print("• Easy (depth 2): Quick moves, basic strategy")
    print("• Medium (depth 3): Balanced play (default)")
    print("• Hard (depth 4): Strong tactical play")
    print("• Expert (depth 5): Very strong play (slower)")
    print("="*60)

def launch_cli():
    """Launch the CLI version."""
    print("\nLaunching CLI version...")
    print("Loading chess engine...")
    
    try:
        # Import and run the CLI version
        from main import main as cli_main
        cli_main()
    except ImportError as e:
        print(f"Error: Could not import CLI version: {e}")
        print("Make sure main.py is in the same directory.")
        return False
    except Exception as e:
        print(f"Error running CLI version: {e}")
        return False
    
    return True

def launch_gui():
    """Launch the GUI version."""
    print("\nLaunching GUI version...")
    print("Loading graphical interface...")
    
    try:
        # Check if tkinter is available
        import tkinter as tk
        
        # Test tkinter
        root = tk.Tk()
        root.withdraw()  # Hide the test window
        root.destroy()
        
        # Import and run the GUI version
        from chess_gui import main as gui_main
        gui_main()
        
    except ImportError as e:
        print(f"Error: Could not import required modules: {e}")
        print("Make sure tkinter is installed (usually comes with Python).")
        print("Also ensure chess_gui.py is in the same directory.")
        return False
    except Exception as e:
        print(f"Error running GUI version: {e}")
        return False
    
    return True

def check_dependencies():
    """Check if required dependencies are available."""
    missing_deps = []
    
    try:
        import chess
    except ImportError:
        missing_deps.append("python-chess")
    
    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter")
    
    if missing_deps:
        print("\n" + "="*60)
        print("MISSING DEPENDENCIES")
        print("="*60)
        print("The following required dependencies are missing:")
        for dep in missing_deps:
            print(f"• {dep}")
        print()
        print("To install missing dependencies:")
        if "python-chess" in missing_deps:
            print("pip install python-chess")
        if "tkinter" in missing_deps:
            print("tkinter usually comes with Python. Try reinstalling Python.")
        print("="*60)
        return False
    
    return True

def main():
    """Main launcher function."""
    print("Chess Bot Launcher")
    print("Checking dependencies...")
    
    if not check_dependencies():
        print("\nPlease install missing dependencies and try again.")
        sys.exit(1)
    
    print("All dependencies found!")
    
    while True:
        show_launcher_menu()
        
        try:
            choice = input("Enter your choice (1-4): ").strip()
            
            if choice == '1':
                # Launch CLI version
                success = launch_cli()
                if success:
                    print("\nCLI version exited normally.")
                else:
                    print("\nCLI version encountered an error.")
                
                # Ask if user wants to return to launcher
                try:
                    return_choice = input("\nReturn to launcher? (y/n): ").strip().lower()
                    if return_choice not in ['y', 'yes']:
                        break
                except KeyboardInterrupt:
                    break
            
            elif choice == '2':
                # Launch GUI version
                success = launch_gui()
                if success:
                    print("\nGUI version exited normally.")
                else:
                    print("\nGUI version encountered an error.")
                
                # Ask if user wants to return to launcher
                try:
                    return_choice = input("\nReturn to launcher? (y/n): ").strip().lower()
                    if return_choice not in ['y', 'yes']:
                        break
                except KeyboardInterrupt:
                    break
            
            elif choice == '3':
                # Show help
                show_help()
                input("\nPress Enter to continue...")
            
            elif choice == '4':
                # Quit
                print("Thanks for using Chess Bot!")
                sys.exit(0)
            
            else:
                print("Invalid choice. Please enter 1-4.")
        
        except KeyboardInterrupt:
            print("\n\nExiting Chess Bot Launcher. Goodbye!")
            sys.exit(0)
        except Exception as e:
            print(f"An error occurred: {e}")
            print("Please try again.")

if __name__ == "__main__":
    main()
