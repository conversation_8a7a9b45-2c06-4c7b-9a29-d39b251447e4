# 🏛️ Unified Chess Bot - Advanced Chess AI System

A comprehensive chess bot implementation featuring advanced AI algorithms, multiple difficulty levels, and both graphical and command-line interfaces.

## 🎯 Overview

The Unified Chess Bot combines the best features of various chess AI techniques into a single, powerful system. It includes:

- **Advanced AI Engine**: Minimax algorithm with alpha-beta pruning
- **Enhanced Features**: Iterative deepening, quiescence search, transposition tables
- **Multiple Interfaces**: Modern GUI and command-line interface
- **Difficulty Levels**: Easy to Expert presets with customizable features
- **Comprehensive Testing**: Full test suite and performance benchmarking
- **Educational Value**: Clear code structure for learning chess AI concepts

## 🚀 Quick Start

### Installation

1. **Install Python 3.7+** (if not already installed)
2. **Install dependencies**:
   ```bash
   pip install python-chess
   ```
3. **Download the unified chess bot files** to a directory
4. **Run the launcher**:
   ```bash
   python unified_launcher.py
   ```

### Playing Your First Game

1. Launch the unified launcher: `python unified_launcher.py`
2. Choose option 1: "Play Chess (GUI)"
3. Select your preferred difficulty level
4. Start playing by clicking on pieces to move them!

## 📁 File Structure

```
unified-chess-bot/
├── unified_chess_bot.py      # Main AI engine with all advanced features
├── unified_chess_gui.py      # Enhanced graphical user interface
├── unified_launcher.py       # Central launcher for all components
├── test_unified_chess_bot.py # Comprehensive test suite
├── README_UNIFIED.md         # This documentation file
├── utils.py                  # Utility functions (existing)
└── requirements.txt          # Python dependencies
```

## 🎮 Features

### AI Engine Features

- **Minimax with Alpha-Beta Pruning**: Core search algorithm for finding best moves
- **Iterative Deepening**: Gradually increases search depth for better time management
- **Quiescence Search**: Analyzes tactical sequences to avoid horizon effect
- **Transposition Tables**: Caches position evaluations for improved efficiency
- **Advanced Evaluation**: 
  - Material balance with piece-square tables
  - Mobility analysis
  - King safety considerations
  - Pawn structure evaluation
  - Center control assessment
- **Opening Book**: Database of common opening moves
- **Endgame Recognition**: Adjusts strategy for endgame positions

### GUI Features

- **Visual Chess Board**: 8x8 board with piece symbols and coordinate labels
- **Drag-and-Drop Interface**: Click pieces to select and move them
- **Real-Time Evaluation**: Position evaluation bar showing current advantage
- **Move History**: Complete game record with algebraic notation
- **Bot Configuration**: 
  - Difficulty level selection (Easy/Medium/Hard/Expert)
  - Feature toggles (Iterative Deepening, Quiescence Search, Transposition Tables)
  - Custom search depth setting
- **Game Management**:
  - Undo/Redo functionality
  - Save/Load games in PGN format
  - New game and color switching
- **Analysis Tools**:
  - Position analysis on demand
  - Bot statistics display (nodes evaluated, time taken, etc.)
  - Material count tracking
  - Opening name recognition

### Difficulty Levels

| Level  | Depth | Features | Target Audience |
|--------|-------|----------|-----------------|
| Easy   | 2     | Basic    | Beginners |
| Medium | 3     | All      | Intermediate |
| Hard   | 4     | All      | Advanced |
| Expert | 5     | Enhanced | Tournament level |

## 🛠️ Technical Details

### Core Algorithms

#### Minimax with Alpha-Beta Pruning
```python
def _minimax(self, board, depth, alpha, beta, maximizing_player):
    # Pruning-based search with move ordering for efficiency
    # Returns best evaluation and move
```

#### Quiescence Search
```python
def _quiescence_search(self, board, alpha, beta, depth):
    # Tactical search focusing on captures and checks
    # Prevents horizon effect in tactical positions
```

#### Transposition Tables
```python
def _store_transposition(self, board, depth, score, flag, best_move):
    # Cache position evaluations using Zobrist hashing
    # Avoids re-computing identical positions
```

### Evaluation Function

The bot uses a sophisticated evaluation function considering:

1. **Material Balance**: Standard piece values with positional adjustments
2. **Piece-Square Tables**: Location-based bonuses for each piece type
3. **Mobility**: Number of legal moves available
4. **King Safety**: Penalties for exposed kings in middlegame
5. **Pawn Structure**: Doubled pawn penalties
6. **Center Control**: Bonuses for controlling central squares
7. **Endgame Considerations**: Different king evaluation for endgames

### Performance Optimizations

- **Move Ordering**: Prioritizes captures, promotions, and checks
- **Hash Move Prioritization**: Uses transposition table best moves first
- **MVV-LVA**: Most Valuable Victim - Least Valuable Attacker for captures
- **Iterative Deepening**: Provides best move even if time runs out

## 🧪 Testing

### Running Tests

```bash
# Run all tests
python test_unified_chess_bot.py

# Or use the launcher
python unified_launcher.py
# Choose option 3: "Run Tests"
```

### Test Categories

1. **Basic Functionality Tests**
   - Bot initialization
   - Move generation
   - Board evaluation
   - Feature configuration

2. **Advanced Feature Tests**
   - Transposition table effectiveness
   - Quiescence search behavior
   - Iterative deepening performance
   - Opening book usage

3. **Preset Configuration Tests**
   - Easy/Medium/Hard/Expert bot verification
   - Feature consistency checking

4. **Performance Tests**
   - Search speed benchmarks
   - Depth scaling analysis
   - Efficiency comparisons

5. **Tactical Tests**
   - Capture recognition
   - Blunder avoidance
   - Checkmate detection

### Performance Benchmarking

```bash
# Run performance benchmark
python unified_launcher.py
# Choose option 4: "Performance Benchmark"
```

Example benchmark results:
```
Testing MediumBot:
⏱️  Time: 1.23 seconds
🧮 Nodes evaluated: 15,847
🔍 Quiescence nodes: 3,251
📋 Transposition hits: 892
⚡ Nodes per second: 12,884
```

## 🎓 Educational Value

This implementation serves as an excellent learning resource for:

### Chess Programming Concepts
- **Search Algorithms**: Understanding minimax and alpha-beta pruning
- **Evaluation Functions**: How computers assess chess positions
- **Optimization Techniques**: Transposition tables, move ordering, quiescence search

### Software Engineering Practices
- **Modular Design**: Separate components for AI, GUI, and testing
- **Object-Oriented Programming**: Clean class hierarchies and interfaces
- **Testing**: Comprehensive test coverage with multiple test categories
- **Documentation**: Well-commented code and clear documentation

### Algorithm Analysis
- **Time Complexity**: Understanding search tree growth and pruning effects
- **Space Complexity**: Memory usage for transposition tables and game state
- **Trade-offs**: Speed vs. accuracy in different configurations

## 🔧 Customization

### Creating Custom Bots

```python
from unified_chess_bot import UnifiedChessBot

# Create a custom bot configuration
custom_bot = UnifiedChessBot(
    depth=4,
    name="MyCustomBot",
    use_iterative_deepening=True,
    use_quiescence_search=True,
    use_transposition_table=True,
    max_quiescence_depth=6
)

# Adjust features at runtime
custom_bot.set_features(
    iterative_deepening=False,
    quiescence_search=True,
    transposition_table=True
)

# Set custom depth
custom_bot.set_depth(5)
```

### Extending the Evaluation Function

Add custom evaluation criteria by extending the `evaluate_board` method:

```python
class CustomBot(UnifiedChessBot):
    def evaluate_board(self, board):
        # Get base evaluation
        score = super().evaluate_board(board)
        
        # Add custom evaluation criteria
        score += self._evaluate_custom_feature(board)
        
        return score
    
    def _evaluate_custom_feature(self, board):
        # Implement custom evaluation logic
        return 0
```

## 🤝 Contributing

Contributions are welcome! Areas for improvement include:

- **New Evaluation Features**: Additional positional considerations
- **Opening Book Expansion**: More comprehensive opening database
- **GUI Enhancements**: Additional visualization features
- **Performance Optimizations**: Further search algorithm improvements
- **Educational Materials**: More learning resources and examples

## 📊 Performance Characteristics

### Typical Performance (Medium Bot)
- **Search Depth**: 3-4 plies
- **Nodes per Second**: 10,000-20,000
- **Move Time**: 1-3 seconds
- **Memory Usage**: <50MB including transposition table

### Scalability
- **Easy Bot**: ~500 nodes, <0.5 seconds
- **Medium Bot**: ~15,000 nodes, ~1.5 seconds  
- **Hard Bot**: ~50,000 nodes, ~3 seconds
- **Expert Bot**: ~200,000 nodes, ~8 seconds

## 🐛 Troubleshooting

### Common Issues

1. **Missing Dependencies**
   ```bash
   pip install python-chess
   ```

2. **GUI Not Starting**
   - Ensure tkinter is installed (usually comes with Python)
   - Try reinstalling Python with tkinter support

3. **Slow Performance**
   - Reduce search depth
   - Disable advanced features for faster play
   - Check system resources

4. **Import Errors**
   - Ensure all unified_*.py files are in the same directory
   - Check Python version (3.7+ required)

### Debug Mode

Enable detailed logging by setting debug flags:

```python
bot = UnifiedChessBot(depth=3)
bot.debug_mode = True  # Enable verbose output
```

## 📈 Future Enhancements

### Planned Features
- **Neural Network Integration**: Deep learning evaluation functions
- **Better Time Management**: Adaptive time allocation
- **Multi-Threading**: Parallel search implementation
- **Advanced Pruning**: Additional pruning techniques
- **Database Integration**: Opening and endgame databases

### Research Opportunities
- **Monte Carlo Tree Search**: Alternative search algorithm
- **Machine Learning**: Position evaluation learning
- **Advanced Heuristics**: Better move ordering and evaluation

## 📄 License

This project is provided for educational and personal use. Feel free to modify and extend for learning purposes.

## 🙏 Acknowledgments

Built upon the excellent `python-chess` library by Niklas Fiekas. Incorporates classical chess programming techniques developed by the chess programming community over decades.

---

**Happy Chess Playing! ♟️**

*"The pawns are the soul of chess." - François-André Danican Philidor*