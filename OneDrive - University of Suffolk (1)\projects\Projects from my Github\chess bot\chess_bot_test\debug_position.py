#!/usr/bin/env python3
"""
Debug script to analyze the specific position and understand the tactical threats.
"""

import chess
from tactical_evaluation_test import TacticalEvaluator

def debug_position():
    """Debug the specific position that's causing confusion."""
    
    # The position from the user's example
    fen = "r2qkb1r/ppp1pppp/5n2/3p1Q2/3n4/2N2b1P/PPP1PPP1/R1B1KB1R w KQkq - 0 7"
    board = chess.Board(fen)
    
    print("POSITION ANALYSIS")
    print("=" * 50)
    print(f"FEN: {fen}")
    print(f"Turn: {'White' if board.turn == chess.WHITE else 'Black'}")
    print()
    
    # Print the board
    print("Board:")
    print(board)
    print()
    
    # Analyze pieces on the board
    print("PIECE LOCATIONS:")
    print("-" * 30)
    for square in chess.SQUARES:
        piece = board.piece_at(square)
        if piece:
            color = "White" if piece.color == chess.WHITE else "Black"
            print(f"{color} {piece.symbol().upper()} on {chess.square_name(square)}")
    print()
    
    # Check what the White Queen on f5 is actually attacking
    queen_square = chess.parse_square("f5")
    queen = board.piece_at(queen_square)
    if queen and queen.piece_type == chess.QUEEN and queen.color == chess.WHITE:
        print("WHITE QUEEN ON F5 ANALYSIS:")
        print("-" * 30)
        attacked_squares = board.attacks(queen_square)
        print(f"Queen attacks these squares: {[chess.square_name(sq) for sq in attacked_squares]}")
        
        # Check what pieces are being attacked
        attacked_pieces = []
        for sq in attacked_squares:
            piece = board.piece_at(sq)
            if piece and piece.color != queen.color:
                attacked_pieces.append((sq, piece))
        
        print(f"Queen attacks these enemy pieces:")
        for sq, piece in attacked_pieces:
            print(f"  - {piece.symbol().upper()} on {chess.square_name(sq)}")
        
        if len(attacked_pieces) >= 2:
            print(f"This is a FORK - Queen attacks {len(attacked_pieces)} pieces simultaneously!")
        print()
    
    # Check if Queen is defended
    print("QUEEN DEFENSE ANALYSIS:")
    print("-" * 30)
    queen_defenders = board.attackers(chess.WHITE, queen_square)
    queen_attackers = board.attackers(chess.BLACK, queen_square)
    
    print(f"White pieces defending Queen on f5: {len(queen_defenders)}")
    for sq in queen_defenders:
        piece = board.piece_at(sq)
        if piece:
            print(f"  - {piece.symbol().upper()} on {chess.square_name(sq)}")
    
    print(f"Black pieces attacking Queen on f5: {len(queen_attackers)}")
    for sq in queen_attackers:
        piece = board.piece_at(sq)
        if piece:
            print(f"  - {piece.symbol().upper()} on {chess.square_name(sq)}")
    
    if len(queen_defenders) == 0 and len(queen_attackers) > 0:
        print("The Queen IS hanging (undefended and under attack)!")
    print()
    
    # Now run the tactical evaluator
    evaluator = TacticalEvaluator()
    threats = evaluator.evaluate_threats(board)
    
    print("TACTICAL EVALUATOR RESULTS:")
    print("-" * 30)
    print(f"White threats: {len(threats[chess.WHITE])}")
    for threat in threats[chess.WHITE]:
        print(f"  - {threat}")
    print()
    print(f"Black threats: {len(threats[chess.BLACK])}")
    for threat in threats[chess.BLACK]:
        print(f"  - {threat}")

if __name__ == "__main__":
    debug_position()