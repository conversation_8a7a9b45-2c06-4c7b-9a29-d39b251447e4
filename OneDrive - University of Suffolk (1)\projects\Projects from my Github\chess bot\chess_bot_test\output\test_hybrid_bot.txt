PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_hybrid_bot.py
=== Testing Hybrid Chess Bot ===

Creating different bot configurations...

Bot Configurations:
MinimaxBot:
  - Depth: 3
  - MCTS Simulations: 1000
  - Top Moves Count: 5
  - Hybrid Mode: False

HybridBot:
  - Depth: 3
  - MCTS Simulations: 500
  - Top Moves Count: 3
  - Hybrid Mode: True

AggressiveHybrid:
  - Depth: 4
  - MCTS Simulations: 1000
  - Top Moves Count: 5
  - Hybrid Mode: True


==================================================
Testing Position 1: Starting Position
FEN: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
==================================================

--- MinimaxBot ---
MinimaxBot evaluated 715 positions in 0.22 seconds
Selected move: g1f3
Best move: g1f3
Move in algebraic notation: Nf3
------------------------------

--- HybridBot ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3']
Minimax scores: [6, 6, -34]
Step 2: Exploring candidates with 500 MCTS simulations...
HybridBot evaluated 1714 positions in 5.76 seconds
Selected move: g1h3
Best move: g1h3
Move in algebraic notation: Nh3
------------------------------

--- AggressiveHybrid ---
AggressiveHybrid using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3']
Minimax scores: [48, 48, 4, 4, -4]
Step 2: Exploring candidates with 1000 MCTS simulations...
AggressiveHybrid evaluated 15422 positions in 16.44 seconds
Selected move: e2e3
Best move: e2e3
Move in algebraic notation: e3
------------------------------

==================================================
Testing Position 2: Middle Game
FEN: r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4
==================================================

--- MinimaxBot ---
MinimaxBot evaluated 4764 positions in 1.33 seconds
Selected move: b1c3
Best move: b1c3
Move in algebraic notation: Nc3
------------------------------

--- HybridBot ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['b1c3', 'b1d2', 'e1e2']
Minimax scores: [68, 54, 14]
Step 2: Exploring candidates with 500 MCTS simulations...
HybridBot evaluated 7049 positions in 8.86 seconds
Selected move: b1c3
Best move: b1c3
Move in algebraic notation: Nc3
------------------------------

--- AggressiveHybrid ---
AggressiveHybrid using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['e1e2', 'c1g5', 'e1f1', 'b1d2', 'c4b5']
Minimax scores: [-117, -117, -121, -125, -127]
Step 2: Exploring candidates with 1000 MCTS simulations...
AggressiveHybrid evaluated 166725 positions in 79.35 seconds
Selected move: c1g5
Best move: c1g5
Move in algebraic notation: Bg5
------------------------------

==================================================
Testing Position 3: Tactical Position
FEN: r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4
==================================================

--- MinimaxBot ---
MinimaxBot evaluated 6859 positions in 3.01 seconds
Selected move: b1c3
Best move: b1c3
Move in algebraic notation: Nc3
------------------------------

--- HybridBot ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['b1c3', 'b1d2', 'e1e2']
Minimax scores: [54, 40, 0]
Step 2: Exploring candidates with 500 MCTS simulations...
HybridBot evaluated 9870 positions in 9.86 seconds
Selected move: b1c3
Best move: b1c3
Move in algebraic notation: Nc3
------------------------------

--- AggressiveHybrid ---
AggressiveHybrid using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['e1e2', 'c1g5', 'e1f1', 'b1d2', 'c4b5']
Minimax scores: [-117, -117, -121, -125, -127]
Step 2: Exploring candidates with 1000 MCTS simulations...
AggressiveHybrid evaluated 325509 positions in 135.59 seconds
Selected move: c4b5
Best move: c4b5
Move in algebraic notation: Bb5
------------------------------

=== Testing Configuration Changes ===

Initial configuration:
{'name': 'ConfigTestBot', 'depth': 3, 'mcts_simulations': 1000, 'top_moves_count': 5, 'use_hybrid': True}

Changing configurations...
ConfigTestBot search depth set to 4
ConfigTestBot MCTS simulations set to 2000
ConfigTestBot top moves count set to 7
ConfigTestBot mode set to minimax only

New configuration:
{'name': 'ConfigTestBot', 'depth': 4, 'mcts_simulations': 2000, 'top_moves_count': 7, 'use_hybrid': False}

Switching back to hybrid mode...
ConfigTestBot mode set to hybrid (Minimax + MCTS)

Testing move selection with new configuration...
ConfigTestBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 7 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3', 'd2d3', 'c2c3']
Minimax scores: [48, 48, 4, 4, -4, -10, -34]
Step 2: Exploring candidates with 2000 MCTS simulations...
ConfigTestBot evaluated 15422 positions in 30.21 seconds
Selected move: b1a3
Selected move: b1a3

=== Testing Complete ===









----------------------------------------------------



PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_hybrid_bot.py
=== Testing Hybrid Chess Bot ===

Creating different bot configurations...

Bot Configurations:
MinimaxBot:
  - Depth: 3
  - MCTS Simulations: 800
  - Top Moves Count: 3
  - Hybrid Mode: False

HybridBot:
  - Depth: 3
  - MCTS Simulations: 500
  - Top Moves Count: 3
  - Hybrid Mode: True

AggressiveHybrid:
  - Depth: 4
  - MCTS Simulations: 1000
  - Top Moves Count: 5
  - Hybrid Mode: True


==================================================
Testing Position 1: Starting Position
FEN: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
==================================================

--- MinimaxBot ---
MinimaxBot evaluated 710 positions in 0.77 seconds
Selected move: g1f3
Best move: g1f3
Move in algebraic notation: Nf3
------------------------------

--- HybridBot ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3']
Minimax scores: [16, 16, -24]
Step 2: Exploring candidates with 500 MCTS simulations...
HybridBot evaluated 1732 positions in 14.83 seconds
Selected move: g1f3
Best move: g1f3
Move in algebraic notation: Nf3
------------------------------

--- AggressiveHybrid ---
AggressiveHybrid using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3']
Minimax scores: [48, 48, 8, -2, -9]
Step 2: Exploring candidates with 1000 MCTS simulations...
AggressiveHybrid evaluated 15026 positions in 43.72 seconds
Selected move: e2e3
Best move: e2e3
Move in algebraic notation: e3
------------------------------

==================================================
Testing Position 2: Middle Game
FEN: r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4
==================================================

--- MinimaxBot ---
MinimaxBot evaluated 3271 positions in 3.71 seconds
Selected move: b1c3
Best move: b1c3
Move in algebraic notation: Nc3
------------------------------

--- HybridBot ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['b1c3', 'b1d2', 'c1d2']
Minimax scores: [113, 99, 69]
Step 2: Exploring candidates with 500 MCTS simulations...
HybridBot evaluated 5584 positions in 19.26 seconds
Selected move: c1d2
Best move: c1d2
Move in algebraic notation: Bd2
------------------------------

--- AggressiveHybrid ---
AggressiveHybrid using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['c1g5', 'e1f1', 'c1e3', 'b1c3', 'e1g1']
Minimax scores: [-78, -97, -102, -114, -121]
Step 2: Exploring candidates with 1000 MCTS simulations...
AggressiveHybrid evaluated 34374 positions in 87.85 seconds
Selected move: e1g1
Best move: e1g1
Move in algebraic notation: O-O
------------------------------

==================================================
Testing Position 3: Tactical Position
FEN: r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4
==================================================

--- MinimaxBot ---
MinimaxBot evaluated 1838 positions in 3.49 seconds
Selected move: b1c3
Best move: b1c3
Move in algebraic notation: Nc3
------------------------------

--- HybridBot ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['b1c3', 'b1d2', 'c1d2']
Minimax scores: [64, 50, 20]
Step 2: Exploring candidates with 500 MCTS simulations...
HybridBot evaluated 4436 positions in 21.38 seconds
Selected move: b1c3
Best move: b1c3
Move in algebraic notation: Nc3
------------------------------

--- AggressiveHybrid ---
AggressiveHybrid using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['c1g5', 'e1f1', 'b1c3', 'b1d2', 'd1e2']
Minimax scores: [-78, -111, -114, -130, -135]
Step 2: Exploring candidates with 1000 MCTS simulations...
AggressiveHybrid evaluated 63879 positions in 94.86 seconds
Selected move: b1d2
Best move: b1d2
Move in algebraic notation: Nbd2
------------------------------

=== Testing Configuration Changes ===

Initial configuration:
{'name': 'ConfigTestBot', 'depth': 4, 'mcts_simulations': 800, 'top_moves_count': 3, 'use_hybrid': True}

Changing configurations...
ConfigTestBot search depth set to 4
ConfigTestBot MCTS simulations set to 2000
ConfigTestBot top moves count set to 7
ConfigTestBot mode set to minimax only

New configuration:
{'name': 'ConfigTestBot', 'depth': 4, 'mcts_simulations': 2000, 'top_moves_count': 7, 'use_hybrid': False}

Switching back to hybrid mode...
ConfigTestBot mode set to hybrid (Minimax + MCTS)

Testing move selection with new configuration...
ConfigTestBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 7 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3', 'd2d3', 'c2c3']
Minimax scores: [48, 48, 8, -2, -9, -15, -44]
Step 2: Exploring candidates with 2000 MCTS simulations...
ConfigTestBot evaluated 15026 positions in 75.01 seconds
Selected move: b1a3
Selected move: b1a3

=== Testing Complete ===











=================================================================




PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_hybrid_bot.py
=== Testing Hybrid Chess Bot ===

Creating different bot configurations...

Bot Configurations:
MinimaxBot:
  - Depth: 3
  - MCTS Simulations: 800
  - Top Moves Count: 3
  - Hybrid Mode: False

HybridBot:
  - Depth: 3
  - MCTS Simulations: 500
  - Top Moves Count: 3
  - Hybrid Mode: True

AggressiveHybrid:
  - Depth: 4
  - MCTS Simulations: 1000
  - Top Moves Count: 5
  - Hybrid Mode: True


==================================================
Testing Position 1: Starting Position
FEN: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
==================================================

--- MinimaxBot ---
MinimaxBot evaluated 710 positions in 0.69 seconds
Selected move: g1f3
Best move: g1f3
Move in algebraic notation: Nf3
------------------------------

--- HybridBot ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3']
Minimax scores: [16, 16, -24]
Step 2: Exploring candidates with 500 MCTS simulations...
HybridBot evaluated 1732 positions in 8.72 seconds
Selected move: b1c3
Best move: b1c3
Move in algebraic notation: Nc3
------------------------------

--- AggressiveHybrid ---
AggressiveHybrid using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3']
Minimax scores: [48, 48, 8, -2, -9]
Step 2: Exploring candidates with 1000 MCTS simulations...
AggressiveHybrid evaluated 15026 positions in 29.03 seconds
Selected move: g1f3
Best move: g1f3
Move in algebraic notation: Nf3
------------------------------

==================================================
Testing Position 2: Middle Game
FEN: r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4
==================================================

--- MinimaxBot ---
MinimaxBot evaluated 3271 positions in 2.39 seconds
Selected move: b1c3
Best move: b1c3
Move in algebraic notation: Nc3
------------------------------

--- HybridBot ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['b1c3', 'b1d2', 'c1d2']
Minimax scores: [113, 99, 69]
Step 2: Exploring candidates with 500 MCTS simulations...
HybridBot evaluated 5584 positions in 15.38 seconds
Selected move: b1c3
Best move: b1c3
Move in algebraic notation: Nc3
------------------------------

--- AggressiveHybrid ---
AggressiveHybrid using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['c1g5', 'e1f1', 'c1e3', 'b1c3', 'e1g1']
Minimax scores: [-78, -97, -102, -114, -121]
Step 2: Exploring candidates with 1000 MCTS simulations...
AggressiveHybrid evaluated 34374 positions in 38.52 seconds
Selected move: c1g5
Best move: c1g5
Move in algebraic notation: Bg5
------------------------------

==================================================
Testing Position 3: Tactical Position
FEN: r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4
==================================================

--- MinimaxBot ---
MinimaxBot evaluated 1838 positions in 1.14 seconds
Selected move: b1c3
Best move: b1c3
Move in algebraic notation: Nc3
------------------------------

--- HybridBot ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['b1c3', 'b1d2', 'c1d2']
Minimax scores: [64, 50, 20]
Step 2: Exploring candidates with 500 MCTS simulations...
HybridBot evaluated 4436 positions in 10.45 seconds
Selected move: b1c3
Best move: b1c3
Move in algebraic notation: Nc3
------------------------------

--- AggressiveHybrid ---
AggressiveHybrid using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['c1g5', 'e1f1', 'b1c3', 'b1d2', 'd1e2']
Minimax scores: [-78, -111, -114, -130, -135]
Step 2: Exploring candidates with 1000 MCTS simulations...
AggressiveHybrid evaluated 63879 positions in 51.36 seconds
Selected move: b1d2
Best move: b1d2
Move in algebraic notation: Nbd2
------------------------------

=== Testing Configuration Changes ===

Initial configuration:
{'name': 'ConfigTestBot', 'depth': 4, 'mcts_simulations': 800, 'top_moves_count': 3, 'use_hybrid': True}

Changing configurations...
ConfigTestBot search depth set to 4
ConfigTestBot MCTS simulations set to 2000
ConfigTestBot top moves count set to 7
ConfigTestBot mode set to minimax only

New configuration:
{'name': 'ConfigTestBot', 'depth': 4, 'mcts_simulations': 2000, 'top_moves_count': 7, 'use_hybrid': False}

Switching back to hybrid mode...
ConfigTestBot mode set to hybrid (Minimax + MCTS)

Testing move selection with new configuration...
ConfigTestBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 7 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3', 'd2d3', 'c2c3']
Minimax scores: [48, 48, 8, -2, -9, -15, -44]
Step 2: Exploring candidates with 2000 MCTS simulations...
ConfigTestBot evaluated 15026 positions in 33.85 seconds
Selected move: g1h3
Selected move: g1h3

=== Testing Complete ===