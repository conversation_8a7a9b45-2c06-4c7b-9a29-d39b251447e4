PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_tactical_fix.py
Testing Tactical Fix for Hanging Pieces
==================================================
Move history:
1. Nc3      d5
2. Nf3      Nc6
3. d4      e6
4. Ne5      Nb4

Current position: r1bqkbnr/ppp2ppp/4p3/3pN3/1n1P4/2N5/PPP1PPPP/R1BQKB1R w KQkq - 2 5
To move: <PERSON>

White plays a3 (attacking the knight on b4)
Position after a3: r1bqkbnr/ppp2ppp/4p3/3pN3/1n1P4/P1N5/1PP1PPPP/R1BQKB1R b KQkq - 0 5
Black to move - knight on b4 is attacked!

==================================================
BEFORE FIX - Bot Analysis:
==================================================
Position evaluation: 47
(Negative values favor Black, Positive favor White)
Hanging pieces penalty: 0
MediumBot statistics:
  - Time: 21.12s
  - Nodes evaluated: 3007
  - Quiescence nodes: 5266
  - Transposition hits: 0
  - Table size: 196
Bot's chosen move: d8d6
Move in standard notation: Qd6
❌ BAD: Bot is not retreating the attacked knight!
Moving piece: q from d8 to d6

==================================================
Expected Behavior:
==================================================
The bot should:
1. Detect that the knight on b4 is hanging
2. Prioritize moving it to safety
3. NOT make attacking moves while ignoring the threat
4. Choose a retreat square like Nc6, Nd5, Na6, or Nc2

==================================================
ANALYZING RETREAT OPTIONS
==================================================
Knight on b4 can move to:
  - c6 (❌ ATTACKED)
  - a6 (✅ SAFE)
  - d3 (❌ ATTACKED)
  - c2 (❌ ATTACKED)
  - a2 (❌ ATTACKED)

==================================================
SUMMARY
==================================================
❌ ISSUE: Bot is still not prioritizing knight retreat.
The hanging piece detection may need further tuning.

Total knight retreat options: 5