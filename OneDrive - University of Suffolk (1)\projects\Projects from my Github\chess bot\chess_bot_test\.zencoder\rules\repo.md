# Chess Bot Information

## Summary
Chess Bot is a complete chess game with AI opponent featuring both command-line and graphical user interfaces. It implements full chess rules including castling, en passant, and promotion. The AI uses a minimax algorithm with alpha-beta pruning and offers multiple difficulty levels.

## Structure
- **bots/**: Contains alternative bot implementations
- **chess_bot.py**: Main AI engine implementation using minimax with alpha-beta pruning
- **chess_game.py**: Core game logic for CLI version
- **chess_gui.py**: GUI version entry point using tkinter
- **launcher.py**: Main launcher to choose between CLI/GUI versions
- **main.py**: CLI version entry point
- **tests/**: Contains test files for various components
- **utils.py**: Utility functions for board display and analysis

## Language & Runtime
**Language**: Python
**Version**: Python 3.7 or higher
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- python-chess==1.999 (Chess rules implementation)
- tkinter (GUI library, usually included with Python)

## Build & Installation
```bash
# Install dependencies
pip install python-chess

# Launch the application
python launcher.py
```

## Main Entry Points
- **launcher.py**: Main entry point that lets users choose between CLI and GUI versions
- **main.py**: Direct entry point for CLI version
- **chess_gui.py**: Direct entry point for GUI version

## Features
- **Multiple Interfaces**: Both CLI (text-based) and GUI (graphical) versions
- **AI Opponent**: Minimax algorithm with alpha-beta pruning
- **Difficulty Levels**: Easy (depth 2), Medium (depth 3), Hard (depth 4), Expert (depth 5)
- **Game Analysis**: Position evaluation and opening recognition
- **Game Persistence**: Save and load games in PGN format
- **Alternative Implementations**: Various bot implementations including hybrid approaches

## Testing
**Framework**: Python's built-in unittest
**Test Files**:
- test_chess_bot.py: Tests for the main chess bot implementation
- test_chess_bot_QS.py: Tests for the Quiescence Search implementation
- test_chess_bot_rook.py: Tests for the rook behavior implementation
- test_hybrid_bot.py: Tests for the hybrid bot implementation
- test_opening_behavior.py: Tests for opening recognition
- test_qs_effect.py: Tests for Quiescence Search effects
- test_rook_behavior.py: Tests for rook movement behavior

**Run Command**:
```bash
python test_chess_bot.py
```