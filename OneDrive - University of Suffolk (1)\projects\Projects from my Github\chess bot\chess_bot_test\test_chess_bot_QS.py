#!/usr/bin/env python3
"""
Unit tests for the chess bot implementation with quiescence search.
"""

import unittest
import chess
import time
from chess_bot_QS import ChessBot
from chess_game import ChessGame

class TestChessBotQS(unittest.TestCase):
    """Test cases for the ChessBot class with quiescence search."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.bot = ChessBot(depth=2, name="TestBotQS")
        self.board = chess.Board()
    
    def test_bot_initialization(self):
        """Test bot initialization."""
        self.assertEqual(self.bot.depth, 2)
        self.assertEqual(self.bot.name, "TestBotQS")
        self.assertIsInstance(self.bot.piece_values, dict)
        self.assertIsInstance(self.bot.pawn_table, list)
        self.assertIsInstance(self.bot.knight_table, list)
    
    def test_board_evaluation_starting_position(self):
        """Test board evaluation at starting position."""
        score = self.bot.evaluate_board(self.board)
        # Starting position should be roughly equal
        self.assertAlmostEqual(score, 0, delta=100)
    
    def test_board_evaluation_checkmate(self):
        """Test board evaluation in checkmate position."""
        # Set up a simple checkmate position
        self.board.set_fen("rnbqkbnr/pppp1ppp/8/4p3/6P1/5P2/PPPPP2P/RNBQKBNR b KQkq - 0 2")
        self.board.push_san("Qh4#")  # Fool's mate
        
        score = self.bot.evaluate_board(self.board)
        # Should heavily favor the winning side
        self.assertGreater(abs(score), 10000)
    
    def test_get_best_move_returns_legal_move(self):
        """Test that get_best_move returns a legal move."""
        move = self.bot.get_best_move(self.board)
        self.assertIsNotNone(move)
        self.assertIn(move, self.board.legal_moves)
    
    def test_minimax_depth_zero(self):
        """Test minimax with depth 0 (should use quiescence search)."""
        score, move = self.bot.minimax(self.board, 0, float('-inf'), float('inf'), True)
        self.assertIsInstance(score, int)
        # At depth 0, move should be None (just evaluation)
        self.assertIsNone(move)
    
    def test_set_depth(self):
        """Test setting bot depth."""
        original_depth = self.bot.depth
        new_depth = 4
        self.bot.set_depth(new_depth)
        self.assertEqual(self.bot.depth, new_depth)
        self.assertNotEqual(self.bot.depth, original_depth)
    
    def test_piece_values(self):
        """Test that piece values are reasonable."""
        # Queen should be worth more than a rook
        self.assertGreater(self.bot.piece_values[chess.QUEEN], 
                          self.bot.piece_values[chess.ROOK])
        
        # Rook should be worth more than a bishop
        self.assertGreater(self.bot.piece_values[chess.ROOK], 
                          self.bot.piece_values[chess.BISHOP])
        
        # Bishop should be worth more than a knight (roughly equal but bishop slightly higher)
        self.assertGreaterEqual(self.bot.piece_values[chess.BISHOP], 
                               self.bot.piece_values[chess.KNIGHT])
        
        # Knight should be worth more than a pawn
        self.assertGreater(self.bot.piece_values[chess.KNIGHT], 
                          self.bot.piece_values[chess.PAWN])
    
    def test_quiescence_search_basic(self):
        """Test basic functionality of quiescence search."""
        # Starting position
        score = self.bot.quiescence_search(self.board, float('-inf'), float('inf'), True)
        self.assertIsInstance(score, int)
        
        # Should be roughly balanced
        self.assertAlmostEqual(score, 0, delta=100)
    
    def test_quiescence_search_capture_position(self):
        """Test quiescence search in a position with captures."""
        # Set up a position with a hanging piece
        self.board.set_fen("rnbqkbnr/ppp1pppp/8/3p4/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")
        
        # White can capture the pawn
        score = self.bot.quiescence_search(self.board, float('-inf'), float('inf'), True)
        
        # Score should reflect the potential capture
        self.assertGreater(score, 0)  # White has advantage due to potential capture
    
    def test_quiescence_search_depth_limit(self):
        """Test that quiescence search respects depth limit."""
        # Create a complex position with many possible captures
        self.board.set_fen("r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 0 4")
        
        # Track nodes before
        nodes_before = self.bot.nodes_evaluated
        
        # Run quiescence search with depth limit
        self.bot.quiescence_search(self.board, float('-inf'), float('inf'), True, 4)
        
        # Check that some nodes were evaluated but not too many (due to depth limit)
        self.assertGreater(self.bot.nodes_evaluated, nodes_before)
    
    def test_move_ordering(self):
        """Test that move ordering prioritizes captures and checks."""
        # Set up a position with captures and checks
        self.board.set_fen("rnbqkbnr/ppp1pppp/8/3p4/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")
        
        # Get all legal moves
        moves = list(self.board.legal_moves)
        
        # Order the moves
        ordered_moves = self.bot._order_moves(self.board, moves)
        
        # The capture exd5 should be among the first moves
        capture_move = chess.Move.from_uci("e4d5")
        if capture_move in moves:  # Make sure it's a legal move
            self.assertIn(capture_move, ordered_moves[:5])  # Should be in the first few moves
    
    def test_bot_prefers_captures(self):
        """Test that bot prefers capturing moves when beneficial."""
        # Set up a position where a capture is clearly beneficial
        self.board.set_fen("rnbqkbnr/ppp1pppp/8/3p4/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")
        
        # Bot should find the capture
        move = self.bot.get_best_move(self.board)
        self.assertIsNotNone(move)
        
        # The best move should be capturing the pawn
        capture_move = chess.Move.from_uci("e4d5")
        self.assertEqual(move, capture_move)
    
    def test_quiescence_search_improves_evaluation(self):
        """Test that quiescence search improves evaluation in tactical positions."""
        # Set up a position with a tactical sequence
        self.board.set_fen("r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 0 4")
        
        # Create two bots - one with quiescence search and one without
        bot_with_qs = ChessBot(depth=2, name="WithQS")
        
        # For the bot without QS, we'll modify the minimax to not use quiescence
        class BotWithoutQS(ChessBot):
            def minimax(self, board, depth, alpha, beta, maximizing_player):
                self.nodes_evaluated += 1
                
                if board.is_game_over() or depth <= 0:
                    return self.evaluate_board(board), None
                
                # Rest of the minimax is the same...
                best_move = None
                legal_moves = list(board.legal_moves)
                ordered_moves = self._order_moves(board, legal_moves)
                
                if maximizing_player:
                    max_eval = float('-inf')
                    for move in ordered_moves:
                        board.push(move)
                        eval_score, _ = self.minimax(board, depth - 1, alpha, beta, False)
                        board.pop()
                        
                        if eval_score > max_eval:
                            max_eval = eval_score
                            best_move = move
                        
                        alpha = max(alpha, eval_score)
                        if beta <= alpha:
                            break
                    
                    return max_eval, best_move
                else:
                    min_eval = float('inf')
                    for move in ordered_moves:
                        board.push(move)
                        eval_score, _ = self.minimax(board, depth - 1, alpha, beta, True)
                        board.pop()
                        
                        if eval_score < min_eval:
                            min_eval = eval_score
                            best_move = move
                        
                        beta = min(beta, eval_score)
                        if beta <= alpha:
                            break
                    
                    return min_eval, best_move
        
        bot_without_qs = BotWithoutQS(depth=2, name="WithoutQS")
        
        # Get moves from both bots
        move_with_qs = bot_with_qs.get_best_move(self.board)
        move_without_qs = bot_without_qs.get_best_move(self.board)
        
        # The moves might be different due to quiescence search
        # But more importantly, the bot with QS should evaluate more nodes
        self.assertGreaterEqual(bot_with_qs.nodes_evaluated, bot_without_qs.nodes_evaluated)


class TestChessBotQSPerformance(unittest.TestCase):
    """Performance tests for the ChessBot with quiescence search."""
    
    def test_quiescence_search_performance(self):
        """Test performance of quiescence search."""
        bot = ChessBot(depth=3, name="PerfBot")
        board = chess.Board()
        
        # Move to a more complex position
        board.push_san("e4")
        board.push_san("e5")
        board.push_san("Nf3")
        board.push_san("Nc6")
        
        # Measure time for quiescence search
        start_time = time.time()
        bot.quiescence_search(board, float('-inf'), float('inf'), True)
        end_time = time.time()
        
        # Should complete in a reasonable time
        self.assertLess(end_time - start_time, 5.0)  # Should take less than 5 seconds
    
    def test_nodes_evaluated_tracking(self):
        """Test that nodes_evaluated is tracked correctly."""
        bot = ChessBot(depth=2, name="NodeTracker")
        board = chess.Board()
        
        # Reset counter
        bot.nodes_evaluated = 0
        
        # Get best move
        bot.get_best_move(board)
        
        # Should have evaluated some nodes
        self.assertGreater(bot.nodes_evaluated, 0)


def run_performance_test():
    """Run a performance test to check bot speed."""
    print("\nRunning performance test for ChessBot with Quiescence Search...")
    
    bot = ChessBot(depth=3, name="PerfBotQS")
    board = chess.Board()
    
    start_time = time.time()
    
    # Test 5 moves
    for i in range(5):
        move = bot.get_best_move(board)
        if move:
            board.push(move)
        else:
            break
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"Performance test completed:")
    print(f"- 5 moves calculated in {total_time:.2f} seconds")
    print(f"- Average time per move: {total_time/5:.2f} seconds")
    print(f"- Total nodes evaluated: {bot.nodes_evaluated}")


if __name__ == "__main__":
    # Run unit tests
    print("Running Chess Bot QS Unit Tests...")
    unittest.main(verbosity=2, exit=False)
    
    # Run performance test
    run_performance_test()