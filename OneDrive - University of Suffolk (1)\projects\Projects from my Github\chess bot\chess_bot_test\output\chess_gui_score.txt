PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score.py
Move: d4, Eval before: 40, Eval after: -65, Change: -105
ChessBot evaluated 868 positions in 0.22 seconds
Move: Nc6, Eval before: -65, Eval after: -19, Change: 46
Move: Nf3, Eval before: -19, Eval after: -69, Change: -50
ChessBot evaluated 1883 positions in 0.54 seconds
Move: Nf6, Eval before: -69, Eval after: -15, Change: 54
Move: Nc3, Eval before: -15, Eval after: -73, Change: -58
<PERSON>Bot evaluated 2079 positions in 0.60 seconds
Move: Nb4, Eval before: -73, Eval after: 45, Change: 118
Move: a3, Eval before: 45, Eval after: -109, Change: -154
<PERSON><PERSON><PERSON> evaluated 6423 positions in 1.76 seconds
Move: Nc6, Eval before: -109, Eval after: -1, Change: 108
Move: Bg5, Eval before: -1, Eval after: -111, Change: -110
ChessBot evaluated 2559 positions in 0.68 seconds
Move: Ng4, Eval before: -111, Eval after: 17, Change: 128
Move: h3, Eval before: 17, Eval after: -147, Change: -164
<PERSON><PERSON><PERSON> evaluated 8006 positions in 2.25 seconds
Move: Nf6, <PERSON><PERSON> before: -147, <PERSON>l after: -31, Change: 116
Move: e3, Eval before: -31, Eval after: -171, Change: -140
ChessBot evaluated 2616 positions in 0.75 seconds
Move: Nh5, Eval before: -171, Eval after: -1, Change: 170
Move: g4, Eval before: -1, Eval after: -174, Change: -173
ChessBot evaluated 6285 positions in 2.00 seconds
Move: Nf6, Eval before: -174, Eval after: -90, Change: 84
Move: Bb5, Eval before: -90, Eval after: -214, Change: -124
ChessBot evaluated 1866 positions in 0.65 seconds
Move: Rg8, Eval before: -214, Eval after: -84, Change: 130
Move: d5, Eval before: -84, Eval after: -215, Change: -131
ChessBot evaluated 2551 positions in 0.75 seconds
Move: a6, Eval before: -215, Eval after: -45, Change: 170
Move: Bxc6, Eval before: -45, Eval after: 161, Change: 206
ChessBot evaluated 1299 positions in 0.38 seconds
Move: dxc6, Eval before: 161, Eval after: -27, Change: -188
Move: Bxf6, Eval before: -27, Eval after: 177, Change: 204
ChessBot evaluated 4035 positions in 1.23 seconds
Move: gxf6, Eval before: 177, Eval after: -1, Change: -178
Move: dxc6, Eval before: -1, Eval after: -43, Change: -42
ChessBot evaluated 9575 positions in 2.80 seconds
Move: Bxg4, Eval before: -43, Eval after: -10, Change: 33
Move: Qxd8+, Eval before: -10, Eval after: 806, Change: 816
ChessBot evaluated 1165 positions in 0.36 seconds
Move: Rxd8, Eval before: 806, Eval after: -26, Change: -832
Move: Ke2, Eval before: -26, Eval after: -154, Change: -128
ChessBot evaluated 15918 positions in 4.78 seconds
Move: Bh5, Eval before: -154, Eval after: -32, Change: 122






=======================================


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score.py
Move: Nf3 (White), Move#: 1
  Eval before: 0, Eval after: 54, Change: 54
  Quality: Okay
ChessBot evaluated 779 positions in 0.20 seconds
Move: Nf6 (Black), Move#: 2
  Eval before: 54, Eval after: 0, Change: -54
  Quality: Okay
Move: Ne5 (White), Move#: 3
  Eval before: 0, Eval after: 22, Change: 22
  Quality: Okay
ChessBot evaluated 1637 positions in 0.41 seconds
Move: Ne4 (Black), Move#: 4
  Eval before: 22, Eval after: 0, Change: -22
  Quality: Okay
Move: Nxf7 (White), Move#: 5
  Eval before: 0, Eval after: 126, Change: 126
  Quality: Good
ChessBot evaluated 790 positions in 0.19 seconds
Move: Kxf7 (Black), Move#: 6
  Eval before: 126, Eval after: -216, Change: -342
  Quality: Excellent
ChessBot evaluated 693 positions in 0.24 seconds
Move: Nf3 (White), Move#: 1
  Eval before: 0, Eval after: 54, Change: 54
  Quality: Okay
ChessBot evaluated 779 positions in 0.29 seconds
Move: Nf6 (Black), Move#: 2
  Eval before: 54, Eval after: 0, Change: -54
  Quality: Okay
ChessBot evaluated 757 positions in 0.27 seconds
Move: Ne5 (White), Move#: 3
  Eval before: 0, Eval after: 22, Change: 22
  Quality: Okay
ChessBot evaluated 1637 positions in 0.59 seconds
Move: Ne4 (Black), Move#: 4
  Eval before: 22, Eval after: 0, Change: -22
  Quality: Okay
ChessBot evaluated 1980 positions in 0.78 seconds
Move: d3 (White), Move#: 5
  Eval before: 0, Eval after: -8, Change: -8
  Quality: Okay
ChessBot evaluated 1649 positions in 0.73 seconds
Move: Nc6 (Black), Move#: 6
  Eval before: -8, Eval after: -62, Change: -54
  Quality: Okay
ChessBot evaluated 1073 positions in 0.48 seconds
Move: Nxc6 (White), Move#: 7
  Eval before: -62, Eval after: 264, Change: 326
  Quality: Excellent
ChessBot evaluated 976 positions in 0.38 seconds
Move: dxc6 (Black), Move#: 8
  Eval before: 264, Eval after: -68, Change: -332
  Quality: Excellent
ChessBot evaluated 1378 positions in 0.58 seconds
Move: dxe4 (White), Move#: 9
  Eval before: -68, Eval after: 291, Change: 359
  Quality: Excellent
ChessBot evaluated 349 positions in 0.08 seconds
Move: Bg4 (Black), Move#: 10
  Eval before: 291, Eval after: 277, Change: -14
  Quality: Okay
ChessBot evaluated 538 positions in 0.18 seconds
Move: Nc3 (White), Move#: 11
  Eval before: 277, Eval after: 327, Change: 50
  Quality: Excellent
ChessBot evaluated 605 positions in 0.15 seconds
Move: Qxd1+ (Black), Move#: 12
  Eval before: 327, Eval after: -639, Change: -966
  Quality: Excellent
ChessBot evaluated 188 positions in 0.07 seconds
Move: Kxd1 (White), Move#: 13
  Eval before: -639, Eval after: 325, Change: 964
  Quality: Excellent
ChessBot evaluated 2859 positions in 0.77 seconds
Move: Rd8+ (Black), Move#: 14
  Eval before: 325, Eval after: 275, Change: -50
  Quality: Excellent
ChessBot evaluated 268 positions in 0.06 seconds
Move: Ke1 (White), Move#: 15
  Eval before: 275, Eval after: 315, Change: 40
  Quality: Good
ChessBot evaluated 3967 positions in 1.04 seconds
Move: Rd4 (Black), Move#: 16
  Eval before: 315, Eval after: 311, Change: -4
  Quality: Okay
ChessBot evaluated 2006 positions in 0.47 seconds
Move: Be3 (White), Move#: 17
  Eval before: 311, Eval after: 315, Change: 4
  Quality: Okay
ChessBot evaluated 1399 positions in 0.42 seconds
Move: Rc4 (Black), Move#: 18
  Eval before: 315, Eval after: 335, Change: 20
  Quality: Okay
ChessBot evaluated 2073 positions in 0.59 seconds
Move: Bxa7 (White), Move#: 19
  Eval before: 335, Eval after: 483, Change: 148
  Quality: Excellent
ChessBot evaluated 2909 positions in 0.98 seconds
Move: Rg8 (Black), Move#: 20
  Eval before: 483, Eval after: 483, Change: 0
  Quality: Okay
ChessBot evaluated 1888 positions in 0.73 seconds
Move: Bb8 (White), Move#: 21
  Eval before: 483, Eval after: 477, Change: -6
  Quality: Okay
ChessBot evaluated 1086 positions in 0.29 seconds
Move: Rh8 (Black), Move#: 22
  Eval before: 477, Eval after: 477, Change: 0
  Quality: Okay
ChessBot evaluated 1299 positions in 0.31 seconds
Move: Bxc7 (White), Move#: 23
  Eval before: 477, Eval after: 641, Change: 164
  Quality: Excellent
ChessBot evaluated 2083 positions in 0.58 seconds
Move: Kd7 (Black), Move#: 24
  Eval before: 641, Eval after: 639, Change: -2
  Quality: Okay
ChessBot evaluated 1613 positions in 0.38 seconds
Move: Be5 (White), Move#: 25
  Eval before: 639, Eval after: 639, Change: 0
  Quality: Okay
ChessBot evaluated 1721 positions in 0.41 seconds
Move: Ke6 (Black), Move#: 26
  Eval before: 639, Eval after: 647, Change: 8
  Quality: Okay
ChessBot evaluated 1537 positions in 0.46 seconds
Move: e3 (White), Move#: 27
  Eval before: 647, Eval after: 629, Change: -18
  Quality: Okay
ChessBot evaluated 1047 positions in 0.32 seconds
Move: Rc5 (Black), Move#: 28
  Eval before: 629, Eval after: 635, Change: 6
  Quality: Okay
Move: Bxg7 (White), Move#: 29
  Eval before: 635, Eval after: 779, Change: 144
  Quality: Excellent
ChessBot evaluated 829 positions in 0.35 seconds
Move: Bxg7 (Black), Move#: 30
  Eval before: 779, Eval after: 411, Change: -368
  Quality: Excellent
Move: d4 (White), Move#: 1
  Eval before: 0, Eval after: -9, Change: -9
  Quality: Okay
ChessBot evaluated 819 positions in 0.31 seconds
Move: Nc6 (Black), Move#: 2
  Eval before: -9, Eval after: -63, Change: -54
  Quality: Okay
Move: Qd3 (White), Move#: 3
  Eval before: -63, Eval after: -33, Change: 30
  Quality: Okay
ChessBot evaluated 1537 positions in 0.54 seconds
Move: Nb4 (Black), Move#: 4
  Eval before: -33, Eval after: -31, Change: 2
  Quality: Okay
Move: Qa6 (White), Move#: 5
  Eval before: -31, Eval after: -31, Change: 0
  Quality: Okay
ChessBot evaluated 767 positions in 0.26 seconds
Move: Nxc2+ (Black), Move#: 6
  Eval before: -31, Eval after: -259, Change: -228
  Quality: Excellent
Move: Kd2 (White), Move#: 7
  Eval before: -259, Eval after: -191, Change: 68
  Quality: Good
ChessBot evaluated 1879 positions in 0.60 seconds
Move: bxa6 (Black), Move#: 8
  Eval before: -191, Eval after: -1079, Change: -888
  Quality: Excellent
Move: Kxc2 (White), Move#: 9
  Eval before: -1079, Eval after: -733, Change: 346
  Quality: Excellent
ChessBot evaluated 791 positions in 0.21 seconds
Move: Rb8 (Black), Move#: 10
  Eval before: -733, Eval after: -747, Change: -14
  Quality: Okay
Move: Kc3 (White), Move#: 11
  Eval before: -747, Eval after: -749, Change: -2
  Quality: Okay
ChessBot evaluated 948 positions in 0.26 seconds
Move: Nf6 (Black), Move#: 12
  Eval before: -749, Eval after: -803, Change: -54
  Quality: Excellent
Move: Kc4 (White), Move#: 13
  Eval before: -803, Eval after: -803, Change: 0
  Quality: Okay
ChessBot evaluated 1704 positions in 0.46 seconds
Move: d5+ (Black), Move#: 14
  Eval before: -803, Eval after: -838, Change: -35
  Quality: Good
Move: Kc5 (White), Move#: 15
  Eval before: -838, Eval after: -794, Change: 44
  Quality: Good
ChessBot evaluated 1332 positions in 0.35 seconds
Move: Kd7 (Black), Move#: 16
  Eval before: -794, Eval after: -786, Change: 8
  Quality: Okay
Move: b4 (White), Move#: 17
  Eval before: -786, Eval after: -825, Change: -39
  Quality: Inaccurate
ChessBot evaluated 796 positions in 0.20 seconds
Move: Rg8 (Black), Move#: 18
  Eval before: -825, Eval after: -823, Change: 2
  Quality: Okay
Move: Nf3 (White), Move#: 19
  Eval before: -823, Eval after: -767, Change: 56
  Quality: Good
ChessBot evaluated 623 positions in 0.15 seconds
Move: Rb5# (Black), Move#: 20
  Eval before: -767, Eval after: -20000, Change: -19233
  Quality: Excellent







--------------------------------------



PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score.py
Move: Nf3, Eval before: 0, Eval after: 54, Change: 54
ChessBot evaluated 779 positions in 0.21 seconds
Move: Nf6, Eval before: 54, Eval after: 0, Change: -54
Move: Ne5, Eval before: 0, Eval after: 126, Change: 126
ChessBot evaluated 1637 positions in 0.44 seconds
Move: Ne4, Eval before: 126, Eval after: -117, Change: -243
Move: Nxf7, Eval before: -117, Eval after: -162, Change: -45
ChessBot evaluated 790 positions in 0.21 seconds
Move: Kxf7, Eval before: -162, Eval after: -333, Change: -171
Move: f3, Eval before: -333, Eval after: -179, Change: 154
ChessBot evaluated 1222 positions in 0.43 seconds
Move: Ng5, Eval before: -179, Eval after: -281, Change: -102
Move: f4, Eval before: -281, Eval after: -204, Change: 77
ChessBot evaluated 977 positions in 0.29 seconds
Move: Ne4, Eval before: -204, Eval after: -324, Change: -120
Move: f5, Eval before: -324, Eval after: -264, Change: 60
ChessBot evaluated 1416 positions in 0.34 seconds
Move: Kf6, Eval before: -264, Eval after: -376, Change: -112
Move: d3, Eval before: -376, Eval after: -205, Change: 171
ChessBot evaluated 1015 positions in 0.25 seconds
Move: Nc5, Eval before: -205, Eval after: -362, Change: -157
Move: d4, Eval before: -362, Eval after: -221, Change: 141
ChessBot evaluated 1010 positions in 0.30 seconds
Move: Na4, Eval before: -221, Eval after: -345, Change: -124
Move: b3, Eval before: -345, Eval after: -221, Change: 124
ChessBot evaluated 790 positions in 0.27 seconds
Move: Nb6, Eval before: -221, Eval after: -305, Change: -84
Move: e3, Eval before: -305, Eval after: -331, Change: -26
ChessBot evaluated 990 positions in 0.33 seconds
Move: Nc6, Eval before: -331, Eval after: -432, Change: -101
Move: Bb5, Eval before: -432, Eval after: -179, Change: 253
ChessBot evaluated 1071 positions in 0.43 seconds
Move: Nb4, Eval before: -179, Eval after: -461, Change: -282
Move: c3, Eval before: -461, Eval after: -225, Change: 236
ChessBot evaluated 1681 positions in 0.47 seconds
Move: N4d5, Eval before: -225, Eval after: -470, Change: -245
Move: c4, Eval before: -470, Eval after: -219, Change: 251
ChessBot evaluated 1446 positions in 0.58 seconds
Move: Nb4, Eval before: -219, Eval after: -454, Change: -235
Move: c5, Eval before: -454, Eval after: -251, Change: 203
ChessBot evaluated 1644 positions in 0.71 seconds
Move: N6d5, Eval before: -251, Eval after: -494, Change: -243
Move: Nc3, Eval before: -494, Eval after: -535, Change: -41
ChessBot evaluated 1033 positions in 0.48 seconds
Move: Nxc3, Eval before: -535, Eval after: -992, Change: -457
Move: Bxd7, Eval before: -992, Eval after: -1083, Change: -91
ChessBot evaluated 1538 positions in 0.59 seconds
Move: Nxd1, Eval before: -1083, Eval after: -1419, Change: -336
Move: Bxc8, Eval before: -1419, Eval after: -1123, Change: 296
ChessBot evaluated 1269 positions in 0.42 seconds
Move: Rxc8, Eval before: -1123, Eval after: -1310, Change: -187
Move: Kxd1, Eval before: -1310, Eval after: -1169, Change: 141
ChessBot evaluated 1615 positions in 0.39 seconds
Move: Qd5, Eval before: -1169, Eval after: -1301, Change: -132









------------------------------------------------




PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score.py
Move: Nf3, Eval before: 0, Eval after: 54, Change: 54
ChessBot evaluated 779 positions in 0.27 seconds
Move: Nf6, Eval before: 54, Eval after: 0, Change: -54
Move: Ne5, Eval before: 0, Eval after: 126, Change: 126
ChessBot evaluated 1641 positions in 0.65 seconds
Move: Ne4, Eval before: 126, Eval after: -117, Change: -243
Move: Nxf7, Eval before: -117, Eval after: -172, Change: -55
ChessBot evaluated 790 positions in 0.33 seconds
Move: Kxf7, Eval before: -172, Eval after: -343, Change: -171
Move: Nf3, Eval before: 0, Eval after: 54, Change: 54
ChessBot evaluated 779 positions in 0.34 seconds
Move: Nf6, Eval before: 54, Eval after: 0, Change: -54
Move: Ng5, Eval before: 0, Eval after: 90, Change: 90
ChessBot evaluated 1529 positions in 0.61 seconds
Move: Ng4, Eval before: 90, Eval after: -96, Change: -186
Move: Ne6, Eval before: -96, Eval after: -272, Change: -176
ChessBot evaluated 1043 positions in 0.44 seconds
Move: dxe6, Eval before: -272, Eval after: -483, Change: -211
Move: f3, Eval before: -483, Eval after: -320, Change: 163
ChessBot evaluated 1370 positions in 0.56 seconds
Move: Ne3, Eval before: -320, Eval after: -983, Change: -663
Move: d4, Eval before: -983, Eval after: -926, Change: 57
Tactical blunder detected: d4 - Left Queen in danger
ChessBot evaluated 997 positions in 0.44 seconds
Move: Nxd1, Eval before: -926, Eval after: -1145, Change: -219
Move: Nf3, Eval before: 0, Eval after: 54, Change: 54
ChessBot evaluated 779 positions in 0.33 seconds
Move: Nf6, Eval before: 54, Eval after: 0, Change: -54
Move: Ng5, Eval before: 0, Eval after: 90, Change: 90
ChessBot evaluated 1529 positions in 0.58 seconds
Move: Ng4, Eval before: 90, Eval after: -96, Change: -186
Move: Ne6, Eval before: -96, Eval after: -272, Change: -176
ChessBot evaluated 1043 positions in 0.41 seconds
Move: dxe6, Eval before: -272, Eval after: -483, Change: -211
Move: f3, Eval before: -483, Eval after: -320, Change: 163
ChessBot evaluated 1370 positions in 0.59 seconds
Move: Ne3, Eval before: -320, Eval after: -983, Change: -663
Move: dxe3, Eval before: -983, Eval after: -32, Change: 951
ChessBot evaluated 260 positions in 0.10 seconds
Move: Qxd1+, Eval before: -32, Eval after: -86, Change: -54
Move: Kf2, Eval before: -86, Eval after: -1196, Change: -1110
Tactical blunder detected: Kf2 - Left Bishop hanging
ChessBot evaluated 1297 positions in 0.39 seconds
Move: Qxc1, Eval before: -1196, Eval after: -1572, Change: -376
Move: Kg3, Eval before: -1572, Eval after: -1363, Change: 209
ChessBot evaluated 1072 positions in 0.32 seconds
Move: Qxb2, Eval before: -1363, Eval after: -1840, Change: -477
Move: Nc3, Eval before: -1840, Eval after: -1828, Change: 12
Tactical blunder detected: Nc3 - Left Rook hanging
ChessBot evaluated 1241 positions in 0.33 seconds
Move: Qxa1, Eval before: -1828, Eval after: -2162, Change: -334
Move: Nb1, Eval before: -2162, Eval after: -2184, Change: -22
Tactical blunder detected: Nb1 - Lost Knight to capture
ChessBot evaluated 917 positions in 0.23 seconds
Move: Qxb1, Eval before: -2184, Eval after: -2500, Change: -316
Move: Kf2, Eval before: -2500, Eval after: -2374, Change: 126
ChessBot evaluated 1277 positions in 0.34 seconds
Move: Nc6, Eval before: -2374, Eval after: -2568, Change: -194
Move: Kg1, Eval before: -2568, Eval after: -2427, Change: 141
ChessBot evaluated 1369 positions in 0.34 seconds
Move: Bd7, Eval before: -2427, Eval after: -2602, Change: -175
Move: g4, Eval before: -2602, Eval after: -2478, Change: 124
ChessBot evaluated 1659 positions in 0.44 seconds
Move: Ne5, Eval before: -2478, Eval after: -2667, Change: -189
Move: h3, Eval before: -2667, Eval after: -2538, Change: 129
ChessBot evaluated 2396 positions in 0.67 seconds
Move: Bb5, Eval before: -2538, Eval after: -2696, Change: -158
Move: f4, Eval before: -2696, Eval after: -2373, Change: 323
ChessBot evaluated 1972 positions in 0.64 seconds
Move: Nc4, Eval before: -2373, Eval after: -2703, Change: -330
Move: Kf2, Eval before: -2703, Eval after: -2520, Change: 183
ChessBot evaluated 2180 positions in 0.65 seconds
Move: O-O-O, Eval before: -2520, Eval after: -2729, Change: -209
Move: Kf3, Eval before: -2729, Eval after: -2556, Change: 173
ChessBot evaluated 3613 positions in 1.07 seconds
Move: Bc6+, Eval before: -2556, Eval after: -2752, Change: -196
Move: e4, Eval before: -2752, Eval after: -2557, Change: 195
ChessBot evaluated 2510 positions in 1.08 seconds
Move: Qe1, Eval before: -2557, Eval after: -2747, Change: -190
Move: Bg2, Eval before: -2747, Eval after: -2413, Change: 334
ChessBot evaluated 1998 positions in 0.63 seconds
Move: Bxe4+, Eval before: -2413, Eval after: -20000, Change: -17587
Move: Kxe4, Eval before: -20000, Eval after: -20000, Change: 0
ChessBot evaluated 1690 positions in 0.71 seconds
Move: Qxe2#, Eval before: -20000, Eval after: -20000, Change: 0
Move: Nf3, Eval before: 0, Eval after: 54, Change: 54
ChessBot evaluated 779 positions in 0.29 seconds
Move: Nf6, Eval before: 54, Eval after: 0, Change: -54
Move: Ne5, Eval before: 0, Eval after: 126, Change: 126
ChessBot evaluated 1641 positions in 0.56 seconds
Move: Ne4, Eval before: 126, Eval after: -117, Change: -243
Move: d3, Eval before: -117, Eval after: 133, Change: 250
ChessBot evaluated 1681 positions in 0.64 seconds
Move: Nc6, Eval before: 133, Eval after: -68, Change: -201
Move: Nxc6, Eval before: -68, Eval after: 291, Change: 359
ChessBot evaluated 974 positions in 0.30 seconds
Move: dxc6, Eval before: 291, Eval after: -202, Change: -493
Move: dxe4, Eval before: -202, Eval after: 291, Change: 493
ChessBot evaluated 293 positions in 0.07 seconds
Move: Be6, Eval before: 291, Eval after: 247, Change: -44
Move: Qxd8+, Eval before: 247, Eval after: 299, Change: 52
ChessBot evaluated 428 positions in 0.10 seconds
Move: Rxd8, Eval before: 299, Eval after: 173, Change: -126
Move: Bg5, Eval before: 173, Eval after: 409, Change: 236
ChessBot evaluated 3754 positions in 1.60 seconds
Move: f6, Eval before: 409, Eval after: 171, Change: -238
Move: Bxf6, Eval before: 171, Eval after: 137, Change: -34
Tactical blunder detected: Bxf6 - Lost Bishop to capture
ChessBot evaluated 1373 positions in 0.54 seconds
Move: exf6, Eval before: 137, Eval after: 15, Change: -122
Move: e5, Eval before: 15, Eval after: 9, Change: -6
ChessBot evaluated 1766 positions in 0.72 seconds
Move: Bb4+, Eval before: 9, Eval after: -136, Change: -145
PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score.py
Move: Nf3, Eval before: 0, Eval after: 54, Change: 54
Move analysis: Nf3 - Okay (slight improvement (+0.5))
ChessBot evaluated 2228 positions in 16.22 seconds
Move: e5, Eval before: 54, Eval after: -179, Change: -233
Move analysis: e5 - Good (improved position by 2.3 pawns)
Move: Nxe5, Eval before: -179, Eval after: 598, Change: 777
Move analysis: Nxe5 - Excellent (gained 7.8 pawns)
ChessBot evaluated 4067 positions in 35.61 seconds
Move: Qg5, Eval before: 598, Eval after: -9202, Change: -9800
Move analysis: Qg5 - Excellent (gained 98.0 pawns; created strong threats)
Move: Nxf7, Eval before: -9202, Eval after: 1207, Change: 10409
Move analysis: Nxf7 - Excellent (gained 104.1 pawns; created strong threats)
ChessBot evaluated 2509 positions in 19.16 seconds
Move: Qe7, Eval before: 1207, Eval after: -18292, Change: -19499
Move analysis: Qe7 - Poor (gained 195.0 pawns; ignored important threat; created strong threats)
ChessBot search depth set to 2
Move: Nxh8, Eval before: -18292, Eval after: 907, Change: 19199
Move analysis: Nxh8 - Excellent (gained 192.0 pawns; created strong threats)
ChessBot evaluated 145 positions in 0.97 seconds
Move: Qd8, Eval before: 907, Eval after: 81, Change: -826
Move analysis: Qd8 - Excellent (gained 8.3 pawns; created strong threats)
Move: Ng6, Eval before: 81, Eval after: 712, Change: 631
Move analysis: Ng6 - Excellent (gained 6.3 pawns; allowed serious threats)
ChessBot evaluated 71 positions in 0.46 seconds
Move: hxg6, Eval before: 712, Eval after: 614, Change: -98
Move analysis: hxg6 - Poor (slight improvement (****); allowed serious threats)
Move: Nc3, Eval before: 614, Eval after: 701, Change: 87
Move analysis: Nc3 - Okay (slight improvement (+0.9))
ChessBot evaluated 97 positions in 0.61 seconds
Move: Nf6, Eval before: 701, Eval after: 404, Change: -297
Move analysis: Nf6 - Good (improved position by 3.0 pawns)
Move: e4, Eval before: 404, Eval after: 974, Change: 570
Move analysis: e4 - Brilliant (gained 5.7 pawns; brilliant sacrifice)
ChessBot evaluated 329 positions in 3.05 seconds
Move: b5, Eval before: 974, Eval after: 306, Change: -668
Move analysis: b5 - Brilliant (gained 6.7 pawns; created tactical pressure; brilliant sacrifice)
Move: e5, Eval before: 306, Eval after: 956, Change: 650
Move analysis: e5 - Brilliant (gained 6.5 pawns; created tactical pressure; brilliant sacrifice)
ChessBot evaluated 241 positions in 1.77 seconds
Move: Ng8, Eval before: 956, Eval after: 619, Change: -337
Move analysis: Ng8 - Excellent (gained 3.4 pawns; created strong threats)
PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score.py
Move: Nf3, Eval before: 0, Eval after: 54, Change: 54
Move analysis: Nf3 - Okay (reasonable opening play)
ChessBot evaluated 2053 positions in 0.98 seconds
Move: Nf6, Eval before: 54, Eval after: 0, Change: -54
Move analysis: Nf6 - Okay (reasonable opening play)
Move: Ne5, Eval before: 0, Eval after: 62, Change: 62
Move analysis: Ne5 - Okay (reasonable opening play)
ChessBot evaluated 1137 positions in 0.73 seconds
Move: Ne4, Eval before: 62, Eval after: 0, Change: -62
Move analysis: Ne4 - Okay (reasonable opening play)
Move: Nxf7, Eval before: 0, Eval after: 306, Change: 306
Move analysis: Nxf7 - Excellent (strong opening move (****))
ChessBot evaluated 754 positions in 0.46 seconds
Move: Kxf7, Eval before: 306, Eval after: -181, Change: -487
Move analysis: Kxf7 - Excellent (strong opening move (****))
Move: d4, Eval before: -181, Eval after: -182, Change: -1
Move analysis: d4 - Okay (maintained position)
ChessBot evaluated 1458 positions in 1.06 seconds
Move: e5, Eval before: -182, Eval after: -173, Change: 9
Move analysis: e5 - Okay (maintained position)
Move: Qd2, Eval before: -173, Eval after: -164, Change: 9
Move analysis: Qd2 - Okay (slight improvement (+0.1))
ChessBot evaluated 2042 positions in 1.32 seconds
Move: Nxd2, Eval before: -164, Eval after: -737, Change: -573
Move analysis: Nxd2 - Excellent (gained 5.7 pawns)
Move: Bxd2, Eval before: -737, Eval after: -660, Change: 77
Move analysis: Bxd2 - Okay (slight improvement (+0.8))
ChessBot evaluated 1667 positions in 0.95 seconds
Move: exd4, Eval before: -660, Eval after: -797, Change: -137
Move analysis: exd4 - Good (improved by 1.4 pawns)
Move: Bh6, Eval before: -797, Eval after: -1135, Change: -338
Move analysis: Bh6 - Blunder (major loss of 3.4 pawns; left Bishop hanging)
ChessBot evaluated 3335 positions in 1.64 seconds
Move: Bb4+, Eval before: -1135, Eval after: -21225, Change: -20090
Move analysis: Bb4+ - Excellent (gained 200.9 pawns)
Move: Bd2, Eval before: -21225, Eval after: -517, Change: 20708
Move analysis: Bd2 - Excellent (gained 207.1 pawns)
ChessBot evaluated 2680 positions in 1.67 seconds
Move: Qe7, Eval before: -517, Eval after: -870, Change: -353
Move analysis: Qe7 - Excellent (gained 3.5 pawns)
Move: Bxb4, Eval before: -870, Eval after: -831, Change: 39
Move analysis: Bxb4 - Okay (slight improvement (+0.4))
ChessBot evaluated 2400 positions in 1.26 seconds
Move: Qxb4+, Eval before: -831, Eval after: -20914, Change: -20083
Move analysis: Qxb4+ - Brilliant (brilliant sacrifice (+200.8))
Move: Nc3, Eval before: -20914, Eval after: -804, Change: 20110
Move analysis: Nc3 - Excellent (gained 201.1 pawns)
ChessBot evaluated 3033 positions in 1.58 seconds
Move: Qxb2, Eval before: -804, Eval after: -1782, Change: -978
Move analysis: Qxb2 - Brilliant (brilliant sacrifice (****))
Move: Nd1, Eval before: -1782, Eval after: -616, Change: 1166
Move analysis: Nd1 - Excellent (gained 11.7 pawns)
ChessBot evaluated 1486 positions in 0.63 seconds
Move: Qxa1, Eval before: -616, Eval after: -1506, Change: -890
Move analysis: Qxa1 - Brilliant (brilliant sacrifice (****))




Move analysis: Nf6 - Okay (reasonable opening play)
Move: Ne5, Eval before: 0, Eval after: 62, Change: 62
Move analysis: Ne5 - Okay (reasonable opening play)
ChessBot evaluated 1137 positions in 0.73 seconds
Move: Ne4, Eval before: 62, Eval after: 0, Change: -62
Move analysis: Ne4 - Okay (reasonable opening play)
Move: Nxf7, Eval before: 0, Eval after: 306, Change: 306
Move analysis: Nxf7 - Excellent (strong opening move (****))
ChessBot evaluated 754 positions in 0.46 seconds
Move: Kxf7, Eval before: 306, Eval after: -181, Change: -487
Move analysis: Kxf7 - Excellent (strong opening move (****))
Move: d4, Eval before: -181, Eval after: -182, Change: -1
Move analysis: d4 - Okay (maintained position)
ChessBot evaluated 1458 positions in 1.06 seconds
Move: e5, Eval before: -182, Eval after: -173, Change: 9
Move analysis: e5 - Okay (maintained position)
Move: Qd2, Eval before: -173, Eval after: -164, Change: 9
Move analysis: Qd2 - Okay (slight improvement (+0.1))
ChessBot evaluated 2042 positions in 1.32 seconds
Move: Nxd2, Eval before: -164, Eval after: -737, Change: -573
Move analysis: Nxd2 - Excellent (gained 5.7 pawns)
Move: Bxd2, Eval before: -737, Eval after: -660, Change: 77
Move analysis: Bxd2 - Okay (slight improvement (+0.8))
ChessBot evaluated 1667 positions in 0.95 seconds
Move: exd4, Eval before: -660, Eval after: -797, Change: -137
Move analysis: exd4 - Good (improved by 1.4 pawns)
Move: Bh6, Eval before: -797, Eval after: -1135, Change: -338
Move analysis: Bh6 - Blunder (major loss of 3.4 pawns; left Bishop hanging)
ChessBot evaluated 3335 positions in 1.64 seconds
Move: Bb4+, Eval before: -1135, Eval after: -21225, Change: -20090
Move analysis: Bb4+ - Excellent (gained 200.9 pawns)
Move: Bd2, Eval before: -21225, Eval after: -517, Change: 20708
Move analysis: Bd2 - Excellent (gained 207.1 pawns)
ChessBot evaluated 2680 positions in 1.67 seconds
Move: Qe7, Eval before: -517, Eval after: -870, Change: -353
Move analysis: Qe7 - Excellent (gained 3.5 pawns)
Move: Bxb4, Eval before: -870, Eval after: -831, Change: 39
Move analysis: Bxb4 - Okay (slight improvement (+0.4))
ChessBot evaluated 2400 positions in 1.26 seconds
Move: Qxb4+, Eval before: -831, Eval after: -20914, Change: -20083
Move analysis: Qxb4+ - Brilliant (brilliant sacrifice (+200.8))
Move: Nc3, Eval before: -20914, Eval after: -804, Change: 20110
Move analysis: Nc3 - Excellent (gained 201.1 pawns)
ChessBot evaluated 3033 positions in 1.58 seconds
Move: Qxb2, Eval before: -804, Eval after: -1782, Change: -978
Move analysis: Qxb2 - Brilliant (brilliant sacrifice (****))
Move: Nd1, Eval before: -1782, Eval after: -616, Change: 1166
Move analysis: Nd1 - Excellent (gained 11.7 pawns)
ChessBot evaluated 1486 positions in 0.63 seconds
Move: Qxa1, Eval before: -616, Eval after: -1506, Change: -890
Move analysis: Qxa1 - Brilliant (brilliant sacrifice (****))
ChessBot evaluated 2312 positions in 1.25 seconds
ChessBot evaluated 5147 positions in 3.08 seconds
ChessBot evaluated 2312 positions in 1.25 seconds
ChessBot evaluated 5147 positions in 3.08 seconds
ChessBot evaluated 2312 positions in 1.25 seconds
ChessBot evaluated 5147 positions in 3.08 seconds
Move: exd4, Eval before: -1995, Eval after: -1871, Change: 124
Move analysis: exd4 - Okay (slight improvement (****))
ChessBot evaluated 3435 positions in 2.51 seconds
Move: Re8+, Eval before: -1871, Eval after: -21931, Change: -20060
Move analysis: Re8+ - Excellent (gained 200.6 pawns)
Move: Be2, Eval before: -21931, Eval after: -1859, Change: 20072
Move analysis: Be2 - Excellent (gained 200.7 pawns)
ChessBot evaluated 2114 positions in 1.62 seconds
Move: Rxe2+, Eval before: -1859, Eval after: -2257, Change: -398
Move analysis: Rxe2+ - Excellent (gained 4.0 pawns)
Move: Kf1, Eval before: -2257, Eval after: -2510, Change: -253
Move analysis: Kf1 - Poor (lost 2.5 pawns; left Knight hanging)
ChessBot evaluated 2722 positions in 1.96 seconds
Move: Rxg2, Eval before: -2510, Eval after: -2696, Change: -186
Move analysis: Rxg2 - Good (improved by 1.9 pawns)
Move: Rg1, Eval before: -2696, Eval after: -2694, Change: 2
Move analysis: Rg1 - Okay (slight improvement (+0.0))
ChessBot evaluated 1971 positions in 1.44 seconds
Move: Rxg1+, Eval before: -2694, Eval after: -22724, Change: -20030
Move analysis: Rxg1+ - Excellent (gained 200.3 pawns)
Move: Kxg1, Eval before: -22724, Eval after: -2612, Change: 20112
Move analysis: Kxg1 - Brilliant (brilliant sacrifice (+201.1))
ChessBot evaluated 1773 positions in 1.06 seconds
Move: Qxd1+, Eval before: -2612, Eval after: -22586, Change: -19974
Move analysis: Qxd1+ - Brilliant (brilliant sacrifice (+199.7))
Move: Kg2, Eval before: -22586, Eval after: -2588, Change: 19998
Move analysis: Kg2 - Excellent (gained 200.0 pawns)
ChessBot evaluated 2481 positions in 1.46 seconds
Move: Qe1, Eval before: -2588, Eval after: -2634, Change: -46
Move analysis: Qe1 - Okay (slight improvement (+0.5))
Move: d3, Eval before: 0, Eval after: -26, Change: -26
Move analysis: d3 - Okay (reasonable opening play)
ChessBot evaluated 848 positions in 0.65 seconds
Move: Nf6, Eval before: -26, Eval after: -80, Change: -54
Move analysis: Nf6 - Okay (reasonable opening play)
Move: e3, Eval before: -80, Eval after: -116, Change: -36
Move analysis: e3 - Okay (reasonable opening play)
ChessBot evaluated 1288 positions in 0.82 seconds
Move: Ng4, Eval before: -116, Eval after: 184, Change: 300
Move analysis: Ng4 - Poor (poor opening move (-3.0); left Knight hanging)
Move: f3, Eval before: 184, Eval after: 128, Change: -56
Move analysis: f3 - Okay (reasonable opening play)
ChessBot evaluated 1024 positions in 0.66 seconds
Move: Ne5, Eval before: 128, Eval after: -208, Change: -336
Move analysis: Ne5 - Excellent (strong opening move (****))
Move: f4, Eval before: -208, Eval after: 112, Change: 320
Move analysis: f4 - Excellent (gained 3.2 pawns)
ChessBot evaluated 1084 positions in 0.58 seconds
Move: Ng4, Eval before: 112, Eval after: 122, Change: 10
Move analysis: Ng4 - Okay (maintained position)
Move: h3, Eval before: 122, Eval after: 82, Change: -40
Move analysis: h3 - Okay (maintained position)
ChessBot evaluated 1050 positions in 0.52 seconds
Move: Nf6, Eval before: 82, Eval after: -218, Change: -300
Move analysis: Nf6 - Excellent (gained 3.0 pawns)
Move: g4, Eval before: -218, Eval after: -267, Change: -49
Move analysis: g4 - Okay (maintained position)
ChessBot evaluated 1391 positions in 0.79 seconds
Move: d5, Eval before: -267, Eval after: -236, Change: 31
Move analysis: d5 - Okay (maintained position)
Move: g5, Eval before: -236, Eval after: -225, Change: 11
Move analysis: g5 - Okay (slight improvement (+0.1))
ChessBot evaluated 1285 positions in 0.68 seconds
Move: Qd7, Eval before: -225, Eval after: -230, Change: -5
Move analysis: Qd7 - Okay (slight improvement (+0.1))
Move: gxf6, Eval before: -230, Eval after: 87, Change: 317
Move analysis: gxf6 - Excellent (gained 3.2 pawns)
ChessBot evaluated 1570 positions in 0.93 seconds
Move: exf6, Eval before: 87, Eval after: 45, Change: -42
Move analysis: exf6 - Okay (slight improvement (+0.4))
Move: Nf3, Eval before: 45, Eval after: 101, Change: 56
Move analysis: Nf3 - Okay (slight improvement (+0.6))
ChessBot evaluated 3782 positions in 2.63 seconds
Move: Bb4+, Eval before: 101, Eval after: 3, Change: -98
Move analysis: Bb4+ - Okay (slight improvement (****))
Move: c3, Eval before: 3, Eval after: 371, Change: 368
Move analysis: c3 - Excellent (gained 3.7 pawns)
ChessBot evaluated 1759 positions in 1.56 seconds
Move: Bxc3+, Eval before: 371, Eval after: 187, Change: -184
Move analysis: Bxc3+ - Good (improved by 1.8 pawns)
Move: Nxc3, Eval before: 187, Eval after: 335, Change: 148
Move analysis: Nxc3 - Okay (slight improvement (****))
ChessBot evaluated 3393 positions in 2.87 seconds
Move: d4, Eval before: 335, Eval after: 316, Change: -19
Move analysis: d4 - Okay (slight improvement (+0.2))
Move: exd4, Eval before: 316, Eval after: 436, Change: 120
Move analysis: exd4 - Okay (slight improvement (****))
ChessBot evaluated 4074 positions in 2.87 seconds
Move: Qe6+, Eval before: 436, Eval after: 331, Change: -105
Move analysis: Qe6+ - Okay (slight improvement (****))
Move: Qe2, Eval before: 331, Eval after: 430, Change: 99
Move analysis: Qe2 - Okay (slight improvement (****))
ChessBot evaluated 444 positions in 0.31 seconds
Move: Nc6, Eval before: 430, Eval after: 374, Change: -56
Move analysis: Nc6 - Okay (slight improvement (+0.6))
Move: Qxe6+, Eval before: 374, Eval after: 556, Change: 182
Move analysis: Qxe6+ - Good (improved by 1.8 pawns)
ChessBot evaluated 642 positions in 0.46 seconds
Move: Bxe6, Eval before: 556, Eval after: 375, Change: -181
Move analysis: Bxe6 - Good (improved by 1.8 pawns)
Move: Nb5, Eval before: 375, Eval after: 383, Change: 8
Move analysis: Nb5 - Okay (slight improvement (+0.1))
ChessBot evaluated 1414 positions in 0.80 seconds
Move: Kd7, Eval before: 383, Eval after: 402, Change: 19
Move analysis: Kd7 - Okay (maintained position)
Move: d5, Eval before: 402, Eval after: 423, Change: 21
Move analysis: d5 - Okay (slight improvement (+0.2))
ChessBot evaluated 1783 positions in 1.28 seconds
Move: Bxd5, Eval before: 423, Eval after: -44, Change: -467
Move analysis: Bxd5 - Brilliant (brilliant sacrifice (****))
Move: Be2, Eval before: -44, Eval after: 290, Change: 334
Move analysis: Be2 - Excellent (gained 3.3 pawns)
ChessBot evaluated 4525 positions in 2.74 seconds
Move: Nb4, Eval before: 290, Eval after: 274, Change: -16
Move analysis: Nb4 - Okay (slight improvement (+0.2))
Move: Rg1, Eval before: 274, Eval after: 265, Change: -9
Move analysis: Rg1 - Okay (maintained position)
Move: Nc2+, Eval before: 265, Eval after: -299, Change: -564
Move: Nc2+, Eval before: 265, Eval after: -299, Change: -564
Move analysis: Nc2+ - Excellent (gained 5.6 pawns)        
Move: Kd2, Eval before: -299, Eval after: 43, Change: 342 
Move analysis: Kd2 - Excellent (gained 3.4 pawns)
ChessBot evaluated 2870 positions in 2.13 seconds
Move: Nxa1, Eval before: 43, Eval after: -189, Change: -232
Move analysis: Nxa1 - Good (improved by 2.3 pawns)  




---------------------------




PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score.py
Move: Nf3, Eval before: 0.0, Eval after: 54.0, Raw change: 54.0, Smoothed: 54.0
Move analysis: Nf3 - Okay (reasonable opening play (+0.5))
ChessBot evaluated 780 positions in 0.46 seconds
Move: Nf6, Eval before: 54.0, Eval after: 0.0, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nf6 - Okay (reasonable opening play (+0.5))
Move: Ng5, Eval before: 0.0, Eval after: 4.0, Raw change: 4.0, Smoothed: 4.0
Move analysis: Ng5 - Okay (neutral opening move)
ChessBot evaluated 1862 positions in 1.19 seconds
Move: Ng4, Eval before: 4.0, Eval after: 0.0, Raw change: -4.0, Smoothed: -4.0
Move analysis: Ng4 - Okay (neutral opening move)
Move: Ne6, Eval before: 0.0, Eval after: 23.0, Raw change: 23.0, Smoothed: 23.0
Move analysis: Ne6 - Okay (reasonable opening play (+0.2))
ChessBot evaluated 1070 positions in 0.67 seconds
Move: dxe6, Eval before: 23.0, Eval after: -327.5, Raw change: -350.5, Smoothed: -350.5
Move analysis: dxe6 - Excellent (strong opening play (****))
Move: Nf3, Eval before: 0.0, Eval after: 54.0, Raw change: 54.0, Smoothed: 54.0
Move analysis: Nf3 - Okay (reasonable opening play (+0.5))
ChessBot evaluated 780 positions in 0.58 seconds
Move: Nf6, Eval before: 54.0, Eval after: 0.0, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nf6 - Okay (reasonable opening play (+0.5))
Move: Ng5, Eval before: 0.0, Eval after: 4.0, Raw change: 4.0, Smoothed: 4.0
Move analysis: Ng5 - Okay (neutral opening move)
ChessBot evaluated 1862 positions in 1.44 seconds
Move: Ng4, Eval before: 4.0, Eval after: 0.0, Raw change: -4.0, Smoothed: -4.0
Move analysis: Ng4 - Okay (neutral opening move)
Move: Nxf7, Eval before: 0.0, Eval after: 231.0, Raw change: 231.0, Smoothed: 231.0
Move analysis: Nxf7 - Excellent (strong opening play (****))
ChessBot evaluated 708 positions in 0.51 seconds
Move: Kxf7, Eval before: 231.0, Eval after: -182.5, Raw change: -413.5, Smoothed: -413.5
Move analysis: Kxf7 - Excellent (strong opening play (****))
Move: Nf3, Eval before: 0.0, Eval after: 54.0, Raw change: 54.0, Smoothed: 54.0
Move analysis: Nf3 - Okay (reasonable opening play (+0.5))
ChessBot evaluated 780 positions in 0.51 seconds
Move: Nf6, Eval before: 54.0, Eval after: 0.0, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nf6 - Okay (reasonable opening play (+0.5))
Move: Ne5, Eval before: 0.0, Eval after: 34.0, Raw change: 34.0, Smoothed: 34.0
Move analysis: Ne5 - Okay (reasonable opening play (+0.3))
ChessBot evaluated 1597 positions in 1.13 seconds
Move: Ne4, Eval before: 34.0, Eval after: 0.0, Raw change: -34.0, Smoothed: -34.0
Move analysis: Ne4 - Okay (reasonable opening play (+0.3))
Move: Nxf7, Eval before: 0.0, Eval after: 201.0, Raw change: 201.0, Smoothed: 201.0
Move analysis: Nxf7 - Excellent (strong opening play (****))
ChessBot evaluated 761 positions in 0.54 seconds
Move: Kxf7, Eval before: 201.0, Eval after: -212.5, Raw change: -413.5, Smoothed: -413.5
Move analysis: Kxf7 - Excellent (strong opening play (****))
Move: d4, Eval before: -212.5, Eval after: -220.5, Raw change: -8.0, Smoothed: -8.0
Move analysis: d4 - Okay (neutral opening move)
ChessBot evaluated 1277 positions in 0.87 seconds
Move: Nc6, Eval before: -220.5, Eval after: -274.5, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nc6 - Okay (reasonable opening play (+0.5))
Move: Qd2, Eval before: -274.5, Eval after: -265.5, Raw change: 9.0, Smoothed: 9.0
Move analysis: Qd2 - Okay (neutral opening move)
ChessBot evaluated 1797 positions in 1.09 seconds
Move: Nxd2, Eval before: -265.5, Eval after: -1156.5, Raw change: -891.0, Smoothed: -534.6
Move analysis: Nxd2 - Excellent (strong opening play (****))
Move: Bxd2, Eval before: -1156.5, Eval after: -787.5, Raw change: 369.0, Smoothed: 369.0
Move analysis: Bxd2 - Excellent (excellent move (****))
ChessBot evaluated 973 positions in 0.50 seconds
Move: Nxd4, Eval before: -787.5, Eval after: -946.5, Raw change: -159.0, Smoothed: -159.0
Move analysis: Nxd4 - Good (good move (****))
Move: Bh6, Eval before: -946.5, Eval after: -954.5, Raw change: -8.0, Smoothed: -8.0
Move analysis: Bh6 - Okay (neutral move)
ChessBot evaluated 967 positions in 0.70 seconds
Move: Nxc2+, Eval before: -954.5, Eval after: -1200.5, Raw change: -246.0, Smoothed: -246.0
Move analysis: Nxc2+ - Excellent (excellent move (****))
Move: Kd2, Eval before: -1200.5, Eval after: -1182.0, Raw change: 18.5, Smoothed: 18.5
Move analysis: Kd2 - Okay (neutral move)
ChessBot evaluated 881 positions in 0.66 seconds
Move: Nxa1, Eval before: -1182.0, Eval after: -1541.0, Raw change: -359.0, Smoothed: -359.0
Move analysis: Nxa1 - Excellent (excellent move (****))
Move: d3, Eval before: 0.0, Eval after: -12.0, Raw change: -12.0, Smoothed: -12.0
Move analysis: d3 - Okay (neutral opening move)
ChessBot evaluated 781 positions in 0.58 seconds
Move: Nf6, Eval before: -12.0, Eval after: -66.0, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nf6 - Okay (reasonable opening play (+0.5))
Move: e3, Eval before: -66.0, Eval after: -88.0, Raw change: -22.0, Smoothed: -22.0
Move analysis: e3 - Okay (reasonable opening play (-0.2))
ChessBot evaluated 991 positions in 0.55 seconds
Move: Nd5, Eval before: -88.0, Eval after: -108.0, Raw change: -20.0, Smoothed: -20.0
Move analysis: Nd5 - Okay (neutral opening move)
Move: f3, Eval before: -108.0, Eval after: -152.0, Raw change: -44.0, Smoothed: -44.0
Move analysis: f3 - Okay (reasonable opening play (-0.4))
ChessBot evaluated 1094 positions in 0.58 seconds
Move: Nb4, Eval before: -152.0, Eval after: -136.0, Raw change: 16.0, Smoothed: 16.0
Move analysis: Nb4 - Okay (neutral opening move)
Move: f4, Eval before: -136.0, Eval after: -138.0, Raw change: -2.0, Smoothed: -2.0
Move analysis: f4 - Okay (neutral opening move)
ChessBot evaluated 1109 positions in 0.53 seconds
Move: N8c6, Eval before: -138.0, Eval after: -188.0, Raw change: -50.0, Smoothed: -50.0
Move analysis: N8c6 - Okay (reasonable opening play (+0.5))
Move: h3, Eval before: -188.0, Eval after: -230.0, Raw change: -42.0, Smoothed: -42.0
Move analysis: h3 - Okay (reasonable opening play (-0.4))
ChessBot evaluated 1394 positions in 1.12 seconds
Move: e6, Eval before: -230.0, Eval after: -220.0, Raw change: 10.0, Smoothed: 10.0
Move analysis: e6 - Okay (neutral opening move)
Move: g4, Eval before: -220.0, Eval after: -269.0, Raw change: -49.0, Smoothed: -49.0
Move analysis: g4 - Okay (acceptable move (-0.5))
ChessBot evaluated 1881 positions in 1.22 seconds
Move: Qh4+, Eval before: -269.0, Eval after: -342.0, Raw change: -73.0, Smoothed: -73.0
Move analysis: Qh4+ - Okay (decent move (+0.7))
Move: Kd2, Eval before: -342.0, Eval after: -302.5, Raw change: 39.5, Smoothed: 39.5
Move analysis: Kd2 - Okay (decent move (+0.4))
ChessBot evaluated 3783 positions in 2.96 seconds
Move: Qf2+, Eval before: -302.5, Eval after: -358.5, Raw change: -56.0, Smoothed: -56.0
Move analysis: Qf2+ - Okay (decent move (+0.6))










---------------------------------------------



PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score.py
Move: Nf3, Eval before: 0.0, Eval after: 54.0, Raw change: 54.0, Smoothed: 54.0
Move analysis: Nf3 - Okay (reasonable opening play (+0.5))
ChessBot evaluated 780 positions in 0.46 seconds
Move: Nf6, Eval before: 54.0, Eval after: 0.0, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nf6 - Okay (reasonable opening play (+0.5))
Move: Ng5, Eval before: 0.0, Eval after: 4.0, Raw change: 4.0, Smoothed: 4.0
Move analysis: Ng5 - Okay (neutral opening move)
ChessBot evaluated 1862 positions in 1.19 seconds
Move: Ng4, Eval before: 4.0, Eval after: 0.0, Raw change: -4.0, Smoothed: -4.0
Move analysis: Ng4 - Okay (neutral opening move)
Move: Ne6, Eval before: 0.0, Eval after: 23.0, Raw change: 23.0, Smoothed: 23.0
Move analysis: Ne6 - Okay (reasonable opening play (+0.2))
ChessBot evaluated 1070 positions in 0.67 seconds
Move: dxe6, Eval before: 23.0, Eval after: -327.5, Raw change: -350.5, Smoothed: -350.5
Move analysis: dxe6 - Excellent (strong opening play (****))
Move: Nf3, Eval before: 0.0, Eval after: 54.0, Raw change: 54.0, Smoothed: 54.0
Move analysis: Nf3 - Okay (reasonable opening play (+0.5))
ChessBot evaluated 780 positions in 0.58 seconds
Move: Nf6, Eval before: 54.0, Eval after: 0.0, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nf6 - Okay (reasonable opening play (+0.5))
Move: Ng5, Eval before: 0.0, Eval after: 4.0, Raw change: 4.0, Smoothed: 4.0
Move analysis: Ng5 - Okay (neutral opening move)
ChessBot evaluated 1862 positions in 1.44 seconds
Move: Ng4, Eval before: 4.0, Eval after: 0.0, Raw change: -4.0, Smoothed: -4.0
Move analysis: Ng4 - Okay (neutral opening move)
Move: Nxf7, Eval before: 0.0, Eval after: 231.0, Raw change: 231.0, Smoothed: 231.0
Move analysis: Nxf7 - Excellent (strong opening play (****))
ChessBot evaluated 708 positions in 0.51 seconds
Move: Kxf7, Eval before: 231.0, Eval after: -182.5, Raw change: -413.5, Smoothed: -413.5
Move analysis: Kxf7 - Excellent (strong opening play (****))
Move: Nf3, Eval before: 0.0, Eval after: 54.0, Raw change: 54.0, Smoothed: 54.0
Move analysis: Nf3 - Okay (reasonable opening play (+0.5))
ChessBot evaluated 780 positions in 0.51 seconds
Move: Nf6, Eval before: 54.0, Eval after: 0.0, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nf6 - Okay (reasonable opening play (+0.5))
Move: Ne5, Eval before: 0.0, Eval after: 34.0, Raw change: 34.0, Smoothed: 34.0
Move analysis: Ne5 - Okay (reasonable opening play (+0.3))
ChessBot evaluated 1597 positions in 1.13 seconds
Move: Ne4, Eval before: 34.0, Eval after: 0.0, Raw change: -34.0, Smoothed: -34.0
Move analysis: Ne4 - Okay (reasonable opening play (+0.3))
Move: Nxf7, Eval before: 0.0, Eval after: 201.0, Raw change: 201.0, Smoothed: 201.0
Move analysis: Nxf7 - Excellent (strong opening play (****))
ChessBot evaluated 761 positions in 0.54 seconds
Move: Kxf7, Eval before: 201.0, Eval after: -212.5, Raw change: -413.5, Smoothed: -413.5
Move analysis: Kxf7 - Excellent (strong opening play (****))
Move: d4, Eval before: -212.5, Eval after: -220.5, Raw change: -8.0, Smoothed: -8.0
Move analysis: d4 - Okay (neutral opening move)
ChessBot evaluated 1277 positions in 0.87 seconds
Move: Nc6, Eval before: -220.5, Eval after: -274.5, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nc6 - Okay (reasonable opening play (+0.5))
Move: Qd2, Eval before: -274.5, Eval after: -265.5, Raw change: 9.0, Smoothed: 9.0
Move analysis: Qd2 - Okay (neutral opening move)
ChessBot evaluated 1797 positions in 1.09 seconds
Move: Nxd2, Eval before: -265.5, Eval after: -1156.5, Raw change: -891.0, Smoothed: -534.6
Move analysis: Nxd2 - Excellent (strong opening play (****))
Move: Bxd2, Eval before: -1156.5, Eval after: -787.5, Raw change: 369.0, Smoothed: 369.0
Move analysis: Bxd2 - Excellent (excellent move (****))
ChessBot evaluated 973 positions in 0.50 seconds
Move: Nxd4, Eval before: -787.5, Eval after: -946.5, Raw change: -159.0, Smoothed: -159.0
Move analysis: Nxd4 - Good (good move (****))
Move: Bh6, Eval before: -946.5, Eval after: -954.5, Raw change: -8.0, Smoothed: -8.0
Move analysis: Bh6 - Okay (neutral move)
ChessBot evaluated 967 positions in 0.70 seconds
Move: Nxc2+, Eval before: -954.5, Eval after: -1200.5, Raw change: -246.0, Smoothed: -246.0
Move analysis: Nxc2+ - Excellent (excellent move (****))
Move: Kd2, Eval before: -1200.5, Eval after: -1182.0, Raw change: 18.5, Smoothed: 18.5
Move analysis: Kd2 - Okay (neutral move)
ChessBot evaluated 881 positions in 0.66 seconds
Move: Nxa1, Eval before: -1182.0, Eval after: -1541.0, Raw change: -359.0, Smoothed: -359.0
Move analysis: Nxa1 - Excellent (excellent move (****))
Move: d3, Eval before: 0.0, Eval after: -12.0, Raw change: -12.0, Smoothed: -12.0
Move analysis: d3 - Okay (neutral opening move)
ChessBot evaluated 781 positions in 0.58 seconds
Move: Nf6, Eval before: -12.0, Eval after: -66.0, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nf6 - Okay (reasonable opening play (+0.5))
Move: e3, Eval before: -66.0, Eval after: -88.0, Raw change: -22.0, Smoothed: -22.0
Move analysis: e3 - Okay (reasonable opening play (-0.2))
ChessBot evaluated 991 positions in 0.55 seconds
Move: Nd5, Eval before: -88.0, Eval after: -108.0, Raw change: -20.0, Smoothed: -20.0
Move analysis: Nd5 - Okay (neutral opening move)
Move: f3, Eval before: -108.0, Eval after: -152.0, Raw change: -44.0, Smoothed: -44.0
Move analysis: f3 - Okay (reasonable opening play (-0.4))
ChessBot evaluated 1094 positions in 0.58 seconds
Move: Nb4, Eval before: -152.0, Eval after: -136.0, Raw change: 16.0, Smoothed: 16.0
Move analysis: Nb4 - Okay (neutral opening move)
Move: f4, Eval before: -136.0, Eval after: -138.0, Raw change: -2.0, Smoothed: -2.0
Move analysis: f4 - Okay (neutral opening move)
ChessBot evaluated 1109 positions in 0.53 seconds
Move: N8c6, Eval before: -138.0, Eval after: -188.0, Raw change: -50.0, Smoothed: -50.0
Move analysis: N8c6 - Okay (reasonable opening play (+0.5))
Move: h3, Eval before: -188.0, Eval after: -230.0, Raw change: -42.0, Smoothed: -42.0
Move analysis: h3 - Okay (reasonable opening play (-0.4))
ChessBot evaluated 1394 positions in 1.12 seconds
Move: e6, Eval before: -230.0, Eval after: -220.0, Raw change: 10.0, Smoothed: 10.0
Move analysis: e6 - Okay (neutral opening move)
Move: g4, Eval before: -220.0, Eval after: -269.0, Raw change: -49.0, Smoothed: -49.0
Move analysis: g4 - Okay (acceptable move (-0.5))
ChessBot evaluated 1881 positions in 1.22 seconds
hange: -73.0, Smoothed: -73.0
Move analysis: Qh4+ - Okay (decent move (+0.7))
Move: Kd2, Eval before: -342.0, Eval after: -302.5, Raw change: 39.5, Smoothed: 39.5
Move analysis: Kd2 - Okay (decent move (+0.4))
ChessBot evaluated 3783 positions in 2.96 seconds
Move: Qf2+, Eval before: -302.5, Eval after: -358.5, Raw change: -56.0, Smoothed: -56.0
Move analysis: Qf2+ - Okay (decent move (+0.6))
PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score.py
Move: Nf3, Eval before: 0.0, Eval after: 54.0, Raw change: 54.0, Smoothed: 54.0
Move analysis: Nf3 - Okay (solid opening move (+0.5))
ChessBot evaluated 780 positions in 0.54 seconds
Move: Nf6, Eval before: 54.0, Eval after: 0.0, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nf6 - Okay (solid opening move (+0.5))
Move: Ng5, Eval before: 0.0, Eval after: 4.0, Raw change: 4.0, Smoothed: 4.0
Move analysis: Ng5 - Okay (reasonable opening play)
ChessBot evaluated 1862 positions in 1.40 seconds
Move: Ng4, Eval before: 4.0, Eval after: 0.0, Raw change: -4.0, Smoothed: -4.0
Move analysis: Ng4 - Okay (reasonable opening play)
Move: Nxf7, Eval before: 0.0, Eval after: 231.0, Raw change: 231.0, Smoothed: 231.0
Move analysis: Nxf7 - Good (strong development (****))
ChessBot evaluated 708 positions in 0.54 seconds
Move: Kxf7, Eval before: 231.0, Eval after: -182.5, Raw change: -413.5, Smoothed: -413.5
Move analysis: Kxf7 - Excellent (outstanding opening (****))
Move: Nf3, Eval before: 0.0, Eval after: 54.0, Raw change: 54.0, Smoothed: 54.0
Move analysis: Nf3 - Okay (solid opening move (+0.5))
ChessBot evaluated 780 positions in 0.45 seconds
Move: Nf6, Eval before: 54.0, Eval after: 0.0, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nf6 - Okay (solid opening move (+0.5))
Move: Ne5, Eval before: 0.0, Eval after: 34.0, Raw change: 34.0, Smoothed: 34.0
Move analysis: Ne5 - Okay (reasonable opening play)
ChessBot evaluated 1597 positions in 0.73 seconds
Move: Ne4, Eval before: 34.0, Eval after: 0.0, Raw change: -34.0, Smoothed: -34.0
Move analysis: Ne4 - Okay (reasonable opening play)
Move: Nxf7, Eval before: 0.0, Eval after: 201.0, Raw change: 201.0, Smoothed: 201.0
Move analysis: Nxf7 - Good (strong development (****))
ChessBot evaluated 761 positions in 0.40 seconds
Move: Kxf7, Eval before: 201.0, Eval after: -212.5, Raw change: -413.5, Smoothed: -413.5
Move analysis: Kxf7 - Excellent (outstanding opening (****))
Move: d4, Eval before: -212.5, Eval after: -220.5, Raw change: -8.0, Smoothed: -8.0
Move analysis: d4 - Okay (acceptable opening (-0.1))
ChessBot evaluated 1277 positions in 0.91 seconds
Move: Nc6, Eval before: -220.5, Eval after: -274.5, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nc6 - Okay (solid opening move (+0.5))
Move: Qd2, Eval before: -274.5, Eval after: -265.5, Raw change: 9.0, Smoothed: 9.0
Move analysis: Qd2 - Okay (reasonable opening play)
ChessBot evaluated 1797 positions in 1.14 seconds
Move: Nxd2, Eval before: -265.5, Eval after: -1156.5, Raw change: -891.0, Smoothed: -534.6
Move analysis: Nxd2 - Excellent (outstanding opening (****))
Move: Bxd2, Eval before: -1156.5, Eval after: -787.5, Raw change: 369.0, Smoothed: 369.0
Move analysis: Bxd2 - Excellent (excellent move (****))
ChessBot evaluated 973 positions in 0.65 seconds
Move: Nxd4, Eval before: -787.5, Eval after: -946.5, Raw change: -159.0, Smoothed: -159.0
Move analysis: Nxd4 - Good (strong move (****))
Move: Bh6, Eval before: -946.5, Eval after: -954.5, Raw change: -8.0, Smoothed: -8.0
Move analysis: Bh6 - Okay (acceptable move (-0.1))
ChessBot evaluated 967 positions in 0.49 seconds
Move: Nxc2+, Eval before: -954.5, Eval after: -1200.5, Raw change: -246.0, Smoothed: -246.0
Move analysis: Nxc2+ - Good (strong move (****))
Move: Kd2, Eval before: -1200.5, Eval after: -1182.0, Raw change: 18.5, Smoothed: 18.5
Move analysis: Kd2 - Okay (reasonable move)
ChessBot evaluated 881 positions in 0.69 seconds
Move: Nxa1, Eval before: -1182.0, Eval after: -1541.0, Raw change: -359.0, Smoothed: -359.0
Move analysis: Nxa1 - Excellent (excellent move (****))
ChessBot evaluated 711 positions in 0.53 seconds
Move: Nf3, Eval before: 0.0, Eval after: 54.0, Raw change: 54.0, Smoothed: 54.0
Move analysis: Nf3 - Okay (solid opening move (+0.5))
Move: f5, Eval before: 54.0, Eval after: 100.0, Raw change: 46.0, Smoothed: 46.0
Move analysis: f5 - Inaccurate (imprecise opening (-0.5))
ChessBot evaluated 805 positions in 0.55 seconds
Move: Ne5, Eval before: 100.0, Eval after: 136.0, Raw change: 36.0, Smoothed: 36.0
Move analysis: Ne5 - Okay (reasonable opening play)
Move: g6, Eval before: 136.0, Eval after: 174.0, Raw change: 38.0, Smoothed: 38.0
Move analysis: g6 - Okay (acceptable opening (-0.4))
ChessBot evaluated 1222 positions in 0.79 seconds
Move: Nc3, Eval before: 174.0, Eval after: 228.0, Raw change: 54.0, Smoothed: 54.0
Move analysis: Nc3 - Okay (solid opening move (+0.5))
Move: d6, Eval before: 228.0, Eval after: 246.0, Raw change: 18.0, Smoothed: 18.0
Move analysis: d6 - Okay (acceptable opening (-0.2))
ChessBot evaluated 847 positions in 0.60 seconds
Move: Nc4, Eval before: 246.0, Eval after: 224.0, Raw change: -22.0, Smoothed: -22.0
Move analysis: Nc4 - Okay (acceptable opening (-0.2))
Move: f4, Eval before: 0.0, Eval after: -46.0, Raw change: -46.0, Smoothed: -46.0
Move analysis: f4 - Inaccurate (imprecise opening (-0.5))
ChessBot evaluated 709 positions in 0.53 seconds
Move: Nf6, Eval before: -46.0, Eval after: -100.0, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nf6 - Okay (solid opening move (+0.5))
Move: g3, Eval before: -100.0, Eval after: -138.0, Raw change: -38.0, Smoothed: -38.0
Move analysis: g3 - Okay (acceptable opening (-0.4))
ChessBot evaluated 964 positions in 0.72 seconds
Move: Ne4, Eval before: -138.0, Eval after: -174.0, Raw change: -36.0, Smoothed: -36.0
Move analysis: Ne4 - Okay (reasonable opening play)
Move: Bg2, Eval before: -174.0, Eval after: -158.0, Raw change: 16.0, Smoothed: 16.0
Move analysis: Bg2 - Okay (reasonable opening play)
ChessBot evaluated 1232 positions in 0.70 seconds
Move: Nc5, Eval before: -158.0, Eval after: -125.0, Raw change: 33.0, Smoothed: 33.0
Move analysis: Nc5 - Okay (acceptable opening (-0.3))
Move: d3, Eval before: 0.0, Eval after: -12.0, Raw change: -12.0, Smoothed: -12.0
Move analysis: d3 - Okay (acceptable opening (-0.1))
ChessBot evaluated 781 positions in 0.40 seconds
Move: Nf6, Eval before: -12.0, Eval after: -66.0, Raw change: -54.0, Smoothed: -54.0
Move analysis: Nf6 - Okay (solid opening move (+0.5))
Move: e3, Eval before: -66.0, Eval after: -88.0, Raw change: -22.0, Smoothed: -22.0
Move analysis: e3 - Okay (acceptable opening (-0.2))
ChessBot evaluated 991 positions in 0.71 seconds
Move: Nd5, Eval before: -88.0, Eval after: -108.0, Raw change: -20.0, Smoothed: -20.0
Move analysis: Nd5 - Okay (reasonable opening play)
Move: f3, Eval before: -108.0, Eval after: -152.0, Raw change: -44.0, Smoothed: -44.0
Move analysis: f3 - Inaccurate (imprecise opening (-0.4))
ChessBot evaluated 1094 positions in 0.50 seconds
Move: Nb4, Eval before: -152.0, Eval after: -136.0, Raw change: 16.0, Smoothed: 16.0
Move analysis: Nb4 - Okay (acceptable opening (-0.2))
Move: f4, Eval before: -136.0, Eval after: -138.0, Raw change: -2.0, Smoothed: -2.0
Move analysis: f4 - Okay (acceptable opening (-0.0))
ChessBot evaluated 1109 positions in 0.84 seconds
Move: N8c6, Eval before: -138.0, Eval after: -188.0, Raw change: -50.0, Smoothed: -50.0
Move analysis: N8c6 - Okay (solid opening move (+0.5))
Move: h3, Eval before: -188.0, Eval after: -230.0, Raw change: -42.0, Smoothed: -42.0
Move analysis: h3 - Inaccurate (imprecise opening (-0.4))
ChessBot evaluated 1394 positions in 1.09 seconds
Move: e6, Eval before: -230.0, Eval after: -220.0, Raw change: 10.0, Smoothed: 10.0
Move analysis: e6 - Okay (acceptable opening (-0.1))
Move: g4, Eval before: -220.0, Eval after: -269.0, Raw change: -49.0, Smoothed: -49.0
Move analysis: g4 - Inaccurate (inaccurate move (-0.5))
ChessBot evaluated 1881 positions in 1.35 seconds
Move: Qh4+, Eval before: -269.0, Eval after: -342.0, Raw change: -73.0, Smoothed: -73.0
Move analysis: Qh4+ - Okay (solid move (+0.7))
Move: Kd2, Eval before: -342.0, Eval after: -302.5, Raw change: 39.5, Smoothed: 39.5
Move analysis: Kd2 - Okay (reasonable move)
ChessBot evaluated 3783 positions in 2.44 seconds
Move: Qf2+, Eval before: -302.5, Eval after: -358.5, Raw change: -56.0, Smoothed: -56.0
Move analysis: Qf2+ - Okay (solid move (+0.6))




-----------------------------------

using enhanced evaluation function from chess_bot2.py:


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score.py
Move: Nf3, Eval before: 0, Eval after: 73, Change: 73
ChessBot evaluated 1245 positions in 2.80 seconds
Move: e6, Eval before: 73, Eval after: -37, Change: -110
Move: Ne5, Eval before: -37, Eval after: 39, Change: 76
ChessBot evaluated 3135 positions in 11.15 seconds
Move: Qf6, Eval before: 39, Eval after: -220, Change: -259
Move: Nxd7, Eval before: -220, Eval after: -254, Change: -34
ChessBot evaluated 1944 positions in 4.28 seconds
Move: Nxd7, Eval before: -254, Eval after: -432, Change: -178
ChessBot search depth set to 2
Move: Nf3, Eval before: 0, Eval after: 73, Change: 73
ChessBot evaluated 169 positions in 0.30 seconds
Move: e6, Eval before: 73, Eval after: -37, Change: -110
Move: Ng5, Eval before: -37, Eval after: -337, Change: -300
ChessBot evaluated 75 positions in 0.16 seconds
Move: Qxg5, Eval before: -337, Eval after: -506, Change: -169
Move: Nf3, Eval before: 0, Eval after: 73, Change: 73
ChessBot evaluated 169 positions in 0.35 seconds
Move: e6, Eval before: 73, Eval after: -37, Change: -110
Move: Ne5, Eval before: -37, Eval after: 39, Change: 76
ChessBot evaluated 160 positions in 0.37 seconds
Move: Qh4, Eval before: 39, Eval after: -155, Change: -194
Move: Nxf7, Eval before: -155, Eval after: -252, Change: -97
ChessBot evaluated 100 positions in 0.17 seconds
Move: Kxf7, Eval before: -252, Eval after: -362, Change: -110
Move: g3, Eval before: -362, Eval after: -230, Change: 132
ChessBot evaluated 288 positions in 0.76 seconds
Move: Qe4, Eval before: -230, Eval after: -419, Change: -189
Move: d3, Eval before: -419, Eval after: -605, Change: -186
ChessBot evaluated 143 positions in 0.21 seconds
Move: Qxh1, Eval before: -605, Eval after: -994, Change: -389
Move: Kd2, Eval before: -994, Eval after: -741, Change: 253
ChessBot evaluated 124 positions in 0.20 seconds
Move: Qxh2, Eval before: -741, Eval after: -935, Change: -194


--


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score.py
Move: Nf3, Eval before: 0, Eval after: 73, Change: 73
ChessBot evaluated 1245 positions in 2.80 seconds
Move: e6, Eval before: 73, Eval after: -37, Change: -110
Move: Ne5, Eval before: -37, Eval after: 39, Change: 76
ChessBot evaluated 3135 positions in 11.15 seconds
Move: Qf6, Eval before: 39, Eval after: -220, Change: -259
Move: Nxd7, Eval before: -220, Eval after: -254, Change: -34
ChessBot evaluated 1944 positions in 4.28 seconds
Move: Nxd7, Eval before: -254, Eval after: -432, Change: -178
ChessBot search depth set to 2
Move: Nf3, Eval before: 0, Eval after: 73, Change: 73
ChessBot evaluated 169 positions in 0.30 seconds
Move: e6, Eval before: 73, Eval after: -37, Change: -110
Move: Ng5, Eval before: -37, Eval after: -337, Change: -300
ChessBot evaluated 75 positions in 0.16 seconds
Move: Qxg5, Eval before: -337, Eval after: -506, Change: -169
Move: Nf3, Eval before: 0, Eval after: 73, Change: 73
ChessBot evaluated 169 positions in 0.35 seconds
Move: e6, Eval before: 73, Eval after: -37, Change: -110
Move: Ne5, Eval before: -37, Eval after: 39, Change: 76
ChessBot evaluated 160 positions in 0.37 seconds
Move: Qh4, Eval before: 39, Eval after: -155, Change: -194
Move: Nxf7, Eval before: -155, Eval after: -252, Change: -97
ChessBot evaluated 100 positions in 0.17 seconds
Move: Kxf7, Eval before: -252, Eval after: -362, Change: -110
Move: g3, Eval before: -362, Eval after: -230, Change: 132
ChessBot evaluated 288 positions in 0.76 seconds
Move: Qe4, Eval before: -230, Eval after: -419, Change: -189
Move: d3, Eval before: -419, Eval after: -605, Change: -186
ChessBot evaluated 143 positions in 0.21 seconds
Move: Qxh1, Eval before: -605, Eval after: -994, Change: -389
Move: Kd2, Eval before: -994, Eval after: -741, Change: 253
ChessBot evaluated 124 positions in 0.20 seconds
Move: Qxh2, Eval before: -741, Eval after: -935, Change: -194
ChessBot search depth set to 5
Move: d4, Eval before: 0, Eval after: 48, Change: 48
ChessBot evaluated 39887 positions in 95.39 seconds
Move: e6, Eval before: 48, Eval after: -55, Change: -103
ChessBot search depth set to 2
Move: e3, Eval before: -55, Eval after: 30, Change: 85
ChessBot evaluated 222 positions in 0.60 seconds
Move: Qh4, Eval before: 30, Eval after: -140, Change: -170
Move: g3, Eval before: -140, Eval after: -1, Change: 139
ChessBot evaluated 226 positions in 0.42 seconds
Move: Bb4+, Eval before: -1, Eval after: -222, Change: -221
Move: c3, Eval before: -222, Eval after: 277, Change: 499
ChessBot evaluated 198 positions in 0.60 seconds
Move: Qe4, Eval before: 277, Eval after: -128, Change: -405
Move: cxb4, Eval before: -128, Eval after: -36, Change: 92
ChessBot evaluated 153 positions in 0.35 seconds
Move: Qxh1, Eval before: -36, Eval after: -357, Change: -321
Move: Qa4, Eval before: -357, Eval after: -233, Change: 124
ChessBot evaluated 117 positions in 0.17 seconds
Move: Qxg1, Eval before: -233, Eval after: -725, Change: -492
Move: Nd2, Eval before: -725, Eval after: -256, Change: 469
ChessBot evaluated 123 positions in 0.19 seconds
Move: Qxh2, Eval before: -256, Eval after: -639, Change: -383
Move: Ne4, Eval before: -639, Eval after: -327, Change: 312
ChessBot evaluated 165 positions in 0.27 seconds
Move: Kf8, Eval before: -327, Eval after: -655, Change: -328
Move: Nc5, Eval before: -655, Eval after: -286, Change: 369
ChessBot evaluated 195 positions in 0.58 seconds
Move: Nf6, Eval before: -286, Eval after: -714, Change: -428
Move: Bd3, Eval before: -714, Eval after: -500, Change: 214
ChessBot evaluated 191 positions in 0.40 seconds
Move: Qh1+, Eval before: -500, Eval after: -819, Change: -319
Move: Kd2, Eval before: -819, Eval after: -351, Change: 468
ChessBot evaluated 165 positions in 0.32 seconds
Move: Kg8, Eval before: -351, Eval after: -789, Change: -438
Move: Nxd7, Eval before: -789, Eval after: -488, Change: 301
ChessBot evaluated 137 positions in 0.30 seconds
Move: Nbxd7, Eval before: -488, Eval after: -1115, Change: -627
Move: Nf3, Eval before: 0, Eval after: 73, Change: 73
ChessBot evaluated 169 positions in 0.46 seconds
Move: e6, Eval before: 73, Eval after: -37, Change: -110
Move: Ne5, Eval before: -37, Eval after: 39, Change: 76
ChessBot evaluated 160 positions in 0.40 seconds
Move: Qh4, Eval before: 39, Eval after: -155, Change: -194
Move: Ng6, Eval before: -155, Eval after: -369, Change: -214
ChessBot evaluated 126 positions in 0.28 seconds
Move: hxg6, Eval before: -369, Eval after: -508, Change: -139
