[using augment code]

complete a full game against bot using CLI and save all moves and result as a .pgn

--



TO DO:

Check Issues on github
--> files it concerns: 
- chess_bot (rook move)
- chess_gui_score (may be easier to start again - decide later)


---

USING ZENCODER WHEN NO FREE AUGMENT CODE CREDITS

(chess_bot_transposition.py | test: GUI, CLI, test_chess_bot.py)

also include transposition tables (is included in dedicated file)
--> AI keeps moving rook left and right

-

(this is for minimax, so change chess_bot.py OR use dedicated file  | test: GUI, CLI, test_chess_bot_QS.py)

implement Quiescence Search (Mitigates effects of the horizon problem encountered by AI engines)
--> see microsoft copilot chat for the code with QS integrated in chess_bot.py (quiescence search chat)

ask: should i be getting this kind of output from chess_bot_QS_testing.py file? AI says yes (see terminal or chat "Race Condition and GUI Fixes")

the AI keeps moving the rook instead of developing the pawns

---------------

(create new file chess_bot_MCTS.py and associated test file | test: <PERSON><PERSON>, CLI, test_chess_bot_MCTS.py)


done a hybrid version, now use a version with just MC<PERSON>
[watch vid first to understand the concept]

commit: MCTS


--------------- 

chess_bot_hybrid.py: 

[see Microsoft copilot chat titled "Comparison of MCTS Configurations"]

[remember to test this with both the (unit) tests and GUI to make sure it still works]

dynamically adjusting top_moves_count based on game phase (e.g., 3 in endgames, 5 in openings/middlegames) and number of mcts simulations. build an adaptive MCTS-style move selection strategy right into it. Dynamic MCTS Parameter Tuning Strategy. Goal: Adjust top_moves_count and mcts_simulations based on the phase of the game or position complexity.

---------------

use randomised algorithms for the chess bot (as opposed to deterministic ones)

---------------

principal variation search

-----------------------

[DON'T DO THIS ONE UNTIL THE END] --> create (endgame) tablebase for the chess bot and implement opening book

organise files into suitable folders (bots, tests, GUI), make sure to import files from correct folders when running tests, GUI etc.



---------------

chess_gui.py:
- add a button to undo moves [DONE - commit: undo functionality]
DONE --> disable all game controls buttons while the bot is thinking and don't allow the user to click on the board while the bot is thinking. this is to prevent the user from causing bugs while the AI is in a prone state (thinking mode)
DONE [commit: disable game controls while bot is thinking]

DONE -->  i also want a redo button, write the logic for it and implement it. for example: If the game hasn't started, the redo button is disabled. If the bot is thinking, the redo button is also disabled.
It checks if the user has used the undo feature; if yes, the redo button can be enabled. this is because you can't redo if you haven't yet used undo for anything   
in short, you can't redo something if you haven't yet undone it. if you have made only one move and then undo it, you can't undo it again, you can only redo it. at that point, you can only undo or proceed to move a piece. if you have moved more than once, then when you undo you will be able to either undo until all the user's pieces are back to the beginning or redo. if you undo multiple times, you have the option to redo as many times as you undid it (at least 2 in this case)
DONE [commit: redo button functionality]

--

DONE chess_gui_arrow.py: [may need to create pull request for this, especially if lots of bugs appear]
--> update: i want everything to be the same but want the undo button to be above the board in the top left (back arrow), and the redo button above the board in the top right (forward arrow).  

arrangement:
< Undo button top left of board and represented by a back arrow, again being grayed out when it’s disabled, just like you did already.
> Redo button top right of board and represented by a forward arrow, again being grayed out when it’s disabled, just like you did for the undo button 
DONE [commit: forward and back arrows]

-----

chess_gui_tracker.py: [may need to create pull request for this]

- include a visual tracker for which pieces have been taken by each side (on GUI version) and record this in the output of the terminal
DONE [commit: tracker included]

----

- evaluation score (see optional description)

- [OPTIONAL: gui_QS already does this] when i switch to black, it should flip the board instead of remaining the same, as i am now playing as black

----------------
----------------
----------------

chess_gui_score.py ; chess_bot.py: [using pull request for this with a branch called "Eval-score-mods"]

IMPORTANT TIP FOR SANITY CHECKING - DEAL WITH ONE PROBLEM AT A TIME AND COMMIT FOR EVERY INDIVIDUAL PROBLEM SOLVED

[DONE] i want to be able to see the game status history so i can see how the program rated each chess move. this will be very helpful as i can then tell you what the rating was for each move so you can debug. 

[note: i didn't realise that the problem with evaluation scoring was to do with chess_bot.py (perhaps also the gui eval thresholds also needed tweaking but not sure), 
so may need to revert commit for chess_gui_score.py from "tweaking evaluation thresholds" to "evaluation baseline fix"]

[see latest zencoder chat (game move history tracker) for this and gemini chats and prompts in other notepad]

[to prompt gemini chat (7d35ca7): "i'm afraid that won't fix it, i think it requires a major rewrite of the bot's evaluation"]


[DONE] fix evaluation baseline ("eval before" should start at +0.00 instead of +0.40)

> file in progress: "search depth; black quality move rating" -> see latest prompt on latest zencoder chat for issue to fix
> saftey net: "tweaking evaluation thresholds"

- [DONE] rating certain moves as "inaccurate" or "okay" when there is no best move e.g. at beginning of the game (no such thing as a bad or good move in this case)
- [DONE] any other issues e.g. how the AI decides which side is winning ; failing to rate "poor" when human player blunders the queen etc



- [refer to latest gemini chat for this | see latest chat in zencoder also for history of all prompts] - issue with evaluation score calculation in chess_bot.py that is the underlying cause of the high proportion of "excellent" ratings (tweaking the thresholds in chess_gui_score.py only addresses the symptoms, not the root cause)
--> update: addressed root cause, now back to tweaking to deal with rating inflation


current issues: [ACROSS LATEST 2 ZENCODER CHATS]
"rating tweak" - rating inflation - need to tweak evaluation thresholds
"rating tweak 2" [if using other set of code for gui score, then issue is with threshold for poor and inaccurate] - we still get moves that class as poor or inaccurate, but seems some moves should have been one but classed as the other

commit: rating tweak (final)

TO DO: - evaluation scoring: should be a maximum of 3.00 (positive or negative), not 200.00, which means game is decided, and how much the game status evaluation bar is filled up on either side should reflect this [note: it doesn't jump by like 200 on every game, some games the jumps are still small like +- 5 but reach max of +- 17]

commit: correct Ev. scoring

[updated: tried it with zencoder - yh it ain't having it]

- rook moving back and forth between h8 and g8. when white (as the user) does this with their rook to, it goes on forever, failing to draw by repetition. 

---------------

chess_gui_score.py:

- [for the pull request "pieces-under-attack"] the sign of the eval change needs to be changed from positive to negative given that the pawn move d4 is no longer rated as okay but instead as poor (as queen is under attack) -> update, it is still technically possible for a move to be poor when positive eval change is small - i.e. it didn't improve white's position as much in comparison to another possible move white could have made
- [for the pull request "tactical eval system"] don't just do specific case for rating pawn move as poor when queen under attack but for any scenario where a piece is under attack or just in general the eval score system should know when a bad move occurs like through tactical decision making


commit: eval change consistency 


files being edited in version control [4 branches]: chess_bot.py ; chess_gui_score.py ; tactical_evaluation.py                                                                                       
pull request this relates to: pieces-under-attack (only deals with a special case of queen under attack)

new pull request: tactical eval system (supposed to deal with general case for any piece e.g. attack / defence tactical awareness)

branches - problems, solutions: [AI says main branch is the most stable and safest to revert to]
main branch - pawn move d4 rated as okay instead of poor (queen not defended)
pieces-under-attack - only accounts for special case of queen under attack, not all the other pieces and scenarios
tactical-eval-system - getting weird results (extreme swings and rating inflation)
tactical-eval-system-experimental - one test fail, moves getting mis-rated


--

now using main branch, so if need to, create NEW pull request for this, not an old one:

easy difficulty: there comes a point where the show hint makes a suggested move that puts white in a disadvantaged position
[update: don't bother, just causes errors]

expert difficulty: what the evaluation score says is sometimes different to what the evaluation bar shows, and the direction can be the wrong way (and it's too slow to calculate best move and for AI bot to move)
[update: it's fine on main branch - better to play on medium though]

the eval score after a move shown on move quality history is not shown  be equal to the eval score after a move shown by the game status evaluation bar score, they should be kept in sync [this is for main branch - on tactical-eval-system branch this is not an issue. it's also not an issue on earlier commits of main branch like "game status history"]

[DONE] issue - some pawn moves by white are decreasing the eval score even as the opening move. other pawn moves that do increase the eval score are still shown by the evaluation bar (shown as red side) to leave white in disadvantaged position 

--

anything else: 

- either magnitude or direction of the evaluation bar (when it swings either way to show advantage (green) or disadvantage for white (red) )
- eval score should be capped at +- 3.00 which should be shown by how much of the bar on either side should be filled up i.e. the right side should be fully filled up with green when score of + 3.00 is reached 


implement tactical evaluation - got a file for this already but needs to be integrated into the application used by chess_gui_score.py | need more moves rated as inaccurate or poor based on the context of the game being played
[think its both tactical evaluation and further tweaking of thresholds that will make it work]

---------------
---------------

main.py:
- the user plays as white however the board is showing the user's side as black and the AI's side as white (wrong way round)
- give option for user to switch between different bots/algorithms that the AI uses instead of having to manually choose which file to import from
- get bot vs bot to use different algorithms against each other so can determine which algorithm plays best
- [DONE] the game always ends before the bot finishes playing against itself (i.e. it never draws, stalemates, checkmates etc.), it just returns game ended after 147 moves for some reason


--------------

chess_bot.py ; chess_bot_rook.py : [see github issue]
- bot always opens with knights (on medium mode) (minimax) --> make sure tests still work if file changed before you commit the changes
;
- the bot keeps moving the rook back and forth on 2 squares on the same row instead of developing the pawns
(note: already partially addressed in chess_bot_rook, so if need to do anything more, only change this file)


-------------------------------------------
-------------------------------------------

get 2 AI bots (different or the same) to play against each other using the GUI

==============================



OPTIONAL (depending on intent):


chess_gui.py:

on GUI, create option before the game to either play against the bot, or see bot vs bot (for this case try different bots like minimax vs MCTS --> this way you can test which bot is best
--> when testing with user against each bot, i suggest playing as closest to the same game as possible to make it fair, but this depends on the bot's moves)
--> record each game between each different type of bot to compare and contrast each algorithm (e.g. minimax vs hybrid) : note any noticeable improvements

--------------------------------------

train neural net to play chess

Bot (minimax) vs Bot (MCTS)

make QS_testing faster and more efficient (particularly on expert mode)

--

get minimax algorithm to show its thinking process in the terminal and how it evaluates the scoring for each move

--

use precomputed data as an optimisation technique to make the search of the game tree faster (like the technique of alpha-beta pruning)

use selective search techniques for when chess bot is unable to explore the full tree
use heuristics to navigate the most relevant parts

-----------------

implement the use of bitboards for generating all legal moves for a given position


[DONE] implement iterative deepening --> works best with transposition tables


------------

[DONE] transposition table using Zobrist hashing to identify unique positions

[DONE] use techniques like move ordering to shrink the effective tree size, letting the bot search deeper

----------------


{attempted, best it can do for now} see if you can make the hybrid version smarter (make sure code is written correctly etc. so it plays a challenging game against human player)

Aim: make AI bot make smarter moves, like capturing a free piece that is hanging

AI making silly mistakes (but perhaps this is normal when playing on easy mode)


--

clearer opening pattern recognition as set in utils.py e.g. king's knight opening but depends on how AI plays
(right now AI openings seem deterministic on lower difficulty settings especially, or sometimes it just never follows particular openings
--> again, could link to smarter AI needed to play more intuitive moves - experiment with algorithms like MCTS)



-------------------------


include evaluation score (on GUI version)
(update: seperate evaluation score code from chess_gui.py file so have clear seperation of concerns if anything goes wrong
--> see prompt in new thread )

files: chess_gui.txt (708 lines) and chess_gui2.txt (729 lines) includes code for evaluation score

commit: evaluation scoring included


-

score should be a max of +- 3.0, (the max indicating the game is decided), and how much the bar is filled up on either side should reflect this

any other issues e.g. how the AI decides which side is winning ; AI failing to rate "poor" when user blunders the queen etc.

AI rating to which side is winning may be wrong especially in cases where it should be obvious which side is winning/losing 

-

disable board interaction while bot is thinking just like it was done in chess_gui

-------------------------

[effects of utils.py not showing when AI bot plays against itself in CLI]

commit: utils update for Bot vs Bot

--




===============================
===============================



DONE:


you forgot to create and write the code for utils.py

incorporate utils.py in the main application so everything just works when i run python main.py

commit: utils.py incorporation



create a desktop based GUI version as well that complements the existing CLI version

commit: GUI version added with Launcher




ask chat to make chess_bot_hybrid.py code written properly and smarter so it works well and is competitive against human player |
- commit corrected hybrid to github | chess_gui - try hybrid version



use monte carlo tree search also (chess bot) -> combine minimax with alpha-beta pruning and MCTS in the chess bot to enhance the bot's decision making process
(leverage the efficient search capabilities of minimax / alpha-beta pruning to find promising moves and then use MCTS to explore those moves in more depth)



commit: minimax with alpha-beta pruning combined with MCTS



-

(chess_bot_transposition.py | test: GUI, CLI, test_chess_bot.py)

also include transposition tables (is included in dedicated file)

commit: transposition tables included




(this is for minimax, so change chess_bot.py OR use dedicated file  | test: GUI, CLI, test_chess_bot_QS.py)

implement Quiescence Search (Mitigates effects of the horizon problem encountered by AI engines)
--> see microsoft copilot chat for the code with QS integrated in chess_bot.py (quiescence search chat)

--> add testing points to see the effect of QS on how the AI plays (see exactly the inner workings of QS)

chess_gui_QS: when i switch to black, it doesn't let me select the piece i want, instead it chooses one that is the equivalent on the other side of the board (left to right)



rook_behaviour (history):
- integrate chess_bot_rook into the codebase in its own file (DONE) and create tests for it in another file (DONE)
[OPTIONAL] --> then update chess_gui (with what's in rook_behaviour) to also be able to use chess_bot_rook (did it in its own file to be safe - can always combine later)





need an adjustment for the functionality of the redo button. when i click redo, i want to be able to redo my move, but then also be allowed to redo the bot's move without the bot interfering by thinking about its moves. basically the bot should only ever be in control once the user makes a new move that doesn't use the undo or redo features.

commit: redo mech adjustment