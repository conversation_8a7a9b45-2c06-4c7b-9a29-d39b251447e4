PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_opening_behavior.py
=== Testing Opening Development ===
After d4: rnbqkbnr/pppppppp/8/8/3P4/8/PPP1PPPP/RNBQKBNR b KQkq - 0 1
After Nc6: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
After e4: r1bqkbnr/pppppppp/2n5/8/3PP3/8/PPP2PPP/RNBQKBNR b KQkq - 0 2
After Nf6: r1bqkb1r/pppppppp/2n2n2/8/3PP3/8/PPP2PPP/RNBQKBNR w KQkq - 1 3
After d5: r1bqkb1r/pppppppp/2n2n2/3P4/4P3/8/PPP2PPP/RNBQKBNR b KQkq - 0 3
After Nb4: r1bqkb1r/pppppppp/5n2/3P4/1n2P3/8/PPP2PPP/RNBQKBNR w KQkq - 1 4
After c3: r1bqkb1r/pppppppp/5n2/3P4/1n2P3/2P5/PP3PPP/RNBQKBNR b KQkq - 0 4
After Nxe4: r1bqkb1r/pppppppp/8/3P4/1n2n3/2P5/PP3PPP/RNBQKBNR w KQkq - 0 5
After f3: r1bqkb1r/pppppppp/8/3P4/1n2n3/2P2P2/PP4PP/RNBQKBNR b KQkq - 0 5
After Nxa2: r1bqkb1r/pppppppp/8/3P4/4n3/2P2P2/nP4PP/RNBQKBNR w KQkq - 0 6
After Rxa2: r1bqkb1r/pppppppp/8/3P4/4n3/2P2P2/RP4PP/1NBQKBNR b Kkq - 0 6
After Nf6: r1bqkb1r/pppppppp/5n2/3P4/8/2P2P2/RP4PP/1NBQKBNR w Kkq - 1 7
After Bc4: r1bqkb1r/pppppppp/5n2/3P4/2B5/2P2P2/RP4PP/1NBQK1NR b Kkq - 2 7

Current position:
r . b q k b . r
p p p p p p p p
. . . . . n . .
. . . P . . . .
. . B . . . . .
. . P . . P . .
R P . . . . P P
. N B Q K . N R

Bot's next moves:
OpeningBot evaluated 1564 positions in 1.29 seconds (TT: 3.1% hit rate, 1515 entries)
Move 1: h8g8 (r)
  ⚠️  WARNING: Rook shuffling on back rank!
  Position after move: r1bqkbr1/pppppppp/5n2/3P4/2B5/2P2P2/RP4PP/1NBQK1NR w Kq - 3 8
OpeningBot evaluated 2713 positions in 2.84 seconds (TT: 14.0% hit rate, 3569 entries)
Move 2: c1g5 (B)
  Position after move: r1bqkbr1/pppppppp/5n2/3P2B1/2B5/2P2P2/RP4PP/1N1QK1NR b Kq - 4 8
OpeningBot evaluated 934 positions in 1.02 seconds (TT: 12.4% hit rate, 4375 entries)
Move 3: d7d6 (p)
  Position after move: r1bqkbr1/ppp1pppp/3p1n2/3P2B1/2B5/2P2P2/RP4PP/1N1QK1NR w Kq - 0 9
OpeningBot evaluated 2038 positions in 2.31 seconds (TT: 14.3% hit rate, 5916 entries)
Move 4: c4b5 (B)
  Position after move: r1bqkbr1/ppp1pppp/3p1n2/1B1P2B1/8/2P2P2/RP4PP/1N1QK1NR b Kq - 1 9
OpeningBot evaluated 150 positions in 0.27 seconds (TT: 14.0% hit rate, 6013 entries)
Move 5: c8d7 (b)
  Position after move: r2qkbr1/pppbpppp/3p1n2/1B1P2B1/8/2P2P2/RP4PP/1N1QK1NR w Kq - 2 10

Final position after 5 moves:
r . . q k b r .
p p p b p p p p
. . . p . n . .
. B . P . . B .
. . . . . . . .
. . P . . P . .
R P . . . . P P
. N . Q K . N R

=== Testing Early Game Priorities ===
Starting position move preferences:
Top 10 opening moves by evaluation:
 1. g1f3 (N) - Score:  +41
 2. b1c3 (N) - Score:  +41
 3. g1h3 (N) - Score:   -5
 4. b1a3 (N) - Score:   -5
 5. d2d4 (P) - Score:  -49
 6. e2e4 (P) - Score:  -52
 7. e2e3 (P) - Score:  -57
 8. d2d3 (P) - Score:  -57
 9. f2f3 (P) - Score:  -67
10. c2c3 (P) - Score:  -67

After 1.e4 e5 2.Nf3:
r n b q k b n r
p p p p . p p p
. . . . . . . .
. . . . p . . .
. . . . P . . .
. . . . . N . .
P P P P . P P P
R N B Q K B . R
Top 10 moves for Black:
 1. g8f6 (n) - Score:  +54
 2. b8c6 (n) - Score:  +54
 3. g8e7 (n) - Score:  +67
 4. f8b4 (b) - Score:  +99
 5. g8h6 (n) - Score: +100
 6. b8a6 (n) - Score: +100
 7. f8d6 (b) - Score: +102
 8. f8c5 (b) - Score: +102
 9. f8a3 (b) - Score: +103
10. f8e7 (b) - Score: +105

=== Rook vs Development Analysis ===
Position after 1.e4 e5 2.Nf3 Nc6 3.Bc4 f5:
r . b q k b n r
p p p p . . p p
. . n . . . . .
. . . . p p . .
. . B . P . . .
. . . . . N . .
P P P P . P P P
R N B Q K . . R

Found 2 rook moves, 0 development moves, 33 other moves

Rook moves:
  h1g1 - Score:  +11
  h1f1 - Score:  +11

Other (top 5) moves:
  c4a6 - Score:   +5
  c4e6 - Score:   +6
  c4d5 - Score:  +20
  c4f7 - Score:  +66
  c4g8 - Score: +282
PS C:\Users\<USER>