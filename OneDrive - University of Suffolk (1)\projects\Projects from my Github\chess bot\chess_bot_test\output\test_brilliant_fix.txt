PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_brilliant_fix.py
Testing brilliant move detection fix...
==================================================
Move: d8g4
Quality: Excellent
Explanation: excellent move (5.0)
Expected: NOT Brilliant (since it's just capturing a pawn, not sacrificing)
✅ PASSED: Move correctly NOT classified as Brilliant

Actual sacrifice test:
Move: d1h5
Quality: Brilliant
Explanation: brilliant sacrifice (5.0)

✅ Fix appears to be working correctly!