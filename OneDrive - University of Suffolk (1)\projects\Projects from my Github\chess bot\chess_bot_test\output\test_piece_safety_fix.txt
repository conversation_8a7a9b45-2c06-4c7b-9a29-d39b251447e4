PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_piece_safety_fix.py
Testing piece safety evaluation fix...
============================================================
=== Testing Queen Captures Defended Pawn ===
Position: White queen on d4, black pawn on e5 defended by knight on f6
r n b q k b . r
p p p p . p p p
. . . . . n . .
. . . . p . . .
. . . Q . . . .
. . . . . . . .
P P P . P P P P
R N B . K B N R
Evaluation before capture: 773

After Qxe5 (queen captures defended pawn):
r n b q k b . r
p p p p . p p p
. . . . . n . .
. . . . Q . . .
. . . . . . . .
. . . . . . . .
P P P . P P P P
R N B . K B N R
Evaluation after capture: -20221
Note: Queen on e5 can be captured by knight on f6

Same position but queen back on d4 (safer):
r n b q k b . r
p p p p . p p p
. . . . . n . .
. . . . . . . .
. . . Q . . . .
. . . . . . . .
P P P . P P P P
R N B . K B N R
Evaluation with queen on d4: -293

The evaluation should be lower when the queen is hanging!

==================================================
Simple hanging queen test:
r n b q k b n r
p p p p p p p p
. . . . . . . .
. . . . . . . .
. . . . Q . . .
. . . . . . . .
P P P P . P P P
R N B . K B N R
Evaluation (queen hanging on e4): -7

Queen moved to e3 (safer):
r n b q k b n r
p p p p p p p p
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . Q . . .
P P P P . P P P
R N B . K B N R
Evaluation (queen safer on e3): -20

============================================================
Test completed. The evaluation should now properly account for
piece safety and hanging pieces!