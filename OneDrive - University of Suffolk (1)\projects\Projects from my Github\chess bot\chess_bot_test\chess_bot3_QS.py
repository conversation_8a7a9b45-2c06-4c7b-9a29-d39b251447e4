import chess
import chess.engine
import random
from typing import Optional, Tuple, List
import time

class ChessBot:
    """
    A chess bot that uses minimax algorithm with alpha-beta pruning
    and an optimized quiescence search to make intelligent moves.
    """
    
    def __init__(self, depth: int = 3, name: str = "ChessBot"):
        """
        Initialize the chess bot.
        """
        self.depth = depth
        self.name = name
        self.nodes_evaluated = 0
        self.piece_values = {
            chess.PAWN: 100, chess.KNIGHT: 320, chess.BISHOP: 330,
            chess.ROOK: 500, chess.QUEEN: 900, chess.KING: 20000
        }
        # Piece-Square Tables remain the same
        self.pawn_table=[0,0,0,0,0,0,0,0,5,5,5,5,5,5,5,5,10,10,20,30,30,20,10,10,15,15,25,35,35,25,15,15,20,20,30,40,40,30,20,20,35,35,40,50,50,40,35,35,50,50,60,70,70,60,50,50,0,0,0,0,0,0,0,0]
        self.knight_table=[-50,-40,-30,-30,-30,-30,-40,-50,-40,-20,0,0,0,0,-20,-40,-30,0,10,15,15,10,0,-30,-30,5,15,20,20,15,5,-30,-30,0,15,20,20,15,0,-30,-30,5,10,15,15,10,5,-30,-40,-20,0,5,5,0,-20,-40,-50,-40,-30,-30,-30,-30,-40,-50]
        self.bishop_table=[-20,-10,-10,-10,-10,-10,-10,-20,-10,0,0,0,0,0,0,-10,-10,0,5,10,10,5,0,-10,-10,5,5,10,10,5,5,-10,-10,0,10,10,10,10,0,-10,-10,10,10,10,10,10,10,-10,-10,5,0,0,0,0,5,-10,-20,-10,-10,-10,-10,-10,-10,-20]
        self.rook_table=[0,0,0,0,0,0,0,0,5,10,10,10,10,10,10,5,-5,0,0,0,0,0,0,-5,-5,0,0,0,0,0,0,-5,-5,0,0,0,0,0,0,-5,-5,0,0,0,0,0,0,-5,-5,0,0,0,0,0,0,-5,0,0,0,5,5,0,0,0]
        self.queen_table=[-20,-10,-10,-5,-5,-10,-10,-20,-10,0,0,0,0,0,0,-10,-10,0,5,5,5,5,0,-10,-5,0,5,5,5,5,0,-5,0,0,5,5,5,5,0,-5,-10,5,5,5,5,5,0,-10,-10,0,5,0,0,0,0,-10,-20,-10,-10,-5,-5,-10,-10,-20]
        self.king_mg_table=[-30,-40,-40,-50,-50,-40,-40,-30,-30,-40,-40,-50,-50,-40,-40,-30,-30,-40,-40,-50,-50,-40,-40,-30,-30,-40,-40,-50,-50,-40,-40,-30,-20,-30,-30,-40,-40,-30,-30,-20,-10,-20,-20,-20,-20,-20,-20,-10,20,20,0,0,0,0,20,20,20,30,10,0,0,10,30,20]
        self.king_eg_table=[-50,-40,-30,-20,-20,-30,-40,-50,-30,-20,-10,0,0,-10,-20,-30,-30,-10,20,30,30,20,-10,-30,-30,-10,30,40,40,30,-10,-30,-30,-10,30,40,40,30,-10,-30,-30,-10,20,30,30,20,-10,-30,-30,-30,0,0,0,0,-30,-30,-50,-30,-30,-30,-30,-30,-30,-50]

    def _is_endgame(self, board: chess.Board) -> bool:
        if not board.pieces(chess.QUEEN, chess.WHITE) and not board.pieces(chess.QUEEN, chess.BLACK):
            return True
        white_material = sum(len(board.pieces(pt, chess.WHITE)) for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])
        black_material = sum(len(board.pieces(pt, chess.BLACK)) for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])
        return (board.pieces(chess.QUEEN, chess.WHITE) and white_material < 2) or \
               (board.pieces(chess.QUEEN, chess.BLACK) and black_material < 2)

    def evaluate_board(self, board: chess.Board) -> int:
        if board.is_checkmate(): return -20000 if board.turn == chess.WHITE else 20000
        if board.is_stalemate() or board.is_insufficient_material(): return 0
        
        score = 0
        is_endgame = self._is_endgame(board)

        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = self.piece_values[piece.piece_type]
                table = None
                if piece.piece_type == chess.PAWN: table = self.pawn_table
                elif piece.piece_type == chess.KNIGHT: table = self.knight_table
                elif piece.piece_type == chess.BISHOP: table = self.bishop_table
                elif piece.piece_type == chess.ROOK: table = self.rook_table
                elif piece.piece_type == chess.QUEEN: table = self.queen_table
                elif piece.piece_type == chess.KING:
                    table = self.king_eg_table if is_endgame else self.king_mg_table
                
                if table:
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                
                score += value if piece.color == chess.WHITE else -value
        return score

    def quiescence_search(self, board: chess.Board, alpha: int, beta: int, depth: int) -> int:
        """
        Optimized quiescence search with depth limit and MVV-LVA move ordering.
        """
        self.nodes_evaluated += 1
        
        # *** KEY CHANGE: Depth limit for quiescence search ***
        if depth == 0 or board.is_game_over():
            return self.evaluate_board(board)

        stand_pat = self.evaluate_board(board)
        
        if board.turn == chess.WHITE:
            if stand_pat >= beta: return beta
            alpha = max(alpha, stand_pat)
        else:
            if stand_pat <= alpha: return alpha
            beta = min(beta, stand_pat)
        
        # *** KEY CHANGE: MVV-LVA move ordering for captures ***
        capture_moves = []
        for move in board.legal_moves:
            if board.is_capture(move):
                victim = board.piece_at(move.to_square)
                # En-passant capture victim is a pawn
                if victim is None:
                    victim_value = self.piece_values[chess.PAWN]
                else:
                    victim_value = self.piece_values[victim.piece_type]
                
                attacker_value = self.piece_values[board.piece_at(move.from_square).piece_type]
                score = victim_value - attacker_value
                capture_moves.append((score, move))
        
        # Sort captures from best to worst
        capture_moves.sort(key=lambda item: item[0], reverse=True)
        
        for _, move in capture_moves:
            board.push(move)
            # *** KEY CHANGE: Decrement quiescence depth ***
            score = self.quiescence_search(board, alpha, beta, depth - 1)
            board.pop()
            
            if board.turn == chess.WHITE:
                if score >= beta: return beta
                alpha = max(alpha, score)
            else:
                if score <= alpha: return alpha
                beta = min(beta, score)
                
        return alpha if board.turn == chess.WHITE else beta

    def minimax(self, board: chess.Board, depth: int, alpha: int, beta: int, 
                maximizing_player: bool) -> Tuple[int, Optional[chess.Move]]:
        self.nodes_evaluated += 1
        
        if depth == 0 or board.is_game_over():
            # *** KEY CHANGE: Call quiescence search with a fixed depth limit ***
            q_depth = 4 # Set a reasonable depth for captures
            q_eval = self.quiescence_search(board, alpha, beta, q_depth)
            return q_eval, None
        
        best_move = None
        moves = sorted(list(board.legal_moves), key=board.is_capture, reverse=True)

        if maximizing_player:
            max_eval = float('-inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                alpha = max(alpha, eval_score)
                if beta <= alpha: break
            return max_eval, best_move
        else: # Minimizing player
            min_eval = float('inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                beta = min(beta, eval_score)
                if beta <= alpha: break
            return min_eval, best_move
    
    def get_best_move(self, board: chess.Board) -> chess.Move:
        self.nodes_evaluated = 0
        start_time = time.time()
        
        maximizing = board.turn == chess.WHITE
        _, best_move = self.minimax(board, self.depth, float('-inf'), float('inf'), maximizing)
        
        end_time = time.time()
        
        if best_move is None:
            try:
                best_move = random.choice(list(board.legal_moves))
            except IndexError:
                return None
        
        print(f"{self.name} evaluated {self.nodes_evaluated} nodes in {end_time - start_time:.2f}s")
        return best_move
    
    def set_depth(self, depth: int):
        self.depth = depth
        print(f"{self.name} search depth set to {depth}")