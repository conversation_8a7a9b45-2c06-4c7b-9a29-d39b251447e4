PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python demo_iterative_deepening.py
ITERATIVE DEEPENING CHESS AI DEMONSTRATION
==========================================

This demonstration shows the key benefits of iterative deepening:
1. Precise time management
2. Anytime algorithm behavior
3. Progressive search refinement
4. Better move ordering from previous iterations

=== ITERATIVE DEEPENING DEMONSTRATION ===


--- Opening Position ---

Time Control: 1.0s
RegularBot evaluated 4128 positions in 1.93 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 1.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.17s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.38s, nodes=774
IterativeBot completed search: 774 nodes in 1.09s
  Regular Bot:
    Move: g1f3
    Time: 2.15s
    Nodes: 4128
    Depth: 4
  Iterative Bot:
    Move: g1f3
    Time: 1.19s
    Nodes: 774
    Max Depth: 8
  Analysis:
    Time efficiency: 81.1%
    Same move: Yes

Time Control: 2.0s
RegularBot evaluated 4128 positions in 2.18 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 2.0s)
  Depth 1: score=54, move=g1f3, time=0.03s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.06s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.53s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.31s, nodes=3624
IterativeBot completed search: 3624 nodes in 2.00s
  Regular Bot:
    Move: g1f3
    Time: 2.20s
    Nodes: 4128
    Depth: 4
  Iterative Bot:
    Move: g1f3
    Time: 2.00s
    Nodes: 3624
    Max Depth: 8
  Analysis:
    Time efficiency: 99.8%
    Same move: Yes

Time Control: 3.0s
RegularBot evaluated 4128 positions in 1.90 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 3.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.04s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.38s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.86s, nodes=4399
  Depth 5: score=53, move=g1f3, time=0.72s, nodes=6211
IterativeBot completed search: 6211 nodes in 3.00s
  Regular Bot:
    Move: g1f3
    Time: 1.90s
    Nodes: 4128
    Depth: 4
  Iterative Bot:
    Move: g1f3
    Time: 3.00s
    Nodes: 6211
    Max Depth: 8
  Analysis:
    Time efficiency: 100.0%
    Same move: Yes

--- Italian Game ---

Time Control: 1.0s
RegularBot evaluated 26292 positions in 19.83 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 1.0s)
  Depth 1: score=168, move=c4f7, time=0.02s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.18s, nodes=313
  Depth 3: score=176, move=e1e2, time=0.79s, nodes=1501
IterativeBot completed search: 1501 nodes in 1.01s
  Regular Bot:
    Move: c1d2
    Time: 19.84s
    Nodes: 26292
    Depth: 4
  Iterative Bot:
    Move: e1e2
    Time: 1.03s
    Nodes: 1501
    Max Depth: 8
  Analysis:
    Time efficiency: 96.7%
    Same move: No
    Regular: c1d2, Iterative: e1e2

Time Control: 2.0s
RegularBot evaluated 26292 positions in 11.88 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 2.0s)
  Depth 1: score=168, move=c4f7, time=0.01s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.10s, nodes=313
  Depth 3: score=167, move=b1c3, time=1.20s, nodes=3122
  Depth 4: score=-21, move=c4f7, time=0.69s, nodes=4662
IterativeBot completed search: 4662 nodes in 2.00s
  Regular Bot:
    Move: c1d2
    Time: 11.88s
    Nodes: 26292
    Depth: 4
  Iterative Bot:
    Move: c4f7
    Time: 2.00s
    Nodes: 4662
    Max Depth: 8
  Analysis:
    Time efficiency: 100.0%
    Same move: No
    Regular: c1d2, Iterative: c4f7

Time Control: 3.0s
RegularBot evaluated 26292 positions in 9.78 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 3.0s)
  Depth 1: score=168, move=c4f7, time=0.01s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.12s, nodes=313
  Depth 3: score=167, move=b1c3, time=1.11s, nodes=3122
  Depth 4: score=-141, move=e1g1, time=1.76s, nodes=6977
IterativeBot completed search: 6977 nodes in 3.00s
  Regular Bot:
    Move: c1d2
    Time: 9.78s
    Nodes: 26292
    Depth: 4
  Iterative Bot:
    Move: e1g1
    Time: 3.00s
    Nodes: 6977
    Max Depth: 8
  Analysis:
    Time efficiency: 100.0%
    Same move: No
    Regular: c1d2, Iterative: e1g1

--- Italian Game Variation ---

Time Control: 1.0s
RegularBot evaluated 13105 positions in 4.01 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 1.0s)
  Depth 1: score=469, move=c4f7, time=0.02s, nodes=35
  Depth 2: score=268, move=e4e5, time=0.10s, nodes=211
  Depth 3: score=631, move=d1f3, time=0.66s, nodes=1931
  Depth 4: score=227, move=d1f3, time=0.20s, nodes=2461
IterativeBot completed search: 2461 nodes in 1.00s
  Regular Bot:
    Move: e4e5
    Time: 4.01s
    Nodes: 13105
    Depth: 4
  Iterative Bot:
    Move: d1f3
    Time: 1.00s
    Nodes: 2461
    Max Depth: 8
  Analysis:
    Time efficiency: 100.0%
    Same move: No
    Regular: e4e5, Iterative: d1f3

Time Control: 2.0s
RegularBot evaluated 13105 positions in 4.13 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 2.0s)
  Depth 1: score=469, move=c4f7, time=0.02s, nodes=35
  Depth 2: score=268, move=e4e5, time=0.07s, nodes=211
  Depth 3: score=631, move=d1f3, time=0.55s, nodes=1931
  Depth 4: score=192, move=c4d3, time=1.37s, nodes=5131
IterativeBot completed search: 5131 nodes in 2.00s
  Regular Bot:
    Move: e4e5
    Time: 4.13s
    Nodes: 13105
    Depth: 4
  Iterative Bot:
    Move: c4d3
    Time: 2.01s
    Nodes: 5131
    Max Depth: 8
  Analysis:
    Time efficiency: 99.7%
    Same move: No
    Regular: e4e5, Iterative: c4d3

Time Control: 3.0s
RegularBot evaluated 13105 positions in 3.92 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 3.0s)
  Depth 1: score=469, move=c4f7, time=0.01s, nodes=35
  Depth 2: score=268, move=e4e5, time=0.10s, nodes=211
  Depth 3: score=631, move=d1f3, time=0.61s, nodes=1931
  Depth 4: score=247, move=b1c3, time=2.27s, nodes=7402
IterativeBot completed search: 7402 nodes in 3.00s
  Regular Bot:
    Move: e4e5
    Time: 3.92s
    Nodes: 13105
    Depth: 4
  Iterative Bot:
    Move: b1c3
    Time: 3.00s
    Nodes: 7402
    Max Depth: 8
  Analysis:
    Time efficiency: 100.0%
    Same move: No
    Regular: e4e5, Iterative: b1c3

--- King and Pawn Endgame ---

Time Control: 1.0s
RegularBot evaluated 82 positions in 0.01 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 1.0s)
  Depth 1: score=53, move=d1e1, time=0.00s, nodes=3
  Depth 2: score=43, move=d1e1, time=0.00s, nodes=11
  Depth 3: score=68, move=d1e1, time=0.00s, nodes=38
  Depth 4: score=67, move=d1e1, time=0.02s, nodes=144
  Depth 5: score=94, move=d1e1, time=0.08s, nodes=538
  Depth 6: score=92, move=d1e1, time=0.25s, nodes=2100
  Depth 7: score=120, move=d1e1, time=0.65s, nodes=5394
IterativeBot completed search: 5394 nodes in 1.00s
  Regular Bot:
    Move: d1e1
    Time: 0.01s
    Nodes: 82
    Depth: 4
  Iterative Bot:
    Move: d1e1
    Time: 1.00s
    Nodes: 5394
    Max Depth: 8
  Analysis:
    Time efficiency: 99.9%
    Same move: Yes

Time Control: 2.0s
RegularBot evaluated 82 positions in 0.01 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 2.0s)
  Depth 1: score=53, move=d1e1, time=0.00s, nodes=3
  Depth 2: score=43, move=d1e1, time=0.00s, nodes=11
  Depth 3: score=68, move=d1e1, time=0.00s, nodes=38
  Depth 4: score=67, move=d1e1, time=0.02s, nodes=144
  Depth 5: score=94, move=d1e1, time=0.07s, nodes=538
  Depth 6: score=92, move=d1e1, time=0.31s, nodes=2100
  Depth 7: score=109, move=d1e1, time=1.41s, nodes=9569
  Depth 8: score=112, move=d1e1, time=0.18s, nodes=10167
IterativeBot completed search: 10167 nodes in 2.00s
  Regular Bot:
    Move: d1e1
    Time: 0.01s
    Nodes: 82
    Depth: 4
  Iterative Bot:
    Move: d1e1
    Time: 2.00s
    Nodes: 10167
    Max Depth: 8
  Analysis:
    Time efficiency: 99.9%
    Same move: Yes

Time Control: 3.0s
RegularBot evaluated 82 positions in 0.02 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 3.0s)
  Depth 1: score=53, move=d1e1, time=0.00s, nodes=3
  Depth 2: score=43, move=d1e1, time=0.00s, nodes=11
  Depth 3: score=68, move=d1e1, time=0.01s, nodes=38
  Depth 4: score=67, move=d1e1, time=0.05s, nodes=144
  Depth 5: score=94, move=d1e1, time=0.10s, nodes=538
  Depth 6: score=92, move=d1e1, time=0.25s, nodes=2100
  Depth 7: score=109, move=d1e1, time=1.97s, nodes=9569
  Depth 8: score=129, move=d1e1, time=0.60s, nodes=12320
IterativeBot completed search: 12320 nodes in 3.00s
  Regular Bot:
    Move: d1e1
    Time: 0.02s
    Nodes: 82
    Depth: 4
  Iterative Bot:
    Move: d1e1
    Time: 3.00s
    Nodes: 12320
    Max Depth: 8
  Analysis:
    Time efficiency: 100.0%
    Same move: Yes


=== ANYTIME ALGORITHM DEMONSTRATION ===

This shows how iterative deepening provides increasingly better moves
as more time is available, making it an 'anytime' algorithm.

Position: Italian Game
r . b q k b . r
p p p p . p p p
. . n . . n . .
. . . . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q K . . R

Results with different time allocations:
Time | Move      | Nodes  | Depth Reached
-----|-----------|--------|---------------
AnyTimeBot starting iterative deepening search (max depth: 10, time limit: 0.5s)
  Depth 1: score=168, move=c4f7, time=0.03s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.12s, nodes=313
  Depth 3: score=170, move=f3g1, time=0.35s, nodes=1278
AnyTimeBot completed search: 1278 nodes in 0.50s
 0.5s | f3g1      |   1278 | Variable
AnyTimeBot starting iterative deepening search (max depth: 10, time limit: 1.0s)
  Depth 1: score=168, move=c4f7, time=0.01s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.11s, nodes=313
  Depth 3: score=174, move=b1d2, time=0.88s, nodes=2376
AnyTimeBot completed search: 2376 nodes in 1.00s
 1.0s | b1d2      |   2376 | Variable
AnyTimeBot starting iterative deepening search (max depth: 10, time limit: 2.0s)
  Depth 1: score=168, move=c4f7, time=0.01s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.12s, nodes=313
  Depth 3: score=167, move=b1c3, time=1.23s, nodes=3122
  Depth 4: score=-95, move=c4f7, time=0.64s, nodes=4553
AnyTimeBot completed search: 4553 nodes in 2.00s
 2.0s | c4f7      |   4553 | Variable
AnyTimeBot starting iterative deepening search (max depth: 10, time limit: 3.0s)
  Depth 1: score=168, move=c4f7, time=0.01s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.11s, nodes=313
  Depth 3: score=167, move=b1c3, time=1.23s, nodes=3122
  Depth 4: score=-141, move=e1g1, time=1.65s, nodes=7236
AnyTimeBot completed search: 7236 nodes in 3.00s
 3.0s | e1g1      |   7236 | Variable
AnyTimeBot starting iterative deepening search (max depth: 10, time limit: 5.0s)
  Depth 1: score=168, move=c4f7, time=0.02s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.18s, nodes=313
  Depth 3: score=167, move=b1c3, time=1.09s, nodes=3122
  Depth 4: score=133, move=f3g5, time=3.71s, nodes=9248
AnyTimeBot completed search: 9248 nodes in 5.13s
 5.0s | f3g5      |   9248 | Variable


=== PROGRESSIVE REFINEMENT DEMONSTRATION ===

This shows how the best move can change as search depth increases,
demonstrating the progressive refinement of iterative deepening.

Position: Italian Game (tactical position)
r . b q k b . r
p p p p . p p p
. . n . . n . .
. . . . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q K . . R

Running search with progressive refinement...
ProgressiveBot starting iterative deepening search (max depth: 6, time limit: 5.0s)
  Depth 1: score=168, move=c4f7, time=0.26s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.68s, nodes=313
  Depth 3: score=184, move=f3h4, time=3.52s, nodes=1154
ProgressiveBot completed search: 1154 nodes in 5.26s

Final best move: f3h4
Total nodes evaluated: 1154


=== SUMMARY ===
Iterative deepening provides:
• Better time management (stops exactly at time limit)
• Anytime behavior (always has a move ready)
• Progressive improvement (deeper search = better moves)
• Efficient search (good move ordering from previous iterations)
• Flexibility (can adjust time vs. strength trade-off)












==========================================================
==========================================================





ITERATIVE DEEPENING CHESS AI DEMONSTRATION
==========================================

This demonstration shows the key benefits of iterative deepening:
1. Precise time management
2. Anytime algorithm behavior
3. Progressive search refinement
4. Better move ordering from previous iterations

=== ITERATIVE DEEPENING DEMONSTRATION ===


--- Opening Position ---

Time Control: 1.0s
RegularBot evaluated 4128 positions in 1.00 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 1.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.02s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.35s, nodes=996
  Depth 4: score=0, move=g1f3, time=0.62s, nodes=2713
IterativeBot completed search: 2713 nodes in 1.00s
  Regular Bot:
    Move: g1f3
    Time: 1.00s
    Nodes: 4128
    Depth: 4
  Iterative Bot:
    Move: g1f3
    Time: 1.00s
    Nodes: 2713
    Max Depth: 8
  Analysis:
    Time efficiency: 100.0%
    Same move: Yes

Time Control: 2.0s
RegularBot evaluated 4128 positions in 0.95 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 2.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.02s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.25s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.30s, nodes=4399
  Depth 5: score=112, move=g1f3, time=0.42s, nodes=5746
IterativeBot completed search: 5746 nodes in 2.00s
  Regular Bot:
    Move: g1f3
    Time: 0.95s
    Nodes: 4128
    Depth: 4
  Iterative Bot:
    Move: g1f3
    Time: 2.00s
    Nodes: 5746
    Max Depth: 8
  Analysis:
    Time efficiency: 100.0%
    Same move: Yes

Time Control: 3.0s
RegularBot evaluated 4128 positions in 1.01 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 3.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.02s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.38s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.24s, nodes=4399
  Depth 5: score=44, move=g1f3, time=1.35s, nodes=8260
IterativeBot completed search: 8260 nodes in 3.00s
  Regular Bot:
    Move: g1f3
    Time: 1.01s
    Nodes: 4128
    Depth: 4
  Iterative Bot:
    Move: g1f3
    Time: 3.00s
    Nodes: 8260
    Max Depth: 8
  Analysis:
    Time efficiency: 100.0%
    Same move: Yes

--- Italian Game ---

Time Control: 1.0s
RegularBot evaluated 26292 positions in 8.41 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 1.0s)
  Depth 1: score=168, move=c4f7, time=0.01s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.11s, nodes=313
  Depth 3: score=167, move=b1c3, time=0.88s, nodes=2845
IterativeBot completed search: 2845 nodes in 1.00s
  Regular Bot:
    Move: c1d2
    Time: 8.41s
    Nodes: 26292
    Depth: 4
  Iterative Bot:
    Move: b1c3
    Time: 1.00s
    Nodes: 2845
    Max Depth: 8
  Analysis:
    Time efficiency: 99.9%
    Same move: No
    Regular: c1d2, Iterative: b1c3

Time Control: 2.0s
RegularBot evaluated 26292 positions in 9.45 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 2.0s)
  Depth 1: score=168, move=c4f7, time=0.01s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.11s, nodes=313
  Depth 3: score=167, move=b1c3, time=0.96s, nodes=3122
  Depth 4: score=-225, move=b1c3, time=0.92s, nodes=4379
IterativeBot completed search: 4379 nodes in 2.08s
  Regular Bot:
    Move: c1d2
    Time: 9.45s
    Nodes: 26292
    Depth: 4
  Iterative Bot:
    Move: b1c3
    Time: 2.21s
    Nodes: 4379
    Max Depth: 8
  Analysis:
    Time efficiency: 89.3%
    Same move: No
    Regular: c1d2, Iterative: b1c3

Time Control: 3.0s
RegularBot evaluated 26292 positions in 19.64 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 3.0s)
  Depth 1: score=168, move=c4f7, time=0.03s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.18s, nodes=313
  Depth 3: score=167, move=b1c3, time=1.14s, nodes=3122
  Depth 4: score=-143, move=e1g1, time=1.64s, nodes=6116
IterativeBot completed search: 6116 nodes in 3.00s
  Regular Bot:
    Move: c1d2
    Time: 19.64s
    Nodes: 26292
    Depth: 4
  Iterative Bot:
    Move: e1g1
    Time: 3.00s
    Nodes: 6116
    Max Depth: 8
  Analysis:
    Time efficiency: 100.0%
    Same move: No
    Regular: c1d2, Iterative: e1g1

--- Italian Game Variation ---

Time Control: 1.0s
RegularBot evaluated 13105 positions in 3.65 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 1.0s)
  Depth 1: score=469, move=c4f7, time=0.02s, nodes=35
  Depth 2: score=268, move=e4e5, time=0.09s, nodes=211
  Depth 3: score=631, move=d1f3, time=0.82s, nodes=1931
  Depth 4: score=20000, move=d1f3, time=0.05s, nodes=1965
  Mate found at depth 4!
IterativeBot completed search: 1965 nodes in 1.02s
  Regular Bot:
    Move: e4e5
    Time: 3.65s
    Nodes: 13105
    Depth: 4
  Iterative Bot:
    Move: d1f3
    Time: 1.03s
    Nodes: 1965
    Max Depth: 8
  Analysis:
    Time efficiency: 97.4%
    Same move: No
    Regular: e4e5, Iterative: d1f3

Time Control: 2.0s
RegularBot evaluated 13105 positions in 3.83 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 2.0s)
  Depth 1: score=469, move=c4f7, time=0.01s, nodes=35
  Depth 2: score=268, move=e4e5, time=0.11s, nodes=211
  Depth 3: score=631, move=d1f3, time=0.58s, nodes=1931
  Depth 4: score=201, move=c4d3, time=1.30s, nodes=4959
IterativeBot completed search: 4959 nodes in 2.00s
  Regular Bot:
    Move: e4e5
    Time: 3.83s
    Nodes: 13105
    Depth: 4
  Iterative Bot:
    Move: c4d3
    Time: 2.00s
    Nodes: 4959
    Max Depth: 8
  Analysis:
    Time efficiency: 100.0%
    Same move: No
    Regular: e4e5, Iterative: c4d3

Time Control: 3.0s
RegularBot evaluated 13105 positions in 3.59 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 3.0s)
  Depth 1: score=469, move=c4f7, time=0.01s, nodes=35
  Depth 2: score=268, move=e4e5, time=0.07s, nodes=211
  Depth 3: score=631, move=d1f3, time=0.76s, nodes=1931
  Depth 4: score=266, move=b1c3, time=2.16s, nodes=7206
IterativeBot completed search: 7206 nodes in 3.00s
  Regular Bot:
    Move: e4e5
    Time: 3.59s
    Nodes: 13105
    Depth: 4
  Iterative Bot:
    Move: b1c3
    Time: 3.00s
    Nodes: 7206
    Max Depth: 8
  Analysis:
    Time efficiency: 99.8%
    Same move: No
    Regular: e4e5, Iterative: b1c3

--- King and Pawn Endgame ---

Time Control: 1.0s
RegularBot evaluated 82 positions in 0.02 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 1.0s)
  Depth 1: score=53, move=d1e1, time=0.00s, nodes=3
  Depth 2: score=43, move=d1e1, time=0.00s, nodes=11
  Depth 3: score=68, move=d1e1, time=0.01s, nodes=38
  Depth 4: score=67, move=d1e1, time=0.03s, nodes=144
  Depth 5: score=94, move=d1e1, time=0.08s, nodes=538
  Depth 6: score=92, move=d1e1, time=0.37s, nodes=2100
  Depth 7: score=119, move=d1e1, time=0.50s, nodes=3940
IterativeBot completed search: 3940 nodes in 1.00s
  Regular Bot:
    Move: d1e1
    Time: 0.02s
    Nodes: 82
    Depth: 4
  Iterative Bot:
    Move: d1e1
    Time: 1.00s
    Nodes: 3940
    Max Depth: 8
  Analysis:
    Time efficiency: 99.7%
    Same move: Yes

Time Control: 2.0s
RegularBot evaluated 82 positions in 0.01 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 2.0s)
  Depth 1: score=53, move=d1e1, time=0.00s, nodes=3
  Depth 2: score=43, move=d1e1, time=0.00s, nodes=11
  Depth 3: score=68, move=d1e1, time=0.01s, nodes=38
  Depth 4: score=67, move=d1e1, time=0.02s, nodes=144
  Depth 5: score=94, move=d1e1, time=0.08s, nodes=538
  Depth 6: score=92, move=d1e1, time=0.34s, nodes=2100
  Depth 7: score=124, move=d1c1, time=1.55s, nodes=8830
IterativeBot completed search: 8830 nodes in 2.00s
  Regular Bot:
    Move: d1e1
    Time: 0.01s
    Nodes: 82
    Depth: 4
  Iterative Bot:
    Move: d1c1
    Time: 2.00s
    Nodes: 8830
    Max Depth: 8
  Analysis:
    Time efficiency: 100.0%
    Same move: No
    Regular: d1e1, Iterative: d1c1

Time Control: 3.0s
RegularBot evaluated 82 positions in 0.01 seconds
IterativeBot starting iterative deepening search (max depth: 8, time limit: 3.0s)
  Depth 1: score=53, move=d1e1, time=0.00s, nodes=3
  Depth 2: score=43, move=d1e1, time=0.00s, nodes=11
  Depth 3: score=68, move=d1e1, time=0.00s, nodes=38
  Depth 4: score=67, move=d1e1, time=0.02s, nodes=144
  Depth 5: score=94, move=d1e1, time=0.09s, nodes=538
  Depth 6: score=92, move=d1e1, time=0.30s, nodes=2100
  Depth 7: score=109, move=d1e1, time=1.68s, nodes=9569
  Depth 8: score=43, move=d1e1, time=0.89s, nodes=13947
IterativeBot completed search: 13947 nodes in 3.00s
  Regular Bot:
    Move: d1e1
    Time: 0.01s
    Nodes: 82
    Depth: 4
  Iterative Bot:
    Move: d1e1
    Time: 3.00s
    Nodes: 13947
    Max Depth: 8
  Analysis:
    Time efficiency: 100.0%
    Same move: Yes


=== ANYTIME ALGORITHM DEMONSTRATION ===

This shows how iterative deepening provides increasingly better moves
as more time is available, making it an 'anytime' algorithm.

Position: Italian Game
r . b q k b . r
p p p p . p p p
. . n . . n . .
. . . . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q K . . R

Results with different time allocations:
Time | Move      | Nodes  | Depth Reached
-----|-----------|--------|---------------
AnyTimeBot starting iterative deepening search (max depth: 10, time limit: 0.5s)
  Depth 1: score=168, move=c4f7, time=0.02s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.13s, nodes=313
  Depth 3: score=184, move=f3h4, time=0.35s, nodes=1154
AnyTimeBot completed search: 1154 nodes in 0.50s
 0.5s | f3h4      |   1154 | Variable
AnyTimeBot starting iterative deepening search (max depth: 10, time limit: 1.0s)
  Depth 1: score=168, move=c4f7, time=0.01s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.09s, nodes=313
  Depth 3: score=273, move=b1d2, time=0.89s, nodes=2289
AnyTimeBot completed search: 2289 nodes in 1.05s
 1.0s | b1d2      |   2289 | Variable
AnyTimeBot starting iterative deepening search (max depth: 10, time limit: 2.0s)
  Depth 1: score=168, move=c4f7, time=0.05s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.19s, nodes=313
  Depth 3: score=167, move=b1c3, time=1.16s, nodes=3122
  Depth 4: score=-21, move=c4f7, time=0.56s, nodes=4644
AnyTimeBot completed search: 4644 nodes in 2.00s
 2.0s | c4f7      |   4644 | Variable
AnyTimeBot starting iterative deepening search (max depth: 10, time limit: 3.0s)
  Depth 1: score=168, move=c4f7, time=0.01s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.09s, nodes=313
  Depth 3: score=167, move=b1c3, time=1.28s, nodes=3122
  Depth 4: score=-159, move=e1g1, time=1.61s, nodes=7618
AnyTimeBot completed search: 7618 nodes in 3.00s
 3.0s | e1g1      |   7618 | Variable
AnyTimeBot starting iterative deepening search (max depth: 10, time limit: 5.0s)
  Depth 1: score=168, move=c4f7, time=0.01s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.17s, nodes=313
  Depth 3: score=167, move=b1c3, time=1.09s, nodes=3122
  Depth 4: score=-85, move=e1f1, time=3.72s, nodes=11495
AnyTimeBot completed search: 11495 nodes in 5.01s
 5.0s | e1f1      |  11495 | Variable


=== PROGRESSIVE REFINEMENT DEMONSTRATION ===

This shows how the best move can change as search depth increases,
demonstrating the progressive refinement of iterative deepening.

Position: Italian Game (tactical position)
r . b q k b . r
p p p p . p p p
. . n . . n . .
. . . . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q K . . R

Running search with progressive refinement...
ProgressiveBot starting iterative deepening search (max depth: 6, time limit: 5.0s)
  Depth 1: score=168, move=c4f7, time=0.01s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.16s, nodes=313
  Depth 3: score=167, move=b1c3, time=1.35s, nodes=3122
  Depth 4: score=-95, move=e1f1, time=3.47s, nodes=11656
ProgressiveBot completed search: 11656 nodes in 5.00s

Final best move: e1f1
Total nodes evaluated: 11656


=== SUMMARY ===
Iterative deepening provides:
• Better time management (stops exactly at time limit)
• Anytime behavior (always has a move ready)
• Progressive improvement (deeper search = better moves)
• Efficient search (good move ordering from previous iterations)
• Flexibility (can adjust time vs. strength trade-off)