#!/usr/bin/env python3
"""
Unified Chess Bot - A comprehensive chess AI with multiple advanced features
Combines the best aspects of all previous implementations
"""

import chess
import chess.engine
import random
import time
import math
from typing import Optional, Tuple, List, Dict
from collections import defaultdict

class UnifiedChessBot:
    """
    A unified chess bot that combines multiple advanced features:
    - Minimax with alpha-beta pruning
    - Iterative deepening
    - Quiescence search
    - Transposition tables
    - Advanced evaluation with piece-square tables
    - Opening book knowledge
    """
    
    def __init__(self, depth: int = 4, name: str = "UnifiedChessBot", 
                 use_iterative_deepening: bool = True, 
                 use_quiescence_search: bool = True,
                 use_transposition_table: bool = True,
                 max_quiescence_depth: int = 4): # can lower from 6 to 4 so can limit search depth to improve performance 
        """
        Initialize the unified chess bot.
        
        Args:
            depth: Maximum search depth
            name: Name of the bot
            use_iterative_deepening: Enable iterative deepening
            use_quiescence_search: Enable quiescence search
            use_transposition_table: Enable transposition tables
            max_quiescence_depth: Maximum depth for quiescence search
        """
        self.depth = depth
        self.name = name
        self.use_iterative_deepening = use_iterative_deepening
        self.use_quiescence_search = use_quiescence_search
        self.use_transposition_table = use_transposition_table
        self.max_quiescence_depth = max_quiescence_depth
        
        # Statistics
        self.nodes_evaluated = 0
        self.transposition_hits = 0
        self.quiescence_nodes = 0
        
        # Transposition table: key = board hash, value = (depth, score, flag, best_move)
        # flag: 0 = exact, 1 = lower bound, 2 = upper bound
        self.transposition_table = {}
        self.max_table_size = 100000
        
        # Piece values
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
        
        # Advanced piece-square tables
        self._init_piece_square_tables()
        
        # Opening book (simple)
        self._init_opening_book()
        
        # Testing flags
        self.use_opening_book = True
        
    def _init_piece_square_tables(self):
        """Initialize piece-square tables for positional evaluation."""
        
        self.pawn_table = [
            0,  0,  0,  0,  0,  0,  0,  0,   # Rank 1
            5,  5,  5,  5,  5,  5,  5,  5,   # Rank 2
            10, 10, 20, 30, 30, 20, 10, 10,  # Rank 3
            15, 15, 25, 35, 35, 25, 15, 15,  # Rank 4
            20, 20, 30, 40, 40, 30, 20, 20,  # Rank 5
            35, 35, 40, 50, 50, 40, 35, 35,  # Rank 6
            50, 50, 60, 70, 70, 60, 50, 50,  # Rank 7
            0,  0,  0,  0,  0,  0,  0,  0    # Rank 8
        ]
        
        self.knight_table = [
            -50,-40,-30,-30,-30,-30,-40,-50,
            -40,-20,  0,  0,  0,  0,-20,-40,
            -30,  0, 10, 15, 15, 10,  0,-30,
            -30,  5, 15, 20, 20, 15,  5,-30,
            -30,  0, 15, 20, 20, 15,  0,-30,
            -30,  5, 10, 15, 15, 10,  5,-30,
            -40,-20,  0,  5,  5,  0,-20,-40,
            -50,-40,-30,-30,-30,-30,-40,-50
        ]

        self.bishop_table = [
            -20,-10,-10,-10,-10,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5, 10, 10,  5,  0,-10,
            -10,  5,  5, 10, 10,  5,  5,-10,
            -10,  0, 10, 10, 10, 10,  0,-10,
            -10, 10, 10, 10, 10, 10, 10,-10,
            -10,  5,  0,  0,  0,  0,  5,-10,
            -20,-10,-10,-10,-10,-10,-10,-20
        ]

        self.rook_table = [
             0,  0,  0,  0,  0,  0,  0,  0,
             5, 10, 10, 10, 10, 10, 10,  5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
             0,  0,  0,  5,  5,  0,  0,  0
        ]

        self.queen_table = [
            -20,-10,-10, -5, -5,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5,  5,  5,  5,  0,-10,
             -5,  0,  5,  5,  5,  5,  0, -5,
              0,  0,  5,  5,  5,  5,  0, -5,
            -10,  5,  5,  5,  5,  5,  0,-10,
            -10,  0,  5,  0,  0,  0,  0,-10,
            -20,-10,-10, -5, -5,-10,-10,-20
        ]

        # King tables for middlegame and endgame
        self.king_mg_table = [
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -20,-30,-30,-40,-40,-30,-30,-20,
            -10,-20,-20,-20,-20,-20,-20,-10,
             20, 20,  0,  0,  0,  0, 20, 20,
             20, 30, 10,  0,  0, 10, 30, 20
        ]
        
        self.king_eg_table = [
            -50,-40,-30,-20,-20,-30,-40,-50,
            -30,-20,-10,  0,  0,-10,-20,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-30,  0,  0,  0,  0,-30,-30,
            -50,-30,-30,-30,-30,-30,-30,-50
        ]
        
    def _init_opening_book(self):
        """Initialize a simple opening book."""
        self.opening_moves = {
            # Common opening moves from starting position
            chess.Board().fen(): ["e2e4", "d2d4", "g1f3", "c2c4"],
            # Italian Game continuation
            "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1": ["e7e5"],
            "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2": ["g1f3"],
            # Add more as needed
        }
        
    def _is_endgame(self, board: chess.Board) -> bool:
        """Determine if the game is in endgame phase."""
        # Count major pieces
        white_queens = len(board.pieces(chess.QUEEN, chess.WHITE))
        black_queens = len(board.pieces(chess.QUEEN, chess.BLACK))
        
        # No queens = endgame
        if white_queens == 0 and black_queens == 0:
            return True
        
        # Few pieces remaining
        white_material = sum(len(board.pieces(pt, chess.WHITE)) 
                           for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])
        black_material = sum(len(board.pieces(pt, chess.BLACK)) 
                           for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])
        
        return (white_material + black_material) <= 6
        
    def _get_piece_square_value(self, piece: chess.Piece, square: int, is_endgame: bool) -> int:
        """Get piece-square table value for a piece on a square."""
        # Mirror square for black pieces
        sq = square if piece.color == chess.WHITE else chess.square_mirror(square)
        
        if piece.piece_type == chess.PAWN:
            return self.pawn_table[sq]
        elif piece.piece_type == chess.KNIGHT:
            return self.knight_table[sq]
        elif piece.piece_type == chess.BISHOP:
            return self.bishop_table[sq]
        elif piece.piece_type == chess.ROOK:
            return self.rook_table[sq]
        elif piece.piece_type == chess.QUEEN:
            return self.queen_table[sq]
        elif piece.piece_type == chess.KING:
            table = self.king_eg_table if is_endgame else self.king_mg_table
            return table[sq]
        
        return 0
    
    def _evaluate_hanging_pieces(self, board: chess.Board) -> int:
        """
        Evaluate hanging pieces and unfavorable exchanges.
        This is CRITICAL for preventing tactical blunders.
        
        Args:
            board: Chess board to evaluate
            
        Returns:
            Score adjustment (negative for hanging pieces)
        """
        score = 0
        
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece is None:
                continue
                
            # Check if piece is attacked
            attackers = board.attackers(not piece.color, square)
            if not attackers:
                continue  # Piece is not attacked
                
            # Check if piece is defended
            defenders = board.attackers(piece.color, square)
            piece_value = self.piece_values[piece.piece_type]
            
            if len(defenders) == 0:
                # Completely undefended - MASSIVE penalty for the side with the hanging piece
                penalty = piece_value * 2
                if piece.color == chess.WHITE:
                    score -= penalty  # White hanging piece hurts White (negative score)
                else:
                    score += penalty  # Black hanging piece helps White (positive score)
            else:
                # Piece is defended, but check if exchange is unfavorable
                # Find the lowest value attacker
                min_attacker_value = float('inf')
                for attacker_square in attackers:
                    attacker_piece = board.piece_at(attacker_square)
                    if attacker_piece:
                        attacker_value = self.piece_values[attacker_piece.piece_type]
                        min_attacker_value = min(min_attacker_value, attacker_value)
                
                # If lowest attacker is worth less than the piece, it's a bad exchange for the defender
                if min_attacker_value < piece_value:
                    exchange_loss = piece_value - min_attacker_value
                    # Heavy penalty for unfavorable exchanges
                    penalty = exchange_loss * 1.5  # 1.5x the material loss
                    
                    if piece.color == chess.WHITE:
                        score -= penalty  # White's bad exchange hurts White
                    else:
                        score += penalty  # Black's bad exchange helps White
                    
        return score
    
    def _evaluate_immediate_threats(self, board: chess.Board) -> int:
        """
        Evaluate immediate tactical threats (captures available next move).
        
        Args:
            board: Chess board to evaluate
            
        Returns:
            Score adjustment for immediate threats
        """
        score = 0
        
        # Evaluate captures available for the current player
        for move in board.legal_moves:
            if board.is_capture(move):
                captured_piece = board.piece_at(move.to_square)
                if captured_piece:
                    capture_value = self.piece_values[captured_piece.piece_type]
                    # Bonus for having capture available
                    if board.turn == chess.WHITE:
                        score += capture_value // 4
                    else:
                        score -= capture_value // 4
                        
        return score
        
    def evaluate_board(self, board: chess.Board) -> int:
        """
        Comprehensive board evaluation function.
        
        Args:
            board: Chess board to evaluate
            
        Returns:
            Evaluation score (positive for white advantage)
        """
        if board.is_checkmate():
            return -20000 if board.turn == chess.WHITE else 20000
        
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        is_endgame = self._is_endgame(board)
        
        # Material and positional evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                # Base piece value
                value = self.piece_values[piece.piece_type]
                
                # Add positional bonus
                value += self._get_piece_square_value(piece, square, is_endgame)
                
                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value
        
        # Hanging piece penalty (CRITICAL for tactical play)
        score += self._evaluate_hanging_pieces(board)
        
        # Immediate threat evaluation
        score += self._evaluate_immediate_threats(board)
        
        # Mobility evaluation
        original_turn = board.turn
        
        board.turn = chess.WHITE
        white_mobility = len(list(board.legal_moves))
        board.turn = chess.BLACK
        black_mobility = len(list(board.legal_moves))
        board.turn = original_turn
        
        mobility_score = (white_mobility - black_mobility) * 3
        score += mobility_score
        
        # King safety in middlegame
        if not is_endgame:
            # Penalize exposed king
            white_king_sq = board.king(chess.WHITE)
            black_king_sq = board.king(chess.BLACK)
            
            if white_king_sq and not board.has_castling_rights(chess.WHITE):
                if chess.square_file(white_king_sq) in [3, 4]:  # King on central files
                    score -= 30
                    
            if black_king_sq and not board.has_castling_rights(chess.BLACK):
                if chess.square_file(black_king_sq) in [3, 4]:  # King on central files
                    score += 30
        
        # Pawn structure bonuses
        score += self._evaluate_pawn_structure(board)
        
        # Center control
        score += self._evaluate_center_control(board)
        
        return score
        
    def _evaluate_pawn_structure(self, board: chess.Board) -> int:
        """Evaluate pawn structure."""
        score = 0
        
        # Doubled pawns penalty
        for file in range(8):
            white_pawns = len([sq for sq in chess.SquareSet(chess.BB_FILES[file]) 
                             if board.piece_at(sq) and board.piece_at(sq).piece_type == chess.PAWN 
                             and board.piece_at(sq).color == chess.WHITE])
            black_pawns = len([sq for sq in chess.SquareSet(chess.BB_FILES[file]) 
                             if board.piece_at(sq) and board.piece_at(sq).piece_type == chess.PAWN 
                             and board.piece_at(sq).color == chess.BLACK])
            
            if white_pawns > 1:
                score -= (white_pawns - 1) * 20
            if black_pawns > 1:
                score += (black_pawns - 1) * 20
        
        return score
        
    def _evaluate_center_control(self, board: chess.Board) -> int:
        """Evaluate center control."""
        center_squares = [chess.D4, chess.D5, chess.E4, chess.E5]
        score = 0
        
        for square in center_squares:
            piece = board.piece_at(square)
            if piece:
                if piece.color == chess.WHITE:
                    score += 10
                else:
                    score -= 10
        
        return score
        
    def _order_moves(self, board: chess.Board, moves: List[chess.Move], 
                    hash_move: Optional[chess.Move] = None) -> List[chess.Move]:
        """Order moves for better alpha-beta pruning."""
        def move_score(move):
            score = 0
            
            # Hash move gets highest priority
            if hash_move and move == hash_move:
                return 10000
            
            # Captures
            if board.is_capture(move):
                captured_piece = board.piece_at(move.to_square)
                moving_piece = board.piece_at(move.from_square)
                if captured_piece and moving_piece:
                    # MVV-LVA (Most Valuable Victim - Least Valuable Attacker)
                    score += (self.piece_values[captured_piece.piece_type] - 
                             self.piece_values[moving_piece.piece_type] // 10)
            
            # Promotions
            if move.promotion:
                score += self.piece_values[move.promotion]
            
            # Checks
            board.push(move)
            if board.is_check():
                score += 50
            board.pop()
            
            # Castle
            if board.is_castling(move):
                score += 30
                
            return score
        
        return sorted(moves, key=move_score, reverse=True)
        
    def _quiescence_search(self, board: chess.Board, alpha: int, beta: int, depth: int) -> int:
        """
        Quiescence search to handle tactical sequences.
        Only considers captures and checks to avoid horizon effect.
        """
        self.quiescence_nodes += 1
        
        # Stand pat evaluation
        stand_pat = self.evaluate_board(board)
        
        if stand_pat >= beta:
            return beta
        if alpha < stand_pat:
            alpha = stand_pat
            
        if depth <= 0:
            return stand_pat
        
        # Generate only captures and checks
        captures = [move for move in board.legal_moves 
                   if board.is_capture(move) or board.gives_check(move)]
        
        if not captures:
            return stand_pat
            
        # Order captures
        captures = self._order_moves(board, captures)
        
        for move in captures:
            board.push(move)
            score = -self._quiescence_search(board, -beta, -alpha, depth - 1)
            board.pop()
            
            if score >= beta:
                return beta
            if score > alpha:
                alpha = score
                
        return alpha
        
    def _store_transposition(self, board: chess.Board, depth: int, score: int, 
                           flag: int, best_move: Optional[chess.Move]):
        """Store position in transposition table."""
        if not self.use_transposition_table:
            return
            
        # Use a simple hash of the FEN as key (fallback method)
        key = hash(board.fen())
        
        # Clear table if it gets too large
        if len(self.transposition_table) > self.max_table_size:
            self.transposition_table.clear()
            
        self.transposition_table[key] = (depth, score, flag, best_move)
        
    def _probe_transposition(self, board: chess.Board, depth: int, alpha: int, beta: int) -> Tuple[Optional[int], Optional[chess.Move]]:
        """Probe transposition table."""
        if not self.use_transposition_table:
            return None, None
            
        # Use a simple hash of the FEN as key (fallback method)
        key = hash(board.fen())
        
        if key in self.transposition_table:
            stored_depth, stored_score, flag, best_move = self.transposition_table[key]
            
            if stored_depth >= depth:
                self.transposition_hits += 1
                
                if flag == 0:  # Exact score
                    return stored_score, best_move
                elif flag == 1 and stored_score >= beta:  # Lower bound
                    return stored_score, best_move
                elif flag == 2 and stored_score <= alpha:  # Upper bound
                    return stored_score, best_move
                    
            return None, best_move  # Return best move even if depth doesn't match
            
        return None, None
    
    def _order_moves(self, board: chess.Board, moves: List[chess.Move], hash_move: Optional[chess.Move] = None) -> List[chess.Move]:
        """
        Order moves for better alpha-beta pruning efficiency.
        Priority: Hash move > Escaping moves > Captures > Others
        
        Args:
            board: Current board position
            moves: List of legal moves
            hash_move: Best move from transposition table
            
        Returns:
            Ordered list of moves
        """
        def move_score(move):
            score = 0
            
            # 1. Hash move gets highest priority
            if hash_move and move == hash_move:
                return 10000
            
            # 2. Check if this move escapes from an attack (CRITICAL for tactical play)
            piece = board.piece_at(move.from_square)
            if piece:
                # Check if piece is currently under attack
                attackers = board.attackers(not piece.color, move.from_square)
                if attackers:
                    # This move escapes from attack - HIGH priority
                    score += 5000
                    
                    # Extra bonus if escaping to a safe square
                    board.push(move)
                    new_attackers = board.attackers(not piece.color, move.to_square)
                    if not new_attackers:
                        score += 1000  # Safe escape
                    board.pop()
            
            # 3. Captures using MVV-LVA (Most Valuable Victim - Least Valuable Attacker)
            if board.is_capture(move):
                victim = board.piece_at(move.to_square)
                attacker = board.piece_at(move.from_square)
                if victim and attacker:
                    # Favor capturing higher value pieces with lower value pieces
                    score += self.piece_values[victim.piece_type] * 10 - self.piece_values[attacker.piece_type]
            
            # 4. Checks
            board.push(move)
            if board.is_check():
                score += 500
            board.pop()
            
            # 5. Center control
            if move.to_square in [chess.E4, chess.E5, chess.D4, chess.D5]:
                score += 50
            
            return score
        
        # Sort moves by score (highest first)
        return sorted(moves, key=move_score, reverse=True)
        
    def _minimax(self, board: chess.Board, depth: int, alpha: int, beta: int, 
                maximizing_player: bool) -> Tuple[int, Optional[chess.Move]]:
        """
        Enhanced minimax with alpha-beta pruning, transposition tables, and quiescence search.
        """
        self.nodes_evaluated += 1
        
        # Probe transposition table
        tt_score, tt_move = self._probe_transposition(board, depth, alpha, beta)
        if tt_score is not None:
            return tt_score, tt_move
            
        if depth == 0 or board.is_game_over():
            if self.use_quiescence_search and depth == 0 and not board.is_game_over():
                score = self._quiescence_search(board, alpha, beta, self.max_quiescence_depth)
            else:
                score = self.evaluate_board(board)
            return score, None
        
        moves = list(board.legal_moves)
        moves = self._order_moves(board, moves, tt_move)
        
        best_move = None
        original_alpha = alpha
        
        if maximizing_player:
            max_eval = float('-inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self._minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            # Store in transposition table
            if max_eval <= original_alpha:
                flag = 2  # Upper bound
            elif max_eval >= beta:
                flag = 1  # Lower bound
            else:
                flag = 0  # Exact
            self._store_transposition(board, depth, max_eval, flag, best_move)
            
            return max_eval, best_move
        else:
            min_eval = float('inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self._minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            # Store in transposition table
            if min_eval <= original_alpha:
                flag = 2  # Upper bound
            elif min_eval >= beta:
                flag = 1  # Lower bound
            else:
                flag = 0  # Exact
            self._store_transposition(board, depth, min_eval, flag, best_move)
            
            return min_eval, best_move
            
    def _iterative_deepening(self, board: chess.Board, max_time: float = 10.0) -> chess.Move:
        """
        Iterative deepening search with time management.
        """
        start_time = time.time()
        best_move = None
        
        for current_depth in range(1, self.depth + 1):
            if time.time() - start_time > max_time:
                break
                
            maximizing = board.turn == chess.WHITE
            try:
                _, move = self._minimax(board, current_depth, float('-inf'), float('inf'), maximizing)
                if move:
                    best_move = move
            except:
                break  # Time's up or other issue
                
        return best_move
        
    def get_best_move(self, board: chess.Board) -> Optional[chess.Move]:
        """
        Get the best move for the current position.
        
        Args:
            board: Current board position
            
        Returns:
            Best move according to the bot's evaluation
        """
        # Reset statistics
        self.nodes_evaluated = 0
        self.transposition_hits = 0
        self.quiescence_nodes = 0
        
        start_time = time.time()
        
        # Check opening book first (if enabled)
        if self.use_opening_book:
            fen = board.fen()
            if fen in self.opening_moves:
                move_uci = random.choice(self.opening_moves[fen])
                try:
                    move = chess.Move.from_uci(move_uci)
                    if move in board.legal_moves:
                        print(f"{self.name} played from opening book: {move}")
                        return move
                except:
                    pass
        
        # Use iterative deepening if enabled
        if self.use_iterative_deepening:
            best_move = self._iterative_deepening(board)
        else:
            maximizing = board.turn == chess.WHITE
            _, best_move = self._minimax(board, self.depth, float('-inf'), float('inf'), maximizing)
        
        end_time = time.time()
        
        # Fallback to random move
        if best_move is None:
            legal_moves = list(board.legal_moves)
            if legal_moves:
                best_move = random.choice(legal_moves)
        
        # Print statistics
        elapsed_time = end_time - start_time
        print(f"{self.name} statistics:")
        print(f"  - Time: {elapsed_time:.2f}s")
        print(f"  - Nodes evaluated: {self.nodes_evaluated}")
        print(f"  - Quiescence nodes: {self.quiescence_nodes}")
        if self.use_transposition_table:
            print(f"  - Transposition hits: {self.transposition_hits}")
            print(f"  - Table size: {len(self.transposition_table)}")
        
        return best_move
        
    def set_depth(self, depth: int):
        """Set the search depth."""
        self.depth = depth
        print(f"{self.name} depth set to {depth}")
        
    def set_features(self, iterative_deepening: bool = None, 
                    quiescence_search: bool = None, 
                    transposition_table: bool = None):
        """Enable/disable bot features."""
        if iterative_deepening is not None:
            self.use_iterative_deepening = iterative_deepening
        if quiescence_search is not None:
            self.use_quiescence_search = quiescence_search
        if transposition_table is not None:
            self.use_transposition_table = transposition_table
            if not transposition_table:
                self.transposition_table.clear()
                
        print(f"{self.name} features updated:")
        print(f"  - Iterative deepening: {self.use_iterative_deepening}")
        print(f"  - Quiescence search: {self.use_quiescence_search}")
        print(f"  - Transposition table: {self.use_transposition_table}")
        
    def get_evaluation(self, board: chess.Board) -> int:
        """Get the evaluation of the current position."""
        return self.evaluate_board(board)
        
    def clear_transposition_table(self):
        """Clear the transposition table."""
        self.transposition_table.clear()
        print(f"{self.name} transposition table cleared")


# Preset configurations for different difficulty levels
class EasyBot(UnifiedChessBot):
    """Easy difficulty bot - fast and basic."""
    def __init__(self):
        super().__init__(
            depth=2, 
            name="EasyBot",
            use_iterative_deepening=False,
            use_quiescence_search=False,
            use_transposition_table=False
        )

class MediumBot(UnifiedChessBot):
    """Medium difficulty bot - balanced."""
    def __init__(self):
        super().__init__(
            depth=3,
            name="MediumBot", 
            use_iterative_deepening=True,
            use_quiescence_search=True,
            use_transposition_table=True
        )

class HardBot(UnifiedChessBot):
    """Hard difficulty bot - strong play."""
    def __init__(self):
        super().__init__(
            depth=4,
            name="HardBot",
            use_iterative_deepening=True,
            use_quiescence_search=True,
            use_transposition_table=True
        )

class ExpertBot(UnifiedChessBot):
    """Expert difficulty bot - very strong play."""
    def __init__(self):
        super().__init__(
            depth=5,
            name="ExpertBot",
            use_iterative_deepening=True,
            use_quiescence_search=True,
            use_transposition_table=True,
            max_quiescence_depth=8
        )