PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python debug_hanging_pieces.py
Debugging Hanging Piece Detection
==================================================
Position: r1bqkbnr/ppp2ppp/4p3/3pN3/1n1P4/P1N5/1PP1PPPP/R1BQKB1R b KQkq - 0 5
Black to move

Checking all pieces for hanging status:
----------------------------------------
B on c1 (White):
  Attackers: None
  Defenders: ['a1', 'd1']
  Status: Safe

Q on d1 (White):
  Attackers: None
  Defenders: ['e1', 'c3']
  Status: Safe

K on e1 (White):
  Attackers: None
  Defenders: ['d1']
  Status: Safe

B on f1 (White):
  Attackers: None
  Defenders: ['e1', 'h1']
  Status: Safe

P on b2 (White):
  Attackers: None
  Defenders: ['c1']
  Status: Safe

P on c2 (White):
  Attackers: ['b4']
  Defenders: ['d1']
  Status: Safe

P on e2 (White):
  Attackers: None
  Defenders: ['d1', 'e1', 'f1', 'c3']
  Status: Safe

P on f2 (White):
  Attackers: None
  Defenders: ['e1']
  Status: Safe

P on g2 (White):
  Attackers: None
  Defenders: ['f1']
  Status: Safe

P on h2 (White):
  Attackers: None
  Defenders: ['h1']
  Status: Safe

P on a3 (White):
  Attackers: None
  Defenders: ['a1', 'b2']
  Status: Safe

N on c3 (White):
  Attackers: None
  Defenders: ['b2']
  Status: Safe

n on b4 (Black):
  Attackers: ['a3']
  Defenders: ['f8']
  Status: Safe

P on d4 (White):
  Attackers: None
  Defenders: ['d1']
  Status: Safe

p on d5 (Black):
  Attackers: ['c3']
  Defenders: ['b4', 'e6', 'd8']
  Status: Safe

N on e5 (White):
  Attackers: None
  Defenders: ['d4']
  Status: Safe

p on e6 (Black):
  Attackers: None
  Defenders: ['f7', 'c8']
  Status: Safe

p on a7 (Black):
  Attackers: None
  Defenders: ['a8']
  Status: Safe

p on b7 (Black):
  Attackers: None
  Defenders: ['c8']
  Status: Safe

p on c7 (Black):
  Attackers: None
  Defenders: ['d8']
  Status: Safe

p on f7 (Black):
  Attackers: ['e5']
  Defenders: ['e8']
  Status: Safe

p on g7 (Black):
  Attackers: None
  Defenders: ['f8']
  Status: Safe

p on h7 (Black):
  Attackers: None
  Defenders: ['h8']
  Status: Safe

b on c8 (Black):
  Attackers: None
  Defenders: ['a8', 'd8']
  Status: Safe

q on d8 (Black):
  Attackers: None
  Defenders: ['e8']
  Status: Safe

k on e8 (Black):
  Attackers: None
  Defenders: ['d8']
  Status: Safe

b on f8 (Black):
  Attackers: None
  Defenders: ['e8']
  Status: Safe

n on g8 (Black):
  Attackers: None
  Defenders: ['h8']
  Status: Safe

==================================================
SPECIFIC TEST: Knight on b4
==================================================
Piece on b4: n
Piece color: Black
White attackers of b4: ['a3']
Black defenders of b4: ['f8']
❌ Knight appears safe according to this analysis

==================================================
TESTING HANGING PIECE FUNCTION
==================================================
Hanging piece function returned: 0

Manually stepping through the function:
  P on c2: 1 attackers, 1 defenders
  n on b4: 1 attackers, 1 defenders
  p on d5: 1 attackers, 3 defenders
  p on f7: 1 attackers, 1 defenders

Manual calculation result: 0
Function returned: 0
✅ Function is working correctly.