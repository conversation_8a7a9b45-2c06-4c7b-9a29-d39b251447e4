#!/usr/bin/env python3
"""
Debug what the bot thinks about positions after key moves
"""

import chess
from unified_chess_bot import MediumBot

def debug_position_evaluation():
    print("Debugging Position Evaluation After Key Moves")
    print("="*60)
    
    # Create the problematic position
    board = chess.Board()
    moves = ["Nc3", "d5", "Nf3", "Nc6", "d4", "e6", "Ne5", "Nb4", "a3"]
    for move in moves:
        board.push_san(move)
    
    print("Starting position (Black to move):")
    print(board)
    print(f"FEN: {board.fen()}")
    print()
    
    bot = MediumBot()
    bot.use_opening_book = False
    
    # Test key moves
    test_moves = ["Na6", "Nxc2+", "Qg5", "Qd6"]
    
    print("Evaluating positions after key moves:")
    print("-" * 60)
    
    original_eval = bot.evaluate_board(board)
    hanging_penalty = bot._evaluate_hanging_pieces(board)
    
    print(f"ORIGINAL POSITION:")
    print(f"  Overall evaluation: {original_eval}")
    print(f"  Hanging piece penalty: {hanging_penalty}")
    print()
    
    for move_san in test_moves:
        try:
            move = board.parse_san(move_san)
            
            # Make the move
            board.push(move)
            
            # Evaluate the new position
            new_eval = bot.evaluate_board(board)
            new_hanging = bot._evaluate_hanging_pieces(board)
            
            print(f"After {move_san}:")
            print(f"  Position evaluation: {new_eval}")
            print(f"  Hanging piece penalty: {new_hanging}")
            print(f"  Change from original: {new_eval - original_eval:+.1f}")
            
            # Check if White has any immediate threats
            white_captures = []
            for legal_move in board.legal_moves:
                if board.is_capture(legal_move):
                    captured_piece = board.piece_at(legal_move.to_square)
                    if captured_piece:
                        white_captures.append(f"{board.san(legal_move)} (captures {captured_piece.symbol()})")
            
            if white_captures:
                print(f"  White threats: {', '.join(white_captures[:3])}")  # Show first 3
            else:
                print(f"  White threats: None")
                
            print()
            
            # Undo the move
            board.pop()
            
        except Exception as e:
            print(f"Error with move {move_san}: {e}")
            print()
    
    # Now let's see what a shallow search thinks
    print("="*60)
    print("SHALLOW SEARCH ANALYSIS (depth 1)")
    print("="*60)
    
    # Test with depth 1 to see immediate evaluations
    bot_shallow = MediumBot()
    bot_shallow.use_opening_book = False
    bot_shallow.depth = 1
    
    best_move_shallow = bot_shallow.get_best_move(board)
    print(f"Depth 1 best move: {board.san(best_move_shallow) if best_move_shallow else 'None'}")
    
    # Test with depth 2
    print("\nSHALLOW SEARCH ANALYSIS (depth 2)")
    print("-" * 40)
    
    bot_depth2 = MediumBot()
    bot_depth2.use_opening_book = False
    bot_depth2.depth = 2
    
    best_move_depth2 = bot_depth2.get_best_move(board)
    print(f"Depth 2 best move: {board.san(best_move_depth2) if best_move_depth2 else 'None'}")

if __name__ == "__main__":
    debug_position_evaluation()