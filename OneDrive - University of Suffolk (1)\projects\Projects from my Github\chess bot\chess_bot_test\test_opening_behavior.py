#!/usr/bin/env python3
"""
Test script to analyze opening behavior and rook development.
"""

import chess
from chess_bot_transposition import ChessBot 

def test_opening_development():
    """Test the bot's behavior in the opening phase."""
    
    print("=== Testing Opening Development ===")
    
    # Start from the position where rook shuffling was occurring
    board = chess.Board()
    moves = [
        "d4", "Nc6", "e4", "Nf6", "d5", "Nb4", "c3", "Nxe4", 
        "f3", "Nxa2", "Rxa2", "Nf6", "Bc4"
    ]
    
    for move_str in moves:
        move = board.parse_san(move_str)
        board.push(move)
        print(f"After {move_str}: {board.fen()}")
    
    print("\nCurrent position:")
    print(board)
    print()
    
    # Test bot behavior for the next few moves
    bot = ChessBot(depth=3, name="OpeningBot", use_transposition_table=True)
    
    print("<PERSON><PERSON>'s next moves:")
    test_board = board.copy()
    
    for i in range(5):  # Test next 5 moves
        if test_board.is_game_over():
            break
            
        move = bot.get_best_move(test_board)
        piece = test_board.piece_at(move.from_square)
        piece_name = piece.symbol() if piece else "?"
        
        print(f"Move {i+1}: {move} ({piece_name})")
        
        # Check if it's a rook move on back rank
        if piece and piece.piece_type == chess.ROOK:
            from_rank = chess.square_rank(move.from_square)
            to_rank = chess.square_rank(move.to_square)
            if ((piece.color == chess.WHITE and from_rank == 0 and to_rank == 0) or
                (piece.color == chess.BLACK and from_rank == 7 and to_rank == 7)):
                print(f"  ⚠️  WARNING: Rook shuffling on back rank!")
        
        test_board.push(move)
        
        # Show alternative moves
        legal_moves = list(test_board.legal_moves)
        if len(legal_moves) > 1:
            print(f"  Position after move: {test_board.fen()}")
    
    print(f"\nFinal position after {len(bot.move_history)} moves:")
    print(test_board)

def test_early_game_priorities():
    """Test what the bot prioritizes in early game positions."""
    
    print("\n=== Testing Early Game Priorities ===")
    
    # Test from starting position
    board = chess.Board()
    bot = ChessBot(depth=2, name="EarlyGameBot")
    
    print("Starting position move preferences:")
    legal_moves = list(board.legal_moves)
    move_scores = []
    
    for move in legal_moves:
        board.push(move)
        score = bot.evaluate_board(board)
        board.pop()
        
        piece = board.piece_at(move.from_square)
        piece_name = piece.symbol() if piece else "?"
        move_scores.append((move, score, piece_name))
    
    # Sort by score
    move_scores.sort(key=lambda x: x[1], reverse=True)
    
    print("Top 10 opening moves by evaluation:")
    for i, (move, score, piece) in enumerate(move_scores[:10]):
        print(f"{i+1:2d}. {move} ({piece}) - Score: {score:+4d}")
    
    # Test after a few moves
    print("\nAfter 1.e4 e5 2.Nf3:")
    board = chess.Board()
    for move_str in ["e4", "e5", "Nf3"]:
        move = board.parse_san(move_str)
        board.push(move)
    
    print(board)
    
    legal_moves = list(board.legal_moves)
    move_scores = []
    
    for move in legal_moves:
        board.push(move)
        score = bot.evaluate_board(board)
        board.pop()
        
        piece = board.piece_at(move.from_square)
        piece_name = piece.symbol() if piece else "?"
        move_scores.append((move, score, piece_name))
    
    move_scores.sort(key=lambda x: x[1], reverse=False)  # Black to move, so lower is better
    
    print("Top 10 moves for Black:")
    for i, (move, score, piece) in enumerate(move_scores[:10]):
        print(f"{i+1:2d}. {move} ({piece}) - Score: {score:+4d}")

def analyze_rook_vs_development():
    """Compare rook moves vs development moves in a specific position."""
    
    print("\n=== Rook vs Development Analysis ===")
    
    # Position where both rook moves and development are possible
    board = chess.Board()
    moves = ["e4", "e5", "Nf3", "Nc6", "Bc4", "f5"]  # Italian Game variation
    
    for move_str in moves:
        move = board.parse_san(move_str)
        board.push(move)
    
    print("Position after 1.e4 e5 2.Nf3 Nc6 3.Bc4 f5:")
    print(board)
    
    bot = ChessBot(depth=2, name="AnalysisBot")
    
    # Analyze specific move types
    legal_moves = list(board.legal_moves)
    rook_moves = []
    development_moves = []
    other_moves = []
    
    for move in legal_moves:
        piece = board.piece_at(move.from_square)
        if piece:
            if piece.piece_type == chess.ROOK:
                rook_moves.append(move)
            elif piece.piece_type in [chess.KNIGHT, chess.BISHOP] and move.from_square in [chess.B8, chess.G8, chess.C8, chess.F8]:
                development_moves.append(move)
            else:
                other_moves.append(move)
    
    print(f"\nFound {len(rook_moves)} rook moves, {len(development_moves)} development moves, {len(other_moves)} other moves")
    
    def evaluate_moves(moves, move_type):
        if not moves:
            return
        print(f"\n{move_type} moves:")
        move_scores = []
        for move in moves:
            board.push(move)
            score = bot.evaluate_board(board)
            board.pop()
            move_scores.append((move, score))
        
        move_scores.sort(key=lambda x: x[1], reverse=False)  # Black to move
        for move, score in move_scores:
            print(f"  {move} - Score: {score:+4d}")
    
    evaluate_moves(rook_moves, "Rook")
    evaluate_moves(development_moves, "Development")
    evaluate_moves(other_moves[:5], "Other (top 5)")

if __name__ == "__main__":
    test_opening_development()
    test_early_game_priorities()
    analyze_rook_vs_development()