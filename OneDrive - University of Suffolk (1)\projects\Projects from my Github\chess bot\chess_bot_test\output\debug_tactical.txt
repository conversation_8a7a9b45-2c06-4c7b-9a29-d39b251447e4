PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python debug_tactical.py
Position before Nxd5:
r . b q k . . r
p p p p . p p p
. . . . . p . .
. B . n . . . .
. b . P . . . .
. . N . P P . .
P P P Q . . P P
R . . . K . N R
FEN: r1bqk2r/pppp1ppp/5p2/1B1n4/1b1P4/2N1PP2/PPPQ2PP/R3K1NR w KQkq - 5 9

Testing move: Nxd5
Position after Nxd5:
r . b q k . . r
p p p p . p p p
. . . . . p . .
. B . N . . . .
. b . P . . . .
. . . . P P . .
P P P Q . . P P
R . . . K . N R

White queen is on: d2
<PERSON> is attacked by Black: True
Queen attackers: ['b4']
  B on b4
Queen defenders: ['e1']

Tactical blunder detected: False
Note:
Move quality: Good
Explanation: strong development (+1.0)