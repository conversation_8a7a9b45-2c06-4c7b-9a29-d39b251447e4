#!/usr/bin/env python3
"""
Test script to verify the brilliant move detection fix.
"""

import chess
from tactical_evaluation_test import TacticalEvaluator, MoveQuality

def test_qxg4_move():
    """Test the specific Qxg4 move that was incorrectly classified as brilliant."""
    
    # Set up the position before move 16...Qxg4
    # This is a simplified test - we'll create a position where a Queen captures a pawn
    board = chess.Board()
    
    # Create a simple position where Black Queen can capture White pawn on g4
    board.set_fen("rnbqkbnr/pppppppp/8/8/6P1/8/PPPPPP1P/RNBQKBNR b KQkq g3 0 1")
    
    # Move: <PERSON> captures pawn on g4
    move = chess.Move.from_uci("d8g4")  # <PERSON> from d8 captures pawn on g4
    
    evaluator = TacticalEvaluator()
    
    # Simulate evaluation change (<PERSON> gains material, so from <PERSON>'s perspective it's positive)
    eval_before = 0
    eval_after = -596  # From <PERSON>'s perspective, <PERSON> gained ~6 pawns worth
    move_number = 32  # This would be move 16 for <PERSON> (16*2 = 32)
    
    quality, explanation = evaluator.evaluate_move_quality(board, move, eval_before, eval_after, move_number)
    
    print(f"Move: {move}")
    print(f"Quality: {quality.value}")
    print(f"Explanation: {explanation}")
    print(f"Expected: NOT Brilliant (since it's just capturing a pawn, not sacrificing)")
    
    # This should NOT be brilliant since the Queen is just capturing a pawn
    if quality == MoveQuality.BRILLIANT:
        print("❌ FAILED: Move incorrectly classified as Brilliant")
        return False
    else:
        print("✅ PASSED: Move correctly NOT classified as Brilliant")
        return True

def test_actual_sacrifice():
    """Test a position with an actual sacrifice to ensure brilliant detection still works."""
    
    # Create a position where a Queen sacrifice would be brilliant
    board = chess.Board()
    board.set_fen("r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 1")
    
    # Hypothetical Queen sacrifice (this is just for testing the logic)
    move = chess.Move.from_uci("d1h5")  # Queen to h5 (hypothetical sacrifice)
    
    evaluator = TacticalEvaluator()
    
    # Simulate a position where the Queen would be hanging after the move
    # but it leads to a huge advantage (like mate in a few moves)
    eval_before = 0
    eval_after = 500  # Huge advantage after sacrifice
    move_number = 25
    
    quality, explanation = evaluator.evaluate_move_quality(board, move, eval_before, eval_after, move_number)
    
    print(f"\nActual sacrifice test:")
    print(f"Move: {move}")
    print(f"Quality: {quality.value}")
    print(f"Explanation: {explanation}")
    
    return True

if __name__ == "__main__":
    print("Testing brilliant move detection fix...")
    print("=" * 50)
    
    success = test_qxg4_move()
    test_actual_sacrifice()
    
    if success:
        print("\n✅ Fix appears to be working correctly!")
    else:
        print("\n❌ Fix needs more work.")