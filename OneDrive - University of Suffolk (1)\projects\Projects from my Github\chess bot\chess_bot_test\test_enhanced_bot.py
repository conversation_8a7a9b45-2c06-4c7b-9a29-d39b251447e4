#!/usr/bin/env python3
"""
Test script for the enhanced chess bot to demonstrate new evaluation features.
"""

import chess
import chess.pgn
from chess_bot2 import ChessBot
import time

def test_position_evaluation():
    """Test the enhanced evaluation function on various positions."""
    
    print("=" * 60)
    print("TESTING ENHANCED CHESS BOT EVALUATION")
    print("=" * 60)
    
    bot = ChessBot(depth=3, name="EnhancedBot")
    
    # Test positions with different characteristics
    test_positions = [
        {
            "name": "Starting Position",
            "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
            "description": "Standard starting position - should be roughly equal"
        },
        {
            "name": "Doubled Pawns Position", 
            "fen": "rnbqkbnr/ppp1pppp/8/3p4/2PP4/8/PP2PPPP/RNBQKBNR b KQkq c3 0 3",
            "description": "<PERSON> has doubled c-pawns after cxd5 cxd4"
        },
        {
            "name": "Isolated Pawn Position",
            "fen": "rnbqkbnr/ppp1p1pp/5p2/3p4/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 0 3",
            "description": "Black has isolated d-pawn"
        },
        {
            "name": "Passed Pawn Position",
            "fen": "8/8/8/3P4/8/8/p7/8 w - - 0 1",
            "description": "White has advanced passed pawn, black has far passed pawn"
        },
        {
            "name": "Bishop Pair Position",
            "fen": "rnbqk2r/pppp1ppp/5n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 4 4",
            "description": "Both sides have bishop pairs"
        },
        {
            "name": "Open File for Rook",
            "fen": "r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3",
            "description": "Semi-open e-file available"
        },
        {
            "name": "Knight Outpost",
            "fen": "rnbqkb1r/ppp1pppp/5n2/3p4/3P4/2N5/PPP1PPPP/R1BQKBNR w KQkq - 2 3",
            "description": "Knight on c3 could be an outpost"
        },
        {
            "name": "King Safety Issue",
            "fen": "rnbq1rk1/ppp2ppp/3p1n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQ1RK1 w - - 0 6",
            "description": "Both kings castled - good king safety"
        },
        {
            "name": "Exposed King",
            "fen": "rnbqkbnr/ppp2ppp/3p4/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 3",
            "description": "Kings still in center - potential safety issues"
        },
        {
            "name": "Endgame Position",
            "fen": "8/8/8/3k4/3K4/8/8/8 w - - 0 1",
            "description": "King and pawn endgame - should use endgame king table"
        }
    ]
    
    for i, position in enumerate(test_positions, 1):
        print(f"\n{i}. {position['name']}")
        print("-" * 40)
        print(f"Description: {position['description']}")
        print(f"FEN: {position['fen']}")
        
        board = chess.Board(position['fen'])
        
        # Display the position
        print("\nPosition:")
        print(board)
        
        # Get evaluation
        start_time = time.time()
        evaluation = bot.evaluate_board(board)
        eval_time = time.time() - start_time
        
        print(f"\nEvaluation: {evaluation:+d} centipawns")
        print(f"Evaluation time: {eval_time:.4f} seconds")
        
        # Interpret evaluation
        if evaluation > 100:
            print("Assessment: White has a significant advantage")
        elif evaluation > 50:
            print("Assessment: White has a slight advantage") 
        elif evaluation > -50:
            print("Assessment: Position is roughly equal")
        elif evaluation > -100:
            print("Assessment: Black has a slight advantage")
        else:
            print("Assessment: Black has a significant advantage")
        
        # Get best move
        if not board.is_game_over():
            print(f"\nFinding best move for {'White' if board.turn else 'Black'}...")
            start_time = time.time()
            best_move = bot.get_best_move(board)
            move_time = time.time() - start_time
            
            if best_move:
                print(f"Best move: {board.san(best_move)}")
                print(f"Move calculation time: {move_time:.2f} seconds")
            else:
                print("No legal moves available")
        
        print("\n" + "=" * 60)

def test_tactical_awareness():
    """Test the bot's ability to find tactical shots."""
    
    print("\nTESTING TACTICAL AWARENESS")
    print("=" * 60)
    
    bot = ChessBot(depth=4, name="TacticalBot")  # Deeper search for tactics
    
    tactical_positions = [
        {
            "name": "Simple Fork",
            "fen": "rnbqkb1r/pppp1ppp/5n2/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 2 3",
            "description": "Look for knight fork opportunities"
        },
        {
            "name": "Pin Position", 
            "fen": "rnbqkbnr/ppp1pppp/8/3p4/3PP3/8/PPP2PPP/RNBQKBNR b KQkq e3 0 2",
            "description": "Bishop can pin knight to king"
        },
        {
            "name": "Discovered Attack",
            "fen": "r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3",
            "description": "Knight move could create discovered attack"
        }
    ]
    
    for i, position in enumerate(tactical_positions, 1):
        print(f"\n{i}. {position['name']}")
        print("-" * 40)
        print(f"Description: {position['description']}")
        
        board = chess.Board(position['fen'])
        print(f"Position:\n{board}")
        
        # Get evaluation and best move
        evaluation = bot.evaluate_board(board)
        print(f"Evaluation: {evaluation:+d}")
        
        best_move = bot.get_best_move(board)
        if best_move:
            print(f"Bot suggests: {board.san(best_move)}")
            
            # Make the move and see new evaluation
            board.push(best_move)
            new_eval = bot.evaluate_board(board)
            improvement = new_eval - evaluation if board.turn == chess.BLACK else evaluation - new_eval
            print(f"Improvement: {improvement:+d} centipawns")
        
        print("\n" + "=" * 40)

def compare_with_simple_bot():
    """Compare enhanced bot with a simple material-only bot."""
    
    print("\nCOMPARING ENHANCED VS SIMPLE EVALUATION")
    print("=" * 60)
    
    enhanced_bot = ChessBot(depth=3, name="Enhanced")
    
    # Create a simple bot for comparison (we'll override its evaluate_board method)
    simple_bot = ChessBot(depth=3, name="Simple")
    
    # Override simple bot's evaluation to be material-only
    def simple_evaluate(board):
        if board.is_checkmate():
            return -20000 if board.turn == chess.WHITE else 20000
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        piece_values = {
            chess.PAWN: 100, chess.KNIGHT: 320, chess.BISHOP: 330,
            chess.ROOK: 500, chess.QUEEN: 900, chess.KING: 20000
        }
        
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = piece_values[piece.piece_type]
                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value
        return score
    
    simple_bot.evaluate_board = simple_evaluate
    
    test_position = chess.Board("rnbqkb1r/ppp1pppp/5n2/3p4/3P4/2N5/PPP1PPPP/R1BQKBNR w KQkq - 2 3")
    
    print("Test Position:")
    print(test_position)
    
    enhanced_eval = enhanced_bot.evaluate_board(test_position)
    simple_eval = simple_bot.evaluate_board(test_position)
    
    print(f"\nEnhanced Bot Evaluation: {enhanced_eval:+d}")
    print(f"Simple Bot Evaluation: {simple_eval:+d}")
    print(f"Difference: {enhanced_eval - simple_eval:+d}")
    
    enhanced_move = enhanced_bot.get_best_move(test_position)
    simple_move = simple_bot.get_best_move(test_position)
    
    print(f"\nEnhanced Bot Move: {test_position.san(enhanced_move) if enhanced_move else 'None'}")
    print(f"Simple Bot Move: {test_position.san(simple_move) if simple_move else 'None'}")

if __name__ == "__main__":
    test_position_evaluation()
    test_tactical_awareness()
    compare_with_simple_bot()
    
    print("\n" + "=" * 60)
    print("TESTING COMPLETE!")
    print("The enhanced bot now includes:")
    print("✓ Pawn structure evaluation (doubled, isolated, passed pawns)")
    print("✓ Piece activity (bishop pair, rook on open files, knight outposts)")
    print("✓ Advanced king safety (pawn shield, open files)")
    print("✓ Tactical threat detection (pins, forks)")
    print("✓ Control of key squares and files")
    print("✓ Endgame vs middlegame awareness")
    print("=" * 60)