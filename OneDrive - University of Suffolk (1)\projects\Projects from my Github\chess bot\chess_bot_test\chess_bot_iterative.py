import chess
import chess.engine
import random
from typing import Optional, Tuple, List, Dict
import time

class ChessBotIterative:
    """
    A chess bot that uses iterative deepening with minimax algorithm 
    and alpha-beta pruning to make intelligent moves.
    """
    
    def __init__(self, max_depth: int = 6, time_limit: float = 5.0, name: str = "ChessBotIterative"):
        """
        Initialize the chess bot with iterative deepening.
        
        Args:
            max_depth: Maximum search depth for iterative deepening
            time_limit: Maximum time to spend on each move (in seconds)
            name: Name of the bot
        """
        self.max_depth = max_depth
        self.time_limit = time_limit
        self.name = name
        self.nodes_evaluated = 0
        self.start_time = 0
        self.best_move_so_far = None
        self.principal_variation = {}  # Store PV moves for better move ordering
        
        # Piece values for evaluation
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
        
        # Piece-Square Tables (same as original)
        self.pawn_table = [
            0,  0,  0,  0,  0,  0,  0,  0,
            5,  5,  5,  5,  5,  5,  5,  5,
            10, 10, 20, 30, 30, 20, 10, 10,
            15, 15, 25, 35, 35, 25, 15, 15,
            20, 20, 30, 40, 40, 30, 20, 20,
            35, 35, 40, 50, 50, 40, 35, 35,
            50, 50, 60, 70, 70, 60, 50, 50,
            0,  0,  0,  0,  0,  0,  0,  0
        ]
        
        self.knight_table = [
            -50,-40,-30,-30,-30,-30,-40,-50,
            -40,-20,  0,  0,  0,  0,-20,-40,
            -30,  0, 10, 15, 15, 10,  0,-30,
            -30,  5, 15, 20, 20, 15,  5,-30,
            -30,  0, 15, 20, 20, 15,  0,-30,
            -30,  5, 10, 15, 15, 10,  5,-30,
            -40,-20,  0,  5,  5,  0,-20,-40,
            -50,-40,-30,-30,-30,-30,-40,-50
        ]

        self.bishop_table = [
            -20,-10,-10,-10,-10,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5, 10, 10,  5,  0,-10,
            -10,  5,  5, 10, 10,  5,  5,-10,
            -10,  0, 10, 10, 10, 10,  0,-10,
            -10, 10, 10, 10, 10, 10, 10,-10,
            -10,  5,  0,  0,  0,  0,  5,-10,
            -20,-10,-10,-10,-10,-10,-10,-20
        ]

        self.rook_table = [
             0,  0,  0,  0,  0,  0,  0,  0,
             5, 10, 10, 10, 10, 10, 10,  5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
             0,  0,  0,  5,  5,  0,  0,  0
        ]

        self.queen_table = [
            -20,-10,-10, -5, -5,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5,  5,  5,  5,  0,-10,
             -5,  0,  5,  5,  5,  5,  0, -5,
              0,  0,  5,  5,  5,  5,  0, -5,
            -10,  5,  5,  5,  5,  5,  0,-10,
            -10,  0,  5,  0,  0,  0,  0,-10,
            -20,-10,-10, -5, -5,-10,-10,-20
        ]

        self.king_mg_table = [
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -20,-30,-30,-40,-40,-30,-30,-20,
            -10,-20,-20,-20,-20,-20,-20,-10,
             20, 20,  0,  0,  0,  0, 20, 20,
             20, 30, 10,  0,  0, 10, 30, 20
        ]
        
        self.king_eg_table = [
            -50,-40,-30,-20,-20,-30,-40,-50,
            -30,-20,-10,  0,  0,-10,-20,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-30,  0,  0,  0,  0,-30,-30,
            -50,-30,-30,-30,-30,-30,-30,-50
        ]

    def _is_endgame(self, board: chess.Board) -> bool:
        """Determine if the game is in an endgame phase."""
        if not board.pieces(chess.QUEEN, chess.WHITE) and not board.pieces(chess.QUEEN, chess.BLACK):
            return True
        
        white_material = sum(len(board.pieces(pt, chess.WHITE)) for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])
        black_material = sum(len(board.pieces(pt, chess.BLACK)) for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])

        if board.pieces(chess.QUEEN, chess.WHITE) and white_material < 2:
            return True
        if board.pieces(chess.QUEEN, chess.BLACK) and black_material < 2:
            return True
            
        return False

    def evaluate_board(self, board: chess.Board) -> int:
        """Evaluate the current board position."""
        if board.is_checkmate():
            return -20000 if board.turn == chess.WHITE else 20000
        
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        is_endgame = self._is_endgame(board)

        # Material and positional evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = self.piece_values[piece.piece_type]
                
                # Add positional bonuses
                if piece.piece_type == chess.PAWN:
                    table = self.pawn_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.KNIGHT:
                    table = self.knight_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.BISHOP:
                    table = self.bishop_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.ROOK:
                    table = self.rook_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.QUEEN:
                    table = self.queen_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.KING:
                    table = self.king_eg_table if is_endgame else self.king_mg_table
                    value += table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                
                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value

        # Mobility calculation
        original_turn = board.turn
        board.turn = chess.WHITE
        white_mobility = board.legal_moves.count()
        board.turn = chess.BLACK
        black_mobility = board.legal_moves.count()
        board.turn = original_turn

        mobility_score = (white_mobility - black_mobility) * 2
        score += mobility_score
        
        return score

    def _is_time_up(self) -> bool:
        """Check if the time limit has been exceeded."""
        return time.time() - self.start_time >= self.time_limit

    def _order_moves(self, board: chess.Board, moves: List[chess.Move], depth: int) -> List[chess.Move]:
        """
        Order moves for better alpha-beta pruning efficiency.
        
        Args:
            board: Current board position
            moves: List of legal moves
            depth: Current search depth
            
        Returns:
            Ordered list of moves
        """
        def move_score(move):
            score = 0
            
            # Prioritize principal variation move from previous iteration
            if depth in self.principal_variation and move == self.principal_variation[depth]:
                score += 10000
            
            # Prioritize captures
            if board.is_capture(move):
                captured_piece = board.piece_at(move.to_square)
                if captured_piece:
                    # MVV-LVA (Most Valuable Victim - Least Valuable Attacker)
                    victim_value = self.piece_values[captured_piece.piece_type]
                    attacker_piece = board.piece_at(move.from_square)
                    if attacker_piece:
                        attacker_value = self.piece_values[attacker_piece.piece_type]
                        score += victim_value - attacker_value // 10
                    else:
                        score += victim_value
            
            # Prioritize checks
            board.push(move)
            if board.is_check():
                score += 50
            board.pop()
            
            # Prioritize promotions
            if move.promotion:
                score += 800
            
            # Prioritize castling
            if board.is_castling(move):
                score += 30
            
            return score
        
        return sorted(moves, key=move_score, reverse=True)

    def minimax(self, board: chess.Board, depth: int, alpha: int, beta: int, 
                maximizing_player: bool, current_depth: int = 0) -> Tuple[int, Optional[chess.Move]]:
        """
        Minimax algorithm with alpha-beta pruning and time management.
        
        Args:
            board: Current board position
            depth: Remaining search depth
            alpha: Alpha value for pruning
            beta: Beta value for pruning
            maximizing_player: True if maximizing player's turn
            current_depth: Current depth from root (for PV storage)
            
        Returns:
            Tuple of (evaluation_score, best_move)
        """
        self.nodes_evaluated += 1
        
        # Check time limit
        if self._is_time_up():
            return self.evaluate_board(board), None
        
        if depth == 0 or board.is_game_over():
            return self.evaluate_board(board), None
        
        best_move = None
        moves = list(board.legal_moves)
        
        # Order moves for better pruning
        moves = self._order_moves(board, moves, current_depth)

        if maximizing_player:
            max_eval = float('-inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, False, current_depth + 1)
                board.pop()
                
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
                
                # Early termination if time is up
                if self._is_time_up():
                    break
            
            return max_eval, best_move
        else:
            min_eval = float('inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, True, current_depth + 1)
                board.pop()
                
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
                
                # Early termination if time is up
                if self._is_time_up():
                    break
            
            return min_eval, best_move

    def iterative_deepening_search(self, board: chess.Board) -> chess.Move:
        """
        Perform iterative deepening search.
        
        Args:
            board: Current board position
            
        Returns:
            Best move found within time limit
        """
        self.start_time = time.time()
        self.nodes_evaluated = 0
        self.best_move_so_far = None
        self.principal_variation = {}
        
        maximizing = board.turn == chess.WHITE
        
        print(f"{self.name} starting iterative deepening search (max depth: {self.max_depth}, time limit: {self.time_limit}s)")
        
        # Iterative deepening loop
        for depth in range(1, self.max_depth + 1):
            if self._is_time_up():
                break
            
            depth_start_time = time.time()
            
            try:
                score, best_move = self.minimax(board, depth, float('-inf'), float('inf'), maximizing)
                
                if best_move is not None:
                    self.best_move_so_far = best_move
                    # Store the principal variation move for this depth
                    self.principal_variation[0] = best_move
                
                depth_time = time.time() - depth_start_time
                
                print(f"  Depth {depth}: score={score}, move={best_move}, time={depth_time:.2f}s, nodes={self.nodes_evaluated}")
                
                # If we found a mate, no need to search deeper
                if abs(score) > 19000:
                    print(f"  Mate found at depth {depth}!")
                    break
                    
            except KeyboardInterrupt:
                print("  Search interrupted by user")
                break
        
        total_time = time.time() - self.start_time
        
        # Fallback to random move if no move found
        if self.best_move_so_far is None:
            try:
                self.best_move_so_far = random.choice(list(board.legal_moves))
                print("  Fallback to random move")
            except IndexError:
                return None
        
        print(f"{self.name} completed search: {self.nodes_evaluated} nodes in {total_time:.2f}s")
        return self.best_move_so_far

    def get_best_move(self, board: chess.Board) -> chess.Move:
        """
        Get the best move for the current position using iterative deepening.
        
        Args:
            board: Current board position
            
        Returns:
            Best move according to the bot's evaluation
        """
        return self.iterative_deepening_search(board)
    
    def set_time_limit(self, time_limit: float):
        """Set the time limit for move search."""
        self.time_limit = time_limit
        print(f"{self.name} time limit set to {time_limit} seconds")
    
    def set_max_depth(self, max_depth: int):
        """Set the maximum search depth."""
        self.max_depth = max_depth
        print(f"{self.name} maximum depth set to {max_depth}")

    def get_search_info(self) -> Dict:
        """Return information about the last search."""
        return {
            'nodes_evaluated': self.nodes_evaluated,
            'time_limit': self.time_limit,
            'max_depth': self.max_depth,
            'best_move': self.best_move_so_far
        }