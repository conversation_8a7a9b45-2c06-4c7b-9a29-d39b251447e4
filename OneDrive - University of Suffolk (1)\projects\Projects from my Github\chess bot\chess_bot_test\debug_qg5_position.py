#!/usr/bin/env python3
"""
Debug specifically what happens when queen goes to g5
"""

import chess
from unified_chess_bot import MediumBot

def debug_qg5_position():
    print("Debugging Qg5 Position")
    print("="*50)
    
    # Create the position after Qg5
    board = chess.Board()
    moves = ["Nc3", "d5", "Nf3", "Nc6", "d4", "e6", "Ne5", "Nb4", "a3", "Qg5"]
    for move in moves:
        board.push_san(move)
    
    print("Position after Qg5:")
    print(board)
    print(f"White to move")
    print()
    
    bot = MediumBot()
    
    # Check each hanging piece manually
    print("Checking all pieces for hanging status:")
    print("-" * 40)
    
    hanging_pieces = []
    
    for square in chess.SQUARES:
        piece = board.piece_at(square)
        if piece is None:
            continue
            
        # Check if piece is attacked
        attackers = board.attackers(not piece.color, square)
        if not attackers:
            continue
            
        # Check if piece is defended
        defenders = board.attackers(piece.color, square)
        piece_value = bot.piece_values[piece.piece_type]
        
        square_name = chess.square_name(square)
        piece_symbol = piece.symbol()
        color_name = "White" if piece.color == chess.WHITE else "Black"
        
        print(f"{piece_symbol} on {square_name} ({color_name}, value {piece_value}):")
        print(f"  Attackers: {[chess.square_name(sq) for sq in attackers]}")
        print(f"  Defenders: {[chess.square_name(sq) for sq in defenders]}")
        
        if len(defenders) == 0:
            penalty = piece_value * 2
            hanging_pieces.append((square_name, piece_symbol, color_name, penalty))
            print(f"  STATUS: HANGING! Penalty: {penalty}")
        else:
            # Check for unfavorable exchange
            min_attacker_value = float('inf')
            for attacker_square in attackers:
                attacker_piece = board.piece_at(attacker_square)
                if attacker_piece:
                    attacker_value = bot.piece_values[attacker_piece.piece_type]
                    min_attacker_value = min(min_attacker_value, attacker_value)
            
            if min_attacker_value < piece_value:
                exchange_loss = piece_value - min_attacker_value
                penalty = exchange_loss * 1.5
                hanging_pieces.append((square_name, piece_symbol, color_name, penalty))
                print(f"  STATUS: UNFAVORABLE EXCHANGE! Loss: {exchange_loss}, Penalty: {penalty}")
            else:
                print(f"  STATUS: Safe or favorable exchange")
        print()
    
    print("Summary of hanging pieces:")
    print("-" * 30)
    total_score = 0
    for square, symbol, color, penalty in hanging_pieces:
        print(f"{symbol} on {square} ({color}): penalty {penalty}")
        if color == "White":
            total_score -= penalty  # White hanging piece hurts White
        else:
            total_score += penalty  # Black hanging piece helps White
    
    print(f"\nTotal hanging piece score: {total_score}")
    
    # Compare with function
    function_score = bot._evaluate_hanging_pieces(board)
    print(f"Function returned: {function_score}")
    
    if abs(total_score - function_score) < 0.1:
        print("✅ Manual calculation matches function")
    else:
        print("❌ MISMATCH!")
    
    # The key question: Why does the bot think this is good?
    print(f"\n{'='*50}")
    print("THE PROBLEM:")
    print("="*50)
    print("The bot is evaluating hanging BLACK pieces as GOOD for the position")
    print("This means it thinks it's good when Black hangs pieces (benefits White)")
    print("But Black is the one making the move!")
    print("Black should AVOID hanging its own pieces!")

if __name__ == "__main__":
    debug_qg5_position()