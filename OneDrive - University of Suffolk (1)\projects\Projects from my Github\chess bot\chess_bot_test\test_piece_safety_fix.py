#!/usr/bin/env python3
"""
Test script to verify that the piece safety evaluation is working correctly.
This tests the specific scenario described by the user: queen captures defended pawn.
"""

import chess
from unified_chess_bot import UnifiedChessBot

def test_queen_captures_defended_pawn():
    """Test the scenario where queen captures a defended pawn."""
    bot = UnifiedChessBot(depth=3)
    
    print("=== Testing Queen Captures Defended Pawn ===")
    
    # Direct setup: Queen on d4, black pawn on e5 defended by knight on f6
    board = chess.Board()
    board.set_fen("rnbqkb1r/pppp1ppp/5n2/4p3/3Q4/8/PPP1PPPP/RNB1KBNR b KQkq - 0 1")
    
    print("Position: White queen on d4, black pawn on e5 defended by knight on f6")
    print(board)
    print(f"Evaluation before capture: {bot.evaluate_board(board)}")
    
    # White queen captures the defended pawn
    board.set_fen("rnbqkb1r/pppp1ppp/5n2/4Q3/8/8/PPP1PPPP/RNB1KBNR b KQkq - 0 1")
    
    print("\nAfter Qxe5 (queen captures defended pawn):")
    print(board)
    print(f"Evaluation after capture: {bot.evaluate_board(board)}")
    print("Note: Queen on e5 can be captured by knight on f6")
    
    # Compare to a position where the queen is safe
    board.set_fen("rnbqkb1r/pppp1ppp/5n2/8/3Q4/8/PPP1PPPP/RNB1KBNR b KQkq - 0 1")
    
    print("\nSame position but queen back on d4 (safer):")
    print(board)
    print(f"Evaluation with queen on d4: {bot.evaluate_board(board)}")
    
    print("\nThe evaluation should be lower when the queen is hanging!")
    return True

def test_simple_hanging_piece():
    """Test a simpler case of a hanging piece."""
    bot = UnifiedChessBot()
    
    # Simple position with a hanging queen
    board = chess.Board()
    board.set_fen("rnbqkbnr/pppppppp/8/8/4Q3/8/PPPP1PPP/RNB1KBNR w KQkq - 0 1")
    
    print("\n" + "="*50)
    print("Simple hanging queen test:")
    print(board)
    print(f"Evaluation (queen hanging on e4): {bot.evaluate_board(board)}")
    
    # Move queen to safety
    board.set_fen("rnbqkbnr/pppppppp/8/8/8/4Q3/PPPP1PPP/RNB1KBNR w KQkq - 0 1")
    print("\nQueen moved to e3 (safer):")
    print(board)
    print(f"Evaluation (queen safer on e3): {bot.evaluate_board(board)}")

if __name__ == "__main__":
    print("Testing piece safety evaluation fix...")
    print("="*60)
    
    test_queen_captures_defended_pawn()
    test_simple_hanging_piece()
    
    print("\n" + "="*60)
    print("Test completed. The evaluation should now properly account for")
    print("piece safety and hanging pieces!")