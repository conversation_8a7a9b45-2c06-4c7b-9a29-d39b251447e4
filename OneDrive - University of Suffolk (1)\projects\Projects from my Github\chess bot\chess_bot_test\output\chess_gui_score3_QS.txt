PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score3_QS.py
ChessBot evaluated 2432 positions in 0.51 seconds
ChessBot evaluated 5008 positions in 1.28 seconds
ChessBot evaluated 1331 positions in 0.23 seconds
ChessBot evaluated 2257 positions in 0.36 seconds
ChessBot evaluated 4129 positions in 1.12 seconds
ChessBot evaluated 35760 positions in 11.11 seconds
ChessBot evaluated 103441 positions in 35.53 seconds
ChessBot evaluated 25822 positions in 9.15 seconds



PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score3_QS.py
ChessBot evaluated 1780 nodes in 0.21s
ChessBot evaluated 6308 nodes in 0.72s
ChessBot evaluated 12263 nodes in 1.86s
ChessBot evaluated 12139 nodes in 1.77s
ChessBot evaluated 4484 nodes in 0.78s
ChessBot evaluated 14900 nodes in 2.65s
ChessBot evaluated 4377 nodes in 0.60s
ChessBot evaluated 8457 nodes in 1.60s
ChessBot evaluated 1111 nodes in 0.17s
ChessBot evaluated 1010 nodes in 0.13s
ChessBot evaluated 3234 nodes in 0.37s
ChessBot evaluated 892 nodes in 0.17s
ChessBot evaluated 4786 nodes in 0.58s
ChessBot evaluated 2938 nodes in 0.34s
ChessBot evaluated 601 nodes in 0.05s
<PERSON>Bot evaluated 1780 nodes in 0.16s
<PERSON>Bot evaluated 2790 nodes in 0.49s
ChessBot evaluated 4119 nodes in 0.68s
<PERSON>Bot evaluated 1491 nodes in 0.18s