PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python launcher.py
Chess Bot Launcher
Checking dependencies...
All dependencies found!

============================================================
CHESS BOT - VERSION SELECTOR
============================================================
Welcome to Chess Bot! Choose your preferred interface:

1. CLI Version - Command Line Interface
   • Text-based interface
   • Lightweight and fast
   • Works in any terminal
   • Full chess notation support

2. GUI Version - Graphical User Interface
   • Visual chess board
   • Drag and drop moves
   • Real-time game analysis
   • Save/load games with file dialogs

3. Help - About Chess Bot
4. Quit
============================================================
Enter your choice (1-4): 2

Launching GUI version...
Loading graphical interface...
can't invoke "event" command: application has been destroyed
    while executing
"event generate $w <<ThemeChanged>>"
    (procedure "ttk::ThemeChanged" line 6)
    invoked from within
"ttk::ThemeChanged"
ChessBot evaluated 868 positions in 0.16 seconds
ChessBot evaluated 1927 positions in 0.47 seconds
ChessBot evaluated 2075 positions in 0.44 seconds
ChessBot evaluated 4779 positions in 1.42 seconds
ChessBot evaluated 2308 positions in 0.59 seconds
ChessBot evaluated 2135 positions in 0.51 seconds
ChessBot evaluated 2002 positions in 0.36 seconds
ChessBot evaluated 3256 positions in 0.63 seconds
ChessBot evaluated 1331 positions in 0.26 seconds
ChessBot evaluated 868 positions in 0.20 seconds
ChessBot evaluated 1679 positions in 0.50 seconds
ChessBot evaluated 2742 positions in 0.54 seconds
ChessBot evaluated 5318 positions in 1.11 seconds
ChessBot evaluated 6747 positions in 1.61 seconds
ChessBot evaluated 6874 positions in 1.59 seconds
ChessBot evaluated 5640 positions in 1.10 seconds
ChessBot evaluated 1315 positions in 0.30 seconds
ChessBot evaluated 2733 positions in 0.71 seconds
ChessBot evaluated 4467 positions in 0.94 seconds
ChessBot evaluated 5133 positions in 1.19 seconds
ChessBot evaluated 5328 positions in 1.48 seconds
ChessBot evaluated 5183 positions in 1.09 seconds
ChessBot evaluated 3294 positions in 0.67 seconds
ChessBot evaluated 738 positions in 0.21 seconds
ChessBot evaluated 3082 positions in 0.60 seconds
ChessBot evaluated 4014 positions in 1.12 seconds
ChessBot evaluated 4429 positions in 0.81 seconds
ChessBot evaluated 3590 positions in 0.68 seconds
ChessBot evaluated 1551 positions in 0.40 seconds
ChessBot evaluated 2772 positions in 0.53 seconds
ChessBot evaluated 2945 positions in 0.54 seconds
ChessBot evaluated 4022 positions in 0.72 seconds
ChessBot evaluated 1879 positions in 0.35 seconds
ChessBot evaluated 2259 positions in 0.51 seconds
ChessBot evaluated 2326 positions in 0.53 seconds
ChessBot evaluated 3303 positions in 0.59 seconds
ChessBot evaluated 2608 positions in 0.69 seconds
ChessBot evaluated 1968 positions in 0.31 seconds
ChessBot evaluated 3848 positions in 0.65 seconds
ChessBot evaluated 1774 positions in 0.40 seconds
ChessBot evaluated 2834 positions in 0.70 seconds
ChessBot evaluated 4008 positions in 1.01 seconds
ChessBot evaluated 2578 positions in 0.54 seconds
ChessBot evaluated 649 positions in 0.13 seconds
ChessBot evaluated 1411 positions in 0.25 seconds
ChessBot evaluated 708 positions in 0.19 seconds
ChessBot evaluated 512 positions in 0.11 seconds
ChessBot evaluated 861 positions in 0.15 seconds
ChessBot evaluated 106 positions in 0.02 seconds
ChessBot evaluated 94 positions in 0.01 seconds
ChessBot evaluated 245 positions in 0.04 seconds
ChessBot evaluated 524 positions in 0.10 seconds
ChessBot evaluated 443 positions in 0.12 seconds
ChessBot evaluated 1620 positions in 0.28 seconds
ChessBot evaluated 2203 positions in 0.36 seconds
ChessBot evaluated 1658 positions in 0.33 seconds
ChessBot evaluated 1735 positions in 0.27 seconds
ChessBot evaluated 1371 positions in 0.25 seconds
ChessBot evaluated 931 positions in 0.13 seconds
ChessBot evaluated 907 positions in 0.12 seconds
ChessBot evaluated 710 positions in 0.11 seconds
ChessBot evaluated 593 positions in 0.09 seconds
ChessBot evaluated 1018 positions in 0.16 seconds
ChessBot evaluated 887 positions in 0.13 seconds
ChessBot evaluated 1896 positions in 0.26 seconds
ChessBot evaluated 275 positions in 0.05 seconds
ChessBot evaluated 90 positions in 0.01 seconds
ChessBot evaluated 182 positions in 0.03 seconds
ChessBot evaluated 161 positions in 0.02 seconds
ChessBot evaluated 98 positions in 0.01 seconds
ChessBot evaluated 763 positions in 0.14 seconds
ChessBot evaluated 1291 positions in 0.26 seconds
ChessBot evaluated 2038 positions in 0.55 seconds
ChessBot evaluated 4576 positions in 1.04 seconds
ChessBot evaluated 1655 positions in 0.43 seconds
ChessBot evaluated 2887 positions in 0.76 seconds
ChessBot evaluated 2407 positions in 0.66 seconds
ChessBot evaluated 2034 positions in 0.54 seconds

GUI version exited normally.

Return to launcher? (y/n):







-------------------------------------------------------




============================================================
CHESS BOT - VERSION SELECTOR
============================================================
Welcome to Chess Bot! Choose your preferred interface:

1. CLI Version - Command Line Interface
   • Text-based interface
   • Lightweight and fast
   • Works in any terminal
   • Full chess notation support

2. GUI Version - Graphical User Interface
   • Visual chess board
   • Drag and drop moves
   • Real-time game analysis
   • Save/load games with file dialogs

3. Help - About Chess Bot
4. Quit
============================================================
Enter your choice (1-4): 3

============================================================
ABOUT CHESS BOT
============================================================
Chess Bot is a complete chess game with AI opponent that offers
both command-line and graphical interfaces.

FEATURES:
• Full chess rules implementation
• AI opponent using minimax algorithm with alpha-beta pruning
• Multiple difficulty levels (Easy to Expert)
• Position analysis and evaluation
• Game saving/loading in PGN format
• Opening recognition
• Move history tracking
• Both CLI and GUI interfaces

CLI VERSION FEATURES:
• Standard algebraic notation input
• Unicode chess piece display
• Interactive help system
• Bot vs Bot demonstration mode
• Position analysis on demand

GUI VERSION FEATURES:
• Visual drag-and-drop interface
• Real-time board highlighting
• Graphical move history
• File dialogs for save/load
• Popup position analysis
• Difficulty selection via radio buttons

SYSTEM REQUIREMENTS:
• Python 3.7 or higher
• python-chess library
• tkinter (usually included with Python)

DIFFICULTY LEVELS:
• Easy (depth 2): Quick moves, basic strategy
• Medium (depth 3): Balanced play (default)
• Hard (depth 4): Strong tactical play
• Expert (depth 5): Very strong play (slower)
============================================================

Press Enter to continue...