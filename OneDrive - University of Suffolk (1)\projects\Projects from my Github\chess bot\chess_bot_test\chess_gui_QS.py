#!/usr/bin/env python3
"""
Chess GUI with Quiescence Search Analysis
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import chess
import chess.pgn
import threading
import time
import io
import os
from chess_bot_QS_testing import ChessBot
from chess_game import ChessGame

class ChessGUI:
    """Chess GUI with Quiescence Search Analysis."""
    
    def __init__(self):
        """Initialize the chess GUI."""
        self.root = tk.Tk()
        self.root.title("Chess Bot - QS Analysis Version")
        self.root.geometry("1100x700")
        self.root.resizable(True, True)
        
        # Game state
        self.board = chess.Board()
        self.bot = ChessBot(depth=3, name="ChessBotQS", use_quiescence=True)
        self.game_history = []
        self.human_color = chess.WHITE
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        self.ai_thinking = False  # Flag to track when AI is thinking
        
        # Colors and styling
        self.light_square_color = "#F0D9B5"
        self.dark_square_color = "#B58863"
        self.highlight_color = "#FFFF00"
        self.selected_color = "#FF6B6B"
        self.legal_move_color = "#90EE90"
        self.disabled_color_overlay = "gray"  # Use standard gray with stipple for transparency
        
        # Unicode chess pieces
        self.piece_symbols = {
            'P': '♙', 'N': '♘', 'B': '♗', 'R': '♖', 'Q': '♕', 'K': '♔',  # White
            'p': '♟', 'n': '♞', 'b': '♝', 'r': '♜', 'q': '♛', 'k': '♚'   # Black
        }
        
        self.setup_ui()
        self.update_board_display()
        self.update_status()
    
    def setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left panel (board)
        left_panel = ttk.Frame(main_frame)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Board canvas
        self.canvas_size = 560
        self.square_size = self.canvas_size // 8
        self.canvas = tk.Canvas(left_panel, width=self.canvas_size, height=self.canvas_size, bg="white")
        self.canvas.pack(padx=10, pady=10)
        self.canvas.bind("<Button-1>", self.on_square_click)
        
        # Status bar
        self.status_var = tk.StringVar()
        status_bar = ttk.Label(left_panel, textvariable=self.status_var, font=("Arial", 12))
        status_bar.pack(pady=5, fill=tk.X)
        
        # Right panel (controls and info)
        right_panel = ttk.Frame(main_frame, padding=(10, 0, 0, 0))
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH)
        
        # Game controls
        controls_frame = ttk.LabelFrame(right_panel, text="Game Controls", padding=10)
        controls_frame.pack(fill=tk.X, pady=5)
        
        # New game button
        new_game_btn = ttk.Button(controls_frame, text="New Game", command=self.new_game)
        new_game_btn.pack(fill=tk.X, pady=2)
        
        # Switch sides button
        switch_btn = ttk.Button(controls_frame, text="Switch Sides", command=self.switch_colors)
        switch_btn.pack(fill=tk.X, pady=2)
        
        # Difficulty selection
        difficulty_frame = ttk.Frame(controls_frame)
        difficulty_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(difficulty_frame, text="Difficulty:").pack(side=tk.LEFT)
        
        self.difficulty_var = tk.StringVar(value="Medium")
        difficulties = [("Easy", 2), ("Medium", 3), ("Hard", 4), ("Expert", 5)]
        
        difficulty_menu = ttk.OptionMenu(
            difficulty_frame, 
            self.difficulty_var,
            "Medium",
            *[d[0] for d in difficulties],
            command=lambda x: self.set_difficulty(next(d[1] for d in difficulties if d[0] == x))
        )
        difficulty_menu.pack(side=tk.RIGHT, fill=tk.X, expand=True)
        
        # QS toggle
        qs_frame = ttk.Frame(controls_frame)
        qs_frame.pack(fill=tk.X, pady=5)
        
        self.qs_var = tk.BooleanVar(value=True)
        qs_check = ttk.Checkbutton(
            qs_frame, 
            text="Use Quiescence Search", 
            variable=self.qs_var,
            command=self.toggle_quiescence
        )
        qs_check.pack(fill=tk.X)
        
        # Save/Load buttons
        save_load_frame = ttk.Frame(controls_frame)
        save_load_frame.pack(fill=tk.X, pady=5)
        
        save_btn = ttk.Button(save_load_frame, text="Save Game", command=self.save_game)
        save_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        
        load_btn = ttk.Button(save_load_frame, text="Load Game", command=self.load_game)
        load_btn.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(2, 0))
        
        # Move history
        history_frame = ttk.LabelFrame(right_panel, text="Move History", padding=10)
        history_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.history_text = tk.Text(history_frame, width=30, height=10, wrap=tk.WORD, font=("Courier", 10))
        self.history_text.pack(fill=tk.BOTH, expand=True)
        
        # QS Statistics
        self.qs_stats_frame = ttk.LabelFrame(right_panel, text="Quiescence Search Stats", padding=10)
        self.qs_stats_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.qs_stats_text = tk.Text(self.qs_stats_frame, width=30, height=10, wrap=tk.WORD, font=("Courier", 10))
        self.qs_stats_text.pack(fill=tk.BOTH, expand=True)
        
        # Analysis button
        analysis_btn = ttk.Button(right_panel, text="Show Position Analysis", command=self.show_analysis)
        analysis_btn.pack(fill=tk.X, pady=5)
    
    def draw_board(self):
        """Draw the chess board."""
        self.canvas.delete("all")
        
        # The outer loop variable 'rank_idx' goes from 0 (rank 1) to 7 (rank 8).
        # The inner loop variable 'visual_col' goes from 0 (left side of canvas) to 7 (right).
        for rank_idx in range(8):
            for visual_col in range(8):
                # Determine the canvas coordinates (x1, y1) for the top-left of the square.
                x1 = visual_col * self.square_size
                if self.human_color == chess.WHITE:
                    # For White, rank 8 (rank_idx 7) is at the top of the canvas (y=0).
                    y1 = (7 - rank_idx) * self.square_size
                else:  # Black
                    # For Black, rank 1 (rank_idx 0) is at the top of the canvas (y=0).
                    y1 = rank_idx * self.square_size
                
                x2 = x1 + self.square_size
                y2 = y1 + self.square_size

                # Determine the actual chess square this visual position corresponds to.
                if self.human_color == chess.WHITE:
                    file_idx = visual_col
                    square = chess.square(file_idx, rank_idx)
                else:  # Black
                    file_idx = 7 - visual_col
                    square = chess.square(file_idx, rank_idx)

                # Determine the square's color based on its actual file and rank.
                is_light_square = (chess.square_file(square) + chess.square_rank(square)) % 2 != 0
                color = self.light_square_color if is_light_square else self.dark_square_color

                # Check if the current square needs to be highlighted.
                if square == self.selected_square:
                    color = self.selected_color
                elif square in self.highlighted_squares:
                    color = self.legal_move_color

                self.canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline="")

                # --- CORRECTED COORDINATE LABELS ---
                # Draw rank numbers on the left edge (visual_col == 0)
                if visual_col == 0:
                    # Text color should be the opposite of the square color for visibility
                    text_color = self.dark_square_color if is_light_square else self.light_square_color
                    rank_label = str(rank_idx + 1)
                    self.canvas.create_text(x1 + 5, y1 + 5, text=rank_label, anchor=tk.NW, fill=text_color, font=("Arial", 8))

                # Determine if we are on the bottom-most visual row to draw file letters
                is_bottom_row = False
                if self.human_color == chess.WHITE and rank_idx == 0:  # Rank 1 is at the bottom for White
                    is_bottom_row = True
                elif self.human_color == chess.BLACK and rank_idx == 7:  # Rank 8 is at the bottom for Black
                    is_bottom_row = True
                
                if is_bottom_row:
                    # Text color should be the opposite of the square color for visibility
                    text_color = self.dark_square_color if is_light_square else self.light_square_color
                    file_label = chr(ord('a') + file_idx)
                    self.canvas.create_text(x2 - 5, y2 - 5, text=file_label, anchor=tk.SE, fill=text_color, font=("Arial", 8))
        
        # If AI is thinking, draw a semi-transparent overlay to indicate the board is disabled
        if self.ai_thinking:
            # Create a stippled overlay to simulate transparency
            self.canvas.create_rectangle(
                0, 0, self.canvas_size, self.canvas_size,
                fill=self.disabled_color_overlay, stipple="gray50"
            )
            
            # Create a white background for the text to make it more readable
            text_bg_size = 200  # Width of the background rectangle
            self.canvas.create_rectangle(
                self.canvas_size // 2 - text_bg_size // 2,
                self.canvas_size // 2 - 20,
                self.canvas_size // 2 + text_bg_size // 2,
                self.canvas_size // 2 + 20,
                fill="white", outline=""
            )
            
            # Add a "Thinking..." text in the center of the board
            self.canvas.create_text(
                self.canvas_size // 2, self.canvas_size // 2,
                text="AI is thinking...",
                font=("Arial", 20, "bold"),
                fill="black"
            )
    
    def draw_pieces(self):
        """Draw the chess pieces on the board."""
        for square in chess.SQUARES:
            piece = self.board.piece_at(square)
            if piece:
                file_idx = chess.square_file(square)
                rank_idx = chess.square_rank(square)
                
                # Adjust for board orientation
                x = file_idx if self.human_color == chess.WHITE else 7 - file_idx
                y = 7 - rank_idx if self.human_color == chess.WHITE else rank_idx
                
                x1 = x * self.square_size + self.square_size // 2
                y1 = y * self.square_size + self.square_size // 2
                
                symbol = self.piece_symbols[piece.symbol()]
                self.canvas.create_text(x1, y1, text=symbol, font=("Arial", 36), fill="black")
    
    def update_board_display(self):
        """Update the board display."""
        self.draw_board()
        self.draw_pieces()
    
    def on_square_click(self, event):
        """Handle square click events."""
        # Ignore clicks when game is over, when it's not the player's turn, or when AI is thinking
        if self.game_over or (self.board.turn != self.human_color) or self.ai_thinking:
            return
        
        # Convert click coordinates to board square
        col = event.x // self.square_size
        row = event.y // self.square_size
        
        # Adjust for board orientation
        if self.human_color == chess.WHITE:
            square = chess.square(col, 7 - row)
        else:
            square = chess.square(7 - col, row)
        
        # If a square is already selected, try to make a move
        if self.selected_square is not None:
            move = chess.Move(self.selected_square, square)
            
            # Check for promotion
            if self.board.piece_at(self.selected_square) and \
               self.board.piece_at(self.selected_square).piece_type == chess.PAWN and \
               ((square >= 56 and self.human_color == chess.WHITE) or \
                (square <= 7 and self.human_color == chess.BLACK)):
                move = chess.Move(self.selected_square, square, promotion=chess.QUEEN)
            
            if move in self.board.legal_moves:
                self.make_move(move)
            
            # Clear selection and highlights
            self.selected_square = None
            self.highlighted_squares = []
        else:
            # Select the square if it has a piece of the player's color
            piece = self.board.piece_at(square)
            if piece and piece.color == self.human_color:
                self.selected_square = square
                self.highlight_legal_moves(square)
        
        self.update_board_display()
    
    def highlight_legal_moves(self, square):
        """Highlight legal moves for the selected piece."""
        self.highlighted_squares = []
        for move in self.board.legal_moves:
            if move.from_square == square:
                self.highlighted_squares.append(move.to_square)
    
    def make_move(self, move):
        """Make a move on the board."""
        # This method may be called from a background thread via root.after()
        # All GUI updates should be thread-safe
        
        # Convert move to SAN before making it
        san = self.board.san(move)
        
        # Make the move
        self.board.push(move)
        
        # Add to history
        self.game_history.append(san)
        
        # Update displays - these are all GUI operations and should be safe
        # since this method is called via root.after() from background threads
        self.update_board_display()
        self.update_history_display()
        self.update_status()
        
        # Check for game over
        if self.board.is_game_over():
            # handle_game_over is now thread-safe
            self.handle_game_over()
            return
        
        # Make bot move if it's the bot's turn
        if self.board.turn != self.human_color:
            # Start the bot's move after a short delay to allow the UI to update
            self.root.after(100, self.make_bot_move)
    
    def make_bot_move(self):
        """Make a move for the bot in a separate thread."""
        # Set the AI thinking flag and update the board display to show the disabled state
        self.ai_thinking = True
        self.update_board_display()
        
        def bot_move_thread():
            try:
                # Update status in the main thread
                self.root.after(0, lambda: self.update_status("Bot is thinking..."))
                
                # Get the best move (computation-intensive task in background thread)
                move = self.bot.get_best_move(self.board)
                
                # Schedule all UI updates to happen in the main thread
                self.root.after(0, lambda: self.update_qs_stats_display())
                
                # Clear the AI thinking flag and update the board display
                def complete_bot_move():
                    self.ai_thinking = False
                    self.update_board_display()
                    
                    if move:
                        # Make the move in the main thread
                        self.make_move(move)
                    else:
                        messagebox.showinfo("Info", "Bot couldn't find a move!")
                
                # Schedule the completion in the main thread
                self.root.after(0, complete_bot_move)
                
            except Exception as e:
                # Handle errors and clear the AI thinking flag
                def handle_error():
                    self.ai_thinking = False
                    self.update_board_display()
                    messagebox.showerror("Error", f"Bot error: {e}")
                
                self.root.after(0, handle_error)
        
        threading.Thread(target=bot_move_thread, daemon=True).start()
    
    def update_status(self, custom_message=None):
        """Update the game status display."""
        if custom_message:
            self.status_var.set(custom_message)
            return
        
        if self.board.is_game_over():
            if self.board.is_checkmate():
                winner = "Black" if self.board.turn == chess.WHITE else "White"
                self.status_var.set(f"Checkmate! {winner} wins.")
            elif self.board.is_stalemate():
                self.status_var.set("Stalemate! Game is drawn.")
            elif self.board.is_insufficient_material():
                self.status_var.set("Insufficient material! Game is drawn.")
            elif self.board.is_fifty_moves():
                self.status_var.set("Fifty-move rule! Game is drawn.")
            elif self.board.is_repetition():
                self.status_var.set("Threefold repetition! Game is drawn.")
            else:
                self.status_var.set("Game over!")
        else:
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            check = " (Check!)" if self.board.is_check() else ""
            player = "Your" if self.board.turn == self.human_color else "Bot's"
            self.status_var.set(f"{turn}'s turn{check} - {player} move")
    
    def update_history_display(self):
        """Update the move history display."""
        self.history_text.delete(1.0, tk.END)
        
        if not self.game_history:
            self.history_text.insert(tk.END, "No moves yet.")
            return
        
        # Format the move history in pairs (White's move, Black's move)
        history_text = ""
        for i in range(0, len(self.game_history), 2):
            move_num = i // 2 + 1
            white_move = self.game_history[i]
            black_move = self.game_history[i+1] if i+1 < len(self.game_history) else ""
            
            history_text += f"{move_num}. {white_move} {black_move}\n"
        
        self.history_text.insert(tk.END, history_text)
        self.history_text.see(tk.END)  # Scroll to the end
    
    def update_qs_stats_display(self):
        """Update the quiescence search statistics display."""
        # This method should only be called from the main thread or scheduled with root.after()
        
        # Get stats from the bot
        stats = self.bot.get_qs_stats()
        
        # Clear the text widget
        self.qs_stats_text.delete(1.0, tk.END)
        
        if not self.bot.use_quiescence:
            self.qs_stats_text.insert(tk.END, "Quiescence search is disabled.")
            return
        
        # Prepare the stats text
        stats_text = "Quiescence Search Statistics:\n\n"
        stats_text += f"Calls: {stats['calls']}\n"
        stats_text += f"Positions evaluated: {stats['positions_evaluated']}\n"
        stats_text += f"Max depth reached: {stats['max_depth_reached']}\n"
        stats_text += f"Captures found: {stats['captures_found']}\n"
        stats_text += f"Score improvements: {stats['score_improvements']}\n"
        
        if stats['score_improvements'] > 0:
            stats_text += f"Avg improvement: {stats['avg_score_improvement']:.2f} points\n"
        
        stats_text += f"Time spent: {stats['time_spent']:.2f}s\n"
        
        # Update the text widget
        self.qs_stats_text.insert(tk.END, stats_text)
    
    def handle_game_over(self):
        """Handle game over state."""
        # This method should only be called from the main thread or scheduled with root.after()
        self.game_over = True
        
        result = "*"
        if self.board.is_checkmate():
            winner = "Black" if self.board.turn == chess.WHITE else "White"
            message = f"Checkmate! {winner} wins."
            result = "0-1" if winner == "Black" else "1-0"
        elif self.board.is_stalemate():
            message = "Stalemate! Game is drawn."
            result = "1/2-1/2"
        elif self.board.is_insufficient_material():
            message = "Insufficient material! Game is drawn."
            result = "1/2-1/2"
        else:
            message = "Game over!"
        
        # Use after(0) to ensure messagebox is shown from the main thread
        self.root.after(0, lambda: messagebox.showinfo("Game Over", message))
        self.update_status()
    
    def new_game(self):
        """Start a new game."""
        self.board = chess.Board()
        self.game_history = []
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        
        self.update_board_display()
        self.update_history_display()
        self.update_status()
        
        # Reset QS stats
        self.bot.reset_qs_stats()
        self.update_qs_stats_display()
        
        # Make bot move if it's black's turn and human is white
        if self.board.turn != self.human_color:
            self.make_bot_move()
    
    def switch_colors(self):
        """Switch player colors."""
        self.human_color = not self.human_color
        self.update_board_display()
        self.update_status()
        
        # Make bot move if it's the bot's turn now
        if self.board.turn != self.human_color and not self.game_over:
            self.make_bot_move()
    
    def set_difficulty(self, depth):
        """Set the bot difficulty."""
        self.bot.set_depth(depth)
        difficulty_name = self.difficulty_var.get()
        messagebox.showinfo("Difficulty Changed", f"Bot difficulty set to {difficulty_name} (depth {depth})")
    
    def toggle_quiescence(self):
        """Toggle quiescence search on or off."""
        use_qs = self.qs_var.get()
        self.bot.toggle_quiescence(use_qs)
        self.update_qs_stats_display()
    
    def save_game(self):
        """Save the current game to a PGN file."""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".pgn",
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
                title="Save Game"
            )
            
            if not filename:
                return
            
            # Create a game object
            game = chess.pgn.Game()
            
            # Set headers
            game.headers["Event"] = "Chess Bot Game"
            game.headers["Site"] = "Chess Bot GUI"
            game.headers["Date"] = time.strftime("%Y.%m.%d")
            game.headers["Round"] = "1"
            game.headers["White"] = "Human" if self.human_color == chess.WHITE else "ChessBot"
            game.headers["Black"] = "ChessBot" if self.human_color == chess.WHITE else "Human"
            game.headers["Result"] = "*"  # Ongoing game
            
            # Recreate the game from move history
            board = chess.Board()
            node = game
            
            for san in self.game_history:
                move = board.parse_san(san)
                node = node.add_variation(move)
                board.push(move)
            
            # Write to file
            with open(filename, "w") as f:
                f.write(str(game))
            
            messagebox.showinfo("Success", "Game saved successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save game: {e}")
    
    def load_game(self):
        """Load a game from a PGN file."""
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
                title="Load Game"
            )
            
            if not filename:
                return
            
            with open(filename) as f:
                game = chess.pgn.read_game(f)
            
            if not game:
                messagebox.showerror("Error", "Invalid PGN file or empty game.")
                return
            
            # Reset the board
            self.board = chess.Board()
            self.game_history = []
            
            # Replay the moves
            for move in game.mainline_moves():
                san = self.board.san(move)
                self.game_history.append(san)
                self.board.push(move)
            
            # Update the display
            self.selected_square = None
            self.highlighted_squares = []
            self.game_over = self.board.is_game_over()
            
            self.update_board_display()
            self.update_history_display()
            self.update_status()
            
            # Reset QS stats
            self.bot.reset_qs_stats()
            self.update_qs_stats_display()
            
            messagebox.showinfo("Success", "Game loaded successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load game: {e}")
    
    def show_analysis(self):
        """Show detailed analysis of the current position."""
        # Create a new window for analysis
        analysis_window = tk.Toplevel(self.root)
        analysis_window.title("Position Analysis")
        analysis_window.geometry("600x400")
        
        # Create a text widget for the analysis
        analysis_text = tk.Text(analysis_window, wrap=tk.WORD, font=("Courier", 10))
        analysis_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Show initial message
        analysis_text.insert(tk.END, "Analyzing position... Please wait.\n")
        
        # Set the AI thinking flag to prevent board interaction during analysis
        self.ai_thinking = True
        self.update_board_display()
        
        # Run analysis in a separate thread
        def run_analysis():
            try:
                # Create a local copy of the board to avoid race conditions
                board_copy = self.board.copy()
                
                # Disable QS temporarily to get baseline evaluation
                original_qs_state = self.bot.use_quiescence
                self.bot.toggle_quiescence(False)
                
                # Get evaluation without QS
                self.bot.reset_qs_stats()
                score_without_qs, move_without_qs = self.bot.minimax(
                    board_copy, self.bot.depth, float('-inf'), float('inf'), 
                    board_copy.turn == chess.WHITE
                )
                
                # Re-enable QS if it was enabled
                self.bot.toggle_quiescence(original_qs_state)
                
                # Get evaluation with QS (if enabled)
                self.bot.reset_qs_stats()
                score_with_qs, move_with_qs = self.bot.minimax(
                    board_copy, self.bot.depth, float('-inf'), float('inf'), 
                    board_copy.turn == chess.WHITE
                )
                
                # Get QS stats
                qs_stats = self.bot.get_qs_stats()
                
                # Format the analysis text
                analysis = f"Position Analysis\n"
                analysis += f"================\n\n"
                analysis += f"FEN: {board_copy.fen()}\n\n"
                
                analysis += f"Evaluation without QS: {score_without_qs}\n"
                if original_qs_state:
                    analysis += f"Evaluation with QS: {score_with_qs}\n"
                    analysis += f"Difference: {score_with_qs - score_without_qs}\n\n"
                
                analysis += f"Best move without QS: {board_copy.san(move_without_qs) if move_without_qs else 'None'}\n"
                if original_qs_state:
                    analysis += f"Best move with QS: {board_copy.san(move_with_qs) if move_with_qs else 'None'}\n\n"
                
                if original_qs_state:
                    analysis += f"QS Statistics:\n"
                    analysis += f"- Calls: {qs_stats['calls']}\n"
                    analysis += f"- Positions evaluated: {qs_stats['positions_evaluated']}\n"
                    analysis += f"- Max depth reached: {qs_stats['max_depth_reached']}\n"
                    analysis += f"- Captures found: {qs_stats['captures_found']}\n"
                    analysis += f"- Score improvements: {qs_stats['score_improvements']}\n"
                    
                    if qs_stats['score_improvements'] > 0:
                        analysis += f"- Avg improvement: {qs_stats['avg_score_improvement']:.2f} points\n"
                    
                    analysis += f"- Time spent: {qs_stats['time_spent']:.2f}s\n\n"
                
                # Calculate material counts
                white_material = self.count_material(chess.WHITE)
                black_material = self.count_material(chess.BLACK)
                
                # Prepare final analysis text
                analysis += f"Material count:\n"
                analysis += f"- White: {white_material}\n"
                analysis += f"- Black: {black_material}\n\n"
                
                legal_moves = list(board_copy.legal_moves)
                captures = [m for m in legal_moves if board_copy.is_capture(m)]
                
                analysis += f"Legal moves: {len(legal_moves)}\n"
                analysis += f"Captures available: {len(captures)}\n"
                
                # Update the text widget and reset AI thinking flag in the main thread
                def update_analysis_text():
                    # Clear the AI thinking flag
                    self.ai_thinking = False
                    self.update_board_display()
                    
                    # Update the analysis text
                    analysis_text.delete(1.0, tk.END)  # Clear the "please wait" message
                    analysis_text.insert(tk.END, analysis)
                
                # Schedule the UI update in the main thread
                analysis_window.after(0, update_analysis_text)
            except Exception as e:
                def show_error():
                    # Clear the AI thinking flag
                    self.ai_thinking = False
                    self.update_board_display()
                    
                    # Show the error
                    analysis_text.delete(1.0, tk.END)
                    analysis_text.insert(tk.END, f"Error during analysis: {e}")
                analysis_window.after(0, show_error)
        
        threading.Thread(target=run_analysis, daemon=True).start()
    
    def count_material(self, color):
        """Count material value for a given color."""
        total = 0
        for square in chess.SQUARES:
            piece = self.board.piece_at(square)
            if piece and piece.color == color:
                total += self.bot.piece_values[piece.piece_type]
        return total
    
    def run(self):
        """Run the GUI main loop."""
        self.root.mainloop()

def main():
    """Main function to run the chess GUI."""
    gui = ChessGUI()
    gui.run()

if __name__ == "__main__":
    main()