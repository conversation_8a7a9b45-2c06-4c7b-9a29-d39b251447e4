#!/usr/bin/env python3
"""
Unified Chess GUI - Enhanced graphical interface for the unified chess bot
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import chess
import chess.pgn
import threading
import time
from unified_chess_bot import UnifiedChessBot, EasyBot, MediumBot, HardBot, ExpertBot
from utils import save_game_pgn, analyze_position, format_analysis, get_opening_name
import io
from datetime import datetime

class UnifiedChessGUI:
    """
    Enhanced GUI Chess game with the unified chess bot.
    Features:
    - Visual chess board with drag-and-drop
    - Multiple difficulty levels with different bot configurations
    - Real-time position evaluation
    - Move analysis and statistics
    - Game saving/loading
    - Undo/redo functionality
    - Bot configuration options
    """
    
    def __init__(self):
        """Initialize the unified chess GUI."""
        self.root = tk.Tk()
        self.root.title("Unified Chess Bot - Advanced Chess AI")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # Game state
        self.board = chess.Board()
        self.bot = MediumBot()  # Default to medium difficulty
        self.game_history = []
        self.undone_moves = []
        self.undone_history = []
        self.human_color = chess.WHITE
        self.selected_square = None
        self.highlighted_squares = []
        self.legal_move_squares = []
        self.game_over = False
        self.bot_thinking = False
        self.show_evaluation = True
        self.current_evaluation = 0
        
        # Hint system
        self.hint_move = None
        self.show_hint = False
        self.hint_enabled = True
        
        # Colors and styling
        self.light_square_color = "#F0D9B5"
        self.dark_square_color = "#B58863"
        self.highlight_color = "#FFFF00"
        self.selected_color = "#FF6B6B"
        self.legal_move_color = "#90EE90"
        self.last_move_color = "#87CEEB"
        self.hint_from_color = "#FFB347"  # Orange for hint from square
        self.hint_to_color = "#FF8C69"    # Salmon for hint to square
        
        # Unicode chess pieces
        self.piece_symbols = {
            'P': '♙', 'N': '♘', 'B': '♗', 'R': '♖', 'Q': '♕', 'K': '♔',  # White
            'p': '♟', 'n': '♞', 'b': '♝', 'r': '♜', 'q': '♛', 'k': '♚'   # Black
        }
        
        self.setup_ui()
        self.update_board_display()
        self.update_status()
        self.update_evaluation_display()
        
        # Initialize button states
        self.root.after(100, self.update_button_states)
    
    def setup_ui(self):
        """Set up the enhanced user interface."""
        # Create main paned window
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel for the chess board
        board_frame = ttk.Frame(main_paned)
        main_paned.add(board_frame, weight=3)
        
        # Chess board canvas
        board_container = ttk.Frame(board_frame)
        board_container.pack(pady=10)
        
        self.canvas = tk.Canvas(board_container, width=560, height=560, bg="white")
        self.canvas.pack()
        self.canvas.bind("<Button-1>", self.on_square_click)
        
        # Evaluation bar below board
        eval_frame = ttk.Frame(board_frame)
        eval_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(eval_frame, text="Position Evaluation:").pack(anchor=tk.W)
        self.eval_canvas = tk.Canvas(eval_frame, width=560, height=30, bg="white")
        self.eval_canvas.pack()
        
        self.eval_label = ttk.Label(eval_frame, text="Evaluation: 0.00", font=("Arial", 10))
        self.eval_label.pack()
        
        # Right panel for controls and information
        right_paned = ttk.PanedWindow(main_paned, orient=tk.VERTICAL)
        main_paned.add(right_paned, weight=2)
        
        # Game controls
        controls_frame = ttk.LabelFrame(right_paned, text="Game Controls", padding=10)
        right_paned.add(controls_frame, weight=1)
        
        # Game control buttons
        self.new_game_button = ttk.Button(controls_frame, text="New Game", command=self.new_game)
        self.new_game_button.pack(fill=tk.X, pady=2)
        
        self.switch_colors_button = ttk.Button(controls_frame, text="Switch Colors", command=self.switch_colors)
        self.switch_colors_button.pack(fill=tk.X, pady=2)
        
        self.undo_button = ttk.Button(controls_frame, text="Undo Move", command=self.undo_move)
        self.undo_button.pack(fill=tk.X, pady=2)
        
        self.redo_button = ttk.Button(controls_frame, text="Redo Move", command=self.redo_move)
        self.redo_button.pack(fill=tk.X, pady=2)
        
        self.hint_button = ttk.Button(controls_frame, text="Show Hint", command=self.toggle_hint)
        self.hint_button.pack(fill=tk.X, pady=2)
        
        # File operations
        file_frame = ttk.Frame(controls_frame)
        file_frame.pack(fill=tk.X, pady=5)
        
        self.save_game_button = ttk.Button(file_frame, text="Save Game", command=self.save_game)
        self.save_game_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=1)
        
        self.load_game_button = ttk.Button(file_frame, text="Load Game", command=self.load_game)
        self.load_game_button.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=1)
        
        # Bot difficulty and configuration
        bot_frame = ttk.LabelFrame(right_paned, text="Bot Configuration", padding=10)
        right_paned.add(bot_frame, weight=1)
        
        # Difficulty selection
        difficulty_frame = ttk.Frame(bot_frame)
        difficulty_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(difficulty_frame, text="Difficulty:").pack(anchor=tk.W)
        
        self.difficulty_var = tk.StringVar(value="Medium")
        difficulties = [
            ("Easy", "EasyBot", "Depth 2, Basic features"),
            ("Medium", "MediumBot", "Depth 3, All features"),
            ("Hard", "HardBot", "Depth 4, All features"),
            ("Expert", "ExpertBot", "Depth 5, Enhanced features")
        ]
        
        for name, bot_class, description in difficulties:
            frame = ttk.Frame(difficulty_frame)
            frame.pack(fill=tk.X, anchor=tk.W)
            
            ttk.Radiobutton(frame, text=name, variable=self.difficulty_var, 
                          value=name, command=lambda n=name: self.set_difficulty(n)).pack(side=tk.LEFT)
            ttk.Label(frame, text=f"({description})", font=("Arial", 8)).pack(side=tk.LEFT, padx=5)
        
        # Bot features configuration
        features_frame = ttk.LabelFrame(bot_frame, text="Advanced Features", padding=5)
        features_frame.pack(fill=tk.X, pady=5)
        
        self.iterative_var = tk.BooleanVar(value=True)
        self.quiescence_var = tk.BooleanVar(value=True)
        self.transposition_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(features_frame, text="Iterative Deepening", 
                       variable=self.iterative_var, command=self.update_bot_features).pack(anchor=tk.W)
        ttk.Checkbutton(features_frame, text="Quiescence Search", 
                       variable=self.quiescence_var, command=self.update_bot_features).pack(anchor=tk.W)
        ttk.Checkbutton(features_frame, text="Transposition Table", 
                       variable=self.transposition_var, command=self.update_bot_features).pack(anchor=tk.W)
        
        # Custom depth
        depth_frame = ttk.Frame(features_frame)
        depth_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(depth_frame, text="Custom Depth:").pack(side=tk.LEFT)
        self.depth_var = tk.StringVar(value="3")
        depth_spinbox = ttk.Spinbox(depth_frame, from_=1, to=8, width=5, 
                                   textvariable=self.depth_var, command=self.update_custom_depth)
        depth_spinbox.pack(side=tk.RIGHT)
        
        # Game status
        status_frame = ttk.LabelFrame(right_paned, text="Game Status", padding=10)
        right_paned.add(status_frame, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="White to move", font=("Arial", 12, "bold"))
        self.status_label.pack()
        
        self.opening_label = ttk.Label(status_frame, text="Opening: Starting Position", font=("Arial", 10))
        self.opening_label.pack()
        
        self.material_label = ttk.Label(status_frame, text="Material: Equal", font=("Arial", 10))
        self.material_label.pack()
        
        # Bot statistics
        stats_frame = ttk.LabelFrame(status_frame, text="Bot Statistics", padding=5)
        stats_frame.pack(fill=tk.X, pady=5)
        
        self.stats_text = tk.Text(stats_frame, height=4, width=30, font=("Courier", 8))
        self.stats_text.pack(fill=tk.X)
        
        # Move history
        history_frame = ttk.LabelFrame(right_paned, text="Move History", padding=10)
        right_paned.add(history_frame, weight=2)
        
        # Scrollable text widget for move history
        history_container = ttk.Frame(history_frame)
        history_container.pack(fill=tk.BOTH, expand=True)
        
        self.history_text = tk.Text(history_container, height=8, width=25, wrap=tk.WORD, font=("Courier", 9))
        history_scrollbar = ttk.Scrollbar(history_container, orient=tk.VERTICAL, command=self.history_text.yview)
        self.history_text.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Analysis button
        self.analyze_button = ttk.Button(history_frame, text="Analyze Position", command=self.show_analysis)
        self.analyze_button.pack(fill=tk.X, pady=2)
        
        # Move quality history button
        self.move_quality_button = ttk.Button(history_frame, text="Move Quality History", command=self.show_move_quality_history)
        self.move_quality_button.pack(fill=tk.X, pady=2)
        
        # Options
        options_frame = ttk.Frame(history_frame)
        options_frame.pack(fill=tk.X, pady=2)
        
        self.show_eval_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Show Evaluation", 
                       variable=self.show_eval_var, command=self.toggle_evaluation).pack(anchor=tk.W)
        
        self.hint_enabled_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Enable Hints", 
                       variable=self.hint_enabled_var, command=self.toggle_hint_enabled).pack(anchor=tk.W)
        
        # Move evaluation mode selection
        eval_mode_frame = ttk.Frame(options_frame)
        eval_mode_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(eval_mode_frame, text="Move Evaluation:").pack(anchor=tk.W)
        self.eval_mode_var = tk.StringVar(value="Shallow Search")
        eval_modes = ["Static (Fastest)", "Shallow Search (Balanced)", "Full Search (Most Accurate)"]
        eval_dropdown = ttk.Combobox(eval_mode_frame, textvariable=self.eval_mode_var, 
                                   values=eval_modes, state="readonly", width=20)
        eval_dropdown.pack(anchor=tk.W, pady=2)
        eval_dropdown.bind("<<ComboboxSelected>>", self.on_evaluation_mode_change)
    
    def draw_board(self):
        """Draw the chess board with enhanced visual features."""
        self.canvas.delete("all")
        square_size = 70
        
        # Get last move for highlighting
        last_move = None
        if self.board.move_stack:
            last_move = self.board.move_stack[-1]
        
        for row in range(8):
            for col in range(8):
                x1 = col * square_size
                y1 = row * square_size
                x2 = x1 + square_size
                y2 = y1 + square_size
                
                # Determine square
                square = chess.square(col, 7 - row)
                is_light = (row + col) % 2 == 0
                
                # Choose color based on square state
                if square == self.selected_square:
                    color = self.selected_color
                elif square in self.legal_move_squares:
                    color = self.legal_move_color
                elif self.show_hint and self.hint_move and square == self.hint_move.from_square:
                    color = self.hint_from_color
                elif self.show_hint and self.hint_move and square == self.hint_move.to_square:
                    color = self.hint_to_color
                elif last_move and (square == last_move.from_square or square == last_move.to_square):
                    color = self.last_move_color
                else:
                    color = self.light_square_color if is_light else self.dark_square_color
                
                # Draw square
                self.canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline="black", width=1)
                
                # Add coordinates
                if col == 0:  # Rank labels
                    self.canvas.create_text(x1 + 8, y1 + 12, text=str(8 - row), 
                                          font=("Arial", 10, "bold"), fill="black")
                if row == 7:  # File labels
                    self.canvas.create_text(x2 - 12, y2 - 8, text=chr(ord('a') + col), 
                                          font=("Arial", 10, "bold"), fill="black")
    
    def draw_pieces(self):
        """Draw the chess pieces on the board."""
        square_size = 70
        
        for row in range(8):
            for col in range(8):
                square = chess.square(col, 7 - row)
                piece = self.board.piece_at(square)
                
                if piece:
                    x = col * square_size + square_size // 2
                    y = row * square_size + square_size // 2
                    
                    piece_symbol = self.piece_symbols.get(piece.symbol(), piece.symbol())
                    self.canvas.create_text(x, y, text=piece_symbol, 
                                          font=("Arial", 40), fill="black")
    
    def update_board_display(self):
        """Update the visual board display."""
        self.draw_board()
        self.draw_pieces()
        self.update_material_count()
    
    def update_evaluation_display(self):
        """Update the evaluation bar and label."""
        if not self.show_eval_var.get():
            return
            
        self.eval_canvas.delete("all")
        
        # Calculate evaluation as percentage
        eval_score = self.current_evaluation
        max_eval = 500  # Cap for display purposes
        
        # Normalize to -1 to 1
        normalized = max(-1, min(1, eval_score / max_eval))
        
        # Draw evaluation bar
        bar_width = 560
        bar_height = 30
        
        # Background
        self.eval_canvas.create_rectangle(0, 0, bar_width, bar_height, fill="gray90", outline="black")
        
        # Center line
        center_x = bar_width // 2
        self.eval_canvas.create_line(center_x, 0, center_x, bar_height, fill="black", width=2)
        
        # Evaluation bar
        if normalized > 0:  # White advantage
            width = int(normalized * center_x)
            self.eval_canvas.create_rectangle(center_x, 0, center_x + width, bar_height, 
                                            fill="lightblue", outline="")
        elif normalized < 0:  # Black advantage
            width = int(-normalized * center_x)
            self.eval_canvas.create_rectangle(center_x - width, 0, center_x, bar_height, 
                                            fill="lightcoral", outline="")
        
        # Update label
        eval_text = f"Evaluation: {eval_score / 100:.2f}"
        if abs(eval_score) > 2000:
            if eval_score > 0:
                eval_text = "Evaluation: White winning"
            else:
                eval_text = "Evaluation: Black winning"
        
        self.eval_label.config(text=eval_text)
    
    def update_material_count(self):
        """Update material count display."""
        white_material = 0
        black_material = 0
        
        piece_values = {
            chess.PAWN: 1, chess.KNIGHT: 3, chess.BISHOP: 3,
            chess.ROOK: 5, chess.QUEEN: 9, chess.KING: 0
        }
        
        for square in chess.SQUARES:
            piece = self.board.piece_at(square)
            if piece:
                value = piece_values[piece.piece_type]
                if piece.color == chess.WHITE:
                    white_material += value
                else:
                    black_material += value
        
        diff = white_material - black_material
        if diff > 0:
            material_text = f"Material: White +{diff}"
        elif diff < 0:
            material_text = f"Material: Black +{-diff}"
        else:
            material_text = "Material: Equal"
        
        self.material_label.config(text=material_text)
    
    def new_game(self):
        """Start a new game."""
        result = messagebox.askyesno("New Game", "Are you sure you want to start a new game?")
        if result:
            # Reset game state
            self.board = chess.Board()
            self.game_history = []
            self.undone_moves = []
            self.undone_history = []
            self.selected_square = None
            self.legal_move_squares = []
            self.hint_move = None
            self.show_hint = False
            self.game_over = False
            self.bot_thinking = False
            
            # Clear bot's move quality history
            self.bot.clear_move_quality_history()
            
            # Update displays
            self.update_board_display()
            self.update_history_display()
            self.update_status()
            self.update_button_states()
            
            # Update evaluation
            if self.show_eval_var.get():
                self.current_evaluation = self.bot.get_evaluation(self.board)
                self.update_evaluation_display()
            
            # If human is black, make bot move first
            if self.human_color == chess.BLACK:
                self.root.after(500, self.make_bot_move)
    
    def handle_game_over(self):
        """Handle game over conditions."""
        self.game_over = True
        self.update_status()
        self.update_button_states()
        
        # Show game over message
        if self.board.is_checkmate():
            winner = "Black" if self.board.turn == chess.WHITE else "White"
            messagebox.showinfo("Game Over", f"Checkmate! {winner} wins!")
        elif self.board.is_stalemate():
            messagebox.showinfo("Game Over", "Stalemate! The game is a draw.")
        elif self.board.is_insufficient_material():
            messagebox.showinfo("Game Over", "Draw by insufficient material!")
        elif self.board.is_seventyfive_moves():
            messagebox.showinfo("Game Over", "Draw by 75-move rule!")
        elif self.board.is_fivefold_repetition():
            messagebox.showinfo("Game Over", "Draw by fivefold repetition!")
    
    def update_button_states(self):
        """Update the state of control buttons based on game state."""
        # Disable buttons when bot is thinking
        if self.bot_thinking:
            self.new_game_button.config(state="disabled")
            self.switch_colors_button.config(state="disabled")
            self.undo_button.config(state="disabled")
            self.redo_button.config(state="disabled")
            self.hint_button.config(state="disabled")
            self.save_game_button.config(state="disabled")
            self.load_game_button.config(state="disabled")
            self.analyze_button.config(state="disabled")
            self.move_quality_button.config(state="disabled")
        else:
            self.new_game_button.config(state="normal")
            self.switch_colors_button.config(state="normal")
            self.save_game_button.config(state="normal")
            self.load_game_button.config(state="normal")
            self.analyze_button.config(state="normal")
            self.move_quality_button.config(state="normal")
            
            # Undo button - enabled if there are moves to undo
            if self.game_history:
                self.undo_button.config(state="normal")
            else:
                self.undo_button.config(state="disabled")
            
            # Redo button - enabled if there are moves to redo
            if self.undone_moves:
                self.redo_button.config(state="normal")
            else:
                self.redo_button.config(state="disabled")
            
            # Hint button - enabled if hints are enabled and it's human's turn
            if self.hint_enabled and not self.game_over and self.board.turn == self.human_color:
                self.hint_button.config(state="normal")
            else:
                self.hint_button.config(state="disabled")
    
    def toggle_hint(self):
        """Toggle hint display on/off."""
        if self.show_hint:
            # Hide current hint
            self.hint_move = None
            self.show_hint = False
            self.hint_button.config(text="Show Hint")
            self.update_board_display()
        else:
            # Show hint
            self.get_hint()
    
    def on_square_click(self, event):
        """Handle mouse clicks on the board."""
        if self.game_over or self.board.turn != self.human_color or self.bot_thinking:
            return
        
        square_size = 70
        col = event.x // square_size
        row = event.y // square_size
        
        if 0 <= col < 8 and 0 <= row < 8:
            clicked_square = chess.square(col, 7 - row)
            
            if self.selected_square is None:
                # Select a piece
                piece = self.board.piece_at(clicked_square)
                if piece and piece.color == self.human_color:
                    self.selected_square = clicked_square
                    self.highlight_legal_moves(clicked_square)
                    self.update_board_display()
            else:
                # Try to make a move
                move = chess.Move(self.selected_square, clicked_square)
                
                # Check for promotion
                piece = self.board.piece_at(self.selected_square)
                if (piece and piece.piece_type == chess.PAWN and 
                    ((piece.color == chess.WHITE and chess.square_rank(clicked_square) == 7) or
                     (piece.color == chess.BLACK and chess.square_rank(clicked_square) == 0))):
                    # Show promotion dialog
                    promotion_piece = self.show_promotion_dialog()
                    move = chess.Move(self.selected_square, clicked_square, promotion=promotion_piece)
                
                if move in self.board.legal_moves:
                    self.make_move(move)
                
                # Clear selection
                self.selected_square = None
                self.legal_move_squares = []
                self.update_board_display()
    
    def show_promotion_dialog(self):
        """Show dialog for pawn promotion."""
        dialog = tk.Toplevel(self.root)
        dialog.title("Pawn Promotion")
        dialog.geometry("300x150")
        dialog.grab_set()
        
        selected_piece = tk.StringVar(value="q")
        
        ttk.Label(dialog, text="Choose promotion piece:", font=("Arial", 12)).pack(pady=10)
        
        pieces_frame = ttk.Frame(dialog)
        pieces_frame.pack(pady=10)
        
        pieces = [("Queen", "q"), ("Rook", "r"), ("Bishop", "b"), ("Knight", "n")]
        for name, value in pieces:
            ttk.Radiobutton(pieces_frame, text=name, variable=selected_piece, value=value).pack(side=tk.LEFT, padx=10)
        
        ttk.Button(dialog, text="OK", command=dialog.destroy).pack(pady=10)
        
        self.root.wait_window(dialog)
        
        piece_map = {"q": chess.QUEEN, "r": chess.ROOK, "b": chess.BISHOP, "n": chess.KNIGHT}
        return piece_map[selected_piece.get()]
    
    def highlight_legal_moves(self, square):
        """Highlight legal moves for the selected piece."""
        self.legal_move_squares = []
        for move in self.board.legal_moves:
            if move.from_square == square:
                self.legal_move_squares.append(move.to_square)
    
    def make_move(self, move):
        """Make a move on the board."""
        # Use the new function with no search evaluation (will use static evaluation)
        self.make_move_with_evaluation(move, search_evaluation=None)
    
    def make_move_with_evaluation(self, move, search_evaluation=None):
        """Make a move on the board with optional search evaluation."""
        try:
            # Get the position before making the move for quality tracking
            position_before_move = self.board.copy()
            static_score_before = self.bot.get_evaluation(position_before_move)
            
            san_move = self.board.san(move)
            self.board.push(move)
            self.game_history.append(san_move)
            
            # Record move quality for human moves (when search_evaluation is None)
            if search_evaluation is None:
                # This is a human move, record its quality
                import time
                self.bot._record_move_quality(
                    position_before_move, 
                    move, 
                    search_score=0,  # Human moves don't have search scores
                    static_score=static_score_before, 
                    depth_used=0, 
                    time_taken=0.0, 
                    move_type="Human"
                )
            
            # Clear redo stack
            self.undone_moves = []
            self.undone_history = []
            
            # Clear hint after move is made
            self.hint_move = None
            self.show_hint = False
            
            # Update displays
            self.update_history_display()
            self.update_status()
            self.update_board_display()
            self.update_button_states()
            
            # Update evaluation - use search evaluation if provided, otherwise static
            if self.show_eval_var.get():
                if search_evaluation is not None:
                    # Use the search evaluation from the bot's thinking
                    self.current_evaluation = search_evaluation
                else:
                    # Fall back to static evaluation
                    self.current_evaluation = self.bot.get_evaluation(self.board)
                self.update_evaluation_display()
            
            # Check for game over
            if self.board.is_game_over():
                self.handle_game_over()
            elif self.board.turn != self.human_color:
                # Bot's turn
                self.root.after(500, self.make_bot_move)
                
        except Exception as e:
            messagebox.showerror("Error", f"Invalid move: {e}")
    
    def make_bot_move(self):
        """Make a move for the bot in a separate thread."""
        def bot_move_thread():
            self.bot_thinking = True
            self.root.after(0, self.disable_all_controls)
            
            try:
                self.root.after(0, lambda: self.update_status("Bot is thinking..."))
                start_time = time.time()
                
                # Get both the move and the search evaluation
                move, search_evaluation = self.bot.get_best_move_with_score(self.board)
                
                end_time = time.time()
                
                if move:
                    # Make the move
                    self.root.after(0, lambda m=move: self.make_move_with_evaluation(m, search_evaluation))
                    
                    # Update bot statistics
                    stats_text = f"Time: {end_time - start_time:.2f}s\\n"
                    stats_text += f"Nodes: {self.bot.nodes_evaluated}\\n"
                    stats_text += f"QS Nodes: {self.bot.quiescence_nodes}\\n"
                    stats_text += f"Eval: {search_evaluation}\\n"
                    if self.bot.use_transposition_table:
                        stats_text += f"TT Hits: {self.bot.transposition_hits}"
                    
                    self.root.after(0, lambda: self.update_bot_stats(stats_text))
                else:
                    self.root.after(0, lambda: messagebox.showinfo("Info", "Bot couldn't find a move!"))
                    
            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda: messagebox.showerror("Error", f"Bot error: {error_msg}"))
            finally:
                self.bot_thinking = False
                self.root.after(0, self.enable_all_controls)
        
        threading.Thread(target=bot_move_thread, daemon=True).start()
    
    def update_bot_stats(self, stats_text):
        """Update bot statistics display."""
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)
    
    def disable_all_controls(self):
        """Disable all game controls while bot is thinking."""
        self.new_game_button.config(state="disabled")
        self.switch_colors_button.config(state="disabled")
        self.undo_button.config(state="disabled")
        self.redo_button.config(state="disabled")
        self.hint_button.config(state="disabled")
        self.save_game_button.config(state="disabled")
        self.load_game_button.config(state="disabled")
        self.analyze_button.config(state="disabled")
    
    def enable_all_controls(self):
        """Re-enable all game controls."""
        self.new_game_button.config(state="normal")
        self.switch_colors_button.config(state="normal")
        self.save_game_button.config(state="normal")
        self.load_game_button.config(state="normal")
        self.analyze_button.config(state="normal")
        self.update_button_states()
    
    def update_button_states(self):
        """Update button states based on game state."""
        # Undo button
        can_undo = len(self.board.move_stack) > 0 and not self.bot_thinking
        if self.human_color == chess.BLACK and len(self.board.move_stack) == 1:
            can_undo = False
        self.undo_button.config(state="normal" if can_undo else "disabled")
        
        # Redo button
        can_redo = len(self.undone_moves) > 0 and not self.bot_thinking
        self.redo_button.config(state="normal" if can_redo else "disabled")
    
    def undo_move(self):
        """Undo the last move(s)."""
        if not self.board.move_stack or self.bot_thinking:
            return
        
        # Determine moves to undo
        moves_to_undo = 1
        if self.board.turn == self.human_color and len(self.board.move_stack) >= 2:
            moves_to_undo = 2
        
        # Store moves for redo
        for i in range(moves_to_undo):
            if self.board.move_stack:
                self.undone_moves.append(self.board.move_stack[-1])
                self.board.pop()
            if self.game_history:
                self.undone_history.append(self.game_history.pop())
        
        # Reset game over state
        self.game_over = False
        
        # Update displays
        self.update_history_display()
        self.update_status()
        self.update_board_display()
        self.update_button_states()
        
        # Update evaluation
        if self.show_eval_var.get():
            self.current_evaluation = self.bot.get_evaluation(self.board)
            self.update_evaluation_display()
        
        # Clear selections and hints
        self.selected_square = None
        self.legal_move_squares = []
        self.hint_move = None
        self.show_hint = False
        
        # If it's bot's turn after undo, make bot move
        if not self.game_over and self.board.turn != self.human_color:
            self.root.after(500, self.make_bot_move)
    
    def redo_move(self):
        """Redo a previously undone move."""
        if not self.undone_moves or self.bot_thinking:
            return
        
        # Determine moves to redo
        moves_to_redo = 1
        if self.board.turn != self.human_color and len(self.undone_moves) >= 2:
            moves_to_redo = 2
        
        # Redo moves
        for i in range(moves_to_redo):
            if self.undone_moves:
                move = self.undone_moves.pop()
                san = self.board.san(move)
                self.board.push(move)
                self.game_history.append(san)
            if self.undone_history:
                self.undone_history.pop()
        
        # Clear hints
        self.hint_move = None
        self.show_hint = False
        
        # Update displays
        self.update_history_display()
        self.update_status()
        self.update_board_display()
        self.update_button_states()
        
        # Update evaluation
        if self.show_eval_var.get():
            self.current_evaluation = self.bot.get_evaluation(self.board)
            self.update_evaluation_display()
        
        # Check for game over
        if self.board.is_game_over():
            self.handle_game_over()
        elif not self.game_over and self.board.turn != self.human_color:
            self.root.after(500, self.make_bot_move)
    
    def update_status(self, custom_message=None):
        """Update the game status display."""
        if custom_message:
            self.status_label.config(text=custom_message)
            return
        
        if self.board.is_checkmate():
            winner = "Black" if self.board.turn == chess.WHITE else "White"
            self.status_label.config(text=f"Checkmate! {winner} wins!")
            self.game_over = True
        elif self.board.is_stalemate():
            self.status_label.config(text="Stalemate! Draw!")
            self.game_over = True
        elif self.board.is_insufficient_material():
            self.status_label.config(text="Insufficient material! Draw!")
            self.game_over = True
        elif self.board.is_check():
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} in check!")
        else:
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} to move")
        
        # Update opening name
        if len(self.game_history) <= 10:
            try:
                opening = get_opening_name(self.board, self.game_history)
                self.opening_label.config(text=f"Opening: {opening}")
            except:
                self.opening_label.config(text="Opening: Unknown")
    
    def update_history_display(self):
        """Update the move history display."""
        self.history_text.delete(1.0, tk.END)
        
        # Format moves in pairs
        for i in range(0, len(self.game_history), 2):
            move_num = (i // 2) + 1
            white_move = self.game_history[i] if i < len(self.game_history) else ""
            black_move = self.game_history[i + 1] if i + 1 < len(self.game_history) else ""
            
            line = f"{move_num:2d}. {white_move:8s}"
            if black_move:
                line += f" {black_move}"
            line += "\\n"
            
            self.history_text.insert(tk.END, line)
        
        self.history_text.see(tk.END)
    
    def update_button_states(self):
        """Update the state of various buttons based on game state."""
        # Undo button
        can_undo = len(self.game_history) > 0 and not self.bot_thinking and not self.game_over
        self.undo_button.config(state="normal" if can_undo else "disabled")
        
        # Redo button
        can_redo = len(self.undone_moves) > 0 and not self.bot_thinking and not self.game_over
        self.redo_button.config(state="normal" if can_redo else "disabled")
        
        # Hint button
        can_hint = (self.hint_enabled and not self.game_over and not self.bot_thinking 
                   and self.board.turn == self.human_color)
        self.hint_button.config(state="normal" if can_hint else "disabled")
        
        # Other game control buttons
        can_control = not self.bot_thinking
        self.new_game_button.config(state="normal" if can_control else "disabled")
        self.switch_colors_button.config(state="normal" if can_control else "disabled")
        self.save_game_button.config(state="normal" if can_control else "disabled")
        self.load_game_button.config(state="normal" if can_control else "disabled")
        self.analyze_button.config(state="normal" if can_control else "disabled")
    
    def handle_game_over(self):
        """Handle game over situation."""
        self.game_over = True
        
        if self.board.is_checkmate():
            winner = "You" if self.board.turn != self.human_color else self.bot.name
            message = f"Checkmate! {winner} wins!"
        elif self.board.is_stalemate():
            message = "Stalemate! The game is a draw."
        elif self.board.is_insufficient_material():
            message = "Insufficient material! The game is a draw."
        else:
            message = "Game over!"
        
        result = messagebox.askyesno("Game Over", f"{message}\n\nWould you like to save this game?")
        if result:
            self.save_game()
    
    def new_game(self):
        """Start a new game."""
        self.board = chess.Board()
        self.game_history = []
        self.undone_moves = []
        self.undone_history = []
        self.selected_square = None
        self.legal_move_squares = []
        self.game_over = False
        self.current_evaluation = 0
        
        # Reset hint state
        self.hint_move = None
        self.show_hint = False
        
        self.update_board_display()
        self.update_status()
        self.update_history_display()
        self.update_evaluation_display()
        self.update_button_states()
        
        # Clear bot stats
        self.stats_text.delete(1.0, tk.END)
        
        # Clear transposition table for fresh start
        self.bot.clear_transposition_table()
    
    def toggle_hint(self):
        """Toggle hint display on/off."""
        if not self.hint_enabled or self.game_over or self.bot_thinking:
            return
            
        if self.board.turn != self.human_color:
            messagebox.showinfo("Hint", "Hints are only available on your turn!")
            return
            
        if self.show_hint:
            # Hide hint
            self.show_hint = False
            self.hint_move = None
            self.hint_button.config(text="Show Hint")
            self.update_board_display()
        else:
            # Show hint
            self.get_hint()
    
    def get_hint(self):
        """Get and display a hint move from the bot."""
        def hint_thread():
            try:
                self.hint_button.config(text="Getting Hint...", state="disabled")
                
                # Use the bot to get the best move as a hint (don't record quality)
                hint_move = self.bot.get_best_move(self.board, record_quality=False)
                
                if hint_move:
                    self.hint_move = hint_move
                    self.show_hint = True
                    
                    # Update UI on main thread
                    self.root.after(0, self.display_hint)
                else:
                    self.root.after(0, lambda: messagebox.showinfo("Hint", "No good moves found!"))
                    
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to get hint: {e}"))
            finally:
                self.root.after(0, self.reset_hint_button)
        
        # Run hint calculation in separate thread to avoid UI freezing
        threading.Thread(target=hint_thread, daemon=True).start()
    
    def display_hint(self):
        """Display the hint move on the board and show move information."""
        if self.hint_move:
            # Update board display to show hint highlighting
            self.update_board_display()
            
            # Show hint information
            from_square = chess.square_name(self.hint_move.from_square)
            to_square = chess.square_name(self.hint_move.to_square)
            
            # Get piece information
            piece = self.board.piece_at(self.hint_move.from_square)
            piece_name = piece.piece_type if piece else "piece"
            piece_names = {
                chess.PAWN: "pawn", chess.KNIGHT: "knight", chess.BISHOP: "bishop",
                chess.ROOK: "rook", chess.QUEEN: "queen", chess.KING: "king"
            }
            piece_name = piece_names.get(piece_name, "piece")
            
            # Check for special moves
            move_description = f"Move {piece_name} from {from_square} to {to_square}"
            
            if self.hint_move.promotion:
                promotion_names = {
                    chess.QUEEN: "queen", chess.ROOK: "rook", 
                    chess.BISHOP: "bishop", chess.KNIGHT: "knight"
                }
                promo_name = promotion_names.get(self.hint_move.promotion, "piece")
                move_description += f" (promote to {promo_name})"
            elif self.board.is_castling(self.hint_move):
                if self.hint_move.to_square > self.hint_move.from_square:
                    move_description = "Castle kingside"
                else:
                    move_description = "Castle queenside"
            elif self.board.is_en_passant(self.hint_move):
                move_description += " (en passant)"
            elif self.board.is_capture(self.hint_move):
                move_description += " (capture)"
            
            # Update button text to show hint info
            self.hint_button.config(text="Hide Hint")
            
            # Show hint in status or as tooltip
            messagebox.showinfo("Hint", f"Suggested move: {move_description}")
    
    def reset_hint_button(self):
        """Reset hint button to normal state."""
        self.hint_button.config(text="Show Hint", state="normal")
    
    def switch_colors(self):
        """Switch human color."""
        self.human_color = not self.human_color
        color_name = "White" if self.human_color == chess.WHITE else "Black"
        messagebox.showinfo("Color Switched", f"You are now playing as {color_name}")
        
        # If it's now bot's turn, make bot move
        if self.board.turn != self.human_color and not self.game_over:
            self.root.after(500, self.make_bot_move)
    
    def set_difficulty(self, difficulty_name):
        """Set bot difficulty."""
        difficulty_map = {
            "Easy": EasyBot,
            "Medium": MediumBot,
            "Hard": HardBot,
            "Expert": ExpertBot
        }
        
        if difficulty_name in difficulty_map:
            old_name = self.bot.name
            self.bot = difficulty_map[difficulty_name]()
            print(f"Switched from {old_name} to {self.bot.name}")
            
            # Update UI to reflect bot features
            self.iterative_var.set(self.bot.use_iterative_deepening)
            self.quiescence_var.set(self.bot.use_quiescence_search)
            self.transposition_var.set(self.bot.use_transposition_table)
            self.depth_var.set(str(self.bot.depth))
    
    def update_bot_features(self):
        """Update bot features based on checkboxes."""
        self.bot.set_features(
            iterative_deepening=self.iterative_var.get(),
            quiescence_search=self.quiescence_var.get(),
            transposition_table=self.transposition_var.get()
        )
    
    def update_custom_depth(self):
        """Update custom search depth."""
        try:
            depth = int(self.depth_var.get())
            self.bot.set_depth(depth)
        except ValueError:
            pass
    
    def toggle_evaluation(self):
        """Toggle evaluation display."""
        self.show_evaluation = self.show_eval_var.get()
        if self.show_evaluation:
            self.current_evaluation = self.bot.get_evaluation(self.board)
            self.update_evaluation_display()
        else:
            self.eval_canvas.delete("all")
            self.eval_label.config(text="Evaluation: Hidden")
    
    def toggle_hint_enabled(self):
        """Toggle hint functionality on/off."""
        self.hint_enabled = self.hint_enabled_var.get()
        if not self.hint_enabled:
            # Clear any currently shown hint
            self.hint_move = None
            self.show_hint = False
            self.update_board_display()
        self.update_button_states()
    
    def save_game(self):
        """Save the current game to PGN file."""
        if not self.game_history:
            messagebox.showinfo("Info", "No moves to save!")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".pgn",
            filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
            title="Save Game"
        )
        
        if filename:
            try:
                # Create PGN game
                game = chess.pgn.Game()
                
                # Set headers
                game.headers["Event"] = "Unified Chess Bot Game"
                game.headers["Date"] = datetime.now().strftime("%Y.%m.%d")
                game.headers["White"] = "Human" if self.human_color == chess.WHITE else self.bot.name
                game.headers["Black"] = self.bot.name if self.human_color == chess.WHITE else "Human"
                
                # Result
                if self.board.is_checkmate():
                    result = "1-0" if not self.board.turn == chess.WHITE else "0-1"
                elif self.board.is_stalemate() or self.board.is_insufficient_material():
                    result = "1/2-1/2"
                else:
                    result = "*"
                game.headers["Result"] = result
                
                # Add moves
                board = chess.Board()
                node = game
                
                for san_move in self.game_history:
                    move = board.parse_san(san_move)
                    node = node.add_variation(move)
                    board.push(move)
                
                # Save to file
                with open(filename, "w") as f:
                    f.write(str(game))
                
                messagebox.showinfo("Success", f"Game saved to {filename}")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save game: {e}")
    
    def load_game(self):
        """Load a game from PGN file."""
        filename = filedialog.askopenfilename(
            filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
            title="Load Game"
        )
        
        if filename:
            try:
                with open(filename, 'r') as f:
                    pgn_content = f.read()
                
                # Parse PGN
                pgn_io = io.StringIO(pgn_content)
                game = chess.pgn.read_game(pgn_io)
                
                if game is None:
                    messagebox.showerror("Error", "Invalid PGN file")
                    return
                
                # Reset game
                self.new_game()
                
                # Apply moves
                board = chess.Board()
                for move in game.mainline_moves():
                    if move in board.legal_moves:
                        san_move = board.san(move)
                        board.push(move)
                        self.game_history.append(san_move)
                
                self.board = board
                self.update_board_display()
                self.update_history_display()
                self.update_status()
                
                # Update evaluation
                if self.show_eval_var.get():
                    self.current_evaluation = self.bot.get_evaluation(self.board)
                    self.update_evaluation_display()
                
                messagebox.showinfo("Success", f"Game loaded from {filename}")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load game: {e}")
    
    def show_analysis(self):
        """Show position analysis in a popup window."""
        try:
            analysis = analyze_position(self.board)
            formatted_analysis = format_analysis(analysis)
            
            # Create analysis window
            analysis_window = tk.Toplevel(self.root)
            analysis_window.title("Position Analysis")
            analysis_window.geometry("500x400")
            
            # Analysis text
            text_frame = ttk.Frame(analysis_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            analysis_text = tk.Text(text_frame, wrap=tk.WORD, font=("Courier", 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=analysis_text.yview)
            analysis_text.configure(yscrollcommand=scrollbar.set)
            
            analysis_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            analysis_text.insert(1.0, formatted_analysis)
            analysis_text.config(state=tk.DISABLED)
            
            # Close button
            ttk.Button(analysis_window, text="Close", command=analysis_window.destroy).pack(pady=5)
            
        except Exception as e:
            messagebox.showerror("Error", f"Analysis failed: {e}")
    
    def show_move_quality_history(self):
        """Show move quality history in a popup window."""
        try:
            history = self.bot.get_move_quality_history()
            
            if not history:
                messagebox.showinfo("Move Quality History", "No move quality data available yet.")
                return
            
            # Create move quality window
            quality_window = tk.Toplevel(self.root)
            quality_window.title("Move Quality History")
            quality_window.geometry("900x600")
            
            # Create main frame with scrollbar
            main_frame = ttk.Frame(quality_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # Create treeview for tabular display
            columns = ('Move', 'Quality', 'Search Score', 'Static Score', 'Difference', 'Depth', 'Time', 'Nodes', 'Type')
            tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=20)
            
            # Configure column headings and widths
            tree.heading('Move', text='Move')
            tree.heading('Quality', text='Quality')
            tree.heading('Search Score', text='Search Score')
            tree.heading('Static Score', text='Static Score')
            tree.heading('Difference', text='Difference')
            tree.heading('Depth', text='Depth')
            tree.heading('Time', text='Time (s)')
            tree.heading('Nodes', text='Nodes')
            tree.heading('Type', text='Type')
            
            tree.column('Move', width=80)
            tree.column('Quality', width=100)
            tree.column('Search Score', width=100)
            tree.column('Static Score', width=100)
            tree.column('Difference', width=100)
            tree.column('Depth', width=60)
            tree.column('Time', width=80)
            tree.column('Nodes', width=80)
            tree.column('Type', width=100)
            
            # Add scrollbars
            v_scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=tree.yview)
            h_scrollbar = ttk.Scrollbar(main_frame, orient=tk.HORIZONTAL, command=tree.xview)
            tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
            
            # Pack treeview and scrollbars
            tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
            
            # Populate the treeview with move quality data
            for move_info in history:
                # Color code based on quality
                quality_colors = {
                    'Excellent': 'green',
                    'Good': 'lightgreen',
                    'Normal': 'black',
                    'Questionable': 'orange',
                    'Poor': 'red',
                    'Blunder': 'darkred'
                }
                
                values = (
                    move_info['move_san'],
                    move_info['quality'],
                    f"{move_info['search_score']:+d}",
                    f"{move_info['static_score']:+d}",
                    f"{move_info['score_difference']:+d}",
                    move_info['depth_used'],
                    f"{move_info['time_taken']:.2f}",
                    move_info['nodes_evaluated'],
                    move_info['move_type']
                )
                
                item = tree.insert('', tk.END, values=values)
                
                # Color code the row based on move quality
                quality = move_info['quality']
                if quality in quality_colors:
                    tree.set(item, 'Quality', move_info['quality'])
                    # Note: Tkinter treeview doesn't support row coloring easily, 
                    # but we can add tags for future enhancement
            
            # Add summary statistics
            summary_frame = ttk.Frame(quality_window)
            summary_frame.pack(fill=tk.X, padx=10, pady=5)
            
            # Calculate summary statistics
            total_moves = len(history)
            excellent_moves = sum(1 for m in history if m['quality'] == 'Excellent')
            good_moves = sum(1 for m in history if m['quality'] == 'Good')
            blunders = sum(1 for m in history if m['quality'] == 'Blunder')
            poor_moves = sum(1 for m in history if m['quality'] == 'Poor')
            avg_time = sum(m['time_taken'] for m in history) / total_moves if total_moves > 0 else 0
            avg_nodes = sum(m['nodes_evaluated'] for m in history) / total_moves if total_moves > 0 else 0
            
            summary_text = f"Summary: {total_moves} moves | "
            summary_text += f"Excellent: {excellent_moves} | Good: {good_moves} | "
            summary_text += f"Poor: {poor_moves} | Blunders: {blunders} | "
            summary_text += f"Avg Time: {avg_time:.2f}s | Avg Nodes: {avg_nodes:.0f}"
            
            summary_label = ttk.Label(summary_frame, text=summary_text, font=("Arial", 10))
            summary_label.pack()
            
            # Add buttons
            button_frame = ttk.Frame(quality_window)
            button_frame.pack(fill=tk.X, padx=10, pady=5)
            
            ttk.Button(button_frame, text="Clear History", 
                      command=lambda: self.clear_move_quality_history(quality_window)).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="Export to CSV", 
                      command=lambda: self.export_move_quality_history()).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="Close", 
                      command=quality_window.destroy).pack(side=tk.RIGHT, padx=5)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to show move quality history: {e}")
    
    def clear_move_quality_history(self, window=None):
        """Clear the move quality history."""
        result = messagebox.askyesno("Clear History", "Are you sure you want to clear the move quality history?")
        if result:
            self.bot.clear_move_quality_history()
            messagebox.showinfo("History Cleared", "Move quality history has been cleared.")
            if window:
                window.destroy()
    
    def export_move_quality_history(self):
        """Export move quality history to CSV file."""
        try:
            history = self.bot.get_move_quality_history()
            
            if not history:
                messagebox.showinfo("Export", "No move quality data to export.")
                return
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Export Move Quality History"
            )
            
            if filename:
                import csv
                
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['move_number', 'move_san', 'move_uci', 'search_score', 'static_score', 
                                'score_difference', 'depth_used', 'time_taken', 'nodes_evaluated', 
                                'move_type', 'quality', 'position_fen']
                    
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    
                    for move_info in history:
                        writer.writerow(move_info)
                
                messagebox.showinfo("Export Successful", f"Move quality history exported to {filename}")
                
        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to export move quality history: {e}")
    
    def toggle_evaluation(self):
        """Toggle evaluation display on/off."""
        self.show_evaluation = self.show_eval_var.get()
        if self.show_evaluation:
            self.current_evaluation = self.bot.get_evaluation(self.board)
            self.update_evaluation_display()
        else:
            self.eval_label.config(text="Evaluation: Hidden")
            self.eval_canvas.delete("all")
    
    def toggle_hint_enabled(self):
        """Toggle hint functionality on/off."""
        self.hint_enabled = self.hint_enabled_var.get()
        if not self.hint_enabled:
            # Hide current hint if showing
            self.hint_move = None
            self.show_hint = False
            self.hint_button.config(text="Show Hint")
            self.update_board_display()
        self.update_button_states()
    
    def on_evaluation_mode_change(self, event=None):
        """Handle evaluation mode change from dropdown."""
        mode = self.eval_mode_var.get()
        
        if mode == "Static (Fastest)":
            self.bot.set_human_evaluation_mode(lightweight=True, depth=0)
            message = ("Static evaluation mode enabled.\n\n" +
                      "Human moves will be evaluated using fast static analysis.\n" +
                      "Best performance, but may miss some tactical nuances.")
        elif mode == "Shallow Search (Balanced)":
            self.bot.set_human_evaluation_mode(lightweight=True, depth=1)
            message = ("Shallow search mode enabled.\n\n" +
                      "Human moves will be evaluated using depth-1 search.\n" +
                      "Good balance between performance and accuracy.")
        else:  # Full Search (Most Accurate)
            self.bot.set_human_evaluation_mode(lightweight=False, depth=2)
            message = ("Full search mode enabled.\n\n" +
                      "Human moves will be evaluated using depth-2 search.\n" +
                      "Most accurate analysis, but may cause slight delays.")
        
        # Show brief status message instead of popup
        self.status_label.config(text=f"Evaluation mode: {mode}")
        self.root.after(3000, lambda: self.update_status())  # Reset status after 3 seconds
    
    def run(self):
        """Start the GUI main loop."""
        self.root.mainloop()


def main():
    """Main function to run the unified chess GUI."""
    try:
        app = UnifiedChessGUI()
        app.run()
    except Exception as e:
        print(f"Error running Unified Chess GUI: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()