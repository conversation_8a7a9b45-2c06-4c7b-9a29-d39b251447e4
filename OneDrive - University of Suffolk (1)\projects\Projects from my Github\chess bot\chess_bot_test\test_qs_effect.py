#!/usr/bin/env python3
"""
Test script to analyze the effect of quiescence search on chess bot play.
"""

import chess
import time
import logging
from chess_bot_QS_testing import ChessBot

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("qs_test_results.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("QS_Test")

def test_position(fen, depth=3, description=""):
    """Test a specific position with and without quiescence search."""
    logger.info(f"Testing position: {description}")
    logger.info(f"FEN: {fen}")
    
    board = chess.Board(fen)
    
    # Create two bots - one with QS and one without
    bot_with_qs = ChessBot(depth=depth, name="WithQS", use_quiescence=True)
    bot_without_qs = ChessBot(depth=depth, name="WithoutQS", use_quiescence=False)
    
    # Get move with QS
    start_time = time.time()
    score_with_qs, move_with_qs = bot_with_qs.minimax(board, depth, float('-inf'), float('inf'), board.turn == chess.WHITE)
    time_with_qs = time.time() - start_time
    
    # Get move without QS
    start_time = time.time()
    score_without_qs, move_without_qs = bot_without_qs.minimax(board, depth, float('-inf'), float('inf'), board.turn == chess.WHITE)
    time_without_qs = time.time() - start_time
    
    # Get QS stats
    qs_stats = bot_with_qs.get_qs_stats()
    
    # Log results
    logger.info(f"Results for depth {depth}:")
    logger.info(f"With QS: Score={score_with_qs}, Move={move_with_qs}, Time={time_with_qs:.2f}s, Nodes={bot_with_qs.nodes_evaluated}")
    logger.info(f"Without QS: Score={score_without_qs}, Move={move_without_qs}, Time={time_without_qs:.2f}s, Nodes={bot_without_qs.nodes_evaluated}")
    
    # Calculate differences
    score_diff = score_with_qs - score_without_qs
    move_diff = "Same" if move_with_qs == move_without_qs else "Different"
    time_ratio = time_with_qs / time_without_qs if time_without_qs > 0 else float('inf')
    nodes_ratio = bot_with_qs.nodes_evaluated / bot_without_qs.nodes_evaluated if bot_without_qs.nodes_evaluated > 0 else float('inf')
    
    logger.info(f"Differences: Score={score_diff}, Move={move_diff}, Time ratio={time_ratio:.2f}x, Nodes ratio={nodes_ratio:.2f}x")
    
    if qs_stats["score_improvements"] > 0:
        logger.info(f"QS improved evaluation {qs_stats['score_improvements']} times")
        logger.info(f"Average improvement: {qs_stats['avg_score_improvement']:.2f} points")
    
    logger.info(f"QS max depth reached: {qs_stats['max_depth_reached']}")
    logger.info(f"QS captures found: {qs_stats['captures_found']}")
    logger.info("-" * 50)
    
    return {
        "position": description,
        "fen": fen,
        "score_with_qs": score_with_qs,
        "score_without_qs": score_without_qs,
        "move_with_qs": move_with_qs,
        "move_without_qs": move_without_qs,
        "time_with_qs": time_with_qs,
        "time_without_qs": time_without_qs,
        "nodes_with_qs": bot_with_qs.nodes_evaluated,
        "nodes_without_qs": bot_without_qs.nodes_evaluated,
        "score_diff": score_diff,
        "move_diff": move_diff,
        "time_ratio": time_ratio,
        "nodes_ratio": nodes_ratio,
        "qs_stats": qs_stats
    }

def run_test_suite():
    """Run a suite of tests on different positions."""
    results = []
    
    # Test 1: Starting position
    results.append(test_position(
        chess.STARTING_FEN,
        depth=3,
        description="Starting position"
    ))
    
    # Test 2: Position with hanging piece
    results.append(test_position(
        "rnbqkbnr/ppp1pppp/8/3p4/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2",
        depth=3,
        description="Position with hanging pawn (White can capture d5)"
    ))
    
    # Test 3: Position with tactical sequence
    results.append(test_position(
        "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 0 4",
        depth=3,
        description="Position with tactical possibilities"
    ))
    
    # Test 4: Position with multiple captures
    results.append(test_position(
        "r1bqkb1r/pppp1Qpp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNB1K2R b KQkq - 0 4",
        depth=3,
        description="Position with queen capture possible"
    ))
    
    # Test 5: Endgame position
    results.append(test_position(
        "4k3/8/8/8/8/8/3P4/4K3 w - - 0 1",
        depth=4,
        description="Simple endgame position"
    ))
    
    # Test 6: Complex middlegame
    results.append(test_position(
        "r2qk2r/pp1nbppp/2p1pn2/3p4/3P4/2NBPN2/PPQ2PPP/R3K2R w KQkq - 0 10",
        depth=3,
        description="Complex middlegame position"
    ))
    
    # Test 7: Position with horizon effect potential
    results.append(test_position(
        "r1bqkb1r/ppp2ppp/2np1n2/4p3/4P3/2NP1N2/PPP2PPP/R1BQKB1R w KQkq - 0 6",
        depth=3,
        description="Position with potential horizon effect"
    ))
    
    # Test 8: Position with capture sequence
    results.append(test_position(
        "r1bqkb1r/ppp2ppp/2n2n2/3pp3/4P3/2NP1N2/PPP2PPP/R1BQKB1R w KQkq - 0 5",
        depth=3,
        description="Position with capture sequence"
    ))
    
    # Summarize results
    logger.info("=" * 50)
    logger.info("TEST SUMMARY")
    logger.info("=" * 50)
    
    different_moves = [r for r in results if r["move_diff"] == "Different"]
    significant_score_diff = [r for r in results if abs(r["score_diff"]) > 50]
    
    logger.info(f"Total positions tested: {len(results)}")
    logger.info(f"Positions where QS changed the move: {len(different_moves)} ({len(different_moves)/len(results)*100:.1f}%)")
    logger.info(f"Positions with significant score difference: {len(significant_score_diff)} ({len(significant_score_diff)/len(results)*100:.1f}%)")
    
    avg_time_ratio = sum(r["time_ratio"] for r in results) / len(results)
    avg_nodes_ratio = sum(r["nodes_ratio"] for r in results) / len(results)
    
    logger.info(f"Average time ratio (QS/no QS): {avg_time_ratio:.2f}x")
    logger.info(f"Average nodes ratio (QS/no QS): {avg_nodes_ratio:.2f}x")
    
    # List positions where QS made a significant difference
    if different_moves:
        logger.info("\nPositions where QS changed the move:")
        for r in different_moves:
            logger.info(f"- {r['position']}: {r['move_without_qs']} -> {r['move_with_qs']}")
    
    if significant_score_diff:
        logger.info("\nPositions with significant score difference:")
        for r in significant_score_diff:
            logger.info(f"- {r['position']}: {r['score_without_qs']} -> {r['score_with_qs']} (diff: {r['score_diff']})")
    
    return results

if __name__ == "__main__":
    print("Running quiescence search effect tests...")
    results = run_test_suite()
    print(f"Tests completed. See qs_test_results.log for detailed results.")
    print(f"Tested {len(results)} positions.")
    
    different_moves = [r for r in results if r["move_diff"] == "Different"]
    print(f"QS changed the move in {len(different_moves)} positions ({len(different_moves)/len(results)*100:.1f}%).")
    
    avg_time_ratio = sum(r["time_ratio"] for r in results) / len(results)
    print(f"QS took on average {avg_time_ratio:.2f}x more time than without QS.")