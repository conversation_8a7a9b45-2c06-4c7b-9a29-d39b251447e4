import chess
import time
from chess_bot_iterative import ChessBotIterative
from chess_bot import ChessBot

def test_iterative_deepening():
    """Test the iterative deepening chess bot."""
    print("=== Testing Iterative Deepening Chess Bot ===\n")
    
    # Create bots for comparison
    iterative_bot = ChessBotIterative(max_depth=6, time_limit=3.0, name="IterativeBot")
    regular_bot = ChessBot(depth=4, name="RegularBot")
    
    # Test positions
    test_positions = [
        # Starting position
        ("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1", "Starting position"),
        
        # Tactical position with a fork opportunity
        ("rnbqkb1r/pppp1ppp/5n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4", "Fork opportunity"),
        
        # Endgame position
        ("8/8/8/8/8/3k4/3P4/3K4 w - - 0 1", "King and pawn endgame"),
        
        # Checkmate in 2 position
        ("2bqkbn1/2pppp2/np2N3/r3P1p1/p2N4/5Q2/PPPPKPP1/RNB2B1R w KQkq - 0 1", "Mate in 2"),
    ]
    
    for fen, description in test_positions:
        print(f"\n--- Testing: {description} ---")
        print(f"FEN: {fen}")
        
        board = chess.Board(fen)
        print(f"Board:\n{board}")
        
        # Test iterative deepening bot
        print(f"\n{iterative_bot.name} analysis:")
        start_time = time.time()
        iterative_move = iterative_bot.get_best_move(board)
        iterative_time = time.time() - start_time
        iterative_info = iterative_bot.get_search_info()
        
        print(f"Best move: {iterative_move}")
        print(f"Nodes evaluated: {iterative_info['nodes_evaluated']}")
        print(f"Time taken: {iterative_time:.2f}s")
        
        # Test regular bot for comparison
        print(f"\n{regular_bot.name} analysis:")
        start_time = time.time()
        regular_move = regular_bot.get_best_move(board)
        regular_time = time.time() - start_time
        
        print(f"Best move: {regular_move}")
        print(f"Nodes evaluated: {regular_bot.nodes_evaluated}")
        print(f"Time taken: {regular_time:.2f}s")
        
        # Compare results
        print(f"\n--- Comparison ---")
        print(f"Same move chosen: {iterative_move == regular_move}")
        if iterative_move != regular_move:
            print(f"Iterative: {iterative_move}, Regular: {regular_move}")
        print(f"Efficiency (nodes/second):")
        print(f"  Iterative: {iterative_info['nodes_evaluated']/max(iterative_time, 0.001):.0f}")
        print(f"  Regular: {regular_bot.nodes_evaluated/max(regular_time, 0.001):.0f}")
        
        print("-" * 50)

def test_time_management():
    """Test time management features of iterative deepening."""
    print("\n=== Testing Time Management ===\n")
    
    bot = ChessBotIterative(max_depth=10, time_limit=1.0, name="TimeLimitBot")
    
    # Test different time limits
    time_limits = [0.5, 1.0, 2.0, 5.0]
    
    board = chess.Board("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")
    
    for time_limit in time_limits:
        print(f"\n--- Testing with {time_limit}s time limit ---")
        bot.set_time_limit(time_limit)
        
        start_time = time.time()
        move = bot.get_best_move(board)
        actual_time = time.time() - start_time
        
        info = bot.get_search_info()
        print(f"Move: {move}")
        print(f"Requested time: {time_limit}s")
        print(f"Actual time: {actual_time:.2f}s")
        print(f"Time efficiency: {actual_time/time_limit:.2f}")
        print(f"Nodes evaluated: {info['nodes_evaluated']}")

def test_depth_scaling():
    """Test how performance scales with maximum depth."""
    print("\n=== Testing Depth Scaling ===\n")
    
    bot = ChessBotIterative(time_limit=5.0, name="DepthTestBot")
    
    # Test different maximum depths
    max_depths = [3, 4, 5, 6, 7]
    
    board = chess.Board("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")
    
    for max_depth in max_depths:
        print(f"\n--- Testing with max depth {max_depth} ---")
        bot.set_max_depth(max_depth)
        
        start_time = time.time()
        move = bot.get_best_move(board)
        actual_time = time.time() - start_time
        
        info = bot.get_search_info()
        print(f"Move: {move}")
        print(f"Max depth: {max_depth}")
        print(f"Time taken: {actual_time:.2f}s")
        print(f"Nodes evaluated: {info['nodes_evaluated']}")

def demonstrate_iterative_deepening():
    """Demonstrate the iterative deepening search process."""
    print("\n=== Demonstrating Iterative Deepening Process ===\n")
    
    # Create a bot with longer time limit to show multiple iterations
    bot = ChessBotIterative(max_depth=8, time_limit=5.0, name="DemoBot")
    
    # Use a tactical position
    board = chess.Board("r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 1")
    
    print("Position:")
    print(board)
    print("\nRunning iterative deepening search...")
    print("(Watch how the search progresses through increasing depths)")
    
    move = bot.get_best_move(board)
    
    print(f"\nFinal best move: {move}")
    info = bot.get_search_info()
    print(f"Total nodes evaluated: {info['nodes_evaluated']}")

if __name__ == "__main__":
    test_iterative_deepening()
    test_time_management()
    test_depth_scaling()
    demonstrate_iterative_deepening()
    
    print("\n=== All tests completed ===")