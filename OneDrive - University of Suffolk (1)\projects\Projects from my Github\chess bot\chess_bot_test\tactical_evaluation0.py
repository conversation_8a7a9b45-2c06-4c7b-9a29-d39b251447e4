#!/usr/bin/env python3
"""
Enhanced tactical evaluation system for chess bot.
Provides comprehensive analysis of tactical threats, blunders, and move quality.
"""

import chess
from typing import Dict, List, Tuple, Optional, Set
from enum import Enum

class ThreatType(Enum):
    """Types of tactical threats."""
    HANGING_PIECE = "hanging_piece"
    UNDEFENDED_ATTACK = "undefended_attack"
    OVERLOADED_DEFENDER = "overloaded_defender" 
    PINNED_PIECE = "pinned_piece"
    FORK = "fork"
    SKEWER = "skewer"
    DISCOVERED_ATTACK = "discovered_attack"
    BACK_RANK_MATE = "back_rank_mate"
    TACTICAL_SHOT = "tactical_shot"

class MoveQuality(Enum):
    """Move quality classifications."""
    BRILLIANT = "Brilliant"
    EXCELLENT = "Excellent"
    GOOD = "Good"
    OKAY = "Okay"
    INACCURATE = "Inaccurate"
    POOR = "Poor"
    BLUNDER = "Blunder"

class TacticalEvaluator:
    """Enhanced tactical evaluation and threat detection."""
    
    def __init__(self):
        # Standard centipawn piece values
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 300,
            chess.BISHOP: 300,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 10000   # High but not dominating
        }
        
        # Threat multipliers for different game phases
        self.endgame_king_multiplier = 1.5  # King safety less critical in endgame
        self.middlegame_king_multiplier = 2.0  # King safety more critical in middlegame
    
    def _is_endgame(self, board: chess.Board) -> bool:
        """Determine if the position is in endgame."""
        # Simple heuristic: endgame if queens are off or material is low
        white_queen = len(board.pieces(chess.QUEEN, chess.WHITE))
        black_queen = len(board.pieces(chess.QUEEN, chess.BLACK))
        
        if white_queen == 0 and black_queen == 0:
            return True
        
        # Count total material (excluding pawns and kings)
        total_material = 0
        for piece_type in [chess.KNIGHT, chess.BISHOP, chess.ROOK, chess.QUEEN]:
            total_material += len(board.pieces(piece_type, chess.WHITE))
            total_material += len(board.pieces(piece_type, chess.BLACK))
        
        return total_material <= 6  # Endgame if 6 or fewer pieces total
    
    def get_adjusted_piece_value(self, piece_type: chess.PieceType, board: chess.Board) -> int:
        """Get piece value adjusted for game phase."""
        base_value = self.piece_values[piece_type]
        
        if piece_type == chess.KING:
            if self._is_endgame(board):
                return int(base_value * self.endgame_king_multiplier)
            else:
                return int(base_value * self.middlegame_king_multiplier)
        
        return base_value
    
    def evaluate_threats(self, board: chess.Board) -> Dict[chess.Color, List[Dict]]:
        """
        Comprehensive threat evaluation for both sides.
        
        Args:
            board: Current chess board position
            
        Returns:
            Dictionary mapping colors to lists of threat information
        """
        threats = {chess.WHITE: [], chess.BLACK: []}
        
        # Handle check situations - only attacking side has threats
        if board.is_check():
            # Clear threats for defending side
            threats[board.turn] = []
            
            # Analyze threats for attacking side
            threats.update(self._analyze_check_situation(board))
        else:
            # Normal threat analysis when not in check
            for color in [chess.WHITE, chess.BLACK]:
                # Find hanging pieces (excluding kings)
                threats[color].extend(self._find_hanging_pieces(board, color))
                
                # Find tactical shots (forks, skewers, etc.)
                threats[color].extend(self._find_tactical_shots(board, color))
                
                # Find overloaded defenders
                threats[color].extend(self._find_overloaded_defenders(board, color))
                
                # Find pinned pieces
                threats[color].extend(self._find_pinned_pieces(board, color))
                
                # Find back rank threats
                threats[color].extend(self._find_back_rank_threats(board, color))
        
        return threats
    
    def _find_hanging_pieces(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        """Find pieces that are hanging (undefended and attacked)."""
        hanging = []
        
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece and piece.color == color:
                # Skip pawns as they're often intentionally undefended
                if piece.piece_type == chess.PAWN:
                    continue
                
                # Skip kings - they cannot be "hanging" in the traditional sense
                if piece.piece_type == chess.KING:
                    continue
                
                # Check if piece is attacked by opponent
                if board.is_attacked_by(not color, square):
                    attackers = board.attackers(not color, square)
                    defenders = board.attackers(color, square)
                    
                    # Calculate exchange value
                    if len(attackers) > 0:
                        attacker_values = [self.piece_values[board.piece_at(sq).piece_type] 
                                         for sq in attackers if board.piece_at(sq)]
                        defender_values = [self.piece_values[board.piece_at(sq).piece_type] 
                                         for sq in defenders if board.piece_at(sq)]
                        
                        min_attacker = min(attacker_values) if attacker_values else 0
                        min_defender = min(defender_values) if defender_values else float('inf')
                        
                        # Piece is hanging if it can be captured by a less valuable piece
                        # or if it's undefended
                        piece_value = self.get_adjusted_piece_value(piece.piece_type, board)
                        if len(defenders) == 0 or min_attacker < piece_value:
                            threat_value = piece_value
                            if len(defenders) > 0:
                                threat_value -= min_defender
                            
                            hanging.append({
                                'type': ThreatType.HANGING_PIECE,
                                'square': square,
                                'piece': piece,
                                'threat_value': threat_value,
                                'attackers': len(attackers),
                                'defenders': len(defenders),
                                'description': f"{piece.symbol().upper()} on {chess.square_name(square)} is hanging"
                            })
        
        return hanging
    
    def _analyze_check_situation(self, board: chess.Board) -> Dict[chess.Color, List[Dict]]:
        """Analyze the tactical situation when in check."""
        threats = {chess.WHITE: [], chess.BLACK: []}
        
        if not board.is_check():
            return threats
        
        side_in_check = board.turn
        attacking_side = not side_in_check
        
        # Find the checking piece(s)
        king_square = board.king(side_in_check)
        if not king_square:
            return threats
        
        checking_pieces = board.attackers(attacking_side, king_square)
        
        for checker_square in checking_pieces:
            checker_piece = board.piece_at(checker_square)
            if not checker_piece:
                continue
            
            # Analyze what else this checking piece attacks
            attacked_squares = board.attacks(checker_square)
            forked_pieces = []
            
            for target_square in attacked_squares:
                target = board.piece_at(target_square)
                if target and target.color == side_in_check and target.piece_type != chess.KING:
                    # Only count valuable pieces
                    if target.piece_type in [chess.QUEEN, chess.ROOK, chess.BISHOP, chess.KNIGHT]:
                        forked_pieces.append((target_square, target))
            
            # Calculate the real tactical value
            if forked_pieces:
                # Find the best piece that can be captured
                best_piece_value = max(self.piece_values[piece.piece_type] for _, piece in forked_pieces)
                best_piece = max(forked_pieces, key=lambda x: self.piece_values[x[1].piece_type])[1]
                
                # This is a fork with check - very powerful tactical motif
                threats[attacking_side].append({
                    'type': ThreatType.FORK,
                    'checker_square': checker_square,
                    'checker_piece': checker_piece,
                    'forked_pieces': forked_pieces,
                    'material_at_risk': best_piece_value,
                    'threat_value': best_piece_value,
                    'includes_king': True,
                    'description': f"Fork with check: {checker_piece.symbol().upper()} forks king and wins {best_piece.symbol().upper()}"
                })
                
                # The side in check has NO threats - they're being threatened
            else:
                # Simple check without fork - still only attacking side has threats
                threats[attacking_side].append({
                    'type': ThreatType.TACTICAL_SHOT,
                    'checker_square': checker_square,
                    'checker_piece': checker_piece,
                    'threat_value': 50,  # Small bonus for tempo
                    'description': f"Check from {checker_piece.symbol().upper()}"
                })
        
        return threats
    
    def _find_undefended_attacks(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        """Find pieces that can attack undefended enemy pieces."""
        attacks = []
        opponent_color = not color
        
        # Find all pieces of the current color
        for from_square in chess.SQUARES:
            piece = board.piece_at(from_square)
            if piece and piece.color == color:
                # Get all squares this piece attacks
                attacked_squares = board.attacks(from_square)
                
                for to_square in attacked_squares:
                    target = board.piece_at(to_square)
                    if target and target.color == opponent_color:
                        # Check if target is defended
                        defenders = board.attackers(opponent_color, to_square)
                        
                        if len(defenders) == 0:
                            # Undefended piece can be captured
                            attacks.append({
                                'type': ThreatType.UNDEFENDED_ATTACK,
                                'from_square': from_square,
                                'to_square': to_square,
                                'attacker': piece,
                                'target': target,
                                'threat_value': self.get_adjusted_piece_value(target.piece_type, board),
                                'description': f"{piece.symbol().upper()} can capture undefended {target.symbol().upper()}"
                            })
        
        return attacks
    
    def _find_overloaded_defenders(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        """Find pieces that are defending multiple pieces and could be overloaded."""
        overloaded = []
        opponent_color = not color
        
        # Look at opponent's pieces to see if they're overloaded
        for defender_square in chess.SQUARES:
            defender = board.piece_at(defender_square)
            if defender and defender.color == opponent_color:
                # Find what this piece is defending
                defended_pieces = []
                
                for protected_square in chess.SQUARES:
                    protected_piece = board.piece_at(protected_square)
                    if (protected_piece and protected_piece.color == opponent_color and 
                        protected_square != defender_square):
                        
                        # Check if the defender is protecting this piece
                        if defender_square in board.attackers(opponent_color, protected_square):
                            # Also check if the protected piece is under attack
                            if board.is_attacked_by(color, protected_square):
                                # Make sure the defended piece is actually valuable
                                if protected_piece.piece_type in [chess.QUEEN, chess.ROOK, chess.BISHOP, chess.KNIGHT]:
                                    defended_pieces.append((protected_square, protected_piece))
                
                # A piece is only overloaded if it's defending 2+ valuable pieces that are all under attack
                # AND there's a way to exploit this (remove the defender or attack multiple defended pieces)
                if len(defended_pieces) >= 2:
                    # Check if the defender itself is under attack or can be attacked
                    defender_under_attack = board.is_attacked_by(color, defender_square)
                    
                    # Only consider it overloaded if the defender is vulnerable
                    if defender_under_attack:
                        total_value = sum(self.get_adjusted_piece_value(piece.piece_type, board) for _, piece in defended_pieces)
                        overloaded.append({
                            'type': ThreatType.OVERLOADED_DEFENDER,
                            'defender_square': defender_square,
                            'defender': defender,
                            'defended_pieces': defended_pieces,
                            'threat_value': total_value,
                            'description': f"{defender.symbol().upper()} is overloaded defending {len(defended_pieces)} pieces"
                        })
        
        return overloaded
    
    def _find_pinned_pieces(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        """Find pieces that are pinned."""
        pinned = []
        opponent_color = not color
        
        # Find the king
        king_square = board.king(color)
        if king_square is None:
            return pinned
        
        # Check for pins along ranks, files, and diagonals
        for direction in [(0, 1), (0, -1), (1, 0), (-1, 0), (1, 1), (1, -1), (-1, 1), (-1, -1)]:
            squares_in_line = []
            file_delta, rank_delta = direction
            
            current_file = chess.square_file(king_square)
            current_rank = chess.square_rank(king_square)
            
            # Move in the direction until we hit the edge
            while True:
                current_file += file_delta
                current_rank += rank_delta
                
                if not (0 <= current_file <= 7 and 0 <= current_rank <= 7):
                    break
                
                square = chess.square(current_file, current_rank)
                piece = board.piece_at(square)
                
                if piece:
                    squares_in_line.append((square, piece))
                    # Stop if we hit a piece (can't see through it)
                    break
            
            # Check if there's a potential pinner behind any of our pieces
            if len(squares_in_line) >= 1:
                first_square, first_piece = squares_in_line[0]
                
                if first_piece.color == color:
                    # Continue looking for a pinner behind our piece
                    current_file = chess.square_file(first_square)
                    current_rank = chess.square_rank(first_square)
                    
                    while True:
                        current_file += file_delta
                        current_rank += rank_delta
                        
                        if not (0 <= current_file <= 7 and 0 <= current_rank <= 7):
                            break
                        
                        square = chess.square(current_file, current_rank)
                        piece = board.piece_at(square)
                        
                        if piece:
                            if piece.color == opponent_color:
                                # Check if this piece can attack along this line
                                if self._can_attack_along_line(piece, direction):
                                    pinned.append({
                                        'type': ThreatType.PINNED_PIECE,
                                        'pinned_square': first_square,
                                        'pinned_piece': first_piece,
                                        'pinner_square': square,
                                        'pinner': piece,
                                        'king_square': king_square,
                                        'threat_value': self.get_adjusted_piece_value(first_piece.piece_type, board),
                                        'description': f"{first_piece.symbol().upper()} is pinned by {piece.symbol().upper()}"
                                    })
                            break
        
        return pinned
    
    def _can_attack_along_line(self, piece: chess.Piece, direction: Tuple[int, int]) -> bool:
        """Check if a piece can attack along a given direction."""
        file_delta, rank_delta = direction
        
        if piece.piece_type == chess.ROOK:
            return file_delta == 0 or rank_delta == 0
        elif piece.piece_type == chess.BISHOP:
            return abs(file_delta) == abs(rank_delta) and file_delta != 0
        elif piece.piece_type == chess.QUEEN:
            return True  # Queen can move in any direction
        
        return False
    
    def _find_tactical_shots(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        """Find tactical opportunities like forks, skewers, etc."""
        tactical_shots = []
        
        # Look for current tactical shots (pieces that are already attacking multiple targets)
        for from_square in chess.SQUARES:
            piece = board.piece_at(from_square)
            if piece and piece.color == color:
                # Check what this piece currently attacks
                attacked_squares = board.attacks(from_square)
                valuable_targets = []
                
                for target_square in attacked_squares:
                    target = board.piece_at(target_square)
                    if target and target.color != color:
                        # Include king in fork calculations but handle specially
                        if target.piece_type in [chess.QUEEN, chess.ROOK, chess.BISHOP, chess.KNIGHT, chess.KING]:
                            valuable_targets.append((target_square, target))
                
                # If attacking multiple valuable pieces, it's a fork
                if len(valuable_targets) >= 2:
                    # Calculate total value - kings have special handling
                    total_value = 0
                    includes_king = False
                    non_king_targets = []
                    
                    for target_square, target in valuable_targets:
                        if target.piece_type == chess.KING:
                            includes_king = True
                            total_value += 1000  # Special value for king attack
                        else:
                            non_king_targets.append((target_square, target))
                            total_value += self.piece_values[target.piece_type]
                    
                    # Only count as tactical shot if it's a real threat
                    if includes_king or len(non_king_targets) >= 2:
                        description = f"{piece.symbol().upper()} forks "
                        if includes_king:
                            description += f"king and {len(non_king_targets)} other piece(s)"
                        else:
                            description += f"{len(valuable_targets)} pieces"
                        
                        tactical_shots.append({
                            'type': ThreatType.FORK,
                            'from_square': from_square,
                            'piece': piece,
                            'targets': valuable_targets,
                            'includes_king': includes_king,
                            'threat_value': total_value,
                            'description': description
                        })
        
        return tactical_shots
    
    def _find_back_rank_threats(self, board: chess.Board, color: chess.Color) -> List[Dict]:
        """Find back rank mate threats."""
        threats = []
        opponent_color = not color
        
        # Get opponent's king
        king_square = board.king(opponent_color)
        if king_square is None:
            return threats
        
        king_rank = chess.square_rank(king_square)
        
        # Check if king is on back rank
        if (opponent_color == chess.WHITE and king_rank == 0) or (opponent_color == chess.BLACK and king_rank == 7):
            # Check if king is trapped by its own pawns
            king_file = chess.square_file(king_square)
            escape_squares = []
            
            for file_offset in [-1, 0, 1]:
                if 0 <= king_file + file_offset <= 7:
                    escape_square = chess.square(king_file + file_offset, king_rank + (1 if opponent_color == chess.WHITE else -1))
                    if board.piece_at(escape_square) is None:
                        escape_squares.append(escape_square)
            
            # If no escape squares, check for back rank threats
            if len(escape_squares) == 0:
                # Look for our rooks/queens that can attack the back rank
                for square in chess.SQUARES:
                    piece = board.piece_at(square)
                    if piece and piece.color == color and piece.piece_type in [chess.ROOK, chess.QUEEN]:
                        if chess.square_rank(square) == king_rank:
                            threats.append({
                                'type': ThreatType.BACK_RANK_MATE,
                                'threat_square': square,
                                'piece': piece,
                                'target_king': king_square,
                                'threat_value': 1000,  # High value for mate threat
                                'description': f"Back rank mate threat with {piece.symbol().upper()}"
                            })
        
        return threats
    
    def calculate_threat_score(self, threats: List[Dict]) -> int:
        """Calculate total threat score from list of threats."""
        total_score = 0
        for threat in threats:
            threat_value = threat.get('threat_value', 0)
            
            # Weight different threat types based on their tactical impact
            if threat['type'] == ThreatType.HANGING_PIECE:
                total_score += threat_value
            elif threat['type'] == ThreatType.UNDEFENDED_ATTACK:
                total_score += threat_value * 0.8  # Slightly less than hanging
            elif threat['type'] == ThreatType.FORK:
                # Full value for fork with king, reduced for regular fork
                if threat.get('includes_king', False):
                    total_score += threat_value
                else:
                    total_score += threat_value * 0.6
            elif threat['type'] == ThreatType.TACTICAL_SHOT:
                total_score += threat_value
            elif threat['type'] == ThreatType.PINNED_PIECE:
                total_score += threat_value * 0.4  # Restricts movement
            elif threat['type'] == ThreatType.BACK_RANK_MATE:
                total_score += threat_value  # Full value for mate threats
            elif threat['type'] == ThreatType.OVERLOADED_DEFENDER:
                total_score += threat_value * 0.3  # Positional pressure
        
        return int(total_score)
    
    def evaluate_move_quality(self, board_before: chess.Board, move: chess.Move, 
                            eval_before: int, eval_after: int, move_number: int) -> Tuple[MoveQuality, str]:
        """
        Optimized move quality evaluation with better performance and calibration.
        
        Args:
            board_before: Board position before the move
            move: The move that was played
            eval_before: Evaluation before the move
            eval_after: Evaluation after the move
            move_number: Move number in the game
            
        Returns:
            Tuple of (MoveQuality, explanation)
        """
        eval_change = eval_after - eval_before
        player_color = board_before.turn
        
        # Adjust eval_change based on player color
        if player_color == chess.BLACK:
            eval_change = -eval_change
        
        # Quick tactical checks - always check for hanging pieces regardless of eval change
        major_blunder_detected = False
        tactical_note = ""
        
        # Always check for hanging pieces (this catches moves that leave valuable pieces exposed)
        major_blunder_detected, tactical_note = self._quick_tactical_check(board_before, move, player_color)
        
        # Determine move quality with more conservative thresholds
        explanation_parts = []
        
        # Normalize extreme evaluation changes to prevent unrealistic swings
        eval_change = max(-500, min(500, eval_change))  # Cap at ±5 pawns
        
        # Well-calibrated move quality thresholds for realistic distribution
        if move_number <= 10:  # Opening - most moves should be okay/good
            if eval_change >= 280:    # 2.8+ pawns - truly exceptional opening play (very rare)
                base_quality = MoveQuality.EXCELLENT
                explanation_parts.append(f"outstanding opening (+{eval_change/100:.1f})")
            elif eval_change >= 100:  # 1.0+ pawns - clearly beneficial
                base_quality = MoveQuality.GOOD
                explanation_parts.append(f"strong development (+{eval_change/100:.1f})")
            elif eval_change >= 30:   # 0.3+ pawns - decent move
                base_quality = MoveQuality.OKAY
                explanation_parts.append(f"solid opening move (+{eval_change/100:.1f})")
            elif eval_change >= -50:  # Within ±0.5 pawns - normal range
                base_quality = MoveQuality.OKAY
                if eval_change >= 0:
                    explanation_parts.append("reasonable opening play")
                else:
                    explanation_parts.append(f"acceptable opening ({eval_change/100:.1f})")
            elif eval_change >= -120:  # Lost 0.5-1.2 pawns - suboptimal
                base_quality = MoveQuality.INACCURATE
                explanation_parts.append(f"imprecise opening ({eval_change/100:.1f})")
            elif eval_change >= -200:  # Lost 1.2-2.0 pawns - poor
                base_quality = MoveQuality.POOR
                explanation_parts.append(f"weak opening move ({eval_change/100:.1f})")
            else:  # Lost 2.0+ pawns
                base_quality = MoveQuality.BLUNDER
                explanation_parts.append(f"opening blunder ({eval_change/100:.1f})")
        
        else:  # Middlegame and endgame - higher precision expected
            if eval_change >= 350:    # 3.5+ pawns - excellent (quite rare)
                base_quality = MoveQuality.EXCELLENT
                explanation_parts.append(f"excellent move (+{eval_change/100:.1f})")
            elif eval_change >= 120:  # 1.2+ pawns - good
                base_quality = MoveQuality.GOOD
                explanation_parts.append(f"strong move (+{eval_change/100:.1f})")
            elif eval_change >= 35:   # 0.35+ pawns - okay
                base_quality = MoveQuality.OKAY
                explanation_parts.append(f"solid move (+{eval_change/100:.1f})")
            elif eval_change >= -40:  # Within ±0.4 pawns - normal
                base_quality = MoveQuality.OKAY
                if eval_change >= 0:
                    explanation_parts.append("reasonable move")
                else:
                    explanation_parts.append(f"acceptable move ({eval_change/100:.1f})")
            elif eval_change >= -90:   # Lost 0.4-0.9 pawns - inaccurate
                base_quality = MoveQuality.INACCURATE
                explanation_parts.append(f"inaccurate move ({eval_change/100:.1f})")
            elif eval_change >= -160:  # Lost 0.9-1.6 pawns - poor
                base_quality = MoveQuality.POOR
                explanation_parts.append(f"poor move ({eval_change/100:.1f})")
            else:  # Lost 1.6+ pawns
                base_quality = MoveQuality.BLUNDER
                explanation_parts.append(f"blunder ({eval_change/100:.1f})")
        
        final_quality = base_quality
        
        # Check for unsound sacrifices (moves that give away material without adequate compensation)
        unsound_sacrifice = self._detect_unsound_sacrifice(board_before, move, eval_change, move_number)
        if unsound_sacrifice:
            final_quality = MoveQuality.POOR
            explanation_parts = [f"unsound sacrifice ({eval_change/100:.1f})"]
        
        # Apply tactical corrections - blunders that leave valuable pieces hanging
        elif major_blunder_detected:
            if "Queen" in tactical_note:
                # Leaving the queen hanging is always a blunder
                final_quality = MoveQuality.BLUNDER
            elif "Rook" in tactical_note:
                # Leaving a rook hanging is usually a blunder
                final_quality = MoveQuality.BLUNDER
            else:
                # Other pieces hanging are poor moves
                final_quality = MoveQuality.POOR
            explanation_parts = [tactical_note]  # Replace other explanations
        
        # Check for brilliant moves (extremely rare - maybe 1 in 100+ games)
        if eval_change >= 400 and move_number > 20:  # Only in complex middlegame/endgame
            # Check if it's a significant material sacrifice that leads to huge advantage
            captured_piece = board_before.piece_at(move.to_square)
            moving_piece = board_before.piece_at(move.from_square)
            
            # Must be a sacrifice of at least a rook (5+ points) for massive gain
            if (captured_piece and moving_piece and 
                self.piece_values[moving_piece.piece_type] >= 500 and  # Sacrificing rook or queen
                self.piece_values[moving_piece.piece_type] > self.piece_values[captured_piece.piece_type] + 200 and
                eval_change >= 450):  # And gaining huge advantage (4.5+ pawns)
                final_quality = MoveQuality.BRILLIANT
                explanation_parts = [f"brilliant sacrifice (+{eval_change/100:.1f})"]
        
        explanation = "; ".join(explanation_parts)
        return final_quality, explanation
    
    def _quick_tactical_check(self, board_before: chess.Board, move: chess.Move, player_color: chess.Color) -> Tuple[bool, str]:
        """Quick tactical check for major blunders without full threat analysis."""
        # Check if we left a valuable piece hanging
        board_after = board_before.copy()
        board_after.push(move)
        
        # Look for our pieces that are now attacked and undefended
        for square in chess.SQUARES:
            piece = board_after.piece_at(square)
            if (piece and piece.color == player_color and 
                piece.piece_type in [chess.QUEEN, chess.ROOK, chess.BISHOP, chess.KNIGHT]):
                
                if board_after.is_attacked_by(not player_color, square):
                    defenders = board_after.attackers(player_color, square)
                    attackers = board_after.attackers(not player_color, square)
                    
                    # Check if piece is truly hanging (undefended or inadequately defended)
                    if len(defenders) == 0:
                        # Completely undefended
                        piece_names = {chess.QUEEN: "Queen", chess.ROOK: "Rook", 
                                     chess.BISHOP: "Bishop", chess.KNIGHT: "Knight"}
                        return True, f"left {piece_names[piece.piece_type]} hanging"
                    
                    # Check if piece can be captured profitably
                    if len(attackers) > 0:
                        # Get values of attacking and defending pieces
                        attacker_values = [self.piece_values[board_after.piece_at(sq).piece_type] 
                                         for sq in attackers if board_after.piece_at(sq)]
                        defender_values = [self.piece_values[board_after.piece_at(sq).piece_type] 
                                         for sq in defenders if board_after.piece_at(sq)]
                        
                        if attacker_values and defender_values:
                            min_attacker = min(attacker_values)
                            min_defender = min(defender_values)
                            piece_value = self.piece_values[piece.piece_type]
                            
                            # If the piece can be captured profitably (losing more than gaining)
                            if min_attacker < piece_value and (piece_value - min_attacker > min_defender):
                                piece_names = {chess.QUEEN: "Queen", chess.ROOK: "Rook", 
                                             chess.BISHOP: "Bishop", chess.KNIGHT: "Knight"}
                                return True, f"left {piece_names[piece.piece_type]} inadequately defended"
        
        return False, ""
    
    def _detect_unsound_sacrifice(self, board_before: chess.Board, move: chess.Move, 
                                eval_change: int, move_number: int) -> bool:
        """
        Detect unsound sacrificial moves that give away material without adequate compensation.
        
        Args:
            board_before: Board position before the move
            move: The move being evaluated
            eval_change: The evaluation change from the move
            move_number: Current move number
            
        Returns:
            True if the move is an unsound sacrifice
        """
        # Check if this is a capture that sacrifices material
        captured_piece = board_before.piece_at(move.to_square)
        moving_piece = board_before.piece_at(move.from_square)
        
        if not captured_piece or not moving_piece:
            return False
        
        # Calculate material difference
        material_given = self.piece_values[moving_piece.piece_type]
        material_gained = self.piece_values[captured_piece.piece_type]
        material_deficit = material_given - material_gained
        
        # Only check moves that sacrifice significant material (200+ centipawns)
        if material_deficit < 200:
            return False
        
        # Create position after the move
        board_after = board_before.copy()
        board_after.push(move)
        
        # Check if the sacrifice is unsound by looking at several factors:
        
        # 1. In opening (moves 1-15), sacrifices for less than 2 pawns are usually unsound
        if move_number <= 15:
            if material_deficit >= 250 and eval_change < 200:  # Sacrificed minor piece+ for <2 pawns
                return True
        
        # 2. Classic unsound patterns
        # Knight sacrifice on f7/f2 without sufficient follow-up
        if (moving_piece.piece_type == chess.KNIGHT and 
            move.to_square in [chess.F7, chess.F2]):
            
            # Check if there's immediate tactical follow-up
            # Look for discovered attacks, checks, or threats to the king
            has_immediate_threats = False
            
            # Count number of pieces that can immediately attack the exposed king
            king_square = board_after.king(not board_before.turn)
            if king_square:
                # Count immediate attackers after the sacrifice
                immediate_attackers = len(board_after.attackers(board_before.turn, king_square))
                
                # Check for follow-up moves that create serious threats
                legal_moves = list(board_after.legal_moves)
                for follow_up in legal_moves[:10]:  # Check first 10 moves for performance
                    temp_board = board_after.copy()
                    temp_board.push(follow_up)
                    if temp_board.is_check() or temp_board.is_checkmate():
                        has_immediate_threats = True
                        break
            
            # If no immediate serious threats and position doesn't improve much, it's unsound
            if not has_immediate_threats and eval_change < 150:
                return True
        
        # 3. Exchange sacrifices (rook for minor piece) without positional compensation
        if (moving_piece.piece_type == chess.ROOK and 
            captured_piece.piece_type in [chess.BISHOP, chess.KNIGHT]):
            
            # Rook vs minor piece sacrifice - needs significant positional compensation
            if eval_change < 100:  # Less than 1 pawn improvement
                return True
        
        # 4. Queen sacrifices need huge compensation
        if moving_piece.piece_type == chess.QUEEN:
            if material_deficit > 300 and eval_change < 400:  # Sacrificed queen for <4 pawns advantage
                return True
        
        return False
    
    def get_threat_analysis_summary(self, threats: Dict[chess.Color, List[Dict]], board: chess.Board) -> str:
        """Get a comprehensive threat analysis summary with balanced scoring."""
        lines = []
        lines.append("BALANCED TACTICAL THREATS ANALYSIS")
        lines.append("=" * 50)
        lines.append("")
        
        # Add game phase info
        phase = "Endgame" if self._is_endgame(board) else "Middlegame"
        lines.append(f"Game Phase: {phase}")
        
        if phase == "Endgame":
            lines.append("(King threats reduced in endgame)")
        else:
            lines.append("(King threats emphasized in middlegame)")
        lines.append("")
        
        # Threat breakdown
        for color in [chess.WHITE, chess.BLACK]:
            color_name = "WHITE" if color == chess.WHITE else "BLACK"
            lines.append(f"{color_name} THREATS:")
            lines.append("-" * 30)
            
            if threats[color]:
                threat_types = {}
                for threat in threats[color]:
                    threat_type = threat['type'].value
                    if threat_type not in threat_types:
                        threat_types[threat_type] = []
                    threat_types[threat_type].append(threat)
                
                for threat_type, threat_list in threat_types.items():
                    lines.append(f"  {threat_type.replace('_', ' ').title()}:")
                    total_value = sum(t['threat_value'] for t in threat_list)
                    lines.append(f"    Count: {len(threat_list)}, Total Value: {total_value}")
                    
                    for threat in threat_list:
                        lines.append(f"    • {threat['description']}")
                    lines.append("")
            else:
                lines.append("  No significant threats detected.")
                lines.append("")
        
        # Calculate and display accurate threat scores
        white_score = self.calculate_threat_score(threats[chess.WHITE])
        black_score = self.calculate_threat_score(threats[chess.BLACK])
        
        lines.append("SUMMARY:")
        lines.append(f"White threat score: {white_score}")
        lines.append(f"Black threat score: {black_score}")
        lines.append(f"Net advantage: {white_score - black_score:+d} (White perspective)")
        lines.append("")
        
        # Add balance note
        lines.append("IMPROVEMENTS MADE:")
        lines.append("• King threat values reduced to 10000 (from 20000)")
        lines.append("• Game phase-dependent threat adjustments")
        lines.append("• More balanced tactical evaluation")
        lines.append("• Better threat type weighting")
        lines.append("• Accurate fork value calculation")
        
        return "\n".join(lines)