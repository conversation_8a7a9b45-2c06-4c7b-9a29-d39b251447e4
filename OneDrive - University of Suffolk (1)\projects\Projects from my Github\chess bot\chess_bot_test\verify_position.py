#!/usr/bin/env python3
"""
Verify the actual position and piece locations
"""

import chess

def verify_position():
    print("Verifying the actual position")
    print("="*50)
    
    # Create the position
    board = chess.Board()
    moves = ["Nc3", "d5", "Nf3", "Nc6", "d4", "e6", "Ne5", "Nb4", "a3"]
    for move in moves:
        board.push_san(move)
    
    print("Board position:")
    print(board)
    print()
    print(f"FEN: {board.fen()}")
    print()
    
    # Check specific squares
    print("Key pieces:")
    print(f"a3: {board.piece_at(chess.A3)}")  # Should be white pawn
    print(f"b4: {board.piece_at(chess.B4)}")  # Should be black knight
    print(f"f8: {board.piece_at(chess.F8)}")  # Check what's on f8
    print()
    
    # Test attackers function more carefully
    print("Testing attackers function:")
    b4 = chess.B4
    
    # Get all squares that attack b4
    all_attackers = []
    for square in chess.SQUARES:
        piece = board.piece_at(square)
        if piece:
            # Check if this piece can attack b4
            if board.is_attacked_by(piece.color, b4):
                # Further verify by checking if piece on square attacks b4
                if square in board.attackers(piece.color, b4):
                    all_attackers.append((square, piece))
    
    print(f"All pieces that can attack b4:")
    for square, piece in all_attackers:
        square_name = chess.square_name(square)
        color = "White" if piece.color == chess.WHITE else "Black"
        print(f"  {piece.symbol()} on {square_name} ({color})")
    
    # Check what chess.py thinks about attackers
    white_attackers = board.attackers(chess.WHITE, b4)
    black_attackers = board.attackers(chess.BLACK, b4)
    
    print(f"\nchess.py says:")
    print(f"White attackers of b4: {[chess.square_name(sq) for sq in white_attackers]}")
    print(f"Black attackers of b4: {[chess.square_name(sq) for sq in black_attackers]}")
    
    # Manual geometric check for f8 to b4
    f8_piece = board.piece_at(chess.F8)
    if f8_piece:
        print(f"\nPiece on f8: {f8_piece.symbol()}")
        print(f"Can f8 piece attack b4? Manual check:")
        
        # Calculate if bishop on f8 can see b4
        f8_to_b4_is_diagonal = abs(chess.square_file(chess.F8) - chess.square_file(chess.B4)) == abs(chess.square_rank(chess.F8) - chess.square_rank(chess.B4))
        print(f"f8 to b4 is diagonal: {f8_to_b4_is_diagonal}")
        
        # Check for obstacles
        if f8_to_b4_is_diagonal:
            # Check if path is clear
            between_squares = list(chess.between(chess.F8, chess.B4))
            obstacles = [board.piece_at(sq) for sq in between_squares if board.piece_at(sq)]
            print(f"Squares between f8 and b4: {[chess.square_name(sq) for sq in between_squares]}")
            print(f"Obstacles: {obstacles}")
            print(f"Path is clear: {len(obstacles) == 0}")
    
    # Check if the knight is really under attack
    print(f"\nIs b4 actually under attack by white? {board.is_attacked_by(chess.WHITE, b4)}")
    print(f"Is b4 actually under attack by black? {board.is_attacked_by(chess.BLACK, b4)}")

if __name__ == "__main__":
    verify_position()