#!/usr/bin/env python3
"""
Debug minimax search to see why <PERSON> is choosing Qg5
"""

import chess
from unified_chess_bot import MediumBot

def trace_minimax_search():
    print("Tracing Minimax Search")
    print("="*50)
    
    # Create the problematic position
    board = chess.Board()
    moves = ["Nc3", "d5", "Nf3", "Nc6", "d4", "e6", "Ne5", "Nb4", "a3"]
    for move in moves:
        board.push_san(move)
    
    print("Position (Black to move):")
    print(board)
    print(f"Turn: {'White' if board.turn == chess.WHITE else 'Black'}")
    print()
    
    bot = MediumBot()
    bot.use_opening_book = False
    bot.use_iterative_deepening = False
    bot.depth = 1  # Shallow search to make it easy to trace
    
    print("Testing key moves with depth 1:")
    print("-" * 40)
    
    # Test key moves manually
    test_moves = ["Na6", "Nxc2+", "Qg5", "Qd6"]
    
    for move_san in test_moves:
        try:
            move = board.parse_san(move_san)
            
            # Manually call minimax
            board.push(move)
            
            # After Black plays, it's <PERSON>'s turn, so maximizing = True
            maximizing = board.turn == chess.WHITE
            eval_score, _ = bot._minimax(board, 0, float('-inf'), float('inf'), maximizing)
            
            board.pop()
            
            print(f"Move: {move_san}")
            print(f"  After move, turn: {'White' if board.turn == chess.WHITE else 'Black'}")
            print(f"  Maximizing: {maximizing}")
            print(f"  Eval from minimax: {eval_score}")
            print(f"  Direct eval: {bot.evaluate_board(board)}")
            print()
            
        except Exception as e:
            print(f"Error with {move_san}: {e}")
            print()
    
    # Now test the full search
    print("="*50)
    print("FULL MINIMAX SEARCH")
    print("="*50)
    
    # Black's turn, so maximizing = False
    maximizing = board.turn == chess.WHITE
    print(f"Root position: Black to move, maximizing = {maximizing}")
    
    # Run the search
    eval_score, best_move = bot._minimax(board, 1, float('-inf'), float('inf'), maximizing)
    
    print(f"Best move: {board.san(best_move) if best_move else 'None'}")
    print(f"Eval score: {eval_score}")
    
    # Let's manually trace the top-level moves
    print("\nManual trace of top-level moves:")
    print("-" * 40)
    
    legal_moves = list(board.legal_moves)
    move_evaluations = []
    
    for move in legal_moves[:10]:  # Test first 10 moves
        move_san = board.san(move)
        
        board.push(move)
        # After Black plays, it's White's turn
        child_maximizing = board.turn == chess.WHITE
        child_eval, _ = bot._minimax(board, 0, float('-inf'), float('inf'), child_maximizing)
        board.pop()
        
        move_evaluations.append((move_san, child_eval))
        print(f"  {move_san}: {child_eval}")
    
    # Sort by evaluation
    move_evaluations.sort(key=lambda x: x[1])
    
    print(f"\nBest moves for Black (lowest eval):")
    for move_san, eval_val in move_evaluations[:5]:
        print(f"  {move_san}: {eval_val}")
    
    print(f"\nWorst moves for Black (highest eval):")
    for move_san, eval_val in move_evaluations[-5:]:
        print(f"  {move_san}: {eval_val}")

if __name__ == "__main__":
    trace_minimax_search()