[sidebar: see prompt history in augment, zencoder etc. ]

https://claude.ai/chat/1710e007-a636-4665-9da1-3edfe7d00dd5

https://chat.deepseek.com/a/chat/s/37f5a849-2ab2-4475-adf5-bb3ad9982a6e



-----------------------

neural network:

https://chatgpt.com/c/684b9b9a-3968-800a-b575-06b54b02328e


---------------------

chess_gui_QS chess piece inconsistency / unalignment / chaotic behaviour :

https://gemini.google.com/app/c6acde13310e7bb1


-

rook back and forth instead of developing pawns:

https://gemini.google.com/app/f999751bcde9fb71



--------------------

undo/redo buttons correction:

https://gemini.google.com/app/c22bd1d8b7491833


------------------

eval score and thresholds:

https://gemini.google.com/app/f19fc048b7d35ca7

https://gemini.google.com/app/08d1d13b0a42c4ef


incorrect move quality ratings in opening moves:

https://aistudio.google.com/prompts/14ovZyxCX1yAZQluC_EGhXqhYDTRQKv2Q

 [eval leniency]

 https://chatgpt.com/c/68589f86-545c-800a-8e20-63af7585a968



analysis of evaluation function:

https://gemini.google.com/app/3299d2e1c1254bd0

https://gemini.google.com/app/2af8cbcab3a7bc8d

-----------------

code explanation:

https://chatgpt.com/c/685c4858-e1e8-800a-895d-bf6e234e31c5



-------------------

move quality ; thresholds:

https://gemini.google.com/app/e2b5b2403234313d


------------------------

search depth issue (note: an increased search depth either too slow or messes up the quality ratings - see comment):

https://gemini.google.com/app/87cdfb2bc50e033e


-----------------------

chess_gui_score3_QS.py (tactical evaluation - recalibrating thresholds)

https://gemini.google.com/app/12d6660008a4aecc


------------------------

threat analysis (tactical_evaluation.py)

https://gemini.google.com/app/843e336092b3df36

https://chatgpt.com/c/686e9d6f-b5ec-800a-9817-cbaaaf6586d1


-----------------------

tactical evaluation fixes:

https://chat.deepseek.com/a/chat/s/832a232c-fbb7-4492-8553-14e2ca6b7f5e
https://chat.deepseek.com/a/chat/s/f970ddda-9eea-4bd6-97ec-ae37247cf0d3

[file: tactical_evaluation_test - may need PR] 
https://chat.deepseek.com/a/chat/s/1a07579e-aef1-4163-bc83-72486c1ce235 
https://chat.deepseek.com/a/chat/s/9c320fdf-9f5f-49d7-83f0-d0e5c2107b97
(doing now)

issues to solve - move quality history rating [DONE] ; all moves being recorded as middlegame [DEEPSEEK COULD NOT DO]


-----------------------

about non-static evaluation function:

https://gemini.google.com/app/5901fd22cd5b4433


------------------------

unified bot - hanging knight piece problem:

https://gemini.google.com/app/0e7e7a2672df6221


----------------------

show hint - search depth:

https://gemini.google.com/app/c98c926a80c57572

------------------

only static evaluation of current position on the board being shown:

https://gemini.google.com/app/1dec1b31b9b1b4e7


-----------------

UI delays due to search depth:

https://chatgpt.com/c/688be742-d2a0-800a-a75b-7a8e2c8cef4a

====================================
====================================



learning about iterative deepening:

https://chatgpt.com/c/68897096-468c-800a-a4cd-49bef6236448


======================================

project planning:

https://chatgpt.com/c/688d4e73-019c-800a-881b-f5d58d827b3a