PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_score2.py
Move: e4
  Short-term: 0.00 -> 0.50
  Medium-term: 0.00 -> 1.45
  Long-term: 0.00 -> 1.45
  Trend: 0.00 -> 0.95
  Adjusted change: 1.74
ChessBot evaluated 811 positions in 0.28 seconds
Move: Nf6
  Short-term: 0.50 -> -0.49
  Medium-term: 1.45 -> -0.53
  Long-term: 1.45 -> -0.53
  Trend: 0.95 -> -0.04
  Adjusted change: -2.28
ChessBot search depth set to 2
Move: Nf3
  Short-term: -0.49 -> -0.59
  Medium-term: -0.53 -> 0.65
  Long-term: -0.53 -> 0.65
  Trend: -0.04 -> 1.24
  Adjusted change: 1.56
ChessBot evaluated 71 positions in 0.02 seconds
Move: Nxe4
  Short-term: -0.59 -> -1.48
  Medium-term: 0.65 -> -1.02
  Long-term: 0.65 -> -1.02
  Trend: 1.24 -> 0.46
  Adjusted change: -1.90
Move: d3
  Short-term: -1.48 -> -0.17
  Medium-term: -1.02 -> 0.55
  Long-term: -1.02 -> 0.55
  Trend: 0.46 -> 0.72
  Adjusted change: 1.65
ChessBot evaluated 176 positions in 0.04 seconds
Move: Nc5
  Short-term: -0.17 -> -1.24
  Medium-term: 0.55 -> -0.53
  Long-term: 0.55 -> -0.53
  Trend: 0.72 -> 0.71
  Adjusted change: -1.08
ChessBot evaluated 153 positions in 0.04 seconds
Move: d4
  Short-term: -1.24 -> -0.17
  Medium-term: -0.53 -> 1.18
  Long-term: -0.53 -> 1.18
  Trend: 0.71 -> 1.35
  Adjusted change: 1.90
ChessBot evaluated 186 positions in 0.08 seconds
Move: Ne4
  Short-term: -0.17 -> -1.16
  Medium-term: 1.18 -> -0.54
  Long-term: 1.18 -> -0.54
  Trend: 1.35 -> 0.62
  Adjusted change: -1.94

