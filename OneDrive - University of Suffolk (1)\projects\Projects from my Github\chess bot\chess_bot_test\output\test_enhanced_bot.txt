PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_enhanced_bot.py
============================================================
TESTING ENHANCED CHESS BOT EVALUATION
============================================================

1. Starting Position
----------------------------------------
Description: Standard starting position - should be roughly equal
FEN: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1

Position:
r n b q k b n r
p p p p p p p p
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . . . . .
P P P P P P P P
R N B Q K B N R

Evaluation: +0 centipawns
Evaluation time: 0.0020 seconds
Assessment: Position is roughly equal

Finding best move for White...
EnhancedBot evaluated 1147 positions in 1.92 seconds
Best move: e3
Move calculation time: 1.92 seconds

============================================================

2. Doubled Pawns Position
----------------------------------------
Description: White has doubled c-pawns after cxd5 cxd4
FEN: rnbqkbnr/ppp1pppp/8/3p4/2PP4/8/PP2PPPP/RNBQKBNR b KQkq c3 0 3

Position:
r n b q k b n r
p p p . p p p p
. . . . . . . .
. . . p . . . .
. . P P . . . .
. . . . . . . .
P P . . P P P P
R N B Q K B N R

Evaluation: +34 centipawns
Evaluation time: 0.0023 seconds
Assessment: Position is roughly equal

Finding best move for Black...
EnhancedBot evaluated 1786 positions in 3.67 seconds
Best move: dxc4
Move calculation time: 3.67 seconds

============================================================

3. Isolated Pawn Position
----------------------------------------
Description: Black has isolated d-pawn
FEN: rnbqkbnr/ppp1p1pp/5p2/3p4/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 0 3

Position:
r n b q k b n r
p p p . p . p p
. . . . . p . .
. . . p . . . .
. . . P . . . .
. . . . . . . .
P P P . P P P P
R N B Q K B N R

Evaluation: -33 centipawns
Evaluation time: 0.0020 seconds
Assessment: Position is roughly equal

Finding best move for White...
EnhancedBot evaluated 2231 positions in 6.96 seconds
Best move: Nc3
Move calculation time: 6.96 seconds

============================================================

4. Passed Pawn Position
----------------------------------------
Description: White has advanced passed pawn, black has far passed pawn
FEN: 8/8/8/3P4/8/8/p7/8 w - - 0 1

Position:
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . P . . . .
. . . . . . . .
. . . . . . . .
p . . . . . . .
. . . . . . . .

Evaluation: -26 centipawns
Evaluation time: 0.0010 seconds
Assessment: Position is roughly equal

Finding best move for White...
EnhancedBot evaluated 10 positions in 0.00 seconds
Best move: d6
Move calculation time: 0.00 seconds

============================================================

5. Bishop Pair Position
----------------------------------------
Description: Both sides have bishop pairs
FEN: rnbqk2r/pppp1ppp/5n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 4 4

Position:
r n b q k . . r
p p p p . p p p
. . . . . n . .
. . b . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q K . . R

Evaluation: +75 centipawns
Evaluation time: 0.0050 seconds
Assessment: White has a slight advantage

Finding best move for White...
EnhancedBot evaluated 4276 positions in 11.72 seconds
Best move: Nc3
Move calculation time: 11.72 seconds

============================================================

6. Open File for Rook
----------------------------------------
Description: Semi-open e-file available
FEN: r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3

Position:
r . b q k b n r
p p p p . p p p
. . n . . . . .
. . . . p . . .
. . . . P . . .
. . . . . N . .
P P P P . P P P
R N B Q K B . R

Evaluation: +2 centipawns
Evaluation time: 0.0020 seconds
Assessment: Position is roughly equal

Finding best move for White...
EnhancedBot evaluated 1398 positions in 3.42 seconds
Best move: Bb5
Move calculation time: 3.42 seconds

============================================================

7. Knight Outpost
----------------------------------------
Description: Knight on c3 could be an outpost
FEN: rnbqkb1r/ppp1pppp/5n2/3p4/3P4/2N5/PPP1PPPP/R1BQKBNR w KQkq - 2 3

Position:
r n b q k b . r
p p p . p p p p
. . . . . n . .
. . . p . . . .
. . . P . . . .
. . N . . . . .
P P P . P P P P
R . B Q K B N R

Evaluation: +18 centipawns
Evaluation time: 0.0050 seconds
Assessment: Position is roughly equal

Finding best move for White...
EnhancedBot evaluated 1918 positions in 7.60 seconds
Best move: Bg5
Move calculation time: 7.60 seconds

============================================================

8. King Safety Issue
----------------------------------------
Description: Both kings castled - good king safety
FEN: rnbq1rk1/ppp2ppp/3p1n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQ1RK1 w - - 0 6

Position:
r n b q . r k .
p p p . . p p p
. . . p . n . .
. . b . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q . R K .

Evaluation: +0 centipawns
Evaluation time: 0.0260 seconds
Assessment: Position is roughly equal

Finding best move for White...
EnhancedBot evaluated 2819 positions in 17.49 seconds
Best move: Bg5
Move calculation time: 17.49 seconds

============================================================

9. Exposed King
----------------------------------------
Description: Kings still in center - potential safety issues
FEN: rnbqkbnr/ppp2ppp/3p4/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 3

Position:
r n b q k b n r
p p p . . p p p
. . . p . . . .
. . . . p . . .
. . . . P . . .
. . . . . . . .
P P P P . P P P
R N B Q K B N R

Evaluation: -66 centipawns
Evaluation time: 0.0030 seconds
Assessment: Black has a slight advantage

Finding best move for White...
EnhancedBot evaluated 1282 positions in 5.32 seconds
Best move: Qh5
Move calculation time: 5.32 seconds

============================================================

10. Endgame Position
----------------------------------------
Description: King and pawn endgame - should use endgame king table
FEN: 8/8/8/3k4/3K4/8/8/8 w - - 0 1

Position:
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . k . . . .
. . . K . . . .
. . . . . . . .
. . . . . . . .
. . . . . . . .

Evaluation: +0 centipawns
Evaluation time: 0.0000 seconds
Assessment: Position is roughly equal

============================================================

TESTING TACTICAL AWARENESS
============================================================

1. Simple Fork
----------------------------------------
Description: Look for knight fork opportunities
Position:
r n b q k b . r
p p p p . p p p
. . . . . n . .
. . . . p . . .
. . . . P . . .
. . . . . . . .
P P P P . P P P
R N B Q K B N R
Evaluation: -66
TacticalBot evaluated 6649 positions in 14.95 seconds
Bot suggests: Nf3
Improvement: +66 centipawns

========================================

2. Pin Position
----------------------------------------
Description: Bishop can pin knight to king
Position:
r n b q k b n r
p p p . p p p p
. . . . . . . .
. . . p . . . .
. . . P P . . .
. . . . . . . .
P P P . . P P P
R N B Q K B N R
Evaluation: +45
TacticalBot evaluated 12550 positions in 35.72 seconds
Bot suggests: c6
Improvement: +37 centipawns

========================================

3. Discovered Attack
----------------------------------------
Description: Knight move could create discovered attack
Position:
r . b q k b n r
p p p p . p p p
. . n . . . . .
. . . . p . . .
. . . . P . . .
. . . . . N . .
P P P P . P P P
R N B Q K B . R
Evaluation: +2
TacticalBot evaluated 12457 positions in 57.20 seconds
Bot suggests: Bb5
Improvement: +27 centipawns

========================================

COMPARING ENHANCED VS SIMPLE EVALUATION
============================================================
Test Position:
r n b q k b . r
p p p . p p p p
. . . . . n . .
. . . p . . . .
. . . P . . . .
. . N . . . . .
P P P . P P P P
R . B Q K B N R

Enhanced Bot Evaluation: +18
Simple Bot Evaluation: +0
Difference: +18
Enhanced evaluated 1918 positions in 9.17 seconds
Simple evaluated 1109 positions in 0.18 seconds

Enhanced Bot Move: Bg5
Simple Bot Move: Nb5

============================================================
TESTING COMPLETE!
The enhanced bot now includes:
✓ Pawn structure evaluation (doubled, isolated, passed pawns)
✓ Piece activity (bishop pair, rook on open files, knight outposts)
✓ Advanced king safety (pawn shield, open files)
✓ Tactical threat detection (pins, forks)
✓ Control of key squares and files
✓ Endgame vs middlegame awareness
============================================================
























============================================================
TESTING ENHANCED CHESS BOT EVALUATION
============================================================

1. Starting Position
----------------------------------------
Description: Standard starting position - should be roughly equal
FEN: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1

Position:
r n b q k b n r
p p p p p p p p
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . . . . .
P P P P P P P P
R N B Q K B N R

Evaluation: +0 centipawns
Evaluation time: 0.0012 seconds
Assessment: Position is roughly equal

Finding best move for White...
EnhancedBot evaluated 1173 positions in 0.92 seconds
Best move: Nc3
Move calculation time: 0.92 seconds

============================================================

2. Doubled Pawns Position
----------------------------------------
Description: White has doubled c-pawns after cxd5 cxd4
FEN: rnbqkbnr/ppp1pppp/8/3p4/2PP4/8/PP2PPPP/RNBQKBNR b KQkq c3 0 3

Position:
r n b q k b n r
p p p . p p p p
. . . . . . . .
. . . p . . . .
. . P P . . . .
. . . . . . . .
P P . . P P P P
R N B Q K B N R

Evaluation: +34 centipawns
Evaluation time: 0.0020 seconds
Assessment: Position is roughly equal

Finding best move for Black...
EnhancedBot evaluated 2362 positions in 2.37 seconds
Best move: c5
Move calculation time: 2.37 seconds

============================================================

3. Isolated Pawn Position
----------------------------------------
Description: Black has isolated d-pawn
FEN: rnbqkbnr/ppp1p1pp/5p2/3p4/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 0 3

Position:
r n b q k b n r
p p p . p . p p
. . . . . p . .
. . . p . . . .
. . . P . . . .
. . . . . . . .
P P P . P P P P
R N B Q K B N R

Evaluation: -33 centipawns
Evaluation time: 0.0010 seconds
Assessment: Position is roughly equal

Finding best move for White...
EnhancedBot evaluated 1947 positions in 3.08 seconds
Best move: e3
Move calculation time: 3.08 seconds

============================================================

4. Passed Pawn Position
----------------------------------------
Description: White has advanced passed pawn, black has far passed pawn
FEN: 8/8/8/3P4/8/8/p7/8 w - - 0 1

Position:
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . P . . . .
. . . . . . . .
. . . . . . . .
p . . . . . . .
. . . . . . . .

Evaluation: -26 centipawns
Evaluation time: 0.0010 seconds
Assessment: Position is roughly equal

Finding best move for White...
EnhancedBot evaluated 10 positions in 0.00 seconds
Best move: d6
Move calculation time: 0.00 seconds

============================================================

5. Bishop Pair Position
----------------------------------------
Description: Both sides have bishop pairs
FEN: rnbqk2r/pppp1ppp/5n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 4 4

Position:
r n b q k . . r
p p p p . p p p
. . . . . n . .
. . b . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q K . . R

Evaluation: +75 centipawns
Evaluation time: 0.0020 seconds
Assessment: White has a slight advantage

Finding best move for White...
EnhancedBot evaluated 3126 positions in 4.84 seconds
Best move: Bxf7+
Move calculation time: 4.85 seconds

============================================================

6. Open File for Rook
----------------------------------------
Description: Semi-open e-file available
FEN: r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3

Position:
r . b q k b n r
p p p p . p p p
. . n . . . . .
. . . . p . . .
. . . . P . . .
. . . . . N . .
P P P P . P P P
R N B Q K B . R

Evaluation: +2 centipawns
Evaluation time: 0.0020 seconds
Assessment: Position is roughly equal

Finding best move for White...
EnhancedBot evaluated 1328 positions in 1.97 seconds
Best move: Bb5
Move calculation time: 1.97 seconds

============================================================

7. Knight Outpost
----------------------------------------
Description: Knight on c3 could be an outpost
FEN: rnbqkb1r/ppp1pppp/5n2/3p4/3P4/2N5/PPP1PPPP/R1BQKBNR w KQkq - 2 3

Position:
r n b q k b . r
p p p . p p p p
. . . . . n . .
. . . p . . . .
. . . P . . . .
. . N . . . . .
P P P . P P P P
R . B Q K B N R

Evaluation: +18 centipawns
Evaluation time: 0.0025 seconds
Assessment: Position is roughly equal

Finding best move for White...
EnhancedBot evaluated 1476 positions in 2.92 seconds
Best move: Bg5
Move calculation time: 2.94 seconds

============================================================

8. King Safety Issue
----------------------------------------
Description: Both kings castled - good king safety
FEN: rnbq1rk1/ppp2ppp/3p1n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQ1RK1 w - - 0 6

Position:
r n b q . r k .
p p p . . p p p
. . . p . n . .
. . b . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q . R K .

Evaluation: +0 centipawns
Evaluation time: 0.0017 seconds
Assessment: Position is roughly equal

Finding best move for White...
EnhancedBot evaluated 1930 positions in 3.02 seconds
Best move: Nxe5
Move calculation time: 3.02 seconds

============================================================

9. Exposed King
----------------------------------------
Description: Kings still in center - potential safety issues
FEN: rnbqkbnr/ppp2ppp/3p4/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 3

Position:
r n b q k b n r
p p p . . p p p
. . . p . . . .
. . . . p . . .
. . . . P . . .
. . . . . . . .
P P P P . P P P
R N B Q K B N R

Evaluation: -66 centipawns
Evaluation time: 0.0020 seconds
Assessment: Black has a slight advantage

Finding best move for White...
EnhancedBot evaluated 2261 positions in 2.61 seconds
Best move: Qf3
Move calculation time: 3.58 seconds

============================================================

10. Endgame Position
----------------------------------------
Description: King and pawn endgame - should use endgame king table
FEN: 8/8/8/3k4/3K4/8/8/8 w - - 0 1

Position:
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . k . . . .
. . . K . . . .
. . . . . . . .
. . . . . . . .
. . . . . . . .

Evaluation: +0 centipawns
Evaluation time: 0.0000 seconds
Assessment: Position is roughly equal

============================================================

TESTING TACTICAL AWARENESS
============================================================

1. Simple Fork
----------------------------------------
Description: Look for knight fork opportunities
Position:
r n b q k b . r
p p p p . p p p
. . . . . n . .
. . . . p . . .
. . . . P . . .
. . . . . . . .
P P P P . P P P
R N B Q K B N R
Evaluation: -66
TacticalBot evaluated 9311 positions in 9.51 seconds
Bot suggests: Qf3
Improvement: +46 centipawns

========================================

2. Pin Position
----------------------------------------
Description: Bishop can pin knight to king
Position:
r n b q k b n r
p p p . p p p p
. . . . . . . .
. . . p . . . .
. . . P P . . .
. . . . . . . .
P P P . . P P P
R N B Q K B N R
Evaluation: +45
TacticalBot evaluated 15652 positions in 15.03 seconds
Bot suggests: c6
Improvement: +37 centipawns

========================================

3. Discovered Attack
----------------------------------------
Description: Knight move could create discovered attack
Position:
r . b q k b n r
p p p p . p p p
. . n . . . . .
. . . . p . . .
. . . . P . . .
. . . . . N . .
P P P P . P P P
R N B Q K B . R
Evaluation: +2
TacticalBot evaluated 19001 positions in 21.03 seconds
Bot suggests: Bb5
Improvement: +27 centipawns

========================================

COMPARING ENHANCED VS SIMPLE EVALUATION
============================================================
Test Position:
r n b q k b . r
p p p . p p p p
. . . . . n . .
. . . p . . . .
. . . P . . . .
. . N . . . . .
P P P . P P P P
R . B Q K B N R

Enhanced Bot Evaluation: +18
Simple Bot Evaluation: +0
Difference: +18
Enhanced evaluated 1476 positions in 1.31 seconds
Simple evaluated 1109 positions in 0.22 seconds

Enhanced Bot Move: Bg5
Simple Bot Move: Nb5

============================================================
TESTING COMPLETE!
The enhanced bot now includes:
✓ Pawn structure evaluation (doubled, isolated, passed pawns)
✓ Piece activity (bishop pair, rook on open files, knight outposts)
✓ Advanced king safety (pawn shield, open files)
✓ Tactical threat detection (pins, forks)
✓ Control of key squares and files
✓ Endgame vs middlegame awareness
============================================================