#!/usr/bin/env python3
"""
Unified Chess Bot Launcher - Central access point for all chess bot components
"""

import sys
import os
import subprocess
import time
from typing import Optional

def print_header():
    """Print the application header."""
    print("\n" + "="*70)
    print("🏛️  UNIFIED CHESS BOT - ADVANCED CHESS AI SYSTEM  🏛️")
    print("="*70)
    print("A comprehensive chess bot with multiple difficulty levels")
    print("Features: Minimax, Alpha-Beta, Iterative Deepening, Quiescence Search")
    print("Transposition Tables, Advanced Evaluation, and Opening Book")
    print("="*70)

def print_main_menu():
    """Display the main menu."""
    print("\n📋 MAIN MENU")
    print("-" * 30)
    print("1. 🎮 Play Chess (GUI)")
    print("2. 🖥️  Play Chess (CLI)")
    print("3. 🧪 Run Tests")
    print("4. 📊 Performance Benchmark")
    print("5. 🏆 Bot vs Bot Demo")
    print("6. ⚙️  Configuration")
    print("7. ℹ️  Help & Information")
    print("8. 🚪 Exit")
    print("-" * 30)

def check_dependencies():
    """Check if all required dependencies are available."""
    print("🔍 Checking dependencies...")
    
    missing_deps = []
    
    try:
        import chess
        print("  ✅ python-chess: OK")
    except ImportError:
        missing_deps.append("python-chess")
        print("  ❌ python-chess: MISSING")
    
    try:
        import tkinter
        print("  ✅ tkinter: OK")
    except ImportError:
        missing_deps.append("tkinter")
        print("  ❌ tkinter: MISSING")
    
    # Check if our unified modules are available
    try:
        from unified_chess_bot import UnifiedChessBot
        print("  ✅ unified_chess_bot: OK")
    except ImportError:
        print("  ❌ unified_chess_bot: MISSING")
        print("     Make sure unified_chess_bot.py is in the current directory")
        return False
    
    try:
        from unified_chess_gui import UnifiedChessGUI
        print("  ✅ unified_chess_gui: OK")
    except ImportError:
        print("  ❌ unified_chess_gui: MISSING")
        print("     Make sure unified_chess_gui.py is in the current directory")
        return False
    
    if missing_deps:
        print(f"\n❌ Missing dependencies: {', '.join(missing_deps)}")
        print("\n📦 To install missing dependencies:")
        if "python-chess" in missing_deps:
            print("   pip install python-chess")
        if "tkinter" in missing_deps:
            print("   tkinter usually comes with Python. Try reinstalling Python.")
        return False
    
    print("  ✅ All dependencies satisfied!")
    return True

def launch_gui():
    """Launch the unified GUI."""
    print("\n🎮 Launching Unified Chess GUI...")
    print("Loading advanced graphical interface...")
    
    try:
        from unified_chess_gui import main as gui_main
        gui_main()
        return True
    except ImportError as e:
        print(f"❌ Error: Could not import GUI: {e}")
        return False
    except Exception as e:
        print(f"❌ Error running GUI: {e}")
        return False

def launch_cli():
    """Launch the CLI version."""
    print("\n🖥️  Launching CLI Chess...")
    print("Loading command line interface...")
    
    try:
        # Try to use existing CLI or create a simple one
        try:
            from main import main as cli_main
            cli_main()
        except ImportError:
            # Create a simple CLI interface
            create_simple_cli()
        return True
    except Exception as e:
        print(f"❌ Error running CLI: {e}")
        return False

def create_simple_cli():
    """Create a simple CLI interface using the unified bot."""
    import chess
    from unified_chess_bot import UnifiedChessBot
    from utils import format_board_unicode, parse_move_input
    
    print("\n♟️  UNIFIED CHESS BOT - CLI MODE")
    print("=" * 40)
    print("Commands: move (e.g., e2e4, Nf3), quit, help, undo, new")
    print("=" * 40)
    
    board = chess.Board()
    bot = UnifiedChessBot(depth=3, name="UnifiedBot")
    game_history = []
    
    # Choose color
    while True:
        color_choice = input("\nChoose your color (w/white or b/black): ").lower()
        if color_choice in ['w', 'white']:
            human_color = chess.WHITE
            print("You are playing as White")
            break
        elif color_choice in ['b', 'black']:
            human_color = chess.BLACK
            print("You are playing as Black")
            break
        else:
            print("Please enter 'w' for white or 'b' for black")
    
    while True:
        # Display board
        print("\n" + format_board_unicode(board))
        
        # Show game status
        if board.is_checkmate():
            winner = "Black" if board.turn == chess.WHITE else "White"
            print(f"\n🏆 Checkmate! {winner} wins!")
            break
        elif board.is_stalemate():
            print("\n🤝 Stalemate! Draw!")
            break
        elif board.is_insufficient_material():
            print("\n🤝 Insufficient material! Draw!")
            break
        elif board.is_check():
            print(f"\n⚠️  {'White' if board.turn == chess.WHITE else 'Black'} is in check!")
        
        print(f"\n🔄 {'White' if board.turn == chess.WHITE else 'Black'} to move")
        
        if board.turn == human_color:
            # Human turn
            while True:
                user_input = input("Enter your move: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("Thanks for playing!")
                    return
                elif user_input.lower() == 'help':
                    print("\nCommands:")
                    print("  - Enter moves in algebraic notation (e.g., e4, Nf3, O-O)")
                    print("  - Or use coordinate notation (e.g., e2e4)")
                    print("  - 'undo' - undo last move")
                    print("  - 'new' - start new game")
                    print("  - 'quit' - exit game")
                    continue
                elif user_input.lower() == 'undo':
                    if len(game_history) >= 2:
                        # Undo both human and bot moves
                        board.pop()
                        board.pop()
                        game_history = game_history[:-2]
                        print("Move undone!")
                        break
                    else:
                        print("No moves to undo!")
                        continue
                elif user_input.lower() == 'new':
                    board = chess.Board()
                    game_history = []
                    print("New game started!")
                    break
                
                # Try to parse the move
                move = parse_move_input(user_input, board)
                if move and move in board.legal_moves:
                    san_move = board.san(move)
                    board.push(move)
                    game_history.append(san_move)
                    print(f"You played: {san_move}")
                    break
                else:
                    print("Invalid move! Try again.")
        else:
            # Bot turn
            print("🤖 Bot is thinking...")
            move = bot.get_best_move(board)
            if move:
                san_move = board.san(move)
                board.push(move)
                game_history.append(san_move)
                print(f"Bot played: {san_move}")
            else:
                print("Bot couldn't find a move!")
                break

def run_tests():
    """Run the test suite."""
    print("\n🧪 Running Unified Chess Bot Tests...")
    print("=" * 50)
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, "test_unified_chess_bot.py"], 
                              capture_output=True, text=True, cwd=".")
        
        print(result.stdout)
        if result.stderr:
            print("Errors:")
            print(result.stderr)
        
        print(f"\nTest execution completed with return code: {result.returncode}")
        
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        print("Trying to run tests directly...")
        
        try:
            import test_unified_chess_bot
            # This would run the tests if imported correctly
            print("✅ Test module imported successfully")
        except ImportError as ie:
            print(f"❌ Could not import test module: {ie}")

def run_benchmark():
    """Run performance benchmark."""
    print("\n📊 Running Performance Benchmark...")
    print("=" * 40)
    
    try:
        from unified_chess_bot import EasyBot, MediumBot, HardBot, ExpertBot
        import chess
        import time
        
        configurations = [
            ("EasyBot", EasyBot()),
            ("MediumBot", MediumBot()),
            ("HardBot", HardBot()),
            ("ExpertBot", ExpertBot())
        ]
        
        board = chess.Board()
        
        for name, bot in configurations:
            print(f"\n🤖 Testing {name}:")
            print("-" * 20)
            
            start_time = time.time()
            move = bot.get_best_move(board)
            end_time = time.time()
            
            elapsed_time = end_time - start_time
            
            print(f"⏱️  Time: {elapsed_time:.2f} seconds")
            print(f"🧮 Nodes evaluated: {bot.nodes_evaluated:,}")
            print(f"🔍 Quiescence nodes: {bot.quiescence_nodes:,}")
            
            if bot.use_transposition_table:
                print(f"📋 Transposition hits: {bot.transposition_hits:,}")
                print(f"💾 Table size: {len(bot.transposition_table):,}")
            
            if elapsed_time > 0:
                nps = bot.nodes_evaluated / elapsed_time
                print(f"⚡ Nodes per second: {nps:,.0f}")
            
            print(f"♟️  Best move: {move}")
        
        print("\n✅ Benchmark completed!")
        
    except Exception as e:
        print(f"❌ Error running benchmark: {e}")

def run_bot_vs_bot_demo():
    """Run a bot vs bot demonstration."""
    print("\n🏆 Bot vs Bot Demonstration")
    print("=" * 35)
    
    try:
        from unified_chess_bot import MediumBot, HardBot
        from utils import format_board_unicode
        import chess
        import time
        
        print("Setting up: MediumBot (White) vs HardBot (Black)")
        
        board = chess.Board()
        white_bot = MediumBot()
        black_bot = HardBot()
        
        white_bot.name = "MediumBot (White)"
        black_bot.name = "HardBot (Black)"
        
        game_history = []
        move_count = 0
        max_moves = 50  # Limit for demo
        
        print(f"\n🎯 Game starting! (Limited to {max_moves} moves)")
        input("Press Enter to start...")
        
        while not board.is_game_over() and move_count < max_moves:
            current_bot = white_bot if board.turn == chess.WHITE else black_bot
            
            print(f"\n--- Move {move_count + 1} ---")
            print(f"🤖 {current_bot.name} thinking...")
            
            start_time = time.time()
            move = current_bot.get_best_move(board)
            end_time = time.time()
            
            if move:
                san_move = board.san(move)
                board.push(move)
                game_history.append(san_move)
                
                print(f"Move: {san_move} (took {end_time - start_time:.2f}s)")
                print(f"Nodes: {current_bot.nodes_evaluated:,}")
                
                # Show board every few moves
                if move_count % 5 == 0 or move_count < 10:
                    print("\nCurrent position:")
                    print(format_board_unicode(board))
                
                move_count += 1
                
                # Small delay for readability
                time.sleep(0.5)
            else:
                print("❌ Bot couldn't find a move!")
                break
        
        # Final position
        print("\n🏁 Final Position:")
        print(format_board_unicode(board))
        
        # Game result
        if board.is_checkmate():
            winner = "MediumBot (White)" if board.turn == chess.BLACK else "HardBot (Black)"
            print(f"\n🏆 Checkmate! {winner} wins!")
        elif board.is_stalemate():
            print("\n🤝 Stalemate! Draw!")
        elif board.is_insufficient_material():
            print("\n🤝 Insufficient material! Draw!")
        elif move_count >= max_moves:
            print(f"\n⏰ Demo completed ({max_moves} moves reached)")
        
        print(f"\nMoves played: {len(game_history)}")
        print("Game history:", " ".join(game_history))
        
    except Exception as e:
        print(f"❌ Error running bot vs bot demo: {e}")

def show_configuration():
    """Show configuration options."""
    print("\n⚙️  CONFIGURATION OPTIONS")
    print("=" * 30)
    
    try:
        from unified_chess_bot import UnifiedChessBot
        
        print("🤖 Available Bot Features:")
        print("  • Minimax with Alpha-Beta Pruning")
        print("  • Iterative Deepening")
        print("  • Quiescence Search")
        print("  • Transposition Tables")
        print("  • Advanced Position Evaluation")
        print("  • Opening Book")
        
        print("\n🎯 Difficulty Presets:")
        print("  • EasyBot: Depth 2, basic features")
        print("  • MediumBot: Depth 3, all features")
        print("  • HardBot: Depth 4, all features")
        print("  • ExpertBot: Depth 5, enhanced features")
        
        print("\n⚡ Performance Settings:")
        print("  • Search depth: 1-8 (higher = stronger but slower)")
        print("  • Quiescence depth: 4-12 (tactical search depth)")
        print("  • Transposition table: Improves search efficiency")
        
        print("\n🎮 GUI Features:")
        print("  • Visual chess board with drag-and-drop")
        print("  • Real-time position evaluation")
        print("  • Move history and analysis")
        print("  • Game saving/loading (PGN format)")
        print("  • Undo/redo functionality")
        print("  • Bot statistics display")
        
        # Demo bot creation
        print("\n🔧 Example: Creating a custom bot")
        bot = UnifiedChessBot(
            depth=4,
            name="CustomBot",
            use_iterative_deepening=True,
            use_quiescence_search=True,
            use_transposition_table=True
        )
        print(f"  Created: {bot.name} with depth {bot.depth}")
        print(f"  Features: ID={bot.use_iterative_deepening}, "
              f"QS={bot.use_quiescence_search}, "
              f"TT={bot.use_transposition_table}")
        
    except Exception as e:
        print(f"❌ Error displaying configuration: {e}")

def show_help():
    """Show help and information."""
    print("\n ℹ️  HELP & INFORMATION")
    print("=" * 25)
    
    print("🎯 ABOUT UNIFIED CHESS BOT")
    print("A comprehensive chess AI system featuring:")
    print("• Advanced minimax algorithm with alpha-beta pruning")
    print("• Multiple difficulty levels with different capabilities")
    print("• Both graphical (GUI) and command-line (CLI) interfaces")
    print("• Comprehensive testing and benchmarking tools")
    
    print("\n🎮 HOW TO PLAY")
    print("1. Choose 'Play Chess (GUI)' for the best experience")
    print("2. Select your difficulty level and bot features")
    print("3. Click on pieces to move them")
    print("4. Use undo/redo, save/load, and analysis features")
    
    print("\n🤖 BOT DIFFICULTIES")
    print("• Easy: Quick moves, good for beginners")
    print("• Medium: Balanced play, good for intermediate players")
    print("• Hard: Strong tactical play, challenging")
    print("• Expert: Very strong play, tournament level")
    
    print("\n🛠️  TECHNICAL FEATURES")
    print("• Iterative Deepening: Gradually increases search depth")
    print("• Quiescence Search: Analyzes tactical sequences")
    print("• Transposition Tables: Avoids re-calculating positions")
    print("• Advanced Evaluation: Considers material, position, mobility")
    print("• Opening Book: Plays common opening moves")
    
    print("\n🧪 TESTING & BENCHMARKING")
    print("• Run Tests: Verify all bot functionality works correctly")
    print("• Performance Benchmark: Compare different bot configurations")
    print("• Bot vs Bot Demo: Watch bots play against each other")
    
    print("\n📝 MOVE NOTATION")
    print("• Standard Algebraic: e4, Nf3, O-O, Qxd5")
    print("• Coordinate notation: e2e4, g1f3")
    print("• Special moves: O-O (kingside castle), O-O-O (queenside)")
    
    print("\n💡 TIPS")
    print("• Start with Medium difficulty for balanced games")
    print("• Use the GUI for the best visual experience")
    print("• Enable position evaluation to see bot's assessment")
    print("• Try different bot features to see their effects")
    
    print("\n📋 SYSTEM REQUIREMENTS")
    print("• Python 3.7 or higher")
    print("• python-chess library (pip install python-chess)")
    print("• tkinter (usually included with Python)")

def main():
    """Main launcher function."""
    print_header()
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Please install missing dependencies and try again.")
        input("Press Enter to exit...")
        sys.exit(1)
    
    time.sleep(1)  # Brief pause for readability
    
    while True:
        print_main_menu()
        
        try:
            choice = input("Enter your choice (1-8): ").strip()
            
            if choice == '1':
                # Launch GUI
                success = launch_gui()
                if success:
                    print("✅ GUI session completed.")
                else:
                    print("❌ GUI session encountered an error.")
                
                input("\\nPress Enter to return to main menu...")
            
            elif choice == '2':
                # Launch CLI
                success = launch_cli()
                if success:
                    print("✅ CLI session completed.")
                else:
                    print("❌ CLI session encountered an error.")
                
                input("\\nPress Enter to return to main menu...")
            
            elif choice == '3':
                # Run tests
                run_tests()
                input("\\nPress Enter to return to main menu...")
            
            elif choice == '4':
                # Performance benchmark
                run_benchmark()
                input("\\nPress Enter to return to main menu...")
            
            elif choice == '5':
                # Bot vs bot demo
                run_bot_vs_bot_demo()
                input("\\nPress Enter to return to main menu...")
            
            elif choice == '6':
                # Configuration
                show_configuration()
                input("\\nPress Enter to return to main menu...")
            
            elif choice == '7':
                # Help
                show_help()
                input("\\nPress Enter to return to main menu...")
            
            elif choice == '8':
                # Exit
                print("\\n👋 Thanks for using Unified Chess Bot!")
                print("   May your games be ever victorious! ♟️")
                sys.exit(0)
            
            else:
                print("❌ Invalid choice. Please enter 1-8.")
        
        except KeyboardInterrupt:
            print("\\n\\n👋 Goodbye! Thanks for using Unified Chess Bot!")
            sys.exit(0)
        except Exception as e:
            print(f"❌ An error occurred: {e}")
            print("Please try again.")

if __name__ == "__main__":
    main()