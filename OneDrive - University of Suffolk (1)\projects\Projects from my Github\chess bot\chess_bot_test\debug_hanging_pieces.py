#!/usr/bin/env python3
"""
Debug script to understand why hanging piece detection isn't working
"""

import chess
from unified_chess_bot import MediumBot

def debug_hanging_detection():
    """Debug the hanging piece detection"""
    
    print("Debugging Hanging Piece Detection")
    print("="*50)
    
    # Create the problematic position
    board = chess.Board()
    moves = ["Nc3", "d5", "Nf3", "Nc6", "d4", "e6", "Ne5", "Nb4", "a3"]
    for move in moves:
        board.push_san(move)
    
    print(f"Position: {board.fen()}")
    print(f"Black to move")
    print()
    
    # Check each square manually
    print("Checking all pieces for hanging status:")
    print("-" * 40)
    
    for square in chess.SQUARES:
        piece = board.piece_at(square)
        if piece is None:
            continue
            
        square_name = chess.square_name(square)
        piece_symbol = piece.symbol()
        piece_color = "White" if piece.color == chess.WHITE else "Black"
        
        # Check attackers
        attackers = board.attackers(not piece.color, square)
        defenders = board.attackers(piece.color, square)
        
        attacker_squares = [chess.square_name(sq) for sq in attackers]
        defender_squares = [chess.square_name(sq) for sq in defenders]
        
        if attackers or defenders:
            print(f"{piece_symbol} on {square_name} ({piece_color}):")
            print(f"  Attackers: {attacker_squares if attacker_squares else 'None'}")
            print(f"  Defenders: {defender_squares if defender_squares else 'None'}")
            
            if len(attackers) > len(defenders):
                hanging_status = "HANGING!" if len(defenders) == 0 else "UNDER-DEFENDED!"
                print(f"  Status: {hanging_status}")
            else:
                print(f"  Status: Safe")
            print()
    
    # Test the specific knight on b4
    print("="*50)
    print("SPECIFIC TEST: Knight on b4")
    print("="*50)
    
    b4_square = chess.B4
    knight = board.piece_at(b4_square)
    
    if knight:
        print(f"Piece on b4: {knight.symbol()}")
        print(f"Piece color: {'White' if knight.color == chess.WHITE else 'Black'}")
        
        # Check who's attacking it
        attackers = board.attackers(chess.WHITE, b4_square)  # White attacking black piece
        defenders = board.attackers(chess.BLACK, b4_square)  # Black defending black piece
        
        print(f"White attackers of b4: {[chess.square_name(sq) for sq in attackers]}")
        print(f"Black defenders of b4: {[chess.square_name(sq) for sq in defenders]}")
        
        if attackers and not defenders:
            print("✅ Knight IS hanging - should be detected!")
        elif len(attackers) > len(defenders):
            print("✅ Knight IS under-defended - should be detected!")
        else:
            print("❌ Knight appears safe according to this analysis")
    else:
        print("❌ No piece found on b4!")
    
    # Test the hanging piece function directly
    print("\n" + "="*50)
    print("TESTING HANGING PIECE FUNCTION")
    print("="*50)
    
    bot = MediumBot()
    hanging_score = bot._evaluate_hanging_pieces(board)
    print(f"Hanging piece function returned: {hanging_score}")
    
    # Let's manually step through the function
    print("\nManually stepping through the function:")
    score = 0
    
    for square in chess.SQUARES:
        piece = board.piece_at(square)
        if piece is None:
            continue
            
        # Check if piece is attacked
        attackers = board.attackers(not piece.color, square)
        if not attackers:
            continue  # Piece is not attacked
            
        # Check if piece is defended
        defenders = board.attackers(piece.color, square)
        
        square_name = chess.square_name(square)
        print(f"  {piece.symbol()} on {square_name}: {len(attackers)} attackers, {len(defenders)} defenders")
        
        if len(attackers) > len(defenders):
            # Piece is hanging or under-defended
            piece_value = bot.piece_values[piece.piece_type]
            
            # Heavy penalty for hanging pieces
            if len(defenders) == 0:
                penalty = piece_value * 2
                print(f"    HANGING! Penalty: {penalty}")
            else:
                penalty = piece_value // 2
                print(f"    UNDER-DEFENDED! Penalty: {penalty}")
            
            if piece.color == chess.WHITE:
                score -= penalty
            else:
                score += penalty
    
    print(f"\nManual calculation result: {score}")
    print(f"Function returned: {hanging_score}")
    
    if score != hanging_score:
        print("❌ MISMATCH! There's a bug in the function.")
    else:
        print("✅ Function is working correctly.")

if __name__ == "__main__":
    debug_hanging_detection()