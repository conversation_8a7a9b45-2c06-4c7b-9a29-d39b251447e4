
"""
Test the improved exchange detection
"""

import chess
from unified_chess_bot import MediumBot

def test_exchange_detection():
    print("Testing Improved Exchange Detection")
    print("="*50)
    
    # Create the problematic position
    board = chess.Board()
    moves = ["Nc3", "d5", "Nf3", "Nc6", "d4", "e6", "Ne5", "Nb4", "a3"]
    for move in moves:
        board.push_san(move)
    
    print("Position after a3 (attacking the knight):")
    print(board)
    print()
    
    # Test the bot's evaluation
    bot = MediumBot()
    bot.use_opening_book = False
    
    # Check hanging piece evaluation
    hanging_score = bot._evaluate_hanging_pieces(board)
    print(f"Hanging piece evaluation: {hanging_score}")
    
    # Manual calculation for the knight on b4
    print("\nManual analysis of knight on b4:")
    b4 = chess.B4
    knight = board.piece_at(b4)
    
    if knight:
        attackers = board.attackers(chess.WHITE, b4)
        defenders = board.attackers(chess.BLACK, b4)
        
        print(f"Knight value: {bot.piece_values[knight.piece_type]}")
        print(f"Attackers: {[chess.square_name(sq) for sq in attackers]}")
        print(f"Defenders: {[chess.square_name(sq) for sq in defenders]}")
        
        if attackers:
            # Find minimum attacker value
            min_attacker_value = float('inf')
            for attacker_square in attackers:
                attacker_piece = board.piece_at(attacker_square)
                if attacker_piece:
                    attacker_value = bot.piece_values[attacker_piece.piece_type]
                    print(f"  Attacker {chess.square_name(attacker_square)}: {attacker_piece.symbol()} (value {attacker_value})")
                    min_attacker_value = min(min_attacker_value, attacker_value)
            
            knight_value = bot.piece_values[knight.piece_type]
            print(f"Minimum attacker value: {min_attacker_value}")
            print(f"Knight value: {knight_value}")
            
            if min_attacker_value < knight_value:
                exchange_loss = knight_value - min_attacker_value
                penalty = exchange_loss * 1.5
                print(f"Exchange loss: {exchange_loss}")
                print(f"Penalty for Black: {penalty}")
                print("✅ UNFAVORABLE EXCHANGE DETECTED!")
            else:
                print("❌ Exchange appears favorable or equal")
    
    # Test bot's move choice
    print(f"\n{'='*50}")
    print("Bot's Move Choice:")
    print("="*50)
    
    overall_eval = bot.evaluate_board(board)
    print(f"Overall position evaluation: {overall_eval}")
    
    best_move = bot.get_best_move(board)
    print(f"Bot's chosen move: {best_move}")
    
    if best_move:
        san_move = board.san(best_move)
        print(f"Move in notation: {san_move}")
        
        # Check if it's moving the knight
        if best_move.from_square == chess.B4:
            print("✅ SUCCESS: Bot is moving the attacked knight!")
        else:
            print(f"❌ Bot chose different move: {san_move}")
            print(f"  From: {chess.square_name(best_move.from_square)}")
            print(f"  To: {chess.square_name(best_move.to_square)}")

if __name__ == "__main__":
    test_exchange_detection()