#!/usr/bin/env python3
"""
Chess Bot GUI - A desktop chess game with AI opponent using tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import chess
import threading
from chess_bot import ChessBot # to use hybrid change to chess_bot_hybrid | to use transposition change to chess_bot_transposition | to use QS change to chess_bot_QS , chess_bot_QS_testing
from utils import save_game_pgn, analyze_position, format_analysis, get_opening_name

class ChessGUI:
    """
    GUI Chess game using tkinter with drag-and-drop functionality.
    """
    
    def __init__(self):
        """Initialize the chess GUI."""
        self.root = tk.Tk()
        self.root.title("Chess Bot - GUI Version")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # Game state
        self.board = chess.Board()
        self.bot = ChessBot(depth=3, name="ChessBot")  # OPTIONAL: to use hybrid, remove this line and uncomment the line below
        #self.bot = ChessBot(depth=4, name="HybridChessBot", mcts_simulations=800, top_moves_count=3, use_hybrid=True)
        self.game_history = []
        self.human_color = chess.WHITE
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        self.bot_thinking = False
        
        # Captured pieces tracking
        self.captured_by_white = []  # Pieces captured by white
        self.captured_by_black = []  # Pieces captured by black
        self.capture_history = []  # Track captures for undo functionality
        
        # Colors and styling
        self.light_square_color = "#F0D9B5"
        self.dark_square_color = "#B58863"
        self.highlight_color = "#FFFF00"
        self.selected_color = "#FF6B6B"
        self.legal_move_color = "#90EE90"
        
        # Unicode chess pieces
        self.piece_symbols = {
            'P': '♙', 'N': '♘', 'B': '♗', 'R': '♖', 'Q': '♕', 'K': '♔',  # White
            'p': '♟', 'n': '♞', 'b': '♝', 'r': '♜', 'q': '♛', 'k': '♚'   # Black
        }
        
        self.setup_ui()
        self.update_board_display()
        self.update_status()
        self.update_captured_pieces_display()
        # Initialize undo button state after UI is set up
        self.root.after(100, self.update_undo_button_state)
    
    def setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left panel for the chess board and captured pieces
        board_frame = ttk.Frame(main_frame)
        board_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        # Captured pieces above the board (Black's captures)
        black_captured_frame = ttk.LabelFrame(board_frame, text="Black Captured", padding=5)
        black_captured_frame.pack(fill=tk.X, pady=(0, 5))
        self.black_captured_label = ttk.Label(black_captured_frame, text="", font=("Arial", 16), 
                                            background="#f0f0f0", relief="sunken", padding=5)
        self.black_captured_label.pack(fill=tk.X)
        
        # Chess board canvas
        self.canvas = tk.Canvas(board_frame, width=480, height=480, bg="white")
        self.canvas.pack()
        self.canvas.bind("<Button-1>", self.on_square_click)
        
        # Captured pieces below the board (White's captures)
        white_captured_frame = ttk.LabelFrame(board_frame, text="White Captured", padding=5)
        white_captured_frame.pack(fill=tk.X, pady=(5, 0))
        self.white_captured_label = ttk.Label(white_captured_frame, text="", font=("Arial", 16),
                                            background="#f0f0f0", relief="sunken", padding=5)
        self.white_captured_label.pack(fill=tk.X)
        
        # Right panel for controls and information
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Game controls
        controls_group = ttk.LabelFrame(control_frame, text="Game Controls", padding=10)
        controls_group.pack(fill=tk.X, pady=(0, 10))
        
        # Store references to all game control buttons
        self.new_game_button = ttk.Button(controls_group, text="New Game", command=self.new_game)
        self.new_game_button.pack(fill=tk.X, pady=2)
        
        self.switch_colors_button = ttk.Button(controls_group, text="Switch Colors", command=self.switch_colors)
        self.switch_colors_button.pack(fill=tk.X, pady=2)
        
        self.undo_button = ttk.Button(controls_group, text="Undo Move", command=self.undo_move)
        self.undo_button.pack(fill=tk.X, pady=2)
        
        self.save_game_button = ttk.Button(controls_group, text="Save Game", command=self.save_game)
        self.save_game_button.pack(fill=tk.X, pady=2)
        
        self.load_game_button = ttk.Button(controls_group, text="Load Game", command=self.load_game)
        self.load_game_button.pack(fill=tk.X, pady=2)
        
        # Bot difficulty
        difficulty_group = ttk.LabelFrame(control_frame, text="Bot Difficulty", padding=10)
        difficulty_group.pack(fill=tk.X, pady=(0, 10))
        
        self.difficulty_var = tk.StringVar(value="Medium")
        difficulties = [("Easy", 2), ("Medium", 3), ("Hard", 4), ("Expert", 5)]
        
        for name, depth in difficulties:
            ttk.Radiobutton(difficulty_group, text=f"{name} (depth {depth})", 
                           variable=self.difficulty_var, value=name,
                           command=lambda d=depth: self.set_difficulty(d)).pack(anchor=tk.W)
        
        # Game status
        status_group = ttk.LabelFrame(control_frame, text="Game Status", padding=10)
        status_group.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(status_group, text="White to move", font=("Arial", 12))
        self.status_label.pack()
        
        self.opening_label = ttk.Label(status_group, text="Opening: Starting Position", font=("Arial", 10))
        self.opening_label.pack()
        
        # Move history
        history_group = ttk.LabelFrame(control_frame, text="Move History", padding=10)
        history_group.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Scrollable text widget for move history
        history_frame = ttk.Frame(history_group)
        history_frame.pack(fill=tk.BOTH, expand=True)
        
        self.history_text = tk.Text(history_frame, height=8, width=25, wrap=tk.WORD)
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_text.yview)
        self.history_text.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Analysis button
        self.analyze_button = ttk.Button(control_frame, text="Analyze Position", command=self.show_analysis)
        self.analyze_button.pack(fill=tk.X)
    
    def draw_board(self):
        """Draw the chess board."""
        self.canvas.delete("all")
        square_size = 60
        
        for row in range(8):
            for col in range(8):
                x1 = col * square_size
                y1 = row * square_size
                x2 = x1 + square_size
                y2 = y1 + square_size
                
                # Determine square color
                is_light = (row + col) % 2 == 0
                square = chess.square(col, 7 - row)
                
                # Choose color based on square state
                if square == self.selected_square:
                    color = self.selected_color
                elif square in self.highlighted_squares:
                    color = self.legal_move_color
                else:
                    color = self.light_square_color if is_light else self.dark_square_color
                
                # Draw square
                self.canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline="black")
                
                # Add coordinates
                if col == 0:  # Rank labels
                    self.canvas.create_text(x1 + 5, y1 + 10, text=str(8 - row), 
                                          font=("Arial", 8), fill="black")
                if row == 7:  # File labels
                    self.canvas.create_text(x2 - 10, y2 - 5, text=chr(ord('a') + col), 
                                          font=("Arial", 8), fill="black")
    
    def draw_pieces(self):
        """Draw the chess pieces on the board."""
        square_size = 60
        
        for row in range(8):
            for col in range(8):
                square = chess.square(col, 7 - row)
                piece = self.board.piece_at(square)
                
                if piece:
                    x = col * square_size + square_size // 2
                    y = row * square_size + square_size // 2
                    
                    piece_symbol = self.piece_symbols.get(piece.symbol(), piece.symbol())
                    self.canvas.create_text(x, y, text=piece_symbol, 
                                          font=("Arial", 36), fill="black")
    
    def update_board_display(self):
        """Update the visual board display."""
        self.draw_board()
        self.draw_pieces()
    
    def get_piece_name(self, piece):
        """Get the full name of a chess piece."""
        piece_names = {
            chess.PAWN: "Pawn",
            chess.KNIGHT: "Knight", 
            chess.BISHOP: "Bishop",
            chess.ROOK: "Rook",
            chess.QUEEN: "Queen",
            chess.KING: "King"
        }
        return piece_names.get(piece.piece_type, "Unknown")
    
    def update_captured_pieces_display(self):
        """Update the visual display of captured pieces."""
        # Display captured pieces using Unicode symbols with better spacing
        white_captured_text = ""
        for piece_symbol in self.captured_by_white:
            white_captured_text += self.piece_symbols.get(piece_symbol, piece_symbol) + "  "
        
        black_captured_text = ""
        for piece_symbol in self.captured_by_black:
            black_captured_text += self.piece_symbols.get(piece_symbol, piece_symbol) + "  "
        
        # Set the text, or show a placeholder if no pieces captured
        self.white_captured_label.config(text=white_captured_text.strip() if white_captured_text.strip() else "No pieces captured")
        self.black_captured_label.config(text=black_captured_text.strip() if black_captured_text.strip() else "No pieces captured")
    
    def reconstruct_captured_pieces(self, game_history):
        """Reconstruct captured pieces from game history."""
        # Reset captured pieces
        self.captured_by_white = []
        self.captured_by_black = []
        self.capture_history = []
        
        # Create a temporary board to replay the game
        temp_board = chess.Board()
        
        print(f"\n🔄 RECONSTRUCTING CAPTURES from {len(game_history)} moves...")
        
        # Replay each move and track captures
        for i, san_move in enumerate(game_history):
            try:
                # Parse the SAN move to get the actual move
                move = temp_board.parse_san(san_move)
                
                # Check if this move captures a piece
                captured_piece = temp_board.piece_at(move.to_square)
                
                # Get the moving piece color
                moving_piece = temp_board.piece_at(move.from_square)
                capturing_color = moving_piece.color if moving_piece else None
                
                # Make the move
                temp_board.push(move)
                
                # Track the capture
                capture_info = None
                if captured_piece and capturing_color is not None:
                    if capturing_color == chess.WHITE:
                        self.captured_by_white.append(captured_piece.symbol())
                        color_name = "White"
                        capture_info = ('white', captured_piece.symbol())
                    else:
                        self.captured_by_black.append(captured_piece.symbol())
                        color_name = "Black"
                        capture_info = ('black', captured_piece.symbol())
                    
                    piece_name = self.get_piece_name(captured_piece)
                    print(f"  Move {i+1}: {color_name} captured {piece_name} ({captured_piece.symbol()})")
                
                # Store capture info for undo functionality
                self.capture_history.append(capture_info)
                
            except Exception as e:
                print(f"  Error processing move {i+1} ({san_move}): {e}")
                # Still add None to capture_history to keep it in sync
                self.capture_history.append(None)
        
        print(f"📊 RECONSTRUCTION COMPLETE:")
        print(f"⚪ White captured: {' '.join(self.captured_by_white) if self.captured_by_white else 'None'}")
        print(f"⚫ Black captured: {' '.join(self.captured_by_black) if self.captured_by_black else 'None'}")
        print("-" * 50)
    
    def on_square_click(self, event):
        """Handle mouse clicks on the board."""
        if self.game_over or self.board.turn != self.human_color:
            return
        
        square_size = 60
        col = event.x // square_size
        row = event.y // square_size
        
        if 0 <= col < 8 and 0 <= row < 8:
            clicked_square = chess.square(col, 7 - row)
            
            if self.selected_square is None:
                # Select a piece
                piece = self.board.piece_at(clicked_square)
                if piece and piece.color == self.human_color:
                    self.selected_square = clicked_square
                    self.highlight_legal_moves(clicked_square)
                    self.update_board_display()
            else:
                # Try to make a move
                move = chess.Move(self.selected_square, clicked_square)
                
                # Check for promotion
                piece = self.board.piece_at(self.selected_square)
                if (piece and piece.piece_type == chess.PAWN and 
                    ((piece.color == chess.WHITE and chess.square_rank(clicked_square) == 7) or
                     (piece.color == chess.BLACK and chess.square_rank(clicked_square) == 0))):
                    # Default to queen promotion for simplicity
                    move = chess.Move(self.selected_square, clicked_square, promotion=chess.QUEEN)
                
                if move in self.board.legal_moves:
                    self.make_move(move)
                
                # Clear selection
                self.selected_square = None
                self.highlighted_squares = []
                self.update_board_display()
    
    def highlight_legal_moves(self, square):
        """Highlight legal moves for the selected piece."""
        self.highlighted_squares = []
        for move in self.board.legal_moves:
            if move.from_square == square:
                self.highlighted_squares.append(move.to_square)
    
    def make_move(self, move):
        """Make a move on the board."""
        try:
            # Check if this move captures a piece before making the move
            captured_piece = self.board.piece_at(move.to_square)
            san_move = self.board.san(move)
            
            # Get the moving piece color for proper capture tracking
            moving_piece = self.board.piece_at(move.from_square)
            capturing_color = moving_piece.color if moving_piece else None
            
            self.board.push(move)
            self.game_history.append(san_move)
            
            # Track captured pieces and print to terminal
            capture_info = None
            if captured_piece:
                if capturing_color == chess.WHITE:
                    self.captured_by_white.append(captured_piece.symbol())
                    color_name = "White"
                    capture_info = ('white', captured_piece.symbol())
                else:
                    self.captured_by_black.append(captured_piece.symbol())
                    color_name = "Black"
                    capture_info = ('black', captured_piece.symbol())
                
                # Terminal output for capture
                piece_name = self.get_piece_name(captured_piece)
                print(f"\n🎯 CAPTURE: {color_name} captured {piece_name} ({captured_piece.symbol()})")
                print(f"⚪ White captured pieces: {' '.join(self.captured_by_white) if self.captured_by_white else 'None'}")
                print(f"⚫ Black captured pieces: {' '.join(self.captured_by_black) if self.captured_by_black else 'None'}")
                print("-" * 50)
            
            # Store capture info for undo functionality
            self.capture_history.append(capture_info)
            
            self.update_history_display()
            self.update_status()
            self.update_board_display()
            self.update_captured_pieces_display()
            
            # Update undo button state
            self.update_undo_button_state()
            
            # Check for game over
            if self.board.is_game_over():
                self.handle_game_over()
            elif self.board.turn != self.human_color:
                # Bot's turn
                self.root.after(500, self.make_bot_move)  # Small delay for better UX
                
        except Exception as e:
            messagebox.showerror("Error", f"Invalid move: {e}")
            
    def update_undo_button_state(self):
        """Update the state of the undo button based on game state."""
        # Let's try a simpler approach: disable undo button when playing as black
        # and only white has moved (exactly one move in the stack)
        
        # Check if we're playing as black and only one move has been made
        if self.human_color == chess.BLACK and len(self.board.move_stack) == 1:
            print(f"Disabling undo button: Playing as BLACK, move stack has 1 move")
            self.undo_button.configure(state="disabled")
            return
            
        # Also disable if no moves or bot is thinking
        if not self.board.move_stack:
            print("Disabling undo button: No moves in stack")
            self.undo_button.configure(state="disabled")
            return
            
        if self.bot_thinking:
            print("Disabling undo button: Bot is thinking")
            self.undo_button.configure(state="disabled")
            return
            
        # In all other cases, enable the undo button
        print(f"Enabling undo button: human_color={self.human_color}, moves={len(self.board.move_stack)}, turn={self.board.turn}")
        self.undo_button.configure(state="normal")
    
    def undo_move(self):
        """Undo the last move (or last two moves if bot's turn)."""
        # Check if there are moves to undo
        if not self.board.move_stack:
            messagebox.showinfo("Info", "No moves to undo!")
            return
        
        # Determine how many moves to undo
        # If it's the human's turn, undo the last bot move and the human move before that
        # If it's the bot's turn (right after human moved), just undo the human's move
        moves_to_undo = 1
        
        # If it's the human's turn and there are at least 2 moves, undo both the bot's and human's last moves
        if self.board.turn == self.human_color and len(self.board.move_stack) >= 2:
            moves_to_undo = 2
            
        # Make sure we have enough moves to undo
        if len(self.board.move_stack) < moves_to_undo:
            moves_to_undo = len(self.board.move_stack)
            
        # Undo the moves and restore captured pieces
        for _ in range(moves_to_undo):
            if self.board.move_stack:
                self.board.pop()
                if self.game_history:
                    self.game_history.pop()
                
                # Restore captured pieces
                if self.capture_history:
                    capture_info = self.capture_history.pop()
                    if capture_info:  # If there was a capture
                        color, piece_symbol = capture_info
                        if color == 'white' and piece_symbol in self.captured_by_white:
                            self.captured_by_white.remove(piece_symbol)
                            print(f"\n↩️ UNDO CAPTURE: Restored {piece_symbol} to Black (removed from White's captures)")
                        elif color == 'black' and piece_symbol in self.captured_by_black:
                            self.captured_by_black.remove(piece_symbol)
                            print(f"\n↩️ UNDO CAPTURE: Restored {piece_symbol} to White (removed from Black's captures)")
                        
                        # Show updated capture status
                        print(f"⚪ White captured pieces: {' '.join(self.captured_by_white) if self.captured_by_white else 'None'}")
                        print(f"⚫ Black captured pieces: {' '.join(self.captured_by_black) if self.captured_by_black else 'None'}")
                        print("-" * 50)
        
        # Reset game state if it was game over
        if self.game_over:
            self.game_over = False
            
        # Update the display
        self.update_history_display()
        self.update_status()
        self.update_board_display()
        self.update_captured_pieces_display()
        
        # Update undo button state
        self.update_undo_button_state()
        
        # Clear any selections
        self.selected_square = None
        self.highlighted_squares = []
        
        # If after undoing, it's the bot's turn, trigger the bot to make a move
        if not self.game_over and self.board.turn != self.human_color:
            self.root.after(500, self.make_bot_move)  # Small delay for better UX
    
    def make_bot_move(self):
        """Make a move for the bot in a separate thread."""
        def bot_move_thread():
            self.bot_thinking = True
            # Disable all controls in the main thread
            self.root.after(0, self.disable_all_controls)
            try:
                self.update_status("Bot is thinking...")
                move = self.bot.get_best_move(self.board)
                
                if move:
                    # Update UI in main thread
                    self.root.after(0, lambda m=move: self.make_move(m))
                else:
                    self.root.after(0, lambda: messagebox.showinfo("Info", "Bot couldn't find a move!"))
            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda: messagebox.showerror("Error", f"Bot error: {error_msg}"))
            finally:
                self.bot_thinking = False
                # Re-enable all controls in the main thread
                self.root.after(0, self.enable_all_controls)
        
        threading.Thread(target=bot_move_thread, daemon=True).start()
    
    def update_status(self, custom_message=None):
        """Update the game status display."""
        if custom_message:
            self.status_label.config(text=custom_message)
            return
        
        if self.board.is_checkmate():
            winner = "Black" if self.board.turn == chess.WHITE else "White"
            self.status_label.config(text=f"Checkmate! {winner} wins!")
            self.game_over = True
        elif self.board.is_stalemate():
            self.status_label.config(text="Stalemate! Draw!")
            self.game_over = True
        elif self.board.is_insufficient_material():
            self.status_label.config(text="Insufficient material! Draw!")
            self.game_over = True
        elif self.board.is_check():
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} in check!")
        else:
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} to move")
        
        # Update opening name
        if len(self.game_history) <= 8:
            opening = get_opening_name(self.board, self.game_history)
            self.opening_label.config(text=f"Opening: {opening}")
    
    def update_history_display(self):
        """Update the move history display."""
        self.history_text.delete(1.0, tk.END)
        
        # Format moves in pairs (White, Black)
        for i in range(0, len(self.game_history), 2):
            move_num = (i // 2) + 1
            white_move = self.game_history[i] if i < len(self.game_history) else ""
            black_move = self.game_history[i + 1] if i + 1 < len(self.game_history) else ""
            
            line = f"{move_num}. {white_move}"
            if black_move:
                line += f" {black_move}"
            line += "\n"
            
            self.history_text.insert(tk.END, line)
        
        # Scroll to bottom
        self.history_text.see(tk.END)
    
    def handle_game_over(self):
        """Handle game over situation."""
        self.game_over = True
        
        if self.board.is_checkmate():
            winner = "You" if self.board.turn != self.human_color else "ChessBot"
            message = f"Checkmate! {winner} win!"
        elif self.board.is_stalemate():
            message = "Stalemate! The game is a draw."
        elif self.board.is_insufficient_material():
            message = "Insufficient material! The game is a draw."
        else:
            message = "Game over!"
        
        result = messagebox.askyesno("Game Over", f"{message}\n\nWould you like to save this game?")
        if result:
            self.save_game()
    
    def new_game(self):
        """Start a new game."""
        self.board = chess.Board()
        self.game_history = []
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        
        # Reset captured pieces
        self.captured_by_white = []
        self.captured_by_black = []
        self.capture_history = []
        
        # Terminal output for new game
        print("\n" + "="*60)
        print("🆕 NEW GAME STARTED")
        print("🎯 Captured pieces tracker initialized")
        print("⚪ White captured pieces: None")
        print("⚫ Black captured pieces: None")
        print("="*60)
        
        self.update_board_display()
        self.update_status()
        self.update_history_display()
        self.update_captured_pieces_display()
        self.update_undo_button_state()
    
    def switch_colors(self):
        """Switch human and bot colors."""
        self.human_color = not self.human_color
        color_name = "White" if self.human_color == chess.WHITE else "Black"
        messagebox.showinfo("Colors Switched", f"You are now playing as {color_name}")
        
        print(f"Colors switched: human_color={self.human_color}, board.turn={self.board.turn}")
        
        # Update undo button state
        self.update_undo_button_state()
        
        # If it's now bot's turn, make a move
        if not self.game_over and self.board.turn != self.human_color:
            print("Bot's turn after color switch, making bot move")
            self.make_bot_move()
            
            # Update undo button state again after bot moves
            self.root.after(1000, self.update_undo_button_state)
    
    def set_difficulty(self, depth):
        """Set the bot difficulty."""
        self.bot.set_depth(depth)
        difficulty_name = self.difficulty_var.get()
        messagebox.showinfo("Difficulty Changed", f"Bot difficulty set to {difficulty_name} (depth {depth})")
    
    def save_game(self):
        """Save the current game to a PGN file."""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".pgn",
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
                title="Save Chess Game"
            )
            
            if filename:
                white_player = "Human" if self.human_color == chess.WHITE else "ChessBot"
                black_player = "ChessBot" if self.human_color == chess.WHITE else "Human"
                
                save_game_pgn(self.board, self.game_history, filename, white_player, black_player)
                messagebox.showinfo("Game Saved", f"Game saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save game: {e}")
    
    def load_game(self):
        """Load a game from a PGN file."""
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
                title="Load Chess Game"
            )
            
            if filename:
                from utils import load_game_pgn
                board, history = load_game_pgn(filename)
                
                self.board = board
                self.game_history = history
                self.selected_square = None
                self.highlighted_squares = []
                self.game_over = self.board.is_game_over()
                
                # Reconstruct captured pieces from the game history
                self.reconstruct_captured_pieces(history)
                
                self.update_board_display()
                self.update_status()
                self.update_history_display()
                self.update_captured_pieces_display()
                
                messagebox.showinfo("Game Loaded", f"Game loaded from {filename}")
                print(f"\n📁 GAME LOADED: {filename}")
                print("🎯 Captured pieces tracker reconstructed from game history")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load game: {e}")
            print(f"❌ ERROR loading game: {e}")
    
    def show_analysis(self):
        """Show position analysis in a popup window."""
        try:
            analysis = analyze_position(self.board)
            analysis_text = format_analysis(analysis)
            
            # Create popup window
            popup = tk.Toplevel(self.root)
            popup.title("Position Analysis")
            popup.geometry("400x500")
            
            # Text widget with scrollbar
            frame = ttk.Frame(popup)
            frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(frame, wrap=tk.WORD, font=("Courier", 10))
            scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            text_widget.insert(1.0, analysis_text)
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to analyze position: {e}")
    
    def disable_all_controls(self):
        """Disable all game controls and board interaction while the bot is thinking."""
        print("=== DISABLING ALL GAME CONTROLS ===")
        # Disable all buttons
        self.new_game_button.configure(state="disabled")
        self.switch_colors_button.configure(state="disabled")
        self.undo_button.configure(state="disabled")
        self.save_game_button.configure(state="disabled")
        self.load_game_button.configure(state="disabled")
        self.analyze_button.configure(state="disabled")
        
        # Unbind board click event to prevent user interaction
        self.canvas.unbind("<Button-1>")
        print("Board interaction disabled")
        
    def enable_all_controls(self):
        """Re-enable all game controls and board interaction."""
        print("=== RE-ENABLING ALL GAME CONTROLS ===")
        # Re-enable all buttons (except undo which is handled separately)
        self.new_game_button.configure(state="normal")
        self.switch_colors_button.configure(state="normal")
        self.save_game_button.configure(state="normal")
        self.load_game_button.configure(state="normal")
        self.analyze_button.configure(state="normal")
        print("Game control buttons re-enabled")
        
        # Update undo button state based on game state
        self.update_undo_button_state()
        
        # Rebind board click event
        self.canvas.bind("<Button-1>", self.on_square_click)
        print("Board interaction re-enabled")
    
    def run(self):
        """Start the GUI application."""
        self.root.mainloop()

def main():
    """Main entry point for the GUI version."""
    try:
        app = ChessGUI()
        app.run()
    except Exception as e:
        print(f"Error starting GUI: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()