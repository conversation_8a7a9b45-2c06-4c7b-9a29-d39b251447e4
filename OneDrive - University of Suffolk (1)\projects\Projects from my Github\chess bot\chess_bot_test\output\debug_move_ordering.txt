PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python debug_move_ordering.py
Debugging Move Ordering
==================================================
Position:
r . b q k b n r
p p p . . p p p
. . . . p . . .
. . . p N . . .
. n . P . . . .
P . N . . . . .
. P P . P P P P
R . B Q K B . R

Total legal moves: 32

Move ordering analysis:
----------------------------------------
ESCAPE BONUS: Nc6 - piece on b4 is under attack!
  RISKY ESCAPE: Moving to c6 is still attacked!
ESCAPE BONUS: Na6 - piece on b4 is under attack!
  SAFE ESCAPE: Moving to a6 is safe!
ESCAPE BONUS: Nd3+ - piece on b4 is under attack!
  RISKY ESCAPE: Moving to d3 is still attacked!
CHECK: Nd3+
ESCAPE BONUS: Nxc2+ - piece on b4 is under attack!
  RISKY ESCAPE: Moving to c2 is still attacked!
CAPTURE: Nxc2+ - score 680
CHECK: Nxc2+
ESCAPE BONUS: Na2 - piece on b4 is under attack!
  RISKY ESCAPE: Moving to a2 is still attacked!
ESCAPE BONUS: f6 - piece on f7 is under attack!
  SAFE ESCAPE: Moving to f6 is safe!
ESCAPE BONUS: f5 - piece on f7 is under attack!
  SAFE ESCAPE: Moving to f5 is safe!

Top 10 moves by scoring:
----------------------------------------
 1. Nxc2+    - Score:   6180
    ^^^ KNIGHT MOVE! ^^^
 2. Na6      - Score:   6000
    ^^^ KNIGHT MOVE! ^^^
 3. f6       - Score:   6000
 4. f5       - Score:   6000
 5. Nd3+     - Score:   5500
    ^^^ KNIGHT MOVE! ^^^
 6. Nc6      - Score:   5000
    ^^^ KNIGHT MOVE! ^^^
 7. Na2      - Score:   5000
    ^^^ KNIGHT MOVE! ^^^
 8. Ne7      - Score:      0
 9. Nh6      - Score:      0
10. Nf6      - Score:      0

Actual ordered moves (first 5):
1. Nxc2+    - Score:   6180
    ^^^ KNIGHT MOVE! ^^^
2. Na6      - Score:   6000
    ^^^ KNIGHT MOVE! ^^^
3. f6       - Score:   6000
4. f5       - Score:   6000
5. Nd3+     - Score:   5500
    ^^^ KNIGHT MOVE! ^^^