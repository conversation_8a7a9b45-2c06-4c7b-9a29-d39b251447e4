import chess
import chess.engine
import random
from typing import Op<PERSON>, Tuple, List
import time

class ChessBot:
    """
    A chess bot that uses minimax algorithm with alpha-beta pruning
    to make intelligent moves.
    """
    
    def __init__(self, depth: int = 3, name: str = "ChessBot"):
        """
        Initialize the chess bot.
        
        Args:
            depth: Search depth for minimax algorithm
            name: Name of the bot
        """
        self.depth = depth
        self.name = name
        self.nodes_evaluated = 0
        
        # Piece values for evaluation
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
        
        # Position tables for piece placement evaluation
        # --- These tables are designed for <PERSON>'s perspective. They are mirrored for Black. ---
        
        self.pawn_table = [
            0,  0,  0,  0,  0,  0,  0,  0,
            50, 50, 50, 50, 50, 50, 50, 50,
            10, 10, 20, 30, 30, 20, 10, 10,
            5,  5, 10, 25, 25, 10,  5,  5,
            0,  0,  0, 20, 20,  0,  0,  0,
            5, -5,-10,  0,  0,-10, -5,  5,
            5, 10, 10,-20,-20, 10, 10,  5,
            0,  0,  0,  0,  0,  0,  0,  0
        ]
        
        self.knight_table = [
            -50,-40,-30,-30,-30,-30,-40,-50,
            -40,-20,  0,  0,  0,  0,-20,-40,
            -30,  0, 10, 15, 15, 10,  0,-30,
            -30,  5, 15, 20, 20, 15,  5,-30,
            -30,  0, 15, 20, 20, 15,  0,-30,
            -30,  5, 10, 15, 15, 10,  5,-30,
            -40,-20,  0,  5,  5,  0,-20,-40,
            -50,-40,-30,-30,-30,-30,-40,-50
        ]

        # ADDED: Bishop positional table
        self.bishop_table = [
            -20,-10,-10,-10,-10,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5, 10, 10,  5,  0,-10,
            -10,  5,  5, 10, 10,  5,  5,-10,
            -10,  0, 10, 10, 10, 10,  0,-10,
            -10, 10, 10, 10, 10, 10, 10,-10,
            -10,  5,  0,  0,  0,  0,  5,-10,
            -20,-10,-10,-10,-10,-10,-10,-20
        ]

        # ADDED: Rook positional table (encourages staying on back ranks early)
        self.rook_table = [
             0,  0,  0,  0,  0,  0,  0,  0,
             5, 10, 10, 10, 10, 10, 10,  5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
             0,  0,  0,  5,  5,  0,  0,  0
        ]

        # ADDED: Queen positional table
        self.queen_table = [
            -20,-10,-10, -5, -5,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5,  5,  5,  5,  0,-10,
             -5,  0,  5,  5,  5,  5,  0, -5,
              0,  0,  5,  5,  5,  5,  0, -5,
            -10,  5,  5,  5,  5,  5,  0,-10,
            -10,  0,  5,  0,  0,  0,  0,-10,
            -20,-10,-10, -5, -5,-10,-10,-20
        ]

        # ADDED: King positional table (split for middle and end game)
        self.king_middle_game_table = [
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -20,-30,-30,-40,-40,-30,-30,-20,
            -10,-20,-20,-20,-20,-20,-20,-10,
             20, 20,  0,  0,  0,  0, 20, 20,
             20, 30, 10,  0,  0, 10, 30, 20
        ]

        self.king_end_game_table = [
            -50,-40,-30,-20,-20,-30,-40,-50,
            -30,-20,-10,  0,  0,-10,-20,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-30,  0,  0,  0,  0,-30,-30,
            -50,-30,-30,-30,-30,-30,-30,-50
        ]
    
    def evaluate_board(self, board: chess.Board, depth: int) -> int:
        """
        Evaluate the current board position.
        
        Args:
            board: Chess board to evaluate
            depth: The remaining search depth, used to prioritize faster mates.
            
        Returns:
            Evaluation score (positive for white advantage, negative for black)
        """
        # A checkmate is more valuable the sooner it is achieved.
        if board.is_checkmate():
            # The side to move is the one that has been checkmated.
            return -(99999 + depth) if board.turn == chess.WHITE else (99999 + depth)
        
        # Explicitly check for all draw conditions and return a neutral score.
        if board.is_stalemate() or board.is_insufficient_material() or board.is_seventyfive_moves() or board.is_fivefold_repetition():
            return 0
        
        score = 0
        
        # Determine if it's end game based on number of pieces
        is_end_game = len(board.piece_map()) <= 10
        
        # Material and positional evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = self.piece_values[piece.piece_type]
                
                # Add positional bonuses
                if piece.piece_type == chess.PAWN:
                    positional_value = self.pawn_table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.KNIGHT:
                    positional_value = self.knight_table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.BISHOP:
                    positional_value = self.bishop_table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.ROOK:
                    positional_value = self.rook_table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.QUEEN:
                    positional_value = self.queen_table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                elif piece.piece_type == chess.KING:
                    if is_end_game:
                        positional_value = self.king_end_game_table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                    else:
                        positional_value = self.king_middle_game_table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
                else:
                    positional_value = 0
                
                value += positional_value

                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value
        
        # The flawed mobility bonus has been removed.
        
        return score

    def minimax(self, board: chess.Board, depth: int, alpha: int, beta: int, 
                maximizing_player: bool) -> Tuple[int, Optional[chess.Move]]:
        """
        Minimax algorithm with alpha-beta pruning.
        
        Args:
            board: Current board position
            depth: Remaining search depth
            alpha: Alpha value for pruning
            beta: Beta value for pruning
            maximizing_player: True if maximizing player's turn
            
        Returns:
            Tuple of (evaluation_score, best_move)
        """
        self.nodes_evaluated += 1
        
        if depth == 0 or board.is_game_over():
            # Pass the current depth to the evaluation function
            return self.evaluate_board(board, depth), None
        
        best_move = None
        
        # Move ordering: prioritize captures
        legal_moves = sorted(list(board.legal_moves), key=lambda move: board.is_capture(move), reverse=True)

        if maximizing_player:
            max_eval = float('-inf')
            for move in legal_moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            return max_eval, best_move
        else:
            min_eval = float('inf')
            for move in legal_moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            return min_eval, best_move
    
    def get_best_move(self, board: chess.Board) -> chess.Move:
        """
        Get the best move for the current position.
        
        Args:
            board: Current board position
            
        Returns:
            Best move according to the bot's evaluation
        """
        self.nodes_evaluated = 0
        start_time = time.time()
        
        # Use minimax to find the best move
        maximizing = board.turn == chess.WHITE
        _, best_move = self.minimax(board, self.depth, float('-inf'), float('inf'), maximizing)
        
        end_time = time.time()
        
        if best_move is None:
            # Fallback to random move if no move found
            legal_moves = list(board.legal_moves)
            best_move = random.choice(legal_moves) if legal_moves else None
        
        print(f"{self.name} evaluated {self.nodes_evaluated} positions in {end_time - start_time:.2f} seconds")
        
        return best_move
    
    def set_depth(self, depth: int):
        """Set the search depth for the bot."""
        self.depth = depth
        print(f"{self.name} search depth set to {depth}")