PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python debug_position_evaluation.py
Debugging Position Evaluation After Key Moves
============================================================
Starting position (Black to move):
r . b q k b n r
p p p . . p p p
. . . . p . . .
. . . p N . . .
. n . P . . . .
P . N . . . . .
. P P . P P P P
R . B Q K B . R
FEN: r1bqkbnr/ppp2ppp/4p3/3pN3/1n1P4/P1N5/1PP1PPPP/R1BQKB1R b KQkq - 0 5

Evaluating positions after key moves:
------------------------------------------------------------
ORIGINAL POSITION:
  Overall evaluation: 377.0
  Hanging piece penalty: 330.0

After Na6:
  Position evaluation: 158
  Hanging piece penalty: 0
  Change from original: -219.0
  White threats: Nxf7 (captures p), Nxd5 (captures p)

After Nxc2+:
  Position evaluation: -29944.0
  Hanging piece penalty: -29880.0
  Change from original: -30321.0
  White threats: Qxc2 (captures n)

After Qg5:
  Position evaluation: 2516.0
  Hanging piece penalty: 2130.0
  Change from original: +2139.0
  White threats: Nxf7 (captures p), Nxd5 (captures p), Bxg5 (captures q)

After Qd6:
  Position evaluation: 519.0
  Hanging piece penalty: 330.0
  Change from original: +142.0
  White threats: Nxf7 (captures p), Nxd5 (captures p), axb4 (captures n)

============================================================
SHALLOW SEARCH ANALYSIS (depth 1)
============================================================
MediumBot statistics:
  - Time: 0.07s
  - Nodes evaluated: 33
  - Quiescence nodes: 39
  - Transposition hits: 0
  - Table size: 1
Depth 1 best move: Nd3+

SHALLOW SEARCH ANALYSIS (depth 2)
----------------------------------------
MediumBot statistics:
  - Time: 4.07s
  - Nodes evaluated: 288
  - Quiescence nodes: 1916
  - Transposition hits: 0
  - Table size: 33
Depth 2 best move: Qd7