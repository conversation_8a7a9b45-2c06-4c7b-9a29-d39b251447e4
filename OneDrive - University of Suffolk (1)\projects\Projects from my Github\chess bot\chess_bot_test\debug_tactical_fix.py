#!/usr/bin/env python3
"""
Debug script to test the tactical evaluation fixes.
"""

import chess
from tactical_evaluation import TacticalEvaluator

def debug_position():
    """Debug the specific position mentioned in the issue."""
    
    # The position from the issue
    fen = "r2qkb1r/ppp2ppp/3pb3/4p3/3n4/3K4/PPP1PnPP/RNBQ1BNR w kq - 0 9"
    board = chess.Board(fen)
    
    print("Position:")
    print(board)
    print(f"FEN: {fen}")
    print(f"Turn: {'White' if board.turn == chess.WHITE else 'Black'}")
    print(f"In check: {board.is_check()}")
    print(f"Checkmate: {board.is_checkmate()}")
    print(f"Stalemate: {board.is_stalemate()}")
    print()
    
    # Test the tactical evaluator
    evaluator = TacticalEvaluator()
    threats = evaluator.evaluate_threats(board)
    
    print("CURRENT ANALYSIS:")
    print("="*50)
    
    for color in [chess.WHITE, chess.BLACK]:
        color_name = "WHITE" if color == chess.WHITE else "BLACK"
        print(f"{color_name} THREATS:")
        print("-" * 30)
        
        if not threats[color]:
            print("No threats found")
        else:
            for threat in threats[color]:
                print(f"• {threat['description']}")
                print(f"  Type: {threat['type'].value}")
                print(f"  Value: {threat['threat_value']}")
                print()
    
    # Check specific pieces
    print("PIECE ANALYSIS:")
    print("-" * 30)
    
    # Check if white king is in check
    white_king_square = board.king(chess.WHITE)
    print(f"White king on {chess.square_name(white_king_square)}")
    print(f"White king in check: {board.is_check() and board.turn == chess.WHITE}")
    
    # Check what's attacking the white king
    if board.is_check() and board.turn == chess.WHITE:
        attackers = board.attackers(chess.BLACK, white_king_square)
        print(f"King attackers: {[chess.square_name(sq) for sq in attackers]}")
        for sq in attackers:
            piece = board.piece_at(sq)
            print(f"  {piece.symbol().upper()} on {chess.square_name(sq)}")
    
    # Look at the knight on f2
    f2_square = chess.parse_square("f2")
    f2_piece = board.piece_at(f2_square)
    if f2_piece:
        print(f"Piece on f2: {f2_piece.symbol().upper()}")
        attacks_from_f2 = board.attacks(f2_square)
        print(f"f2 attacks: {[chess.square_name(sq) for sq in attacks_from_f2]}")
        
        # Check what pieces are on those squares
        for sq in attacks_from_f2:
            piece = board.piece_at(sq)
            if piece:
                print(f"  Can attack {piece.symbol().upper()} on {chess.square_name(sq)}")

if __name__ == "__main__":
    debug_position()