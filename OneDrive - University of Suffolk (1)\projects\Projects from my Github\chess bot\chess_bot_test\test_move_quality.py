#!/usr/bin/env python3
"""
Test script to verify move quality assessment is working correctly.
"""

import chess
from unified_chess_bot import UnifiedChessBot

def test_move_quality():
    """Test move quality assessment with known good and bad moves."""
    bot = UnifiedChessBot(depth=2)
    
    # Test case 1: Opening move d4 (should be good/normal)
    board = chess.Board()
    move = chess.Move.from_uci("d2d4")
    
    print("=== Test Case 1: Opening move d4 ===")
    print(f"Position before: {board.fen()}")
    
    static_score_before = bot.evaluate_board(board)
    print(f"Static score before: {static_score_before}")
    
    # Simulate the move quality recording
    bot._record_move_quality(board, move, 0, static_score_before, 0, 0.0, "Human")
    
    history = bot.get_move_quality_history()
    if history:
        last_move = history[-1]
        print(f"Move: {last_move['move_san']}")
        print(f"Quality: {last_move['quality']}")
        print(f"Score difference: {last_move['score_difference']}")
    
    print()
    
    # Test case 2: Bad move - moving queen to be captured
    # Set up a position where Qd2 is a blunder
    board = chess.Board("rnbqk2r/pppp1ppp/3bp3/8/4n3/8/PPP2PPP/RNBQKBNR w KQkq - 0 5")
    move = chess.Move.from_uci("d1d2")  # Qd2 - bad move, queen can be captured
    
    print("=== Test Case 2: Blunder move Qd2 ===")
    print(f"Position before: {board.fen()}")
    
    static_score_before = bot.evaluate_board(board)
    print(f"Static score before: {static_score_before}")
    
    # Clear previous history
    bot.clear_move_quality_history()
    
    # Simulate the move quality recording
    bot._record_move_quality(board, move, 0, static_score_before, 0, 0.0, "Human")
    
    history = bot.get_move_quality_history()
    if history:
        last_move = history[-1]
        print(f"Move: {last_move['move_san']}")
        print(f"Quality: {last_move['quality']}")
        print(f"Score difference: {last_move['score_difference']}")
    
    print()
    
    # Test case 3: Good move - capturing a piece
    board = chess.Board("rnbqkb1r/pppp1ppp/4pn2/3P4/8/8/PPP1PPPP/RNBQKBNR w KQkq - 1 3")
    move = chess.Move.from_uci("d5e6")  # dxe6 - capturing pawn
    
    print("=== Test Case 3: Good move dxe6 ===")
    print(f"Position before: {board.fen()}")
    
    static_score_before = bot.evaluate_board(board)
    print(f"Static score before: {static_score_before}")
    
    # Clear previous history
    bot.clear_move_quality_history()
    
    # Simulate the move quality recording
    bot._record_move_quality(board, move, 0, static_score_before, 0, 0.0, "Human")
    
    history = bot.get_move_quality_history()
    if history:
        last_move = history[-1]
        print(f"Move: {last_move['move_san']}")
        print(f"Quality: {last_move['quality']}")
        print(f"Score difference: {last_move['score_difference']}")

if __name__ == "__main__":
    test_move_quality()