#!/usr/bin/env python3
"""
Test the evaluation fix for symmetric positions.
"""

import chess
from chess_bot import ChessBot

def test_evaluation_consistency():
    """Test that evaluation is consistent from <PERSON>'s perspective."""
    
    # Create a bot instance
    bot = ChessBot(depth=3)
    
    # Create board and make the moves from the example
    board = chess.Board()
    
    print("=== TESTING EVALUATION CONSISTENCY ===")
    
    # Test that we always evaluate from <PERSON>'s perspective
    print(f"Starting position evaluation: {bot.minimax(board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=True)[0]}")
    
    # Move 1: Nf3
    board.push(chess.Move.from_uci("g1f3"))
    eval_after_nf3 = bot.minimax(board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=True)[0]
    print(f"After 1. Nf3: {eval_after_nf3}")
    
    # Move 1: ...Nf6
    board.push(chess.Move.from_uci("g8f6"))
    eval_after_nf6 = bot.minimax(board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=True)[0]
    print(f"After 1... Nf6: {eval_after_nf6}")
    
    # Move 2: Nc3
    board.push(chess.Move.from_uci("b1c3"))
    eval_after_nc3 = bot.minimax(board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=True)[0]
    print(f"After 2. Nc3: {eval_after_nc3}")
    
    # Move 2: ...Nc6
    board.push(chess.Move.from_uci("b8c6"))
    eval_after_nc6 = bot.minimax(board, depth=2, alpha=float('-inf'), beta=float('inf'), maximizing_player=True)[0]
    print(f"After 2... Nc6: {eval_after_nc6}")
    
    print("\n=== EVALUATION ANALYSIS ===")
    print(f"Starting position: 0 (expected)")
    print(f"After symmetric moves: {eval_after_nc6} (should be close to 0)")
    print(f"Difference from starting position: {eval_after_nc6 - 0}")
    
    # Test with different depths to see consistency
    print("\n=== TESTING WITH DIFFERENT DEPTHS ===")
    for depth in [1, 2, 3, 4]:
        eval_score = bot.minimax(board, depth=depth, alpha=float('-inf'), beta=float('inf'), maximizing_player=True)[0]
        print(f"Depth {depth}: {eval_score}")

if __name__ == "__main__":
    test_evaluation_consistency()