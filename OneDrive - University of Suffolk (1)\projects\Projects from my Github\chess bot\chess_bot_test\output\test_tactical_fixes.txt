PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_tactical_fixes.py
Testing tactical evaluation fixes...
==================================================
Testing normal captures...
<PERSON> captures Knight:
  Flagged as blunder: False ()
  Flagged as unsound sacrifice: False
  ✅ PASSED: Normal capture correctly not flagged

Testing <PERSON> captures pawn...
Queen captures pawn:
  Flagged as blunder: False ()
  Flagged as unsound sacrifice: False
  ✅ PASSED: Queen capturing pawn correctly not flagged

Testing actual hanging piece...
Position with hanging Knight:
  Hanging piece detected: False ()

Testing move quality evaluation...
Normal developing move Nf6:
  Quality: Okay
  Explanation: acceptable opening (-0.5)
  ✅ PASSED: Normal move has reasonable quality

==================================================
Results: 4/4 tests passed
✅ All fixes appear to be working correctly!