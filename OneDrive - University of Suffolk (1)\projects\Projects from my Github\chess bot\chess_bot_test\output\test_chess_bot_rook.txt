PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_chess_bot_rook.py
Running Chess Bot Rook Unit Tests...
test_bot_initialization (__main__.TestChessBotRook.test_bot_initialization)
Test bot initialization with default and custom parameters. ... ok
test_end_game_detection (__main__.TestChessBotRook.test_end_game_detection)
Test that end game is correctly detected. ... ok
test_evaluate_board_checkmate (__main__.TestChessBotRook.test_evaluate_board_checkmate)
Test board evaluation in checkmate position. ... ok
test_evaluate_board_material_advantage (__main__.TestChessBotRook.test_evaluate_board_material_advantage)
Test board evaluation with material advantage. ... ok
test_evaluate_board_stalemate (__main__.TestChessBotRook.test_evaluate_board_stalemate)
Test board evaluation in stalemate position. ... ok
test_evaluate_board_starting_position (__main__.TestChessBotRook.test_evaluate_board_starting_position)
Test board evaluation at starting position. ... ok
test_get_best_move_improves_position (__main__.TestChessBotRook.test_get_best_move_improves_position)
Test that bot makes moves that improve its position. ... TestRookBot evaluated 117 positions in 0.02 seconds
ok
test_get_best_move_returns_legal_move (__main__.TestChessBotRook.test_get_best_move_returns_legal_move)
Test that get_best_move returns a legal move. ... TestRookBot evaluated 80 positions in 0.02 seconds
ok
test_minimax_depth_zero (__main__.TestChessBotRook.test_minimax_depth_zero)
Test minimax with depth 0. ... ok
test_minimax_returns_best_move (__main__.TestChessBotRook.test_minimax_returns_best_move)
Test that minimax returns a valid move that improves position. ... ok
test_move_ordering (__main__.TestChessBotRook.test_move_ordering)
Test that move ordering prioritizes captures. ... ok
test_nodes_evaluated_counter (__main__.TestChessBotRook.test_nodes_evaluated_counter)
Test that nodes_evaluated counter works correctly. ... TestRookBot evaluated 80 positions in 0.02 seconds
ok
test_positional_evaluation (__main__.TestChessBotRook.test_positional_evaluation)
Test that positional evaluation works correctly. ... ok
test_positional_tables (__main__.TestChessBotRook.test_positional_tables)
Test that positional tables are properly initialized. ... ok
test_set_depth (__main__.TestChessBotRook.test_set_depth)
Test setting bot depth. ... TestRookBot search depth set to 4
ok

----------------------------------------------------------------------
Ran 15 tests in 0.117s

OK

Running performance test...
PerfBot evaluated 693 positions in 0.15 seconds
PerfBot evaluated 799 positions in 0.17 seconds
PerfBot evaluated 706 positions in 0.13 seconds
Performance test completed:
- 3 moves calculated in 0.45 seconds
- Average time per move: 0.15 seconds
- Total nodes evaluated: 706















======================================
======================================


cases where it failed:


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_chess_bot_rook.py
Running Chess Bot Rook Unit Tests...
test_bot_initialization (__main__.TestChessBotRook.test_bot_initialization)
Test bot initialization with default and custom parameters. ... ok
test_end_game_detection (__main__.TestChessBotRook.test_end_game_detection)
Test that end game is correctly detected. ... ok
test_evaluate_board_checkmate (__main__.TestChessBotRook.test_evaluate_board_checkmate)
Test board evaluation in checkmate position. ... ok
test_evaluate_board_material_advantage (__main__.TestChessBotRook.test_evaluate_board_material_advantage)
Test board evaluation with material advantage. ... ok
test_evaluate_board_stalemate (__main__.TestChessBotRook.test_evaluate_board_stalemate)
Test board evaluation in stalemate position. ... FAIL
test_evaluate_board_starting_position (__main__.TestChessBotRook.test_evaluate_board_starting_position)
Test board evaluation at starting position. ... ok
test_get_best_move_captures_queen (__main__.TestChessBotRook.test_get_best_move_captures_queen)
Test that bot captures a queen when possible. ... ERROR
test_get_best_move_returns_legal_move (__main__.TestChessBotRook.test_get_best_move_returns_legal_move)
Test that get_best_move returns a legal move. ... TestRookBot evaluated 80 positions in 0.03 seconds
ok
test_minimax_depth_zero (__main__.TestChessBotRook.test_minimax_depth_zero)
Test minimax with depth 0. ... ok
test_minimax_returns_best_move (__main__.TestChessBotRook.test_minimax_returns_best_move)
Test that minimax returns a valid move. ... ERROR
test_move_ordering (__main__.TestChessBotRook.test_move_ordering)
Test that move ordering prioritizes captures. ... ok
test_nodes_evaluated_counter (__main__.TestChessBotRook.test_nodes_evaluated_counter)
Test that nodes_evaluated counter works correctly. ... TestRookBot evaluated 80 positions in 0.03 seconds
ok
test_positional_evaluation (__main__.TestChessBotRook.test_positional_evaluation)
Test that positional evaluation works correctly. ... ok
test_positional_tables (__main__.TestChessBotRook.test_positional_tables)
Test that positional tables are properly initialized. ... ok
test_set_depth (__main__.TestChessBotRook.test_set_depth)
Test setting bot depth. ... TestRookBot search depth set to 4
ok

======================================================================
ERROR: test_get_best_move_captures_queen (__main__.TestChessBotRook.test_get_best_move_captures_queen)
Test that bot captures a queen when possible.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test\test_chess_bot_rook.py", line 136, in test_get_best_move_captures_queen
    self.board.push_san("Qh4")  # Expose the queen
    ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\chess\__init__.py", line 3105, in push_san
    move = self.parse_san(san)
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\chess\__init__.py", line 3087, in parse_san
    raise IllegalMoveError(f"illegal san: {san!r} in {self.fen()}")
chess.IllegalMoveError: illegal san: 'Qh4' in rnb1kbnr/pppp1ppp/8/4p3/8/3P4/PPP1PPPP/RNBQKBNR b KQkq - 0 2

======================================================================
ERROR: test_minimax_returns_best_move (__main__.TestChessBotRook.test_minimax_returns_best_move)
Test that minimax returns a valid move.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test\test_chess_bot_rook.py", line 114, in test_minimax_returns_best_move
    self.board.push_san("Qh4")  # Expose the queen
    ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\chess\__init__.py", line 3105, in push_san
    move = self.parse_san(san)
           ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\chess\__init__.py", line 3087, in parse_san
    raise IllegalMoveError(f"illegal san: {san!r} in {self.fen()}")
chess.IllegalMoveError: illegal san: 'Qh4' in rnb1kbnr/pppp1ppp/8/4p3/8/3P4/PPP1PPPP/RNBQKBNR b KQkq - 0 2

======================================================================
FAIL: test_evaluate_board_stalemate (__main__.TestChessBotRook.test_evaluate_board_stalemate)
Test board evaluation in stalemate position.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test\test_chess_bot_rook.py", line 84, in test_evaluate_board_stalemate
    self.assertTrue(self.board.is_stalemate())
AssertionError: False is not true

----------------------------------------------------------------------
Ran 15 tests in 0.184s

FAILED (failures=1, errors=2)

Running performance test...
PerfBot evaluated 693 positions in 0.17 seconds
PerfBot evaluated 799 positions in 0.21 seconds
PerfBot evaluated 706 positions in 0.19 seconds
Performance test completed:
- 3 moves calculated in 0.57 seconds
- Average time per move: 0.19 seconds