#!/usr/bin/env python3
"""
Test script for the hybrid chess bot that combines minimax and MCTS.
"""

import chess
from chess_bot import ChessBot

def test_hybrid_bot():
    """Test the hybrid chess bot functionality."""
    print("=== Testing Hybrid Chess Bot ===\n")
    
    # Create different bot configurations
    print("Creating different bot configurations...")
    
    # Pure minimax bot
    minimax_bot = ChessBot(depth=3, name="MinimaxBot", use_hybrid=False)
    
    # Hybrid bot with default settings
    hybrid_bot = ChessBot(depth=3, name="HybridBot", mcts_simulations=500, 
                         top_moves_count=3, use_hybrid=True)
    
    # Aggressive hybrid bot (more simulations, more candidates)
    aggressive_bot = ChessBot(depth=4, name="AggressiveHybrid", mcts_simulations=1000, 
                             top_moves_count=5, use_hybrid=True)
    
    bots = [minimax_bot, hybrid_bot, aggressive_bot]
    
    # Display configurations
    print("\nBot Configurations:")
    for bot in bots:
        config = bot.get_config()
        print(f"{config['name']}:")
        print(f"  - Depth: {config['depth']}")
        print(f"  - MCTS Simulations: {config['mcts_simulations']}")
        print(f"  - Top Moves Count: {config['top_moves_count']}")
        print(f"  - Hybrid Mode: {config['use_hybrid']}")
        print()
    
    # Test on a few different positions
    test_positions = [
        # Starting position
        chess.Board(),
        
        # Middle game position
        chess.Board("r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4"),
        
        # Tactical position
        chess.Board("r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4")
    ]
    
    position_names = ["Starting Position", "Middle Game", "Tactical Position"]
    
    for i, (board, pos_name) in enumerate(zip(test_positions, position_names)):
        print(f"\n{'='*50}")
        print(f"Testing Position {i+1}: {pos_name}")
        print(f"FEN: {board.fen()}")
        print(f"{'='*50}")
        
        for bot in bots:
            print(f"\n--- {bot.name} ---")
            try:
                move = bot.get_best_move(board.copy())
                print(f"Best move: {move}")
                
                # Show the move in algebraic notation
                if move:
                    temp_board = board.copy()
                    san_move = temp_board.san(move)
                    print(f"Move in algebraic notation: {san_move}")
                
            except Exception as e:
                print(f"Error: {e}")
            
            print("-" * 30)

def test_configuration_changes():
    """Test dynamic configuration changes."""
    print("\n=== Testing Configuration Changes ===\n")
    
    bot = ChessBot(name="ConfigTestBot")
    
    print("Initial configuration:")
    print(bot.get_config())
    
    print("\nChanging configurations...")
    bot.set_depth(4)
    bot.set_mcts_simulations(2000)
    bot.set_top_moves_count(7)
    bot.set_hybrid_mode(False)
    
    print("\nNew configuration:")
    print(bot.get_config())
    
    print("\nSwitching back to hybrid mode...")
    bot.set_hybrid_mode(True)
    
    # Test a simple position
    board = chess.Board()
    print(f"\nTesting move selection with new configuration...")
    move = bot.get_best_move(board)
    print(f"Selected move: {move}")

if __name__ == "__main__":
    test_hybrid_bot()
    test_configuration_changes()
    print("\n=== Testing Complete ===")