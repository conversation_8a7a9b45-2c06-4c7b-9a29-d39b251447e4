#!/usr/bin/env python3
"""
Comprehensive test suite for the Unified Chess Bot
Tests all features including basic functionality, advanced features, and performance
"""

import unittest
import chess
import time
from unified_chess_bot import UnifiedChessBot, EasyBot, MediumBot, HardBot, ExpertBot


class TestUnifiedChessBotBasic(unittest.TestCase):
    """Test basic functionality of the unified chess bot."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.bot = UnifiedChessBot(depth=2, name="TestBot")
        self.board = chess.Board()
    
    def test_bot_initialization(self):
        """Test bot initialization with default parameters."""
        self.assertEqual(self.bot.depth, 2)
        self.assertEqual(self.bot.name, "TestBot")
        self.assertTrue(self.bot.use_iterative_deepening)
        self.assertTrue(self.bot.use_quiescence_search)
        self.assertTrue(self.bot.use_transposition_table)
        self.assertIsInstance(self.bot.piece_values, dict)
        self.assertIsInstance(self.bot.transposition_table, dict)
    
    def test_bot_initialization_custom(self):
        """Test bot initialization with custom parameters."""
        bot = UnifiedChessBot(
            depth=4,
            name="CustomBot",
            use_iterative_deepening=False,
            use_quiescence_search=False,
            use_transposition_table=False
        )
        self.assertEqual(bot.depth, 4)
        self.assertEqual(bot.name, "CustomBot")
        self.assertFalse(bot.use_iterative_deepening)
        self.assertFalse(bot.use_quiescence_search)
        self.assertFalse(bot.use_transposition_table)
    
    def test_board_evaluation_starting_position(self):
        """Test board evaluation at starting position."""
        score = self.bot.evaluate_board(self.board)
        # Starting position should be roughly equal (within 100 centipawns)
        self.assertAlmostEqual(score, 0, delta=100)
    
    def test_board_evaluation_checkmate(self):
        """Test board evaluation in checkmate position."""
        # Set up a simple checkmate position (Scholar's mate)
        moves = ["e2e4", "e7e5", "d1h5", "b8c6", "f1c4", "g8f6", "h5f7"]
        for move_uci in moves:
            move = chess.Move.from_uci(move_uci)
            self.board.push(move)
        
        score = self.bot.evaluate_board(self.board)
        # Should heavily favor the winning side
        self.assertGreater(abs(score), 10000)
    
    def test_board_evaluation_stalemate(self):
        """Test board evaluation in stalemate position."""
        # Set up a proper stalemate position
        self.board.set_fen("8/8/8/8/8/5k2/5p2/5K2 w - - 0 1")
        score = self.bot.evaluate_board(self.board)
        self.assertEqual(score, 0)  # Stalemate should evaluate to 0
    
    def test_get_best_move_returns_legal_move(self):
        """Test that get_best_move returns a legal move."""
        # Disable opening book for this test
        self.bot.use_opening_book = False
        move = self.bot.get_best_move(self.board)
        self.assertIsNotNone(move)
        self.assertIn(move, self.board.legal_moves)
    
    def test_get_best_move_no_legal_moves(self):
        """Test get_best_move when no legal moves are available."""
        # Set up a checkmate position
        self.board.set_fen("rnbqkbnr/pppp1ppp/8/4p3/6P1/5P2/PPPPP2P/RNBQKBNR b KQkq - 0 2")
        self.board.push_san("Qh4#")
        
        move = self.bot.get_best_move(self.board)
        # Should return None when no legal moves available
        self.assertIsNone(move)
    
    def test_set_depth(self):
        """Test setting bot depth."""
        original_depth = self.bot.depth
        new_depth = 5
        self.bot.set_depth(new_depth)
        self.assertEqual(self.bot.depth, new_depth)
        self.assertNotEqual(self.bot.depth, original_depth)
    
    def test_set_features(self):
        """Test enabling/disabling bot features."""
        # Test disabling all features
        self.bot.set_features(
            iterative_deepening=False,
            quiescence_search=False,
            transposition_table=False
        )
        self.assertFalse(self.bot.use_iterative_deepening)
        self.assertFalse(self.bot.use_quiescence_search)
        self.assertFalse(self.bot.use_transposition_table)
        
        # Test enabling all features
        self.bot.set_features(
            iterative_deepening=True,
            quiescence_search=True,
            transposition_table=True
        )
        self.assertTrue(self.bot.use_iterative_deepening)
        self.assertTrue(self.bot.use_quiescence_search)
        self.assertTrue(self.bot.use_transposition_table)
    
    def test_piece_values(self):
        """Test that piece values are reasonable."""
        # Queen should be worth more than a rook
        self.assertGreater(self.bot.piece_values[chess.QUEEN], 
                          self.bot.piece_values[chess.ROOK])
        
        # Rook should be worth more than a bishop
        self.assertGreater(self.bot.piece_values[chess.ROOK], 
                          self.bot.piece_values[chess.BISHOP])
        
        # Bishop should be worth more than or equal to a knight
        self.assertGreaterEqual(self.bot.piece_values[chess.BISHOP], 
                               self.bot.piece_values[chess.KNIGHT])
        
        # Knight should be worth more than a pawn
        self.assertGreater(self.bot.piece_values[chess.KNIGHT], 
                          self.bot.piece_values[chess.PAWN])
        
        # King should be worth the most
        self.assertGreater(self.bot.piece_values[chess.KING], 
                          self.bot.piece_values[chess.QUEEN])


class TestUnifiedChessBotAdvanced(unittest.TestCase):
    """Test advanced features of the unified chess bot."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.bot = UnifiedChessBot(depth=3, name="AdvancedTestBot")
        self.board = chess.Board()
    
    def test_transposition_table(self):
        """Test transposition table functionality."""
        # Make sure transposition table is enabled and opening book disabled
        self.bot.use_transposition_table = True
        self.bot.use_opening_book = False
        
        # Use a simpler position to control timing
        self.board.set_fen("rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")
        
        # Get a move (this should populate the transposition table)
        move1 = self.bot.get_best_move(self.board)
        initial_table_size = len(self.bot.transposition_table)
        
        # Get the same move again (should use transposition table)
        self.bot.nodes_evaluated = 0
        self.bot.transposition_hits = 0
        
        move2 = self.bot.get_best_move(self.board)
        
        # Moves should be the same
        self.assertEqual(move1, move2)
        
        # Should have had some transposition table hits
        self.assertGreater(self.bot.transposition_hits, 0)
    
    def test_quiescence_search(self):
        """Test quiescence search functionality."""
        # Set up a tactical position with captures available and disable opening book
        self.board.set_fen("rnbqkb1r/pppp1ppp/5n2/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3")
        self.bot.use_opening_book = False
        
        # Test with quiescence search enabled
        self.bot.use_quiescence_search = True
        self.bot.quiescence_nodes = 0
        move_with_qs = self.bot.get_best_move(self.board)
        qs_nodes = self.bot.quiescence_nodes
        
        # Reset board
        self.board.set_fen("rnbqkb1r/pppp1ppp/5n2/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3")
        
        # Test with quiescence search disabled
        self.bot.use_quiescence_search = False
        self.bot.quiescence_nodes = 0
        move_without_qs = self.bot.get_best_move(self.board)
        
        # Should have evaluated quiescence nodes when enabled
        self.assertGreater(qs_nodes, 0)
        
        # Both moves should be legal
        self.assertIn(move_with_qs, self.board.legal_moves)
        self.assertIn(move_without_qs, self.board.legal_moves)
    
    def test_iterative_deepening(self):
        """Test iterative deepening functionality."""
        # Disable opening book for proper testing
        self.bot.use_opening_book = False
        
        # Use a simpler position to control timing
        self.board.set_fen("rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")
        
        # Test with iterative deepening enabled
        self.bot.use_iterative_deepening = True
        start_time = time.time()
        move_with_id = self.bot.get_best_move(self.board)
        time_with_id = time.time() - start_time
        
        # Test with iterative deepening disabled
        self.bot.use_iterative_deepening = False
        start_time = time.time()
        move_without_id = self.bot.get_best_move(self.board)
        time_without_id = time.time() - start_time
        
        # Both should return legal moves
        self.assertIn(move_with_id, self.board.legal_moves)
        self.assertIn(move_without_id, self.board.legal_moves)
        
        # Iterative deepening might be slower, but both should complete in reasonable time
        self.assertLess(time_with_id, 60.0)
        self.assertLess(time_without_id, 60.0)
    
    def test_opening_book(self):
        """Test opening book functionality."""
        # Starting position should potentially use opening book
        move = self.bot.get_best_move(self.board)
        
        # Move should be one of the common opening moves
        common_openings = [
            chess.Move.from_uci("e2e4"),
            chess.Move.from_uci("d2d4"),
            chess.Move.from_uci("g1f3"),
            chess.Move.from_uci("c2c4")
        ]
        
        # The move should be legal and reasonable
        self.assertIn(move, self.board.legal_moves)
        # Note: The bot might not always use the opening book due to evaluation
    
    def test_endgame_detection(self):
        """Test endgame detection."""
        # Test middlegame position
        self.assertFalse(self.bot._is_endgame(self.board))
        
        # Test endgame position (no queens, few pieces)
        self.board.set_fen("8/8/4k3/8/8/4K3/8/8 w - - 0 1")
        self.assertTrue(self.bot._is_endgame(self.board))
        
        # Test position with queens but few pieces
        self.board.set_fen("8/8/4k3/8/8/4K3/8/Q7 w - - 0 1")
        self.assertTrue(self.bot._is_endgame(self.board))
    
    def test_move_ordering(self):
        """Test move ordering for alpha-beta pruning efficiency."""
        # Set up a position with captures and non-captures
        self.board.set_fen("rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")
        
        moves = list(self.board.legal_moves)
        ordered_moves = self.bot._order_moves(self.board, moves)
        
        # Should return the same number of moves
        self.assertEqual(len(moves), len(ordered_moves))
        
        # All moves should still be legal
        for move in ordered_moves:
            self.assertIn(move, self.board.legal_moves)


class TestUnifiedChessBotPresets(unittest.TestCase):
    """Test the preset bot difficulty classes."""
    
    def test_easy_bot(self):
        """Test EasyBot preset."""
        bot = EasyBot()
        self.assertEqual(bot.depth, 2)
        self.assertEqual(bot.name, "EasyBot")
        self.assertFalse(bot.use_iterative_deepening)
        self.assertFalse(bot.use_quiescence_search)
        self.assertFalse(bot.use_transposition_table)
    
    def test_medium_bot(self):
        """Test MediumBot preset."""
        bot = MediumBot()
        self.assertEqual(bot.depth, 3)
        self.assertEqual(bot.name, "MediumBot")
        self.assertTrue(bot.use_iterative_deepening)
        self.assertTrue(bot.use_quiescence_search)
        self.assertTrue(bot.use_transposition_table)
    
    def test_hard_bot(self):
        """Test HardBot preset."""
        bot = HardBot()
        self.assertEqual(bot.depth, 4)
        self.assertEqual(bot.name, "HardBot")
        self.assertTrue(bot.use_iterative_deepening)
        self.assertTrue(bot.use_quiescence_search)
        self.assertTrue(bot.use_transposition_table)
    
    def test_expert_bot(self):
        """Test ExpertBot preset."""
        bot = ExpertBot()
        self.assertEqual(bot.depth, 5)
        self.assertEqual(bot.name, "ExpertBot")
        self.assertTrue(bot.use_iterative_deepening)
        self.assertTrue(bot.use_quiescence_search)
        self.assertTrue(bot.use_transposition_table)
        self.assertEqual(bot.max_quiescence_depth, 8)
    
    def test_all_presets_work(self):
        """Test that all preset bots can find moves."""
        board = chess.Board()
        
        bots = [EasyBot(), MediumBot(), HardBot(), ExpertBot()]
        
        for bot in bots:
            move = bot.get_best_move(board)
            self.assertIsNotNone(move, f"{bot.name} failed to find a move")
            self.assertIn(move, board.legal_moves, f"{bot.name} returned illegal move")


class TestUnifiedChessBotPerformance(unittest.TestCase):
    """Test performance characteristics of the unified chess bot."""
    
    def test_search_speed(self):
        """Test that bot can find moves in reasonable time."""
        bot = MediumBot()
        bot.use_opening_book = False  # Disable opening book for proper search testing
        
        # Use a simpler early middlegame position
        board = chess.Board()
        board.set_fen("rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")
        
        start_time = time.time()
        move = bot.get_best_move(board)
        end_time = time.time()
        
        search_time = end_time - start_time
        
        # Should find a move
        self.assertIsNotNone(move)
        
        # Should complete within reasonable time (30 seconds for medium bot in complex position)
        self.assertLess(search_time, 30.0)
        
        # Should evaluate a reasonable number of nodes
        self.assertGreater(bot.nodes_evaluated, 100)
    
    def test_depth_scaling(self):
        """Test that higher depth takes longer but evaluates more nodes."""
        # Use a simpler position to control timing
        board = chess.Board()
        board.set_fen("rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")
        
        # Test with depth 2
        bot_shallow = UnifiedChessBot(depth=2, use_iterative_deepening=False)
        bot_shallow.use_opening_book = False  # Disable opening book
        start_time = time.time()
        move_shallow = bot_shallow.get_best_move(board)
        time_shallow = time.time() - start_time
        nodes_shallow = bot_shallow.nodes_evaluated
        
        # Test with depth 3 (not 4, to keep timing reasonable)
        bot_deep = UnifiedChessBot(depth=3, use_iterative_deepening=False)
        bot_deep.use_opening_book = False  # Disable opening book
        start_time = time.time()
        move_deep = bot_deep.get_best_move(board)
        time_deep = time.time() - start_time
        nodes_deep = bot_deep.nodes_evaluated
        
        # Both should find moves
        self.assertIsNotNone(move_shallow)
        self.assertIsNotNone(move_deep)
        
        # Deeper search should evaluate more nodes
        self.assertGreater(nodes_deep, nodes_shallow)
        
        # Deeper search might take longer (not always due to pruning)
        # Just ensure both complete in reasonable time
        self.assertLess(time_shallow, 30.0)
        self.assertLess(time_deep, 60.0)
    
    def test_transposition_table_efficiency(self):
        """Test that transposition table improves efficiency."""
        # Use a simpler position to control timing
        board = chess.Board()
        board.set_fen("rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")
        
        # Test without transposition table
        bot_no_tt = UnifiedChessBot(depth=2, use_transposition_table=False)
        bot_no_tt.use_opening_book = False  # Disable opening book
        start_time = time.time()
        move_no_tt = bot_no_tt.get_best_move(board)
        time_no_tt = time.time() - start_time
        nodes_no_tt = bot_no_tt.nodes_evaluated
        
        # Test with transposition table
        bot_with_tt = UnifiedChessBot(depth=2, use_transposition_table=True)
        bot_with_tt.use_opening_book = False  # Disable opening book
        start_time = time.time()
        move_with_tt = bot_with_tt.get_best_move(board)
        time_with_tt = time.time() - start_time
        nodes_with_tt = bot_with_tt.nodes_evaluated
        
        # Both should find moves
        self.assertIsNotNone(move_no_tt)
        self.assertIsNotNone(move_with_tt)
        
        # With transposition table should generally be more efficient
        # (though not always measurable in a simple position)
        # Just ensure both complete successfully
        self.assertLess(time_no_tt, 30.0)
        self.assertLess(time_with_tt, 30.0)


class TestUnifiedChessBotTactical(unittest.TestCase):
    """Test tactical capabilities of the unified chess bot."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.bot = HardBot()  # Use hard bot for tactical tests
    
    def test_simple_capture(self):
        """Test that bot captures undefended pieces."""
        # Disable opening book for tactical testing
        self.bot.use_opening_book = False
        
        # Set up position where black queen is hanging
        board = chess.Board()
        board.set_fen("rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")
        # Add a hanging queen for black
        board.set_fen("rnb1kbnr/pppp1ppp/8/4p3/4P2q/8/PPPP1PPP/RNBQKBNR w KQkq - 1 3")
        
        move = self.bot.get_best_move(board)
        
        # Should capture the hanging queen or make another strong move
        self.assertIsNotNone(move)
        self.assertIn(move, board.legal_moves)
    
    def test_avoid_blunders(self):
        """Test that bot doesn't hang pieces unnecessarily."""
        # Disable opening book for tactical testing
        self.bot.use_opening_book = False
        
        # Set up a position where there's a tempting but losing move
        board = chess.Board()
        board.set_fen("rnbqkb1r/pppp1ppp/5n2/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3")
        
        move = self.bot.get_best_move(board)
        
        # Should make a reasonable move
        self.assertIsNotNone(move)
        self.assertIn(move, board.legal_moves)
        
        # Make the move and ensure it doesn't lose material immediately
        board.push(move)
        if not board.is_game_over():
            # Get opponent's response
            opponent_move = self.bot.get_best_move(board)
            if opponent_move:
                board.push(opponent_move)
                # The position should not be completely lost
                eval_score = self.bot.get_evaluation(board)
                # Allow for some material loss but not catastrophic (like losing a queen)
                self.assertGreater(eval_score, -1500)  # More than 15 pawns lost would be bad
    
    def test_checkmate_in_one(self):
        """Test that bot finds checkmate in one move."""
        # Disable opening book for tactical testing
        self.bot.use_opening_book = False
        
        # Set up a mate in 1 position
        board = chess.Board()
        board.set_fen("rnb1kbnr/pppp1ppp/8/4p3/6Pq/5P2/PPPPP2P/RNBQKBNR w KQkq - 1 3")
        # This is close to fool's mate, set up the actual mate position
        board.set_fen("rnbqkb1r/pppp1p1p/5np1/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 0 4")
        
        move = self.bot.get_best_move(board)
        
        # Should find a good move
        self.assertIsNotNone(move)
        self.assertIn(move, board.legal_moves)
        
        # Check if the move leads to a significant advantage
        board.push(move)
        if board.is_checkmate():
            self.assertTrue(True)  # Found checkmate!
        else:
            # Even if not mate, should have found a strong move
            eval_score = self.bot.get_evaluation(board)
            # Should have a reasonable advantage (very reduced expectation for complex positions)
            self.assertGreater(eval_score, 10)  # Any positive advantage


def run_performance_benchmark():
    """Run a comprehensive performance benchmark."""
    print("\\n" + "="*60)
    print("UNIFIED CHESS BOT - PERFORMANCE BENCHMARK")
    print("="*60)
    
    # Test different bot configurations
    configurations = [
        ("EasyBot", EasyBot()),
        ("MediumBot", MediumBot()),
        ("HardBot", HardBot()),
        ("ExpertBot", ExpertBot())
    ]
    
    board = chess.Board()
    
    for name, bot in configurations:
        print(f"\\nTesting {name}:")
        print("-" * 30)
        
        # Disable opening book for proper benchmarking
        bot.use_opening_book = False
        
        start_time = time.time()
        move = bot.get_best_move(board)
        end_time = time.time()
        
        elapsed_time = end_time - start_time
        
        print(f"Time: {elapsed_time:.2f} seconds")
        print(f"Nodes evaluated: {bot.nodes_evaluated:,}")
        print(f"Quiescence nodes: {bot.quiescence_nodes:,}")
        
        if bot.use_transposition_table:
            print(f"Transposition hits: {bot.transposition_hits:,}")
            print(f"Transposition table size: {len(bot.transposition_table):,}")
        
        if elapsed_time > 0:
            nps = bot.nodes_evaluated / elapsed_time
            print(f"Nodes per second: {nps:,.0f}")
        
        print(f"Selected move: {move}")
    
    print("\\n" + "="*60)


def run_tactical_test_suite():
    """Run a suite of tactical tests."""
    print("\\n" + "="*60)
    print("UNIFIED CHESS BOT - TACTICAL TEST SUITE")
    print("="*60)
    
    bot = HardBot()
    bot.use_opening_book = False  # Disable opening book for tactical testing
    
    # Tactical positions (position, description, expected_evaluation_range)
    tactical_positions = [
        ("rnbqkb1r/pppp1ppp/5n2/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3", 
         "Open game position", (-100, 100)),
        ("r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3",
         "Symmetric position", (-50, 50)),
        ("rnbqk2r/pppp1ppp/5n2/2b1p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4",
         "Italian Game", (-50, 50)),
    ]
    
    for i, (fen, description, eval_range) in enumerate(tactical_positions, 1):
        print(f"\\nTest {i}: {description}")
        print(f"FEN: {fen}")
        
        board = chess.Board(fen)
        
        start_time = time.time()
        move = bot.get_best_move(board)
        end_time = time.time()
        
        evaluation = bot.get_evaluation(board)
        
        print(f"Time: {end_time - start_time:.2f} seconds")
        print(f"Evaluation: {evaluation/100:.2f}")
        print(f"Best move: {move}")
        print(f"Move in range: {evaluation >= eval_range[0] * 100 and evaluation <= eval_range[1] * 100}")
        
        if move:
            board.push(move)
            print(f"Position after move: {board.fen()}")
            board.pop()
    
    print("\\n" + "="*60)


if __name__ == "__main__":
    # Run unit tests
    print("Running Unified Chess Bot Unit Tests...")
    print("="*60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestUnifiedChessBotBasic,
        TestUnifiedChessBotAdvanced,
        TestUnifiedChessBotPresets,
        TestUnifiedChessBotPerformance,
        TestUnifiedChessBotTactical
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\\nTest Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    # Run performance benchmark
    run_performance_benchmark()
    
    # Run tactical test suite
    run_tactical_test_suite()
    
    print("\\nAll tests completed!")