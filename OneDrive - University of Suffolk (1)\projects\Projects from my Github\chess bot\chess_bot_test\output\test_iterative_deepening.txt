PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_iterative_deepening.py
=== Testing Iterative Deepening Chess Bot ===


--- Testing: Starting position ---
FEN: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Board:
r n b q k b n r
p p p p p p p p
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . . . . .
P P P P P P P P
R N B Q K B N R

IterativeBot analysis:
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.05s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.39s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.36s, nodes=4399
  Depth 5: score=68, move=g1f3, time=1.17s, nodes=6947
IterativeBot completed search: 6947 nodes in 3.00s
Best move: g1f3
Nodes evaluated: 6947
Time taken: 3.00s

RegularBot analysis:
RegularBot evaluated 4128 positions in 1.13 seconds
Best move: g1f3
Nodes evaluated: 4128
Time taken: 1.13s

--- Comparison ---
Same move chosen: True
Efficiency (nodes/second):
  Iterative: 2315
  Regular: 3664
--------------------------------------------------

--- Testing: Fork opportunity ---
FEN: rnbqkb1r/pppp1ppp/5n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4
Board:
r n b q k b . r
p p p p . p p p
. . . . . n . .
. . . . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q K . . R

IterativeBot analysis:
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=218, move=c4f7, time=0.02s, nodes=39
  Depth 2: score=56, move=f3e5, time=0.06s, nodes=143
  Depth 3: score=239, move=f3e5, time=1.36s, nodes=2393
  Depth 4: score=54, move=e1f1, time=1.56s, nodes=5572
IterativeBot completed search: 5572 nodes in 3.00s
Best move: e1f1
Nodes evaluated: 5572
Time taken: 3.00s

RegularBot analysis:
RegularBot evaluated 11642 positions in 4.24 seconds
Best move: c4f7
Nodes evaluated: 11642
Time taken: 4.24s

--- Comparison ---
Same move chosen: False
Iterative: e1f1, Regular: c4f7
Efficiency (nodes/second):
  Iterative: 1857
  Regular: 2748
--------------------------------------------------

--- Testing: King and pawn endgame ---
FEN: 8/8/8/8/8/3k4/3P4/3K4 w - - 0 1
Board:
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . k . . . .
. . . P . . . .
. . . K . . . .

IterativeBot analysis:
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=53, move=d1e1, time=0.00s, nodes=3
  Depth 2: score=43, move=d1e1, time=0.00s, nodes=11
  Depth 3: score=68, move=d1e1, time=0.00s, nodes=38
  Depth 4: score=67, move=d1e1, time=0.02s, nodes=144
  Depth 5: score=94, move=d1e1, time=0.08s, nodes=538
  Depth 6: score=92, move=d1e1, time=0.27s, nodes=2100
IterativeBot completed search: 2100 nodes in 0.37s
Best move: d1e1
Nodes evaluated: 2100
Time taken: 0.37s

RegularBot analysis:
RegularBot evaluated 82 positions in 0.01 seconds
Best move: d1e1
Nodes evaluated: 82
Time taken: 0.01s

--- Comparison ---
Same move chosen: True
Efficiency (nodes/second):
  Iterative: 5674
  Regular: 7454
--------------------------------------------------

--- Testing: Mate in 2 ---
FEN: 2bqkbn1/2pppp2/np2N3/r3P1p1/p2N4/5Q2/PPPPKPP1/RNB2B1R w KQkq - 0 1
Board:
. . b q k b n .
. . p p p p . .
n p . . N . . .
r . . . P . p .
p . . N . . . .
. . . . . Q . .
P P P P K P P .
R N B . . B . R

IterativeBot analysis:
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=1809, move=e6d8, time=0.02s, nodes=52
  Depth 2: score=1511, move=e6d8, time=0.06s, nodes=177
  Depth 3: score=1621, move=e6d8, time=1.47s, nodes=3673
  Depth 4: score=1358, move=e6d8, time=1.44s, nodes=7445
IterativeBot completed search: 7445 nodes in 3.00s
Best move: e6d8
Nodes evaluated: 7445
Time taken: 3.00s

RegularBot analysis:
RegularBot evaluated 3187 positions in 0.94 seconds
Best move: e6d8
Nodes evaluated: 3187
Time taken: 0.95s

--- Comparison ---
Same move chosen: True
Efficiency (nodes/second):
  Iterative: 2481
  Regular: 3367
--------------------------------------------------

=== Testing Time Management ===


--- Testing with 0.5s time limit ---
TimeLimitBot time limit set to 0.5 seconds
TimeLimitBot starting iterative deepening search (max depth: 10, time limit: 0.5s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.04s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.36s, nodes=996
  Depth 4: score=0, move=g1f3, time=0.08s, nodes=1186
TimeLimitBot completed search: 1186 nodes in 0.50s
Move: g1f3
Requested time: 0.5s
Actual time: 0.50s
Time efficiency: 1.00
Nodes evaluated: 1186

--- Testing with 1.0s time limit ---
TimeLimitBot time limit set to 1.0 seconds
TimeLimitBot starting iterative deepening search (max depth: 10, time limit: 1.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.04s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.29s, nodes=996
  Depth 4: score=0, move=g1f3, time=0.66s, nodes=3307
TimeLimitBot completed search: 3307 nodes in 1.00s
Move: g1f3
Requested time: 1.0s
Actual time: 1.00s
Time efficiency: 1.00
Nodes evaluated: 3307

--- Testing with 2.0s time limit ---
TimeLimitBot time limit set to 2.0 seconds
TimeLimitBot starting iterative deepening search (max depth: 10, time limit: 2.0s)
  Depth 1: score=54, move=g1f3, time=0.00s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.02s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.26s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.28s, nodes=4399
  Depth 5: score=81, move=g1f3, time=0.43s, nodes=5922
TimeLimitBot completed search: 5922 nodes in 2.00s
Move: g1f3
Requested time: 2.0s
Actual time: 2.00s
Time efficiency: 1.00
Nodes evaluated: 5922

--- Testing with 5.0s time limit ---
TimeLimitBot time limit set to 5.0 seconds
TimeLimitBot starting iterative deepening search (max depth: 10, time limit: 5.0s)
  Depth 1: score=54, move=g1f3, time=0.00s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.02s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.25s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.33s, nodes=4399
  Depth 5: score=77, move=b1a3, time=3.39s, nodes=13892
TimeLimitBot completed search: 13892 nodes in 5.00s
Move: b1a3
Requested time: 5.0s
Actual time: 5.00s
Time efficiency: 1.00
Nodes evaluated: 13892

=== Testing Depth Scaling ===


--- Testing with max depth 3 ---
DepthTestBot maximum depth set to 3
DepthTestBot starting iterative deepening search (max depth: 3, time limit: 5.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.04s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.39s, nodes=996
DepthTestBot completed search: 996 nodes in 0.44s
Move: g1f3
Max depth: 3
Time taken: 0.44s
Nodes evaluated: 996

--- Testing with max depth 4 ---
DepthTestBot maximum depth set to 4
DepthTestBot starting iterative deepening search (max depth: 4, time limit: 5.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.04s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.42s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.42s, nodes=4399
DepthTestBot completed search: 4399 nodes in 1.89s
Move: g1f3
Max depth: 4
Time taken: 1.89s
Nodes evaluated: 4399

--- Testing with max depth 5 ---
DepthTestBot maximum depth set to 5
DepthTestBot starting iterative deepening search (max depth: 5, time limit: 5.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.02s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.27s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.09s, nodes=4399
  Depth 5: score=290, move=g1h3, time=3.71s, nodes=9855
DepthTestBot completed search: 9855 nodes in 5.11s
Move: g1h3
Max depth: 5
Time taken: 5.13s
Nodes evaluated: 9855

--- Testing with max depth 6 ---
DepthTestBot maximum depth set to 6
DepthTestBot starting iterative deepening search (max depth: 6, time limit: 5.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.25s, nodes=94
  Depth 3: score=54, move=g1f3, time=1.03s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.76s, nodes=4399
  Depth 5: score=98, move=g1f3, time=1.18s, nodes=4810
DepthTestBot completed search: 4810 nodes in 5.18s
Move: g1f3
Max depth: 6
Time taken: 5.39s
Nodes evaluated: 4810

--- Testing with max depth 7 ---
DepthTestBot maximum depth set to 7
DepthTestBot starting iterative deepening search (max depth: 7, time limit: 5.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.45s, nodes=94
  Depth 3: score=54, move=g1f3, time=2.25s, nodes=996
  Depth 4: score=-9, move=g1f3, time=1.54s, nodes=1752
DepthTestBot completed search: 1752 nodes in 5.09s
Move: g1f3
Max depth: 7
Time taken: 5.17s
Nodes evaluated: 1752

=== Demonstrating Iterative Deepening Process ===

Position:
r . b q k b . r
p p p p . p p p
. . n . . n . .
. . . . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q K . . R

Running iterative deepening search...
(Watch how the search progresses through increasing depths)
DemoBot starting iterative deepening search (max depth: 8, time limit: 5.0s)
  Depth 1: score=168, move=c4f7, time=0.02s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.22s, nodes=313
  Depth 3: score=167, move=b1c3, time=2.01s, nodes=3122
  Depth 4: score=-165, move=e1g1, time=2.58s, nodes=7086
DemoBot completed search: 7086 nodes in 5.00s

Final best move: e1g1
Total nodes evaluated: 7086

=== All tests completed ===













=================================================






PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_iterative_deepening.py
=== Testing Iterative Deepening Chess Bot ===


--- Testing: Starting position ---
FEN: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Board:
r n b q k b n r
p p p p p p p p
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . . . . .
P P P P P P P P
R N B Q K B N R

IterativeBot analysis:
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.03s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.34s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.37s, nodes=4399
  Depth 5: score=44, move=g1f3, time=1.25s, nodes=7837
IterativeBot completed search: 7837 nodes in 3.00s
Best move: g1f3
Nodes evaluated: 7837
Time taken: 3.00s

RegularBot analysis:
RegularBot evaluated 4128 positions in 1.27 seconds
Best move: g1f3
Nodes evaluated: 4128
Time taken: 1.27s

--- Comparison ---
Same move chosen: True
Efficiency (nodes/second):
  Iterative: 2612
  Regular: 3251
--------------------------------------------------

--- Testing: Fork opportunity ---
FEN: rnbqkb1r/pppp1ppp/5n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4
Board:
r n b q k b . r
p p p p . p p p
. . . . . n . .
. . . . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q K . . R

IterativeBot analysis:
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=218, move=c4f7, time=0.02s, nodes=39
  Depth 2: score=56, move=f3e5, time=0.05s, nodes=143
  Depth 3: score=239, move=f3e5, time=1.07s, nodes=2393
  Depth 4: score=-29, move=c4f7, time=1.87s, nodes=5639
IterativeBot completed search: 5639 nodes in 3.00s
Best move: c4f7
Nodes evaluated: 5639
Time taken: 3.00s

RegularBot analysis:
RegularBot evaluated 11642 positions in 3.72 seconds
Best move: c4f7
Nodes evaluated: 11642
Time taken: 3.72s

--- Comparison ---
Same move chosen: True
Efficiency (nodes/second):
  Iterative: 1879
  Regular: 3132
--------------------------------------------------

--- Testing: King and pawn endgame ---
FEN: 8/8/8/8/8/3k4/3P4/3K4 w - - 0 1
Board:
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . k . . . .
. . . P . . . .
. . . K . . . .

IterativeBot analysis:
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=53, move=d1e1, time=0.00s, nodes=3
  Depth 2: score=43, move=d1e1, time=0.00s, nodes=11
  Depth 3: score=68, move=d1e1, time=0.01s, nodes=38
  Depth 4: score=67, move=d1e1, time=0.02s, nodes=144
  Depth 5: score=94, move=d1e1, time=0.08s, nodes=538
  Depth 6: score=92, move=d1e1, time=0.24s, nodes=2100
IterativeBot completed search: 2100 nodes in 0.35s
Best move: d1e1
Nodes evaluated: 2100
Time taken: 0.35s

RegularBot analysis:
RegularBot evaluated 82 positions in 0.01 seconds
Best move: d1e1
Nodes evaluated: 82
Time taken: 0.01s

--- Comparison ---
Same move chosen: True
Efficiency (nodes/second):
  Iterative: 6042
  Regular: 7447
--------------------------------------------------

--- Testing: Mate in 2 ---
FEN: 2bqkbn1/2pppp2/np2N3/r3P1p1/p2N4/5Q2/PPPPKPP1/RNB2B1R w KQkq - 0 1
Board:
. . b q k b n .
. . p p p p . .
n p . . N . . .
r . . . P . p .
p . . N . . . .
. . . . . Q . .
P P P P K P P .
R N B . . B . R

IterativeBot analysis:
IterativeBot starting iterative deepening search (max depth: 6, time limit: 3.0s)
  Depth 1: score=1809, move=e6d8, time=0.02s, nodes=52
  Depth 2: score=1511, move=e6d8, time=0.05s, nodes=177
  Depth 3: score=1621, move=e6d8, time=1.30s, nodes=3673
  Depth 4: score=20000, move=e2d1, time=1.64s, nodes=7892
  Mate found at depth 4!
IterativeBot completed search: 7892 nodes in 3.00s
Best move: e2d1
Nodes evaluated: 7892
Time taken: 3.00s

RegularBot analysis:
RegularBot evaluated 3187 positions in 0.90 seconds
Best move: e6d8
Nodes evaluated: 3187
Time taken: 0.90s

--- Comparison ---
Same move chosen: False
Iterative: e2d1, Regular: e6d8
Efficiency (nodes/second):
  Iterative: 2629
  Regular: 3557
--------------------------------------------------

=== Testing Time Management ===


--- Testing with 0.5s time limit ---
TimeLimitBot time limit set to 0.5 seconds
TimeLimitBot starting iterative deepening search (max depth: 10, time limit: 0.5s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.02s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.25s, nodes=996
  Depth 4: score=0, move=g1f3, time=0.23s, nodes=1667
TimeLimitBot completed search: 1667 nodes in 0.50s
Move: g1f3
Requested time: 0.5s
Actual time: 0.50s
Time efficiency: 1.00
Nodes evaluated: 1667

--- Testing with 1.0s time limit ---
TimeLimitBot time limit set to 1.0 seconds
TimeLimitBot starting iterative deepening search (max depth: 10, time limit: 1.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.05s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.26s, nodes=996
  Depth 4: score=0, move=g1f3, time=0.68s, nodes=3176
TimeLimitBot completed search: 3176 nodes in 1.00s
Move: g1f3
Requested time: 1.0s
Actual time: 1.00s
Time efficiency: 1.00
Nodes evaluated: 3176

--- Testing with 2.0s time limit ---
TimeLimitBot time limit set to 2.0 seconds
TimeLimitBot starting iterative deepening search (max depth: 10, time limit: 2.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.04s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.46s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.19s, nodes=4399
  Depth 5: score=147, move=g1f3, time=0.31s, nodes=5191
TimeLimitBot completed search: 5191 nodes in 2.00s
Move: g1f3
Requested time: 2.0s
Actual time: 2.00s
Time efficiency: 1.00
Nodes evaluated: 5191

--- Testing with 5.0s time limit ---
TimeLimitBot time limit set to 5.0 seconds
TimeLimitBot starting iterative deepening search (max depth: 10, time limit: 5.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.02s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.24s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.24s, nodes=4399
  Depth 5: score=70, move=h2h3, time=3.49s, nodes=15433
TimeLimitBot completed search: 15433 nodes in 5.00s
Move: h2h3
Requested time: 5.0s
Actual time: 5.00s
Time efficiency: 1.00
Nodes evaluated: 15433

=== Testing Depth Scaling ===


--- Testing with max depth 3 ---
DepthTestBot maximum depth set to 3
DepthTestBot starting iterative deepening search (max depth: 3, time limit: 5.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.03s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.26s, nodes=996
DepthTestBot completed search: 996 nodes in 0.30s
Move: g1f3
Max depth: 3
Time taken: 0.30s
Nodes evaluated: 996

--- Testing with max depth 4 ---
DepthTestBot maximum depth set to 4
DepthTestBot starting iterative deepening search (max depth: 4, time limit: 5.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.03s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.38s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.21s, nodes=4399
DepthTestBot completed search: 4399 nodes in 1.63s
Move: g1f3
Max depth: 4
Time taken: 1.63s
Nodes evaluated: 4399

--- Testing with max depth 5 ---
DepthTestBot maximum depth set to 5
DepthTestBot starting iterative deepening search (max depth: 5, time limit: 5.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.03s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.28s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.26s, nodes=4399
  Depth 5: score=44, move=g1f3, time=3.42s, nodes=14748
DepthTestBot completed search: 14748 nodes in 5.00s
Move: g1f3
Max depth: 5
Time taken: 5.00s
Nodes evaluated: 14748

--- Testing with max depth 6 ---
DepthTestBot maximum depth set to 6
DepthTestBot starting iterative deepening search (max depth: 6, time limit: 5.0s)
  Depth 1: score=54, move=g1f3, time=0.00s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.02s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.25s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.24s, nodes=4399
  Depth 5: score=44, move=g1f3, time=3.48s, nodes=14522
DepthTestBot completed search: 14522 nodes in 5.00s
Move: g1f3
Max depth: 6
Time taken: 5.00s
Nodes evaluated: 14522

--- Testing with max depth 7 ---
DepthTestBot maximum depth set to 7
DepthTestBot starting iterative deepening search (max depth: 7, time limit: 5.0s)
  Depth 1: score=54, move=g1f3, time=0.01s, nodes=21
  Depth 2: score=0, move=g1f3, time=0.03s, nodes=94
  Depth 3: score=54, move=g1f3, time=0.26s, nodes=996
  Depth 4: score=0, move=g1f3, time=1.54s, nodes=4399
  Depth 5: score=44, move=g1f3, time=3.17s, nodes=12527
DepthTestBot completed search: 12527 nodes in 5.00s
Move: g1f3
Max depth: 7
Time taken: 5.00s
Nodes evaluated: 12527

=== Demonstrating Iterative Deepening Process ===

Position:
r . b q k b . r
p p p p . p p p
. . n . . n . .
. . . . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q K . . R

Running iterative deepening search...
(Watch how the search progresses through increasing depths)
DemoBot starting iterative deepening search (max depth: 8, time limit: 5.0s)
  Depth 1: score=168, move=c4f7, time=0.01s, nodes=39
  Depth 2: score=-97, move=b1c3, time=0.10s, nodes=313
  Depth 3: score=167, move=b1c3, time=1.36s, nodes=3122
  Depth 4: score=123, move=c4b5, time=3.53s, nodes=9014
DemoBot completed search: 9014 nodes in 5.00s

Final best move: c4b5
Total nodes evaluated: 9014