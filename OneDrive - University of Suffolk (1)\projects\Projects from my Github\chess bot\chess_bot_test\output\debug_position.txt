PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python debug_position.py
POSITION ANALYSIS
==================================================
FEN: r2qkb1r/ppp1pppp/5n2/3p1Q2/3n4/2N2b1P/PPP1PPP1/R1B1KB1R w KQkq - 0 7
Turn: White

Board:
r . . q k b . r
p p p . p p p p
. . . . . n . .
. . . p . Q . .
. . . n . . . .
. . N . . b . P
P P P . P P P .
R . B . K B . R

PIECE LOCATIONS:
------------------------------
White R on a1
White B on c1
White K on e1
White B on f1
White R on h1
White P on a2
White P on b2
White P on c2
White P on e2
White P on f2
White P on g2
White N on c3
Black B on f3
White P on h3
Black N on d4
Black P on d5
White Q on f5
Black N on f6
Black P on a7
Black P on b7
Black P on c7
Black P on e7
Black P on f7
Black P on g7
Black P on h7
Black R on a8
Black Q on d8
Black K on e8
Black B on f8
Black R on h8

WHITE QUEEN ON F5 ANALYSIS:
------------------------------
Queen attacks these squares: ['c2', 'd3', 'f3', 'h3', 'e4', 'f4', 'g4', 'd5', 'e5', 'g5', 'h5', 'e6', 'f6', 'g6', 'd7', 'h7', 'c8']
Queen attacks these enemy pieces:
  - B on f3
  - P on d5
  - N on f6
  - P on h7
This is a FORK - Queen attacks 4 pieces simultaneously!

QUEEN DEFENSE ANALYSIS:
------------------------------
White pieces defending Queen on f5: 0
Black pieces attacking Queen on f5: 1
  - N on d4
The Queen IS hanging (undefended and under attack)!

TACTICAL EVALUATOR RESULTS:
------------------------------
White threats: 2
  - {'type': <ThreatType.HANGING_PIECE: 'hanging_piece'>, 'square': 21, 'piece': Piece.from_symbol('b'), 'threat_value': 0, 'attackers': 3, 'defenders': 1, 'description': 'B on f3 is hanging'}
  - {'type': <ThreatType.FORK: 'fork'>, 'from_square': 37, 'piece': Piece.from_symbol('Q'), 'targets': [(21, Piece.from_symbol('b')), (45, Piece.from_symbol('n'))], 'includes_king': False, 'threat_value': 600, 'description': 'Q forks 2 pieces'}

Black threats: 2
  - {'type': <ThreatType.HANGING_PIECE: 'hanging_piece'>, 'square': 37, 'piece': Piece.from_symbol('Q'), 'threat_value': 900, 'attackers': 1, 'defenders': 0, 'description': 'Q on f5 is hanging'}
  - {'type': <ThreatType.UNDEFENDED_ATTACK: 'undefended_attack'>, 'from_square': 27, 'to_square': 37, 'attacker': Piece.from_symbol('n'), 'target': Piece.from_symbol('Q'), 'threat_value': 900, 'description': 'N can capture undefended Q'}