#!/usr/bin/env python3
"""
Chess Bot GUI - A desktop chess game with AI opponent using tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import chess
import threading
from chess_bot import ChessBot # to use hybrid change to chess_bot_hybrid | to use transposition change to chess_bot_transposition | to use QS change to chess_bot_QS , chess_bot_QS_testing
from utils import save_game_pgn, analyze_position, format_analysis, get_opening_name

class ChessGUI:
    """
    GUI Chess game using tkinter with drag-and-drop functionality.
    """
    
    def __init__(self):
        """Initialize the chess GUI."""
        self.root = tk.Tk()
        self.root.title("Chess Bot - GUI Version")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # Game state
        self.board = chess.Board()
        self.bot = ChessBot(depth=3, name="ChessBot")  # OPTIONAL: to use hybrid, remove this line and uncomment the line below
        #self.bot = ChessBot(depth=4, name="HybridChessBot", mcts_simulations=800, top_moves_count=3, use_hybrid=True)
        self.game_history = []
        self.undone_moves = []  # Stack to store undone moves for redo functionality
        self.undone_history = []  # Stack to store undone move history (SAN notation)
        self.human_color = chess.WHITE
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        self.bot_thinking = False
        
        # Colors and styling
        self.light_square_color = "#F0D9B5"
        self.dark_square_color = "#B58863"
        self.highlight_color = "#FFFF00"
        self.selected_color = "#FF6B6B"
        self.legal_move_color = "#90EE90"
        
        # Unicode chess pieces
        self.piece_symbols = {
            'P': '♙', 'N': '♘', 'B': '♗', 'R': '♖', 'Q': '♕', 'K': '♔',  # White
            'p': '♟', 'n': '♞', 'b': '♝', 'r': '♜', 'q': '♛', 'k': '♚'   # Black
        }
        
        self.setup_ui()
        self.update_board_display()
        self.update_status()
        # Initialize button states after UI is set up
        self.root.after(100, lambda: (self.update_undo_button_state(), self.update_redo_button_state()))
    
    def setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left panel for the chess board
        board_frame = ttk.Frame(main_frame)
        board_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        # Chess board canvas
        self.canvas = tk.Canvas(board_frame, width=480, height=480, bg="white")
        self.canvas.pack()
        self.canvas.bind("<Button-1>", self.on_square_click)
        
        # Right panel for controls and information
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Game controls
        controls_group = ttk.LabelFrame(control_frame, text="Game Controls", padding=10)
        controls_group.pack(fill=tk.X, pady=(0, 10))
        
        # Store references to all game control buttons
        self.new_game_button = ttk.Button(controls_group, text="New Game", command=self.new_game)
        self.new_game_button.pack(fill=tk.X, pady=2)
        
        self.switch_colors_button = ttk.Button(controls_group, text="Switch Colors", command=self.switch_colors)
        self.switch_colors_button.pack(fill=tk.X, pady=2)
        
        self.undo_button = ttk.Button(controls_group, text="Undo Move", command=self.undo_move)
        self.undo_button.pack(fill=tk.X, pady=2)
        
        self.redo_button = ttk.Button(controls_group, text="Redo Move", command=self.redo_move)
        self.redo_button.pack(fill=tk.X, pady=2)
        
        self.save_game_button = ttk.Button(controls_group, text="Save Game", command=self.save_game)
        self.save_game_button.pack(fill=tk.X, pady=2)
        
        self.load_game_button = ttk.Button(controls_group, text="Load Game", command=self.load_game)
        self.load_game_button.pack(fill=tk.X, pady=2)
        
        # Bot difficulty
        difficulty_group = ttk.LabelFrame(control_frame, text="Bot Difficulty", padding=10)
        difficulty_group.pack(fill=tk.X, pady=(0, 10))
        
        self.difficulty_var = tk.StringVar(value="Medium")
        difficulties = [("Easy", 2), ("Medium", 3), ("Hard", 4), ("Expert", 5)]
        
        for name, depth in difficulties:
            ttk.Radiobutton(difficulty_group, text=f"{name} (depth {depth})", 
                           variable=self.difficulty_var, value=name,
                           command=lambda d=depth: self.set_difficulty(d)).pack(anchor=tk.W)
        
        # Game status
        status_group = ttk.LabelFrame(control_frame, text="Game Status", padding=10)
        status_group.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(status_group, text="White to move", font=("Arial", 12))
        self.status_label.pack()
        
        self.opening_label = ttk.Label(status_group, text="Opening: Starting Position", font=("Arial", 10))
        self.opening_label.pack()
        
        # Move history
        history_group = ttk.LabelFrame(control_frame, text="Move History", padding=10)
        history_group.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Scrollable text widget for move history
        history_frame = ttk.Frame(history_group)
        history_frame.pack(fill=tk.BOTH, expand=True)
        
        self.history_text = tk.Text(history_frame, height=8, width=25, wrap=tk.WORD)
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_text.yview)
        self.history_text.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Analysis button
        self.analyze_button = ttk.Button(control_frame, text="Analyze Position", command=self.show_analysis)
        self.analyze_button.pack(fill=tk.X)
    
    def draw_board(self):
        """Draw the chess board."""
        self.canvas.delete("all")
        square_size = 60
        
        for row in range(8):
            for col in range(8):
                x1 = col * square_size
                y1 = row * square_size
                x2 = x1 + square_size
                y2 = y1 + square_size
                
                # Determine square color
                is_light = (row + col) % 2 == 0
                square = chess.square(col, 7 - row)
                
                # Choose color based on square state
                if square == self.selected_square:
                    color = self.selected_color
                elif square in self.highlighted_squares:
                    color = self.legal_move_color
                else:
                    color = self.light_square_color if is_light else self.dark_square_color
                
                # Draw square
                self.canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline="black")
                
                # Add coordinates
                if col == 0:  # Rank labels
                    self.canvas.create_text(x1 + 5, y1 + 10, text=str(8 - row), 
                                          font=("Arial", 8), fill="black")
                if row == 7:  # File labels
                    self.canvas.create_text(x2 - 10, y2 - 5, text=chr(ord('a') + col), 
                                          font=("Arial", 8), fill="black")
    
    def draw_pieces(self):
        """Draw the chess pieces on the board."""
        square_size = 60
        
        for row in range(8):
            for col in range(8):
                square = chess.square(col, 7 - row)
                piece = self.board.piece_at(square)
                
                if piece:
                    x = col * square_size + square_size // 2
                    y = row * square_size + square_size // 2
                    
                    piece_symbol = self.piece_symbols.get(piece.symbol(), piece.symbol())
                    self.canvas.create_text(x, y, text=piece_symbol, 
                                          font=("Arial", 36), fill="black")
    
    def update_board_display(self):
        """Update the visual board display."""
        self.draw_board()
        self.draw_pieces()
    
    def on_square_click(self, event):
        """Handle mouse clicks on the board."""
        if self.game_over or self.board.turn != self.human_color:
            return
        
        square_size = 60
        col = event.x // square_size
        row = event.y // square_size
        
        if 0 <= col < 8 and 0 <= row < 8:
            clicked_square = chess.square(col, 7 - row)
            
            if self.selected_square is None:
                # Select a piece
                piece = self.board.piece_at(clicked_square)
                if piece and piece.color == self.human_color:
                    self.selected_square = clicked_square
                    self.highlight_legal_moves(clicked_square)
                    self.update_board_display()
            else:
                # Try to make a move
                move = chess.Move(self.selected_square, clicked_square)
                
                # Check for promotion
                piece = self.board.piece_at(self.selected_square)
                if (piece and piece.piece_type == chess.PAWN and 
                    ((piece.color == chess.WHITE and chess.square_rank(clicked_square) == 7) or
                     (piece.color == chess.BLACK and chess.square_rank(clicked_square) == 0))):
                    # Default to queen promotion for simplicity
                    move = chess.Move(self.selected_square, clicked_square, promotion=chess.QUEEN)
                
                if move in self.board.legal_moves:
                    self.make_move(move)
                
                # Clear selection
                self.selected_square = None
                self.highlighted_squares = []
                self.update_board_display()
    
    def highlight_legal_moves(self, square):
        """Highlight legal moves for the selected piece."""
        self.highlighted_squares = []
        for move in self.board.legal_moves:
            if move.from_square == square:
                self.highlighted_squares.append(move.to_square)
    
    def make_move(self, move):
        """Make a move on the board."""
        try:
            san_move = self.board.san(move)
            self.board.push(move)
            self.game_history.append(san_move)
            
            # Clear the redo stack when a new move is made
            self.undone_moves = []
            self.undone_history = []
            
            self.update_history_display()
            self.update_status()
            self.update_board_display()
            
            # Update button states
            self.update_undo_button_state()
            self.update_redo_button_state()
            
            # Check for game over
            if self.board.is_game_over():
                self.handle_game_over()
            elif self.board.turn != self.human_color:
                # Bot's turn
                self.root.after(500, self.make_bot_move)  # Small delay for better UX
                
        except Exception as e:
            messagebox.showerror("Error", f"Invalid move: {e}")
            
    def update_undo_button_state(self):
        """Update the state of the undo button based on game state."""
        # Let's try a simpler approach: disable undo button when playing as black
        # and only white has moved (exactly one move in the stack)
        
        # Check if we're playing as black and only one move has been made
        if self.human_color == chess.BLACK and len(self.board.move_stack) == 1:
            print(f"Disabling undo button: Playing as BLACK, move stack has 1 move")
            self.undo_button.configure(state="disabled")
            return
            
        # Also disable if no moves or bot is thinking
        if not self.board.move_stack:
            print("Disabling undo button: No moves in stack")
            self.undo_button.configure(state="disabled")
            return
            
        if self.bot_thinking:
            print("Disabling undo button: Bot is thinking")
            self.undo_button.configure(state="disabled")
            return
            
        # In all other cases, enable the undo button
        print(f"Enabling undo button: human_color={self.human_color}, moves={len(self.board.move_stack)}, turn={self.board.turn}")
        self.undo_button.configure(state="normal")
        
    def update_redo_button_state(self):
        """Update the state of the redo button based on game state."""
        # Disable redo button if:
        # 1. There are no moves to redo, or
        # 2. Bot is thinking
        
        if not self.undone_moves:
            print("Disabling redo button: No moves to redo")
            self.redo_button.configure(state="disabled")
            return
            
        if self.bot_thinking:
            print("Disabling redo button: Bot is thinking")
            self.redo_button.configure(state="disabled")
            return
            
        # In all other cases, enable the redo button
        # Note: Even though there may be multiple moves in the stack, we only redo them as a unit
        # (either 1 move or a human+bot pair), not individually
        print(f"Enabling redo button: 1 action available to redo (containing {len(self.undone_moves)} moves)")
        self.redo_button.configure(state="normal")
    
    def undo_move(self):
        """Undo the last move (or last two moves if bot's turn)."""
        # Check if there are moves to undo
        if not self.board.move_stack:
            messagebox.showinfo("Info", "No moves to undo!")
            return
        
        # For debugging
        print(f"Starting undo with {len(self.board.move_stack)} moves in move stack")
        print(f"Current board state: {self.board.fen()}")
        
        # Determine how many moves to undo
        # If it's the human's turn, undo the last bot move and the human move before that
        # If it's the bot's turn (right after human moved), just undo the human's move
        moves_to_undo = 1
        
        # If it's the human's turn and there are at least 2 moves, undo both the bot's and human's last moves
        if self.board.turn == self.human_color and len(self.board.move_stack) >= 2:
            moves_to_undo = 2
            
        # Make sure we have enough moves to undo
        if len(self.board.move_stack) < moves_to_undo:
            moves_to_undo = len(self.board.move_stack)
        
        # This is one undo action, but it might contain multiple moves
        if moves_to_undo == 1:
            print(f"Undoing 1 move (human or bot)")
        else:
            print(f"Undoing 2 moves (human + bot pair)")
        
        # Store the moves we're going to undo
        moves_to_store = []
        history_to_store = []
        
        # Get the last moves_to_undo moves from the stack (without popping yet)
        for i in range(moves_to_undo):
            if i < len(self.board.move_stack):
                move_index = len(self.board.move_stack) - 1 - i
                if move_index >= 0:
                    moves_to_store.append(self.board.move_stack[move_index])
                    if i < len(self.game_history):
                        history_index = len(self.game_history) - 1 - i
                        if history_index >= 0:
                            history_to_store.append(self.game_history[history_index])
        
        # Create a new board with all moves except the last moves_to_undo
        temp_board = chess.Board()
        temp_history = []
        
        # Apply all moves except the last moves_to_undo
        for i, move in enumerate(self.board.move_stack):
            if i < len(self.board.move_stack) - moves_to_undo:
                san = temp_board.san(move)
                temp_board.push(move)
                if i < len(self.game_history):
                    temp_history.append(self.game_history[i])
        
        # Update the board and history
        self.board = temp_board
        self.game_history = temp_history
        
        # Add the undone moves to our redo stacks
        self.undone_moves.extend(moves_to_store)
        self.undone_history.extend(history_to_store)
        
        print(f"New board state: {self.board.fen()}")
        if len(moves_to_store) == 1:
            print(f"Added 1 move to redo stack, now has {len(self.undone_moves)} total moves in stack")
        else:
            print(f"Added {len(moves_to_store)} moves as a single redo action, now has {len(self.undone_moves)} total moves in stack")
        
        # Reset game state if it was game over
        if self.game_over:
            self.game_over = False
            
        # Update the display
        self.update_history_display()
        self.update_status()
        self.update_board_display()
        
        # Update button states
        self.update_undo_button_state()
        self.update_redo_button_state()
        
        # Clear any selections
        self.selected_square = None
        self.highlighted_squares = []
        
        # If after undoing, it's the bot's turn, trigger the bot to make a move
        if not self.game_over and self.board.turn != self.human_color:
            self.root.after(500, self.make_bot_move)  # Small delay for better UX
            
    def redo_move(self):
        """Redo a previously undone move."""
        # Check if there are moves to redo
        if not self.undone_moves:
            messagebox.showinfo("Info", "No moves to redo!")
            return
        
        # For debugging
        print(f"Starting redo with {len(self.undone_moves)} moves in redo stack")
        print(f"Current board state: {self.board.fen()}")
        
        # We need to recreate the board from scratch to avoid piece color issues
        # First, save the current FEN position
        current_fen = self.board.fen()
        
        # Determine how many moves to redo
        moves_to_redo = 1
        
        # If it's the bot's turn and there are at least 2 moves to redo, redo both
        if self.board.turn != self.human_color and len(self.undone_moves) >= 2:
            moves_to_redo = 2
            
        # Make sure we have enough moves to redo
        if len(self.undone_moves) < moves_to_redo:
            moves_to_redo = len(self.undone_moves)
        
        # This is one redo action, but it might contain multiple moves
        if moves_to_redo == 1:
            print(f"Redoing 1 move (human or bot)")
        else:
            print(f"Redoing 2 moves (human + bot pair)")
        
        # Get the moves to redo (without popping yet)
        moves_to_apply = []
        history_to_restore = []
        
        for i in range(moves_to_redo):
            if i < len(self.undone_moves):
                moves_to_apply.append(self.undone_moves[len(self.undone_moves) - 1 - i])
                if i < len(self.undone_history):
                    history_to_restore.append(self.undone_history[len(self.undone_history) - 1 - i])
        
        # Now recreate the board and apply all moves up to the current position
        temp_board = chess.Board()
        temp_history = []
        
        # Apply all moves from the beginning up to current position
        for move in self.board.move_stack:
            san = temp_board.san(move)
            temp_board.push(move)
            temp_history.append(san)
        
        # Now apply the moves to redo
        for move in reversed(moves_to_apply):
            try:
                san = temp_board.san(move)
                temp_board.push(move)
                temp_history.append(san)
            except Exception as e:
                print(f"Error applying move {move}: {e}")
                messagebox.showerror("Error", f"Failed to redo move: {e}")
                return
        
        # If we got here, all moves were applied successfully
        # Update the actual board and history
        self.board = temp_board
        self.game_history = temp_history
        
        # Remove the redone moves from the undo stacks
        for _ in range(moves_to_redo):
            if self.undone_moves:
                self.undone_moves.pop()
            if self.undone_history:
                self.undone_history.pop()
        
        print(f"New board state: {self.board.fen()}")
        
        # Update the display
        self.update_history_display()
        self.update_status()
        self.update_board_display()
        
        # Update button states
        self.update_undo_button_state()
        self.update_redo_button_state()
        
        # Clear any selections
        self.selected_square = None
        self.highlighted_squares = []
        
        # Check for game over
        if self.board.is_game_over():
            self.handle_game_over()
        # If after redoing, it's the bot's turn, trigger the bot to make a move
        elif not self.game_over and self.board.turn != self.human_color:
            self.root.after(500, self.make_bot_move)  # Small delay for better UX
    
    def make_bot_move(self):
        """Make a move for the bot in a separate thread."""
        def bot_move_thread():
            self.bot_thinking = True
            # Disable all controls in the main thread
            self.root.after(0, self.disable_all_controls)
            try:
                self.update_status("Bot is thinking...")
                move = self.bot.get_best_move(self.board)
                
                if move:
                    # Update UI in main thread
                    self.root.after(0, lambda m=move: self.make_move(m))
                else:
                    self.root.after(0, lambda: messagebox.showinfo("Info", "Bot couldn't find a move!"))
            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda: messagebox.showerror("Error", f"Bot error: {error_msg}"))
            finally:
                self.bot_thinking = False
                # Re-enable all controls in the main thread
                self.root.after(0, self.enable_all_controls)
        
        threading.Thread(target=bot_move_thread, daemon=True).start()
    
    def update_status(self, custom_message=None):
        """Update the game status display."""
        if custom_message:
            self.status_label.config(text=custom_message)
            return
        
        if self.board.is_checkmate():
            winner = "Black" if self.board.turn == chess.WHITE else "White"
            self.status_label.config(text=f"Checkmate! {winner} wins!")
            self.game_over = True
        elif self.board.is_stalemate():
            self.status_label.config(text="Stalemate! Draw!")
            self.game_over = True
        elif self.board.is_insufficient_material():
            self.status_label.config(text="Insufficient material! Draw!")
            self.game_over = True
        elif self.board.is_check():
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} in check!")
        else:
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} to move")
        
        # Update opening name
        if len(self.game_history) <= 8:
            opening = get_opening_name(self.board, self.game_history)
            self.opening_label.config(text=f"Opening: {opening}")
    
    def update_history_display(self):
        """Update the move history display."""
        self.history_text.delete(1.0, tk.END)
        
        # Format moves in pairs (White, Black)
        for i in range(0, len(self.game_history), 2):
            move_num = (i // 2) + 1
            white_move = self.game_history[i] if i < len(self.game_history) else ""
            black_move = self.game_history[i + 1] if i + 1 < len(self.game_history) else ""
            
            line = f"{move_num}. {white_move}"
            if black_move:
                line += f" {black_move}"
            line += "\n"
            
            self.history_text.insert(tk.END, line)
        
        # Scroll to bottom
        self.history_text.see(tk.END)
    
    def handle_game_over(self):
        """Handle game over situation."""
        self.game_over = True
        
        if self.board.is_checkmate():
            winner = "You" if self.board.turn != self.human_color else "ChessBot"
            message = f"Checkmate! {winner} win!"
        elif self.board.is_stalemate():
            message = "Stalemate! The game is a draw."
        elif self.board.is_insufficient_material():
            message = "Insufficient material! The game is a draw."
        else:
            message = "Game over!"
        
        result = messagebox.askyesno("Game Over", f"{message}\n\nWould you like to save this game?")
        if result:
            self.save_game()
    
    def new_game(self):
        """Start a new game."""
        self.board = chess.Board()
        self.game_history = []
        self.undone_moves = []  # Clear redo stack
        self.undone_history = []  # Clear redo history
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        
        self.update_board_display()
        self.update_status()
        self.update_history_display()
        self.update_undo_button_state()
        self.update_redo_button_state()
    
    def switch_colors(self):
        """Switch human and bot colors."""
        self.human_color = not self.human_color
        color_name = "White" if self.human_color == chess.WHITE else "Black"
        messagebox.showinfo("Colors Switched", f"You are now playing as {color_name}")
        
        print(f"Colors switched: human_color={self.human_color}, board.turn={self.board.turn}")
        
        # Clear redo stack when switching colors
        self.undone_moves = []
        self.undone_history = []
        
        # Update button states
        self.update_undo_button_state()
        self.update_redo_button_state()
        
        # If it's now bot's turn, make a move
        if not self.game_over and self.board.turn != self.human_color:
            print("Bot's turn after color switch, making bot move")
            self.make_bot_move()
            
            # Update button states again after bot moves
            self.root.after(1000, lambda: (self.update_undo_button_state(), self.update_redo_button_state()))
    
    def set_difficulty(self, depth):
        """Set the bot difficulty."""
        self.bot.set_depth(depth)
        difficulty_name = self.difficulty_var.get()
        messagebox.showinfo("Difficulty Changed", f"Bot difficulty set to {difficulty_name} (depth {depth})")
    
    def save_game(self):
        """Save the current game to a PGN file."""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".pgn",
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
                title="Save Chess Game"
            )
            
            if filename:
                white_player = "Human" if self.human_color == chess.WHITE else "ChessBot"
                black_player = "ChessBot" if self.human_color == chess.WHITE else "Human"
                
                save_game_pgn(self.board, self.game_history, filename, white_player, black_player)
                messagebox.showinfo("Game Saved", f"Game saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save game: {e}")
    
    def load_game(self):
        """Load a game from a PGN file."""
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
                title="Load Chess Game"
            )
            
            if filename:
                from utils import load_game_pgn
                board, history = load_game_pgn(filename)
                
                self.board = board
                self.game_history = history
                self.selected_square = None
                self.highlighted_squares = []
                self.game_over = self.board.is_game_over()
                
                self.update_board_display()
                self.update_status()
                self.update_history_display()
                
                messagebox.showinfo("Game Loaded", f"Game loaded from {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load game: {e}")
    
    def show_analysis(self):
        """Show position analysis in a popup window."""
        try:
            analysis = analyze_position(self.board)
            analysis_text = format_analysis(analysis)
            
            # Create popup window
            popup = tk.Toplevel(self.root)
            popup.title("Position Analysis")
            popup.geometry("400x500")
            
            # Text widget with scrollbar
            frame = ttk.Frame(popup)
            frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(frame, wrap=tk.WORD, font=("Courier", 10))
            scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            text_widget.insert(1.0, analysis_text)
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to analyze position: {e}")
    
    def disable_all_controls(self):
        """Disable all game controls and board interaction while the bot is thinking."""
        print("=== DISABLING ALL GAME CONTROLS ===")
        # Disable all buttons
        self.new_game_button.configure(state="disabled")
        self.switch_colors_button.configure(state="disabled")
        self.undo_button.configure(state="disabled")
        self.redo_button.configure(state="disabled")
        self.save_game_button.configure(state="disabled")
        self.load_game_button.configure(state="disabled")
        self.analyze_button.configure(state="disabled")
        
        # Unbind board click event to prevent user interaction
        self.canvas.unbind("<Button-1>")
        print("Board interaction disabled")
        
    def enable_all_controls(self):
        """Re-enable all game controls and board interaction."""
        print("=== RE-ENABLING ALL GAME CONTROLS ===")
        # Re-enable all buttons (except undo and redo which are handled separately)
        self.new_game_button.configure(state="normal")
        self.switch_colors_button.configure(state="normal")
        self.save_game_button.configure(state="normal")
        self.load_game_button.configure(state="normal")
        self.analyze_button.configure(state="normal")
        print("Game control buttons re-enabled")
        
        # Update undo and redo button states based on game state
        self.update_undo_button_state()
        self.update_redo_button_state()
        
        # Rebind board click event
        self.canvas.bind("<Button-1>", self.on_square_click)
        print("Board interaction re-enabled")
    
    def run(self):
        """Start the GUI application."""
        self.root.mainloop()

def main():
    """Main entry point for the GUI version."""
    try:
        app = ChessGUI()
        app.run()
    except Exception as e:
        print(f"Error starting GUI: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()