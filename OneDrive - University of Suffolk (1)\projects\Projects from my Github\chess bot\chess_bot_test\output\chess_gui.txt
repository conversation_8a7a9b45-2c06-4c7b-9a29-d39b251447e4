PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui.py
ChessBot search depth set to 5
ChessBot evaluated 51965 positions in 14.73 seconds
ChessBot evaluated 124939 positions in 44.33 seconds
ChessBot search depth set to 3
ChessBot evaluated 2599 positions in 0.81 seconds
ChessBot evaluated 4425 positions in 1.29 seconds
ChessBot evaluated 3363 positions in 1.00 seconds
ChessBot evaluated 5175 positions in 1.19 seconds
ChessBot evaluated 3494 positions in 0.93 seconds
ChessBot evaluated 3331 positions in 1.04 seconds
ChessBot evaluated 7219 positions in 1.88 seconds
ChessBot evaluated 1232 positions in 0.30 seconds
ChessBot evaluated 2807 positions in 0.92 seconds
ChessBot evaluated 662 positions in 0.19 seconds





==================================



                                                                                                          python chess_gui.py
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['b8c6', 'g8f6', 'g8h6', 'b8a6', 'e7e6']
Minimax scores: [-70, -15, 25, 25, 55]
Step 2: Exploring candidates with 1000 MCTS simulations...
ChessBot evaluated 2430 positions in 11.10 seconds
Selected move: g8h6
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g7h6', 'b8c6', 'e7e5', 'c7c5', 'b8a6']
Minimax scores: [19, 134, 216, 226, 266]
Step 2: Exploring candidates with 1000 MCTS simulations...
ChessBot evaluated 3791 positions in 12.13 seconds
Selected move: g7h6
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['f8g7', 'b8c6', 'h8g8', 'b8a6', 'h6h5']
Minimax scores: [-30, -22, 25, 42, 50]
Step 2: Exploring candidates with 1000 MCTS simulations...
ChessBot evaluated 4465 positions in 13.02 seconds
Selected move: f8g7
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['b8c6', 'b8a6', 'e8f8', 'g7f6', 'e8g8']
Minimax scores: [-40, 10, 20, 20, 20]
Step 2: Exploring candidates with 1000 MCTS simulations...
ChessBot evaluated 8100 positions in 13.02 seconds
Selected move: e8f8
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['b8c6', 'b8a6', 'h8g8', 'f8g8', 'f8e8']
Minimax scores: [-71, -19, -9, -9, -9]
Step 2: Exploring candidates with 1000 MCTS simulations...
ChessBot evaluated 8012 positions in 11.89 seconds
Selected move: f8e8
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['b8a6', 'h8g8', 'h8f8', 'e8f8', 'g7f6']
Minimax scores: [-64, -54, -54, -54, -54]
Step 2: Exploring candidates with 1000 MCTS simulations...
ChessBot evaluated 8357 positions in 12.85 seconds
Selected move: h8g8
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g7e5', 'g7h8', 'g7f6', 'b8a6', 'g8h8']
Minimax scores: [-130, -127, -127, -106, -96]
Step 2: Exploring candidates with 1000 MCTS simulations...
ChessBot evaluated 9283 positions in 14.00 seconds
Selected move: g7h8




-----------------------------------------------

median and optimal version (3 moves minimax ; 800 MCTS simulations)


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui.py
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['b8c6', 'g8f6', 'g8h6']
Minimax scores: [-60, -17, 33]
Step 2: Exploring candidates with 800 MCTS simulations...
ChessBot evaluated 2593 positions in 18.64 seconds
Selected move: b8c6
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['g8f6', 'c6b4', 'e7e6']
Minimax scores: [-116, -105, -41]
Step 2: Exploring candidates with 800 MCTS simulations...
ChessBot evaluated 3171 positions in 17.57 seconds
Selected move: e7e6
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['d8f6', 'f8b4', 'g8f6']
Minimax scores: [-183, -153, -47]
Step 2: Exploring candidates with 800 MCTS simulations...
ChessBot evaluated 6024 positions in 21.98 seconds
Selected move: g8f6
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['f6e4', 'f8b4', 'c6b4']
Minimax scores: [-258, -226, -144]
Step 2: Exploring candidates with 800 MCTS simulations...
ChessBot evaluated 6297 positions in 25.21 seconds
Selected move: f6e4




---------------------------------------



                                                                                  python chess_gui.pyrs\aidan_1k98io6\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test>
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax (depth 3)...
Top moves from minimax: ['b8c6', 'g8f6', 'g8h6']
Minimax scores: [-60, -17, 33]
Step 2: Exploring candidates with 600 MCTS simulations...
ChessBot evaluated 2593 positions in 14.69 seconds
Selected move: b8c6
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax (depth 3)...
Top moves from minimax: ['g8f6', 'c6b4', 'e7e6']
Minimax scores: [-183, -167, -108]
Step 2: Exploring candidates with 600 MCTS simulations...
ChessBot evaluated 3212 positions in 15.52 seconds
Selected move: c6b4
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax (depth 3)...
Top moves from minimax: ['b4d5', 'b4c6', 'b4a6']
Minimax scores: [-163, -96, -3]
Step 2: Exploring candidates with 600 MCTS simulations...
ChessBot evaluated 2268 positions in 14.47 seconds
Selected move: b4d5
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax (depth 3)...
Top moves from minimax: ['d5f6', 'd5b6', 'g8f6']
Minimax scores: [-79, -27, 79]
Step 2: Exploring candidates with 600 MCTS simulations...
ChessBot evaluated 2481 positions in 13.99 seconds
Selected move: d5f6
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax (depth 3)...
Top moves from minimax: ['f6e4', 'f6d5', 'a8b8']
Minimax scores: [-134, -117, 101]
Step 2: Exploring candidates with 600 MCTS simulations...
ChessBot evaluated 2016 positions in 12.49 seconds
Selected move: a8b8
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax (depth 3)...
Top moves from minimax: ['g8f6', 'e7f6', 'g7f6']
Minimax scores: [105, 166, 228]
Step 2: Exploring candidates with 600 MCTS simulations...
ChessBot evaluated 2087 positions in 12.54 seconds
Selected move: g8f6



----------------------------------------



PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui.py
ChessBot evaluated 868 positions in 0.47 seconds (TT: 15.9% hit rate, 730 entries)
ChessBot evaluated 1955 positions in 0.79 seconds (TT: 11.3% hit rate, 2481 entries)
ChessBot evaluated 2155 positions in 0.90 seconds (TT: 10.7% hit rate, 4395 entries)
ChessBot evaluated 6405 positions in 2.26 seconds (TT: 10.9% hit rate, 10080 entries)
ChessBot evaluated 2632 positions in 0.89 seconds (TT: 10.5% hit rate, 12471 entries)
ChessBot evaluated 7966 positions in 3.21 seconds (TT: 9.9% hit rate, 19719 entries)
ChessBot evaluated 2733 positions in 0.97 seconds (TT: 9.7% hit rate, 22222 entries)
ChessBot evaluated 6943 positions in 2.67 seconds (TT: 9.4% hit rate, 28592 entries)
ChessBot evaluated 2123 positions in 1.08 seconds (TT: 9.3% hit rate, 30536 entries)
ChessBot evaluated 2443 positions in 1.03 seconds (TT: 9.3% hit rate, 32754 entries)
ChessBot evaluated 2399 positions in 0.81 seconds (TT: 9.3% hit rate, 34940 entries)
ChessBot evaluated 321 positions in 0.11 seconds (TT: 9.2% hit rate, 35243 entries)
ChessBot evaluated 1372 positions in 0.59 seconds (TT: 9.1% hit rate, 36516 entries)
ChessBot evaluated 5472 positions in 1.85 seconds (TT: 9.6% hit rate, 41220 entries)




-------

ChessBot evaluated 868 positions in 0.44 seconds (TT: 15.9% hit rate, 730 entries)
ChessBot evaluated 1448 positions in 0.56 seconds (TT: 12.6% hit rate, 2022 entries)
ChessBot evaluated 2849 positions in 1.15 seconds (TT: 11.5% hit rate, 4556 entries)
ChessBot evaluated 6388 positions in 2.44 seconds (TT: 11.1% hit rate, 10247 entries)
ChessBot evaluated 2040 positions in 0.81 seconds (TT: 11.0% hit rate, 12047 entries)
ChessBot evaluated 1627 positions in 0.60 seconds (TT: 11.2% hit rate, 13452 entries)
ChessBot evaluated 1242 positions in 0.50 seconds (TT: 10.8% hit rate, 14602 entries)
ChessBot evaluated 1196 positions in 0.59 seconds (TT: 10.7% hit rate, 15694 entries)
ChessBot evaluated 1992 positions in 0.69 seconds (TT: 10.3% hit rate, 17538 entries)
ChessBot evaluated 2563 positions in 0.92 seconds (TT: 9.9% hit rate, 19922 entries)
ChessBot evaluated 743 positions in 0.22 seconds (TT: 10.1% hit rate, 20535 entries)
ChessBot evaluated 1787 positions in 0.74 seconds (TT: 10.3% hit rate, 22102 entries)
ChessBot evaluated 2708 positions in 0.90 seconds (TT: 10.6% hit rate, 24434 entries)
ChessBot evaluated 1642 positions in 0.59 seconds (TT: 10.6% hit rate, 25891 entries)
ChessBot evaluated 3364 positions in 1.38 seconds (TT: 10.4% hit rate, 28961 entries)
ChessBot evaluated 2799 positions in 1.04 seconds (TT: 10.2% hit rate, 31503 entries)
ChessBot evaluated 975 positions in 0.48 seconds (TT: 10.2% hit rate, 32373 entries)
ChessBot evaluated 4437 positions in 1.69 seconds (TT: 9.9% hit rate, 36452 entries)
ChessBot evaluated 1516 positions in 0.54 seconds (TT: 10.0% hit rate, 37755 entries)
ChessBot evaluated 2156 positions in 0.78 seconds (TT: 10.0% hit rate, 39703 entries)
ChessBot evaluated 1118 positions in 0.46 seconds (TT: 9.9% hit rate, 40733 entries)
ChessBot evaluated 1174 positions in 0.40 seconds (TT: 9.9% hit rate, 41786 entries)
ChessBot evaluated 1527 positions in 0.65 seconds (TT: 9.7% hit rate, 43199 entries)
ChessBot evaluated 4427 positions in 1.49 seconds (TT: 10.7% hit rate, 46660 entries)
ChessBot evaluated 966 positions in 0.41 seconds (TT: 10.7% hit rate, 47506 entries)
ChessBot evaluated 858 positions in 0.27 seconds (TT: 10.8% hit rate, 48232 entries)
ChessBot evaluated 1166 positions in 0.39 seconds (TT: 10.8% hit rate, 49271 entries)
ChessBot evaluated 2538 positions in 1.07 seconds (TT: 10.7% hit rate, 51543 entries)
ChessBot evaluated 5834 positions in 2.37 seconds (TT: 11.0% hit rate, 56556 entries)
ChessBot evaluated 2864 positions in 1.18 seconds (TT: 10.9% hit rate, 59140 entries)
ChessBot evaluated 8778 positions in 3.37 seconds (TT: 10.7% hit rate, 67141 entries)
ChessBot evaluated 1184 positions in 0.39 seconds (TT: 10.6% hit rate, 68204 entries)
ChessBot evaluated 5048 positions in 1.83 seconds (TT: 10.6% hit rate, 72742 entries)
ChessBot evaluated 2101 positions in 0.88 seconds (TT: 10.6% hit rate, 74626 entries)
ChessBot evaluated 2318 positions in 0.91 seconds (TT: 10.6% hit rate, 76671 entries)
ChessBot evaluated 3658 positions in 1.28 seconds (TT: 10.5% hit rate, 79996 entries)
ChessBot evaluated 8278 positions in 3.21 seconds (TT: 10.4% hit rate, 87468 entries)
ChessBot evaluated 543 positions in 0.21 seconds (TT: 10.4% hit rate, 87946 entries)
ChessBot evaluated 777 positions in 0.28 seconds (TT: 10.4% hit rate, 88719 entries)
ChessBot evaluated 5234 positions in 2.10 seconds (TT: 10.8% hit rate, 92939 entries)
ChessBot evaluated 3473 positions in 1.40 seconds (TT: 10.8% hit rate, 96049 entries)
ChessBot evaluated 587 positions in 0.24 seconds (TT: 10.7% hit rate, 96628 entries)
ChessBot evaluated 325 positions in 0.10 seconds (TT: 10.7% hit rate, 96942 entries)
ChessBot evaluated 637 positions in 0.23 seconds (TT: 10.7% hit rate, 97542 entries)
ChessBot evaluated 215 positions in 0.12 seconds (TT: 10.6% hit rate, 97749 entries)
ChessBot evaluated 161 positions in 0.09 seconds (TT: 10.6% hit rate, 97908 entries)
ChessBot evaluated 906 positions in 0.52 seconds (TT: 10.6% hit rate, 98768 entries)
ChessBot evaluated 127 positions in 0.05 seconds (TT: 10.6% hit rate, 98893 entries)





===========================================



[with Quiescence search - generally quicker]

PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui.py
ChessBot evaluated 1517 positions in 0.24 seconds
ChessBot evaluated 3393 positions in 0.58 seconds
ChessBot evaluated 2129 positions in 0.36 seconds
ChessBot evaluated 1590 positions in 0.26 seconds
ChessBot evaluated 1956 positions in 0.35 seconds
ChessBot evaluated 1160 positions in 0.17 seconds
ChessBot evaluated 1592 positions in 0.28 seconds
ChessBot evaluated 3991 positions in 0.91 seconds
ChessBot evaluated 1473 positions in 0.31 seconds
ChessBot evaluated 1985 positions in 0.32 seconds
ChessBot evaluated 2188 positions in 0.36 seconds



ChessBot evaluated 1577 positions in 3.55 seconds
QS improved evaluation 63 times, avg: 117.43 points


ChessBot evaluated 2149 positions in 2.88 seconds
QS improved evaluation 102 times, avg: 197.93 points

ChessBot evaluated 2663 positions in 3.82 seconds
QS improved evaluation 145 times, avg: 222.17 points

ChessBot evaluated 1013 positions in 1.59 seconds
QS improved evaluation 38 times, avg: 518.34 points

ChessBot evaluated 939 positions in 2.60 seconds
QS improved evaluation 47 times, avg: 164.06 points

ChessBot evaluated 1832 positions in 2.53 seconds
QS improved evaluation 106 times, avg: 156.19 points

ChessBot evaluated 653 positions in 1.42 seconds
QS improved evaluation 11 times, avg: 257.91 points







=============================================================
=============================================================

Testing undo button functionality, particularly in the cases where it should be disabled:

PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui.py
Disabling undo button: No moves in stack
Enabling undo button: human_color=True, moves=1, turn=False
ChessBot evaluated 860 positions in 0.20 seconds
Disabling undo button: Bot is thinking
Enabling undo button: human_color=True, moves=2, turn=True
Enabling undo button: human_color=True, moves=2, turn=True
Disabling undo button: No moves in stack
Enabling undo button: human_color=True, moves=1, turn=False
ChessBot evaluated 851 positions in 0.22 seconds
Disabling undo button: Bot is thinking
Enabling undo button: human_color=True, moves=2, turn=True
Enabling undo button: human_color=True, moves=2, turn=True
Enabling undo button: human_color=True, moves=3, turn=False
ChessBot evaluated 1430 positions in 0.27 seconds
Disabling undo button: Bot is thinking
Enabling undo button: human_color=True, moves=4, turn=True
Enabling undo button: human_color=True, moves=4, turn=True
Enabling undo button: human_color=True, moves=2, turn=True
Disabling undo button: No moves in stack
Colors switched: human_color=False, board.turn=True
Disabling undo button: No moves in stack
Bot's turn after color switch, making bot move
ChessBot evaluated 715 positions in 0.18 seconds
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling undo button: human_color=False, moves=2, turn=True
ChessBot evaluated 856 positions in 0.14 seconds
Disabling undo button: Bot is thinking
Enabling undo button: human_color=False, moves=3, turn=False
Enabling undo button: human_color=False, moves=3, turn=False
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling undo button: No moves in stack
ChessBot search depth set to 5
Colors switched: human_color=True, board.turn=True
Disabling undo button: No moves in stack
Enabling undo button: human_color=True, moves=1, turn=False
ChessBot evaluated 67507 positions in 13.75 seconds
Disabling undo button: Bot is thinking
Enabling undo button: human_color=True, moves=2, turn=True
Enabling undo button: human_color=True, moves=2, turn=True


----


Disabling undo button: No moves in stack
Colors switched: human_color=False, board.turn=True
Disabling undo button: No moves in stack
Bot's turn after color switch, making bot move
ChessBot evaluated 715 positions in 0.18 seconds
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling undo button: human_color=False, moves=2, turn=True
ChessBot evaluated 786 positions in 0.12 seconds
Disabling undo button: Bot is thinking
Enabling undo button: human_color=False, moves=3, turn=False
Enabling undo button: human_color=False, moves=3, turn=False
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling undo button: No moves in stack
Colors switched: human_color=True, board.turn=True
Disabling undo button: No moves in stack
Enabling undo button: human_color=True, moves=1, turn=False
ChessBot evaluated 851 positions in 0.25 seconds
Disabling undo button: Bot is thinking
Enabling undo button: human_color=True, moves=2, turn=True
Enabling undo button: human_color=True, moves=2, turn=True
Colors switched: human_color=False, board.turn=True
Enabling undo button: human_color=False, moves=2, turn=True
Bot's turn after color switch, making bot move
ChessBot evaluated 1639 positions in 0.42 seconds
Disabling undo button: Bot is thinking
Enabling undo button: human_color=False, moves=3, turn=False
Enabling undo button: human_color=False, moves=3, turn=False
Enabling undo button: human_color=False, moves=3, turn=False
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling undo button: human_color=False, moves=2, turn=True
ChessBot evaluated 1639 positions in 0.42 seconds
Disabling undo button: Bot is thinking
Enabling undo button: human_color=False, moves=3, turn=False
Enabling undo button: human_color=False, moves=3, turn=False
Enabling undo button: human_color=False, moves=4, turn=True
ChessBot evaluated 3738 positions in 0.96 seconds
Disabling undo button: Bot is thinking
Enabling undo button: human_color=False, moves=5, turn=False
Enabling undo button: human_color=False, moves=5, turn=False
Enabling undo button: human_color=False, moves=3, turn=False






PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui.py        
Disabling undo button: No moves in stack
ChessBot search depth set to 5
Colors switched: human_color=False, board.turn=True
Disabling undo button: No moves in stack
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
Disabling undo button: Bot is thinking
ChessBot evaluated 31026 positions in 7.31 seconds
Disabling undo button: Playing as BLACK, move stack has 1 move
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Disabling undo button: Playing as BLACK, move stack has 1 move
Board interaction re-enabled
Enabling undo button: human_color=False, moves=2, turn=True
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 46909 positions in 11.10 seconds
Enabling undo button: human_color=False, moves=3, turn=False
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Board interaction re-enabled
Enabling undo button: human_color=False, moves=4, turn=True
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 95507 positions in 20.22 seconds
Enabling undo button: human_color=False, moves=5, turn=False
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=5, turn=False
Board interaction re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling undo button: Playing as BLACK, move stack has 1 move







---------------------------------------------------------
---------------------------------------------------------



redo functionality:


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui.py
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
Enabling undo button: human_color=True, moves=1, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 868 positions in 0.15 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 2 moves in move stack
Current board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves to redo stack, now has 2 moves
Disabling undo button: No moves in stack
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Redoing 1 moves
New board state: rnbqkbnr/pppppppp/8/8/3P4/8/PPP1PPPP/RNBQKBNR b KQkq - 0 1
Enabling undo button: human_color=True, moves=1, turn=False
Enabling redo button: 1 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 868 positions in 0.19 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 2 moves in move stack
Current board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves to redo stack, now has 2 moves
Disabling undo button: No moves in stack
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Redoing 1 moves
New board state: rnbqkbnr/pppppppp/8/8/3P4/8/PPP1PPPP/RNBQKBNR b KQkq - 0 1
Enabling undo button: human_color=True, moves=1, turn=False
Enabling redo button: 1 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 868 positions in 0.14 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=3, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1748 positions in 0.44 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 4 moves in move stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/3P4/4P3/PPP2PPP/RNBQKBNR w KQkq - 1 3
Undoing 2 moves
New board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Redoing 1 moves
New board state: r1bqkbnr/pppppppp/2n5/8/3P4/4P3/PPP2PPP/RNBQKBNR b KQkq - 0 2
Enabling undo button: human_color=True, moves=3, turn=False
Enabling redo button: 1 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1748 positions in 0.33 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 4 moves in move stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/3P4/4P3/PPP2PPP/RNBQKBNR w KQkq - 1 3
Undoing 2 moves
New board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Redoing 1 moves
New board state: r1bqkbnr/pppppppp/2n5/8/3P4/4P3/PPP2PPP/RNBQKBNR b KQkq - 0 2
Enabling undo button: human_color=True, moves=3, turn=False
Enabling redo button: 1 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1748 positions in 0.31 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 4 moves in move stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/3P4/4P3/PPP2PPP/RNBQKBNR w KQkq - 1 3
Undoing 2 moves
New board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Starting undo with 2 moves in move stack
Current board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves to redo stack, now has 4 moves
Disabling undo button: No moves in stack
Enabling redo button: 4 moves available to redo
Starting redo with 4 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Redoing 1 moves
New board state: rnbqkbnr/pppppppp/8/8/3P4/8/PPP1PPPP/RNBQKBNR b KQkq - 0 1
Enabling undo button: human_color=True, moves=1, turn=False
Enabling redo button: 3 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 868 positions in 0.23 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 2 moves in move stack
Current board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves to redo stack, now has 2 moves
Disabling undo button: No moves in stack
Enabling redo button: 2 moves available to redo
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
Colors switched: human_color=False, board.turn=True
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 715 positions in 0.13 seconds
Board interaction disabled
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling redo button: No moves to redo
Board interaction re-enabled
Disabling undo button: Playing as BLACK, move stack has 1 move
Disabling redo button: No moves to redo
Enabling undo button: human_color=False, moves=2, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 761 positions in 0.18 seconds
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 3 moves in move stack
Current board state: rnbqkb1r/pppppppp/5n2/4N3/8/8/PPPPPPPP/RNBQKB1R b KQkq - 3 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Added 2 moves to redo stack, now has 2 moves
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Redoing 1 moves
New board state: rnbqkb1r/pppppppp/5n2/8/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 2 2
Enabling undo button: human_color=False, moves=2, turn=True
Enabling redo button: 1 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 761 positions in 0.12 seconds
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 3 moves in move stack
Current board state: rnbqkb1r/pppppppp/5n2/4N3/8/8/PPPPPPPP/RNBQKB1R b KQkq - 3 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Added 2 moves to redo stack, now has 2 moves
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling redo button: 2 moves available to redo
Enabling undo button: human_color=False, moves=2, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 856 positions in 0.15 seconds
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 3 moves in move stack
Current board state: rnbqkbnr/ppppp1pp/8/4Np2/8/8/PPPPPPPP/RNBQKB1R b KQkq - 1 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Added 2 moves to redo stack, now has 2 moves
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Redoing 1 moves
New board state: rnbqkbnr/ppppp1pp/8/5p2/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Enabling undo button: human_color=False, moves=2, turn=True
Enabling redo button: 1 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 856 positions in 0.17 seconds
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 3 moves in move stack
Current board state: rnbqkbnr/ppppp1pp/8/4Np2/8/8/PPPPPPPP/RNBQKB1R b KQkq - 1 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Added 2 moves to redo stack, now has 2 moves
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling redo button: 2 moves available to redo
Enabling undo button: human_color=False, moves=2, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 875 positions in 0.16 seconds
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=4, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 3168 positions in 0.52 seconds
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 5 moves in move stack
Current board state: rnbqkb1r/ppppp1pp/5p1n/8/2N5/8/PPPPPPPP/RNBQKB1R b KQkq - 1 3
Undoing 2 moves
New board state: rnbqkb1r/pppppppp/7n/4N3/8/8/PPPPPPPP/RNBQKB1R b KQkq - 3 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=False, moves=3, turn=False
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkb1r/pppppppp/7n/4N3/8/8/PPPPPPPP/RNBQKB1R b KQkq - 3 2
Redoing 1 moves
New board state: rnbqkb1r/ppppp1pp/5p1n/4N3/8/8/PPPPPPPP/RNBQKB1R w KQkq - 0 3
Enabling undo button: human_color=False, moves=4, turn=True
Enabling redo button: 1 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 3168 positions in 0.58 seconds
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 5 moves in move stack
Current board state: rnbqkb1r/ppppp1pp/5p1n/8/2N5/8/PPPPPPPP/RNBQKB1R b KQkq - 1 3
Undoing 2 moves
New board state: rnbqkb1r/pppppppp/7n/4N3/8/8/PPPPPPPP/RNBQKB1R b KQkq - 3 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=False, moves=3, turn=False
Enabling redo button: 2 moves available to redo
Starting undo with 3 moves in move stack
Current board state: rnbqkb1r/pppppppp/7n/4N3/8/8/PPPPPPPP/RNBQKB1R b KQkq - 3 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Added 2 moves to redo stack, now has 4 moves
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling redo button: 4 moves available to redo
ChessBot search depth set to 5
Enabling undo button: human_color=False, moves=2, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 53839 positions in 10.59 seconds
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 3 moves in move stack
Current board state: rnbqkbnr/pp1ppppp/8/2p5/8/4PN2/PPPP1PPP/RNBQKB1R b KQkq - 0 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Added 2 moves to redo stack, now has 2 moves
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Redoing 1 moves
New board state: rnbqkbnr/pp1ppppp/8/2p5/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Enabling undo button: human_color=False, moves=2, turn=True
Enabling redo button: 1 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 53839 positions in 8.77 seconds
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 3 moves in move stack
Current board state: rnbqkbnr/pp1ppppp/8/2p5/8/4PN2/PPPP1PPP/RNBQKB1R b KQkq - 0 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Added 2 moves to redo stack, now has 2 moves
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/5N2/PPPPPPPP/RNBQKB1R b KQkq - 1 1
Redoing 1 moves
New board state: rnbqkbnr/pp1ppppp/8/2p5/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Enabling undo button: human_color=False, moves=2, turn=True
Enabling redo button: 1 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 53839 positions in 12.23 seconds
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Colors switched: human_color=True, board.turn=False
Enabling undo button: human_color=True, moves=3, turn=False
Disabling redo button: No moves to redo
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
Disabling undo button: Bot is thinking
Disabling redo button: No moves to redo
ChessBot evaluated 127517 positions in 22.91 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 4 moves in move stack
Current board state: rnbqkb1r/pp1ppppp/5n2/2p5/8/4PN2/PPPP1PPP/RNBQKB1R w KQkq - 1 3
Undoing 2 moves
New board state: rnbqkbnr/pp1ppppp/8/2p5/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pp1ppppp/8/2p5/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Redoing 1 moves
New board state: rnbqkbnr/pp1ppppp/8/2p5/8/4PN2/PPPP1PPP/RNBQKB1R b KQkq - 0 2
Enabling undo button: human_color=True, moves=3, turn=False
Enabling redo button: 1 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 127517 positions in 27.63 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
ChessBot search depth set to 2
ChessBot search depth set to 4
ChessBot search depth set to 2
Starting undo with 4 moves in move stack
Current board state: rnbqkb1r/pp1ppppp/5n2/2p5/8/4PN2/PPPP1PPP/RNBQKB1R w KQkq - 1 3
Undoing 2 moves
New board state: rnbqkbnr/pp1ppppp/8/2p5/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pp1ppppp/8/2p5/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Redoing 1 moves
New board state: rnbqkbnr/pp1ppppp/8/2p5/8/4PN2/PPPP1PPP/RNBQKB1R b KQkq - 0 2
Enabling undo button: human_color=True, moves=3, turn=False
Enabling redo button: 1 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 142 positions in 0.03 seconds
Board interaction disabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 4 moves in move stack
Current board state: r1bqkbnr/pp1ppppp/2n5/2p5/8/4PN2/PPPP1PPP/RNBQKB1R w KQkq - 1 3
Undoing 2 moves
New board state: rnbqkbnr/pp1ppppp/8/2p5/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pp1ppppp/8/2p5/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Redoing 1 moves
New board state: rnbqkbnr/pp1ppppp/8/2p5/8/4PN2/PPPP1PPP/RNBQKB1R b KQkq - 0 2
Enabling undo button: human_color=True, moves=3, turn=False
Enabling redo button: 1 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 142 positions in 0.03 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 4 moves in move stack
Current board state: r1bqkbnr/pp1ppppp/2n5/2p5/8/4PN2/PPPP1PPP/RNBQKB1R w KQkq - 1 3
Undoing 2 moves
New board state: rnbqkbnr/pp1ppppp/8/2p5/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Starting undo with 2 moves in move stack
Current board state: rnbqkbnr/pp1ppppp/8/2p5/8/5N2/PPPPPPPP/RNBQKB1R w KQkq - 0 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves to redo stack, now has 4 moves
Disabling undo button: No moves in stack
Enabling redo button: 4 moves available to redo
Enabling undo button: human_color=True, moves=1, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 89 positions in 0.02 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=3, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 103 positions in 0.02 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 4 moves in move stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/3P4/2N5/PPP1PPPP/R1BQKBNR w KQkq - 1 3
Undoing 2 moves
New board state: rnbqkb1r/pppppppp/5n2/8/8/2N5/PPPPPPPP/R1BQKBNR w KQkq - 2 2
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 2 moves available to redo
Starting redo with 2 moves in redo stack
Current board state: rnbqkb1r/pppppppp/5n2/8/8/2N5/PPPPPPPP/R1BQKBNR w KQkq - 2 2
Redoing 1 moves
New board state: rnbqkb1r/pppppppp/5n2/8/3P4/2N5/PPP1PPPP/R1BQKBNR b KQkq - 0 2
Enabling undo button: human_color=True, moves=3, turn=False
Enabling redo button: 1 moves available to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 103 positions in 0.02 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled




------------------------------------




PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui.py
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
Enabling undo button: human_color=True, moves=1, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 868 positions in 0.16 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 2 moves in move stack
Current board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Undoing 2 moves (human + bot pair)
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves as a single redo action, now has 2 total moves in stack
Disabling undo button: No moves in stack
Enabling redo button: 1 action available to redo (containing 2 moves)
Starting redo with 2 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Redoing 1 move (human or bot)
New board state: rnbqkbnr/pppppppp/8/8/3P4/8/PPP1PPPP/RNBQKBNR b KQkq - 0 1
Enabling undo button: human_color=True, moves=1, turn=False
Enabling redo button: 1 action available to redo (containing 1 moves)
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 868 positions in 0.16 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=3, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1883 positions in 0.42 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=5, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2638 positions in 0.46 seconds
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 6 moves in move stack
Current board state: r1bqkb1r/pppppppp/5n2/8/1n1P3P/5N2/PPP1PPP1/RNBQKB1R w KQkq - 1 4
Undoing 2 moves (human + bot pair)
New board state: r1bqkb1r/pppppppp/2n2n2/8/3P4/5N2/PPP1PPPP/RNBQKB1R w KQkq - 3 3
Added 2 moves as a single redo action, now has 2 total moves in stack
Enabling undo button: human_color=True, moves=4, turn=True
Enabling redo button: 1 action available to redo (containing 2 moves)
Starting undo with 4 moves in move stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/3P4/5N2/PPP1PPPP/RNBQKB1R w KQkq - 3 3
Undoing 2 moves (human + bot pair)
New board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Added 2 moves as a single redo action, now has 4 total moves in stack
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 1 action available to redo (containing 4 moves)
Starting undo with 2 moves in move stack
Current board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Undoing 2 moves (human + bot pair)
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves as a single redo action, now has 6 total moves in stack
Disabling undo button: No moves in stack
Enabling redo button: 1 action available to redo (containing 6 moves)
Starting redo with 6 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Redoing 1 move (human or bot)
New board state: rnbqkbnr/pppppppp/8/8/3P4/8/PPP1PPPP/RNBQKBNR b KQkq - 0 1
Enabling undo button: human_color=True, moves=1, turn=False
Enabling redo button: 1 action available to redo (containing 5 moves)
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 868 positions in 0.23 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled




----------



PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui.py
Disabling undo button: No moves in stack
Disabling redo button: No moves to redo
Enabling undo button: human_color=True, moves=1, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 868 positions in 0.23 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=3, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1748 positions in 0.52 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 4 moves in move stack
Current board state: r1bqkb1r/pppppppp/2n2n2/8/3P4/4P3/PPP2PPP/RNBQKBNR w KQkq - 1 3
Undoing 2 moves (human + bot pair)
New board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Added 2 moves as a single redo action, now has 2 total moves in stack
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 1 action available to redo (containing 2 moves)
Starting undo with 2 moves in move stack
Current board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Undoing 2 moves (human + bot pair)
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves as a single redo action, now has 4 total moves in stack
Disabling undo button: No moves in stack
Enabling redo button: 1 action available to redo (containing 4 moves)
Starting redo with 4 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Redoing 1 move (human or bot)
New board state: rnbqkbnr/pppppppp/8/8/3P4/8/PPP1PPPP/RNBQKBNR b KQkq - 0 1
Enabling undo button: human_color=True, moves=1, turn=False
Enabling redo button: 1 action available to redo (containing 3 moves)
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 868 positions in 0.18 seconds
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Colors switched: human_color=False, board.turn=True
Enabling undo button: human_color=False, moves=2, turn=True
Disabling redo button: No moves to redo
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1927 positions in 0.55 seconds
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Enabling undo button: human_color=False, moves=4, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2584 positions in 0.79 seconds
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=6, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2936 positions in 0.84 seconds
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 7 moves in move stack
Current board state: r1bqkbnr/pppp2pp/2n2p2/4P3/5B2/8/PPP1PPPP/RN1QKBNR b KQkq - 0 4
Undoing 2 moves (human + bot pair)
New board state: r1bqkbnr/ppppp1pp/2n2p2/8/3P1B2/8/PPP1PPPP/RN1QKBNR b KQkq - 1 3
Added 2 moves as a single redo action, now has 2 total moves in stack
Enabling undo button: human_color=False, moves=5, turn=False
Enabling redo button: 1 action available to redo (containing 2 moves)
ChessBot search depth set to 5
Starting undo with 5 moves in move stack
Current board state: r1bqkbnr/ppppp1pp/2n2p2/8/3P1B2/8/PPP1PPPP/RN1QKBNR b KQkq - 1 3
Undoing 2 moves (human + bot pair)
New board state: r1bqkbnr/pppppppp/2n5/6B1/3P4/8/PPP1PPPP/RN1QKBNR b KQkq - 2 2
Added 2 moves as a single redo action, now has 4 total moves in stack
Enabling undo button: human_color=False, moves=3, turn=False
Enabling redo button: 1 action available to redo (containing 4 moves)
Starting redo with 4 moves in redo stack
Current board state: r1bqkbnr/pppppppp/2n5/6B1/3P4/8/PPP1PPPP/RN1QKBNR b KQkq - 2 2
Redoing 1 move (human or bot)
New board state: r1bqkbnr/ppppp1pp/2n2p2/6B1/3P4/8/PPP1PPPP/RN1QKBNR w KQkq - 0 3
Enabling undo button: human_color=False, moves=4, turn=True
Enabling redo button: 1 action available to redo (containing 3 moves)
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 167433 positions in 44.04 seconds
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
ChessBot search depth set to 2
Enabling undo button: human_color=False, moves=6, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 264 positions in 0.07 seconds
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 7 moves in move stack
Current board state: r1bqkbnr/ppppp1pp/2n5/3P1p2/5B2/8/PPP1PPPP/RN1QKBNR b KQkq - 0 4
Undoing 2 moves (human + bot pair)
New board state: r1bqkbnr/ppppp1pp/2n2p2/8/3P1B2/8/PPP1PPPP/RN1QKBNR b KQkq - 1 3
Added 2 moves as a single redo action, now has 2 total moves in stack
Enabling undo button: human_color=False, moves=5, turn=False
Enabling redo button: 1 action available to redo (containing 2 moves)
Starting undo with 5 moves in move stack
Current board state: r1bqkbnr/ppppp1pp/2n2p2/8/3P1B2/8/PPP1PPPP/RN1QKBNR b KQkq - 1 3
Undoing 2 moves (human + bot pair)
New board state: r1bqkbnr/pppppppp/2n5/6B1/3P4/8/PPP1PPPP/RN1QKBNR b KQkq - 2 2
Added 2 moves as a single redo action, now has 4 total moves in stack
Enabling undo button: human_color=False, moves=3, turn=False
Enabling redo button: 1 action available to redo (containing 4 moves)
Starting redo with 4 moves in redo stack
Current board state: r1bqkbnr/pppppppp/2n5/6B1/3P4/8/PPP1PPPP/RN1QKBNR b KQkq - 2 2
Redoing 1 move (human or bot)
New board state: r1bqkbnr/ppppp1pp/2n2p2/6B1/3P4/8/PPP1PPPP/RN1QKBNR w KQkq - 0 3
Enabling undo button: human_color=False, moves=4, turn=True
Enabling redo button: 1 action available to redo (containing 3 moves)
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 322 positions in 0.10 seconds
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 5 moves in move stack
Current board state: r1bqkbnr/ppppp1pp/2n2p2/8/3P1B2/8/PPP1PPPP/RN1QKBNR b KQkq - 1 3
Undoing 2 moves (human + bot pair)
New board state: r1bqkbnr/pppppppp/2n5/6B1/3P4/8/PPP1PPPP/RN1QKBNR b KQkq - 2 2
Added 2 moves as a single redo action, now has 2 total moves in stack
Enabling undo button: human_color=False, moves=3, turn=False
Enabling redo button: 1 action available to redo (containing 2 moves)
Starting undo with 3 moves in move stack
Current board state: r1bqkbnr/pppppppp/2n5/6B1/3P4/8/PPP1PPPP/RN1QKBNR b KQkq - 2 2
Undoing 2 moves (human + bot pair)
New board state: rnbqkbnr/pppppppp/8/8/3P4/8/PPP1PPPP/RNBQKBNR b KQkq - 0 1
Added 2 moves as a single redo action, now has 4 total moves in stack
Disabling undo button: Playing as BLACK, move stack has 1 move
Enabling redo button: 1 action available to redo (containing 4 moves)
Starting redo with 4 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/3P4/8/PPP1PPPP/RNBQKBNR b KQkq - 0 1
Redoing 1 move (human or bot)
New board state: r1bqkbnr/pppppppp/2n5/8/3P4/8/PPP1PPPP/RNBQKBNR w KQkq - 1 2
Enabling undo button: human_color=False, moves=2, turn=True
Enabling redo button: 1 action available to redo (containing 3 moves)
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 186 positions in 0.05 seconds
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=3, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
ChessBot search depth set to 4
Enabling undo button: human_color=False, moves=4, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 17583 positions in 3.82 seconds
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=5, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=False, moves=6, turn=True
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 10975 positions in 2.23 seconds
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 7 moves in move stack
Current board state: r1bqkbnr/pppPp2p/6p1/5p2/8/8/PPP1PPPP/RNBQKBNR b KQkq - 0 4
Undoing 2 moves (human + bot pair)
New board state: r1bqkbnr/ppppp1pp/2P5/5p2/8/8/PPP1PPPP/RNBQKBNR b KQkq - 0 3
Added 2 moves as a single redo action, now has 2 total moves in stack
Enabling undo button: human_color=False, moves=5, turn=False
Enabling redo button: 1 action available to redo (containing 2 moves)
Starting redo with 2 moves in redo stack
Current board state: r1bqkbnr/ppppp1pp/2P5/5p2/8/8/PPP1PPPP/RNBQKBNR b KQkq - 0 3
Redoing 1 move (human or bot)
New board state: r1bqkbnr/ppppp2p/2P3p1/5p2/8/8/PPP1PPPP/RNBQKBNR w KQkq - 0 4
Enabling undo button: human_color=False, moves=6, turn=True
Enabling redo button: 1 action available to redo (containing 1 moves)
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 10975 positions in 2.44 seconds
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=7, turn=False
Disabling redo button: No moves to redo
Board interaction re-enabled
Colors switched: human_color=True, board.turn=False
Enabling undo button: human_color=True, moves=7, turn=False
Disabling redo button: No moves to redo
Bot's turn after color switch, making bot move





--------------------------------



=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1045 positions in 0.31 seconds
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Enabling undo button: human_color=True, moves=5, turn=False
Disabling redo button: No moves to redo
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 5399 positions in 1.56 seconds
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=6, turn=True
Disabling redo button: No moves to redo
Board interaction re-enabled
Starting undo with 6 moves in move stack
Current board state: rnbqkb1r/pp1ppppp/2p5/1B5Q/4n3/8/PPPP1PPP/RNB1K1NR w KQkq - 0 4
Undoing 2 moves
New board state: rnbqkb1r/pppppppp/8/1B6/4n3/8/PPPP1PPP/RNBQK1NR w KQkq - 0 3
Added 2 moves to redo stack, now has 2 moves
Enabling undo button: human_color=True, moves=4, turn=True
Enabling redo button: 1 move available to redo
Starting undo with 4 moves in move stack
Current board state: rnbqkb1r/pppppppp/8/1B6/4n3/8/PPPP1PPP/RNBQK1NR w KQkq - 0 3
Undoing 2 moves
New board state: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Added 2 moves to redo stack, now has 4 moves
Enabling undo button: human_color=True, moves=2, turn=True
Enabling redo button: 1 move available to redo
Starting undo with 2 moves in move stack
Current board state: rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2
Undoing 2 moves
New board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Added 2 moves to redo stack, now has 6 moves
Disabling undo button: No moves in stack
Enabling redo button: 1 move available to redo
Starting redo with 6 moves in redo stack
Current board state: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
Redoing 1 moves
New board state: rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq - 0 1
Enabling undo button: human_color=True, moves=1, turn=False
Enabling redo button: 1 move available to redo