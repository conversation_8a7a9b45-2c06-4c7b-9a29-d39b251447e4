PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_evaluation_fix.py
=== TESTING EVALUATION CONSISTENCY ===
Starting position evaluation: 0
After 1. Nf3: -3
After 1... Nf6: 0
After 2. Nc3: 54
After 2... Nc6: -5

=== EVALUATION ANALYSIS ===
Starting position: 0 (expected)
After symmetric moves: -5 (should be close to 0)
Difference from starting position: -5

=== TESTING WITH DIFFERENT DEPTHS ===
Depth 1: 44
Depth 2: -5
Depth 3: 115
Depth 4: -62