import chess
import chess.engine
import random
import math
from typing import Op<PERSON>, Tuple, List, Dict
import time
from dataclasses import dataclass

@dataclass
class MCTSNode:
    """Node for Monte Carlo Tree Search."""
    move: Optional[chess.Move] = None
    parent: Optional['MCTSNode'] = None
    children: List['MCTSNode'] = None
    wins: int = 0
    visits: int = 0
    untried_moves: List[chess.Move] = None
    
    def __post_init__(self):
        self.children = []
        self.untried_moves = []

    def uct_select_child(self, exploration: float = 1.41) -> 'MCTSNode':
        """Select child node using UCT formula."""
        return max(self.children, 
                  key=lambda c: (c.wins / c.visits) + 
                               exploration * math.sqrt(math.log(self.visits) / c.visits))

    def add_child(self, move: chess.Move, board: chess.Board) -> 'MCTSNode':
        """Add a new child node for the given move."""
        board.push(move)
        child = MCTSNode(move=move, parent=self)
        board.pop()
        self.untried_moves.remove(move)
        self.children.append(child)
        return child

    def update(self, result: float):
        """Update node statistics."""
        self.visits += 1
        self.wins += result


def simulate_random_game(board: chess.Board) -> float:
    """Simulate a random game from current position and return result."""
    while not board.is_game_over():
        move = random.choice(list(board.legal_moves))
        board.push(move)
    
    if board.is_checkmate():
        return 1.0 if board.turn != chess.WHITE else 0.0
    return 0.5  # Draw


class ChessBot:
    """
    A chess bot that combines minimax with alpha-beta pruning and Monte Carlo Tree Search
    for enhanced decision making.
    """
    
    def __init__(self, depth: int = 3, name: str = "ChessBot", 
                 mcts_iterations: int = 100, top_moves: int = 3):
        """
        Initialize the chess bot.
        
        Args:
            depth: Search depth for minimax algorithm
            name: Name of the bot
            mcts_iterations: Number of MCTS iterations per move
            top_moves: Number of top moves from minimax to explore with MCTS
        """
        self.depth = depth
        self.name = name
        self.mcts_iterations = mcts_iterations
        self.top_moves = top_moves
        self.nodes_evaluated = 0
        self.mcts_nodes_visited = 0
        
        # Piece values for evaluation
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
        
        # Position tables for piece placement evaluation
        self.pawn_table = [
            0,  0,  0,  0,  0,  0,  0,  0,
            50, 50, 50, 50, 50, 50, 50, 50,
            10, 10, 20, 30, 30, 20, 10, 10,
            5,  5, 10, 25, 25, 10,  5,  5,
            0,  0,  0, 20, 20,  0,  0,  0,
            5, -5,-10,  0,  0,-10, -5,  5,
            5, 10, 10,-20,-20, 10, 10,  5,
            0,  0,  0,  0,  0,  0,  0,  0
        ]
        
        self.knight_table = [
            -50,-40,-30,-30,-30,-30,-40,-50,
            -40,-20,  0,  0,  0,  0,-20,-40,
            -30,  0, 10, 15, 15, 10,  0,-30,
            -30,  5, 15, 20, 20, 15,  5,-30,
            -30,  0, 15, 20, 20, 15,  0,-30,
            -30,  5, 10, 15, 15, 10,  5,-30,
            -40,-20,  0,  5,  5,  0,-20,-40,
            -50,-40,-30,-30,-30,-30,-40,-50
        ]
    
    def evaluate_board(self, board: chess.Board) -> int:
        """
        Evaluate the current board position.
        
        Args:
            board: Chess board to evaluate
            
        Returns:
            Evaluation score (positive for white advantage, negative for black)
        """
        if board.is_checkmate():
            return -20000 if board.turn else 20000
        
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        
        # Material evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = self.piece_values[piece.piece_type]
                
                # Add positional bonuses
                if piece.piece_type == chess.PAWN:
                    if piece.color == chess.WHITE:
                        value += self.pawn_table[square]
                    else:
                        value += self.pawn_table[chess.square_mirror(square)]
                elif piece.piece_type == chess.KNIGHT:
                    if piece.color == chess.WHITE:
                        value += self.knight_table[square]
                    else:
                        value += self.knight_table[chess.square_mirror(square)]
                
                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value
        
        # Mobility bonus
        legal_moves = len(list(board.legal_moves))
        if board.turn == chess.WHITE:
            score += legal_moves * 2
        else:
            score -= legal_moves * 2
        
        return score
    
    def minimax(self, board: chess.Board, depth: int, alpha: int, beta: int, 
                maximizing_player: bool) -> Tuple[int, Optional[chess.Move]]:
        """
        Minimax algorithm with alpha-beta pruning.
        
        Args:
            board: Current board position
            depth: Remaining search depth
            alpha: Alpha value for pruning
            beta: Beta value for pruning
            maximizing_player: True if maximizing player's turn
            
        Returns:
            Tuple of (evaluation_score, best_move)
        """
        self.nodes_evaluated += 1
        
        if depth == 0 or board.is_game_over():
            return self.evaluate_board(board), None
        
        best_move = None
        
        if maximizing_player:
            max_eval = float('-inf')
            for move in board.legal_moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            return max_eval, best_move
        else:
            min_eval = float('inf')
            for move in board.legal_moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            return min_eval, best_move
    
    def get_best_move(self, board: chess.Board) -> chess.Move:
        """
        Get the best move for the current position.
        
        Args:
            board: Current board position
            
        Returns:
            Best move according to the bot's evaluation
        """
        self.nodes_evaluated = 0
        start_time = time.time()
        
        # Use minimax to find the best move
        maximizing = board.turn == chess.WHITE
        _, best_move = self.minimax(board, self.depth, float('-inf'), float('inf'), maximizing)
        
        end_time = time.time()
        
        if best_move is None:
            # Fallback to random move if no move found
            legal_moves = list(board.legal_moves)
            best_move = random.choice(legal_moves) if legal_moves else None
        
        print(f"{self.name} evaluated {self.nodes_evaluated} positions in {end_time - start_time:.2f} seconds")
        
        return best_move
    
    def set_depth(self, depth: int):
        """Set the search depth for the bot."""
        self.depth = depth
        print(f"{self.name} search depth set to {depth}")

    def mcts_search(self, board: chess.Board, root_moves: List[chess.Move]) -> chess.Move:
        """Perform Monte Carlo Tree Search on given moves."""
        root = MCTSNode()
        root.untried_moves = root_moves.copy()
        
        for _ in range(self.mcts_iterations):
            node = root
            temp_board = board.copy()
            
            # Selection - choose child nodes until we find an unexplored move
            while not node.untried_moves and node.children:
                node = node.uct_select_child()
                temp_board.push(node.move)
                self.mcts_nodes_visited += 1
            
            # Expansion - add a new child node if possible
            if node.untried_moves:
                move = random.choice(node.untried_moves)
                temp_board.push(move)
                node = node.add_child(move, temp_board)
                self.mcts_nodes_visited += 1
            
            # Simulation - play random game from this position
            result = simulate_random_game(temp_board.copy())
            
            # Backpropagation - update stats along the path
            while node is not None:
                node.update(result)
                node = node.parent
        
        # Return move with highest visit count
        return max(root.children, key=lambda c: c.visits).move

    def get_top_moves(self, board: chess.Board, n: int) -> List[chess.Move]:
        """Get top N moves from minimax evaluation."""
        moves = list(board.legal_moves)
        if len(moves) <= n:
            return moves
            
        # Evaluate all moves with minimax at reduced depth
        scored_moves = []
        for move in moves:
            board.push(move)
            score, _ = self.minimax(board, self.depth-1, float('-inf'), float('inf'), not board.turn)
            board.pop()
            scored_moves.append((score, move))
        
        # Sort and return top N moves
        scored_moves.sort(reverse=(board.turn == chess.WHITE))
        return [move for score, move in scored_moves[:n]]

    def get_best_move(self, board: chess.Board) -> chess.Move:
        """
        Get the best move using hybrid minimax + MCTS approach.
        
        1. Use minimax to find top promising moves
        2. Use MCTS to explore those moves in depth
        3. Select best move based on MCTS results
        """
        self.nodes_evaluated = 0
        self.mcts_nodes_visited = 0
        start_time = time.time()
        
        # Step 1: Get top moves from minimax
        top_moves = self.get_top_moves(board, self.top_moves)
        
        # Step 2: Run MCTS on top moves
        if len(top_moves) > 1:
            best_move = self.mcts_search(board, top_moves)
        else:
            best_move = top_moves[0] if top_moves else None
        
        # Fallback to random if no move found
        if best_move is None:
            legal_moves = list(board.legal_moves)
            best_move = random.choice(legal_moves) if legal_moves else None
        
        end_time = time.time()
        print(f"{self.name} evaluated {self.nodes_evaluated} minimax nodes and "
              f"{self.mcts_nodes_visited} MCTS nodes in {end_time - start_time:.2f} seconds")
        
        return best_move
