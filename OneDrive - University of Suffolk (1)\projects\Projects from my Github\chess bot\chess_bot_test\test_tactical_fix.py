#!/usr/bin/env python3
"""
Test script to verify the tactical fix for hanging pieces
"""

import chess
from unified_chess_bot import Medium<PERSON>ot

def test_hanging_piece_scenario():
    """Test the specific scenario where black should retreat the knight"""
    
    print("Testing Tactical Fix for Hanging Pieces")
    print("="*50)
    
    # Create the problematic position from the user's example
    board = chess.Board()
    
    # Play the moves leading to the problem
    moves = [
        "Nc3", "d5",
        "Nf3", "Nc6", 
        "d4", "e6",
        "Ne5", "Nb4"  # Knight moves to attacked square
    ]
    
    print("Move history:")
    for i in range(0, len(moves), 2):
        white_move = moves[i]
        black_move = moves[i+1] if i+1 < len(moves) else ""
        board.push_san(white_move)
        print(f"{(i//2)+1}. {white_move}", end="")
        
        if black_move:
            board.push_san(black_move)
            print(f"      {black_move}")
        else:
            print()
    
    print(f"\nCurrent position: {board.fen()}")
    print(f"To move: {'White' if board.turn == chess.WHITE else 'Black'}")
    
    # Now <PERSON> plays a3, attacking the knight
    print("\n<PERSON><PERSON><PERSON> plays a3 (attacking the knight on b4)")
    board.push_san("a3")
    
    print(f"Position after a3: {board.fen()}")
    print(f"Black to move - knight on b4 is attacked!")
    
    # Check what the bot thinks about this position
    bot = MediumBot()
    bot.use_opening_book = False  # Disable opening book
    
    print("\n" + "="*50)
    print("BEFORE FIX - Bot Analysis:")
    print("="*50)
    
    # Analyze the position
    evaluation = bot.evaluate_board(board)
    print(f"Position evaluation: {evaluation}")
    print(f"(Negative values favor Black, Positive favor White)")
    
    # Check if bot detects hanging pieces
    hanging_score = bot._evaluate_hanging_pieces(board)
    print(f"Hanging pieces penalty: {hanging_score}")
    
    # Get bot's best move
    best_move = bot.get_best_move(board)
    print(f"Bot's chosen move: {best_move}")
    
    if best_move:
        print(f"Move in standard notation: {board.san(best_move)}")
        
        # Check if it's a retreat move
        if best_move.from_square == chess.B4:
            print("✅ GOOD: Bot is moving the attacked knight!")
        else:
            print("❌ BAD: Bot is not retreating the attacked knight!")
            
        # Analyze the move
        piece = board.piece_at(best_move.from_square)
        if piece:
            print(f"Moving piece: {piece.symbol()} from {chess.square_name(best_move.from_square)} to {chess.square_name(best_move.to_square)}")
            
        # Check if move is a capture
        if board.is_capture(best_move):
            captured = board.piece_at(best_move.to_square)
            print(f"This move captures: {captured.symbol() if captured else 'unknown'}")
    
    print("\n" + "="*50)
    print("Expected Behavior:")
    print("="*50)
    print("The bot should:")
    print("1. Detect that the knight on b4 is hanging")
    print("2. Prioritize moving it to safety")
    print("3. NOT make attacking moves while ignoring the threat")
    print("4. Choose a retreat square like Nc6, Nd5, Na6, or Nc2")
    
    return best_move

def test_safe_retreat_squares():
    """Test what retreat squares are available and which is best"""
    
    print("\n" + "="*50)
    print("ANALYZING RETREAT OPTIONS")
    print("="*50)
    
    board = chess.Board()
    # Recreate the position
    moves = ["Nc3", "d5", "Nf3", "Nc6", "d4", "e6", "Ne5", "Nb4", "a3"]
    for move in moves:
        board.push_san(move)
    
    print(f"Knight on b4 can move to:")
    knight_moves = []
    
    for move in board.legal_moves:
        if move.from_square == chess.B4:
            knight_moves.append(move)
            to_square_name = chess.square_name(move.to_square)
            
            # Test if the destination is safe
            board.push(move)
            attackers = board.attackers(chess.WHITE, move.to_square)
            is_safe = len(attackers) == 0
            board.pop()
            
            safety_status = "✅ SAFE" if is_safe else "❌ ATTACKED"
            print(f"  - {to_square_name} ({safety_status})")
    
    return knight_moves

if __name__ == "__main__":
    best_move = test_hanging_piece_scenario()
    retreat_options = test_safe_retreat_squares()
    
    print(f"\n" + "="*50)
    print("SUMMARY")
    print("="*50)
    
    if best_move and best_move.from_square == chess.B4:
        print("✅ SUCCESS: Bot is moving the attacked knight!")
        print("The tactical fix is working correctly.")
    else:
        print("❌ ISSUE: Bot is still not prioritizing knight retreat.")
        print("The hanging piece detection may need further tuning.")
        
    print(f"\nTotal knight retreat options: {len(retreat_options)}")