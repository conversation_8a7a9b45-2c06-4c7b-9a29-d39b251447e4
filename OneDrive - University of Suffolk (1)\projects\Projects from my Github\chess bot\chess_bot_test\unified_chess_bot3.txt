#!/usr/bin/env python3
"""
Unified Chess Bot - A comprehensive chess AI with multiple advanced features
Combines the best aspects of all previous implementations
"""

import chess
import chess.engine
import chess.polyglot  # <--- ADD THIS LINE
import random
import time
import math
from typing import Optional, Tuple, List, Dict
from collections import defaultdict

class UnifiedChessBot:
    """
    A unified chess bot that combines multiple advanced features:
    - Minimax with alpha-beta pruning
    - Iterative deepening
    - Quiescence search
    - Transposition tables
    - Advanced evaluation with piece-square tables
    - Opening book knowledge
    """
    
    def __init__(self, depth: int = 4, name: str = "UnifiedChessBot", 
                 use_iterative_deepening: bool = True, 
                 use_quiescence_search: bool = True,
                 use_transposition_table: bool = True,
                 max_quiescence_depth: int = 8): # can lower from 6 to 4 so can limit search depth to improve performance 
        """
        Initialize the unified chess bot.
        
        Args:
            depth: Maximum search depth
            name: Name of the bot
            use_iterative_deepening: Enable iterative deepening
            use_quiescence_search: Enable quiescence search
            use_transposition_table: Enable transposition tables
            max_quiescence_depth: Maximum depth for quiescence search
        """
        self.depth = depth
        self.name = name
        self.use_iterative_deepening = use_iterative_deepening
        self.use_quiescence_search = use_quiescence_search
        self.use_transposition_table = use_transposition_table
        self.max_quiescence_depth = max_quiescence_depth
        
        # Statistics
        self.nodes_evaluated = 0
        self.transposition_hits = 0
        self.quiescence_nodes = 0
        
        # Move quality tracking
        self.move_quality_history = []  # List of (move_san, search_score, static_score, depth_used, time_taken)
        
        # Transposition table: key = board hash, value = (depth, score, flag, best_move)
        # flag: 0 = exact, 1 = lower bound, 2 = upper bound
        self.transposition_table = {}
        self.max_table_size = 100000
        
        # Piece values
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
        
        # Advanced piece-square tables
        self._init_piece_square_tables()
        
        # Opening book (simple)
        self._init_opening_book()
        
        # Testing flags
        self.use_opening_book = True
        
    def _init_piece_square_tables(self):
        """Initialize piece-square tables for positional evaluation."""
        
        self.pawn_table = [
            0,  0,  0,  0,  0,  0,  0,  0,   # Rank 1
            5,  5,  5,  5,  5,  5,  5,  5,   # Rank 2
            10, 10, 20, 30, 30, 20, 10, 10,  # Rank 3
            15, 15, 25, 35, 35, 25, 15, 15,  # Rank 4
            20, 20, 30, 40, 40, 30, 20, 20,  # Rank 5
            35, 35, 40, 50, 50, 40, 35, 35,  # Rank 6
            50, 50, 60, 70, 70, 60, 50, 50,  # Rank 7
            0,  0,  0,  0,  0,  0,  0,  0    # Rank 8
        ]
        
        self.knight_table = [
            -50,-40,-30,-30,-30,-30,-40,-50,
            -40,-20,  0,  0,  0,  0,-20,-40,
            -30,  0, 10, 15, 15, 10,  0,-30,
            -30,  5, 15, 20, 20, 15,  5,-30,
            -30,  0, 15, 20, 20, 15,  0,-30,
            -30,  5, 10, 15, 15, 10,  5,-30,
            -40,-20,  0,  5,  5,  0,-20,-40,
            -50,-40,-30,-30,-30,-30,-40,-50
        ]

        self.bishop_table = [
            -20,-10,-10,-10,-10,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5, 10, 10,  5,  0,-10,
            -10,  5,  5, 10, 10,  5,  5,-10,
            -10,  0, 10, 10, 10, 10,  0,-10,
            -10, 10, 10, 10, 10, 10, 10,-10,
            -10,  5,  0,  0,  0,  0,  5,-10,
            -20,-10,-10,-10,-10,-10,-10,-20
        ]

        self.rook_table = [
             0,  0,  0,  0,  0,  0,  0,  0,
             5, 10, 10, 10, 10, 10, 10,  5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
            -5,  0,  0,  0,  0,  0,  0, -5,
             0,  0,  0,  5,  5,  0,  0,  0
        ]

        self.queen_table = [
            -20,-10,-10, -5, -5,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5,  5,  5,  5,  0,-10,
             -5,  0,  5,  5,  5,  5,  0, -5,
              0,  0,  5,  5,  5,  5,  0, -5,
            -10,  5,  5,  5,  5,  5,  0,-10,
            -10,  0,  5,  0,  0,  0,  0,-10,
            -20,-10,-10, -5, -5,-10,-10,-20
        ]

        # King tables for middlegame and endgame
        self.king_mg_table = [
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -30,-40,-40,-50,-50,-40,-40,-30,
            -20,-30,-30,-40,-40,-30,-30,-20,
            -10,-20,-20,-20,-20,-20,-20,-10,
             20, 20,  0,  0,  0,  0, 20, 20,
             20, 30, 10,  0,  0, 10, 30, 20
        ]
        
        self.king_eg_table = [
            -50,-40,-30,-20,-20,-30,-40,-50,
            -30,-20,-10,  0,  0,-10,-20,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 30, 40, 40, 30,-10,-30,
            -30,-10, 20, 30, 30, 20,-10,-30,
            -30,-30,  0,  0,  0,  0,-30,-30,
            -50,-30,-30,-30,-30,-30,-30,-50
        ]
        
    def _init_opening_book(self):
        """Initialize a simple opening book."""
        self.opening_moves = {
            # Common opening moves from starting position
            chess.Board().fen(): ["e2e4", "d2d4", "g1f3", "c2c4"],
            # Italian Game continuation
            "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1": ["e7e5"],
            "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2": ["g1f3"],
            # Add more as needed
        }
        
    def _is_endgame(self, board: chess.Board) -> bool:
        """Determine if the game is in endgame phase."""
        # Count major pieces
        white_queens = len(board.pieces(chess.QUEEN, chess.WHITE))
        black_queens = len(board.pieces(chess.QUEEN, chess.BLACK))
        
        # No queens = endgame
        if white_queens == 0 and black_queens == 0:
            return True
        
        # Few pieces remaining
        white_material = sum(len(board.pieces(pt, chess.WHITE)) 
                           for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])
        black_material = sum(len(board.pieces(pt, chess.BLACK)) 
                           for pt in [chess.ROOK, chess.BISHOP, chess.KNIGHT])
        
        return (white_material + black_material) <= 6
        
    def _get_piece_square_value(self, piece: chess.Piece, square: int, is_endgame: bool) -> int:
        """Get piece-square table value for a piece on a square."""
        # Mirror square for black pieces
        sq = square if piece.color == chess.WHITE else chess.square_mirror(square)
        
        if piece.piece_type == chess.PAWN:
            return self.pawn_table[sq]
        elif piece.piece_type == chess.KNIGHT:
            return self.knight_table[sq]
        elif piece.piece_type == chess.BISHOP:
            return self.bishop_table[sq]
        elif piece.piece_type == chess.ROOK:
            return self.rook_table[sq]
        elif piece.piece_type == chess.QUEEN:
            return self.queen_table[sq]
        elif piece.piece_type == chess.KING:
            table = self.king_eg_table if is_endgame else self.king_mg_table
            return table[sq]
        
        return 0
        
    def evaluate_board(self, board: chess.Board) -> int:
        """
        Enhanced comprehensive board evaluation function.
        
        Args:
            board: Chess board to evaluate
            
        Returns:
            Evaluation score (positive for white advantage)
        """
        if board.is_checkmate():
            return -20000 if board.turn == chess.WHITE else 20000
        
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        is_endgame = self._is_endgame(board)
        
        # Material and positional evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                # Base piece value
                value = self.piece_values[piece.piece_type]
                
                # Add positional bonus
                value += self._get_piece_square_value(piece, square, is_endgame)
                
                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value
        
        # Mobility evaluation (optimized)
        original_turn = board.turn
        
        board.turn = chess.WHITE
        white_mobility = len(list(board.legal_moves))
        board.turn = chess.BLACK
        black_mobility = len(list(board.legal_moves))
        board.turn = original_turn
        
        mobility_score = (white_mobility - black_mobility) * 3
        score += mobility_score
        
        # Enhanced king safety
        score += self._evaluate_king_safety(board, is_endgame)
        
        # Enhanced pawn structure evaluation (reduced weight)
        score += self._evaluate_pawn_structure(board) // 2
        
        # Center control and space
        score += self._evaluate_center_control(board)
        
        # Piece coordination and activity (reduced weight)
        score += self._evaluate_piece_coordination(board) // 2
        
        # Tactical patterns (reduced weight to avoid over-penalizing)
        score += self._evaluate_tactical_patterns(board) // 3
        
        # Endgame specific evaluation
        if is_endgame:
            score += self._evaluate_endgame_factors(board) // 2
        
        return score
        
    def _evaluate_pawn_structure(self, board: chess.Board) -> int:
        """Enhanced pawn structure evaluation."""
        score = 0
        
        white_pawns = board.pieces(chess.PAWN, chess.WHITE)
        black_pawns = board.pieces(chess.PAWN, chess.BLACK)
        
        # Evaluate for both colors
        for color in [chess.WHITE, chess.BLACK]:
            color_pawns = board.pieces(chess.PAWN, color)
            enemy_pawns = board.pieces(chess.PAWN, not color)
            multiplier = 1 if color == chess.WHITE else -1
            
            # Doubled pawns penalty
            for file in range(8):
                file_pawns = len([sq for sq in color_pawns if chess.square_file(sq) == file])
                if file_pawns > 1:
                    score += multiplier * (file_pawns - 1) * -25
            
            # Isolated pawns penalty
            for pawn_sq in color_pawns:
                file = chess.square_file(pawn_sq)
                has_neighbor = False
                
                # Check adjacent files for friendly pawns
                for adj_file in [file - 1, file + 1]:
                    if 0 <= adj_file <= 7:
                        if any(chess.square_file(sq) == adj_file for sq in color_pawns):
                            has_neighbor = True
                            break
                
                if not has_neighbor:
                    score += multiplier * -20  # Isolated pawn penalty
            
            # Passed pawns bonus
            for pawn_sq in color_pawns:
                if self._is_passed_pawn(board, pawn_sq, color):
                    rank = chess.square_rank(pawn_sq)
                    # Bonus increases as pawn advances
                    if color == chess.WHITE:
                        bonus = (rank - 1) * 15 + 20
                    else:
                        bonus = (6 - rank) * 15 + 20
                    score += multiplier * bonus
            
            # Backward pawns penalty
            for pawn_sq in color_pawns:
                if self._is_backward_pawn(board, pawn_sq, color):
                    score += multiplier * -15
            
            # Pawn chains bonus
            for pawn_sq in color_pawns:
                if self._is_pawn_chain(board, pawn_sq, color):
                    score += multiplier * 10
        
        return score
        
    def _evaluate_center_control(self, board: chess.Board) -> int:
        """Evaluate center control."""
        center_squares = [chess.D4, chess.D5, chess.E4, chess.E5]
        score = 0
        
        for square in center_squares:
            piece = board.piece_at(square)
            if piece:
                if piece.color == chess.WHITE:
                    score += 10
                else:
                    score -= 10
        
        return score
    
    def _is_passed_pawn(self, board: chess.Board, pawn_sq: int, color: chess.Color) -> bool:
        """Check if a pawn is passed (no enemy pawns can stop it)."""
        file = chess.square_file(pawn_sq)
        rank = chess.square_rank(pawn_sq)
        
        enemy_pawns = board.pieces(chess.PAWN, not color)
        
        # Check if any enemy pawns can stop this pawn
        for enemy_sq in enemy_pawns:
            enemy_file = chess.square_file(enemy_sq)
            enemy_rank = chess.square_rank(enemy_sq)
            
            # Enemy pawn is on same file or adjacent file
            if abs(enemy_file - file) <= 1:
                # Check if enemy pawn is ahead of our pawn
                if color == chess.WHITE and enemy_rank > rank:
                    return False
                elif color == chess.BLACK and enemy_rank < rank:
                    return False
        
        return True
    
    def _is_backward_pawn(self, board: chess.Board, pawn_sq: int, color: chess.Color) -> bool:
        """Check if a pawn is backward (can't advance safely and has no support)."""
        file = chess.square_file(pawn_sq)
        rank = chess.square_rank(pawn_sq)
        
        friendly_pawns = board.pieces(chess.PAWN, color)
        enemy_pawns = board.pieces(chess.PAWN, not color)
        
        # Check if pawn has friendly support on adjacent files
        has_support = False
        for adj_file in [file - 1, file + 1]:
            if 0 <= adj_file <= 7:
                for friendly_sq in friendly_pawns:
                    if chess.square_file(friendly_sq) == adj_file:
                        friendly_rank = chess.square_rank(friendly_sq)
                        if color == chess.WHITE and friendly_rank >= rank - 1:
                            has_support = True
                        elif color == chess.BLACK and friendly_rank <= rank + 1:
                            has_support = True
        
        if has_support:
            return False
        
        # Check if advancing would be safe
        advance_sq = pawn_sq + (8 if color == chess.WHITE else -8)
        if 0 <= advance_sq <= 63:
            # Check if enemy pawns control the advance square
            for enemy_sq in enemy_pawns:
                enemy_file = chess.square_file(enemy_sq)
                if abs(enemy_file - file) == 1:
                    enemy_rank = chess.square_rank(enemy_sq)
                    advance_rank = chess.square_rank(advance_sq)
                    if color == chess.WHITE and enemy_rank == advance_rank + 1:
                        return True
                    elif color == chess.BLACK and enemy_rank == advance_rank - 1:
                        return True
        
        return False
    
    def _is_pawn_chain(self, board: chess.Board, pawn_sq: int, color: chess.Color) -> bool:
        """Check if a pawn is part of a pawn chain (protected by another pawn)."""
        file = chess.square_file(pawn_sq)
        rank = chess.square_rank(pawn_sq)
        
        friendly_pawns = board.pieces(chess.PAWN, color)
        
        # Check diagonal squares behind the pawn for friendly pawns
        for adj_file in [file - 1, file + 1]:
            if 0 <= adj_file <= 7:
                support_rank = rank - 1 if color == chess.WHITE else rank + 1
                if 0 <= support_rank <= 7:
                    support_sq = chess.square(adj_file, support_rank)
                    if support_sq in friendly_pawns:
                        return True
        
        return False
    
    def _evaluate_king_safety(self, board: chess.Board, is_endgame: bool) -> int:
        """Enhanced king safety evaluation."""
        if is_endgame:
            return 0  # King safety less important in endgame
        
        score = 0
        
        for color in [chess.WHITE, chess.BLACK]:
            multiplier = 1 if color == chess.WHITE else -1
            king_sq = board.king(color)
            
            if king_sq is None:
                continue
            
            king_file = chess.square_file(king_sq)
            king_rank = chess.square_rank(king_sq)
            
            # Penalize king on central files
            if not board.has_castling_rights(color):
                if king_file in [3, 4]:  # d, e files
                    score += multiplier * -40
            
            # Evaluate pawn shield
            shield_score = 0
            for file_offset in [-1, 0, 1]:
                shield_file = king_file + file_offset
                if 0 <= shield_file <= 7:
                    # Look for pawns in front of king
                    for rank_offset in [1, 2]:
                        if color == chess.WHITE:
                            shield_rank = king_rank + rank_offset
                        else:
                            shield_rank = king_rank - rank_offset
                        
                        if 0 <= shield_rank <= 7:
                            shield_sq = chess.square(shield_file, shield_rank)
                            piece = board.piece_at(shield_sq)
                            if piece and piece.piece_type == chess.PAWN and piece.color == color:
                                shield_score += 15 if rank_offset == 1 else 10
                                break
                    else:
                        # No pawn shield on this file
                        shield_score -= 20
            
            score += multiplier * shield_score
            
            # Simplified enemy piece proximity check
            attack_score = 0
            for piece_type in [chess.QUEEN, chess.ROOK]:  # Only check major pieces for performance
                enemy_pieces = board.pieces(piece_type, not color)
                for piece_sq in enemy_pieces:
                    distance = max(abs(chess.square_file(piece_sq) - king_file),
                                 abs(chess.square_rank(piece_sq) - king_rank))
                    if distance <= 2:  # Only very close pieces
                        attack_score -= (3 - distance) * 8
            
            score += multiplier * attack_score
        
        return score
    
    def _evaluate_piece_coordination(self, board: chess.Board) -> int:
        """Evaluate piece coordination and activity."""
        score = 0
        
        for color in [chess.WHITE, chess.BLACK]:
            multiplier = 1 if color == chess.WHITE else -1
            
            # Rook on open/semi-open files
            rooks = board.pieces(chess.ROOK, color)
            for rook_sq in rooks:
                file = chess.square_file(rook_sq)
                
                # Check for pawns on this file
                file_squares = chess.SquareSet(chess.BB_FILES[file])
                all_pawns_on_file = [sq for sq in file_squares if board.piece_at(sq) and board.piece_at(sq).piece_type == chess.PAWN]
                friendly_pawns_on_file = [sq for sq in all_pawns_on_file if board.piece_at(sq).color == color]
                
                if len(all_pawns_on_file) == 0:
                    score += multiplier * 25  # Open file bonus
                elif len(friendly_pawns_on_file) == 0:
                    score += multiplier * 15  # Semi-open file bonus
            
            # Bishop pair bonus
            bishops = board.pieces(chess.BISHOP, color)
            if len(bishops) >= 2:
                score += multiplier * 30
            
            # Knight outposts
            knights = board.pieces(chess.KNIGHT, color)
            for knight_sq in knights:
                if self._is_knight_outpost(board, knight_sq, color):
                    score += multiplier * 20
        
        return score
    
    def _is_knight_outpost(self, board: chess.Board, knight_sq: int, color: chess.Color) -> bool:
        """Check if a knight is on an outpost (strong square not attackable by enemy pawns)."""
        file = chess.square_file(knight_sq)
        rank = chess.square_rank(knight_sq)
        
        # Must be in enemy territory
        if color == chess.WHITE and rank < 4:
            return False
        if color == chess.BLACK and rank > 3:
            return False
        
        enemy_pawns = board.pieces(chess.PAWN, not color)
        
        # Check if enemy pawns can attack this square
        for enemy_sq in enemy_pawns:
            enemy_file = chess.square_file(enemy_sq)
            enemy_rank = chess.square_rank(enemy_sq)
            
            # Enemy pawn can potentially attack this square
            if abs(enemy_file - file) == 1:
                if color == chess.WHITE and enemy_rank < rank:
                    return False
                elif color == chess.BLACK and enemy_rank > rank:
                    return False
        
        return True
    
    def _evaluate_tactical_patterns(self, board: chess.Board) -> int:
        """Evaluate basic tactical patterns."""
        score = 0
        
        # Check for hanging pieces (undefended pieces under attack)
        for color in [chess.WHITE, chess.BLACK]:
            multiplier = 1 if color == chess.WHITE else -1
            
            for piece_type in [chess.QUEEN, chess.ROOK, chess.BISHOP, chess.KNIGHT]:
                pieces = board.pieces(piece_type, color)
                for piece_sq in pieces:
                    if self._is_piece_hanging(board, piece_sq, color):
                        penalty = self.piece_values[piece_type] // 4
                        score += multiplier * -penalty
        
        return score
    
    def _is_piece_hanging(self, board: chess.Board, piece_sq: int, color: chess.Color) -> bool:
        """Check if a piece is hanging (attacked and not defended adequately)."""
        # Check if piece is attacked by enemy
        if not board.is_attacked_by(not color, piece_sq):
            return False
        
        # Check if piece is defended by friendly pieces
        if not board.is_attacked_by(color, piece_sq):
            return True  # Attacked but not defended = hanging
        
        # If both attacked and defended, it's not simply hanging
        # (More sophisticated analysis would require SEE - Static Exchange Evaluation)
        return False
    
    def _evaluate_endgame_factors(self, board: chess.Board) -> int:
        """Evaluate endgame-specific factors."""
        score = 0
        
        # King activity in endgame
        for color in [chess.WHITE, chess.BLACK]:
            multiplier = 1 if color == chess.WHITE else -1
            king_sq = board.king(color)
            
            if king_sq is not None:
                # Centralized king bonus
                king_file = chess.square_file(king_sq)
                king_rank = chess.square_rank(king_sq)
                
                center_distance = max(abs(king_file - 3.5), abs(king_rank - 3.5))
                centralization_bonus = int((4 - center_distance) * 10)
                score += multiplier * centralization_bonus
        
        # Pawn promotion potential
        for color in [chess.WHITE, chess.BLACK]:
            multiplier = 1 if color == chess.WHITE else -1
            pawns = board.pieces(chess.PAWN, color)
            
            for pawn_sq in pawns:
                rank = chess.square_rank(pawn_sq)
                if color == chess.WHITE and rank >= 6:
                    score += multiplier * (rank - 5) * 20
                elif color == chess.BLACK and rank <= 1:
                    score += multiplier * (2 - rank) * 20
        
        return score
        
    def _order_moves(self, board: chess.Board, moves: List[chess.Move], 
                    hash_move: Optional[chess.Move] = None) -> List[chess.Move]:
        """Order moves for better alpha-beta pruning."""
        def move_score(move):
            score = 0
            
            # Hash move gets highest priority
            if hash_move and move == hash_move:
                return 10000
            
            # Captures
            if board.is_capture(move):
                captured_piece = board.piece_at(move.to_square)
                moving_piece = board.piece_at(move.from_square)
                if captured_piece and moving_piece:
                    # MVV-LVA (Most Valuable Victim - Least Valuable Attacker)
                    score += (self.piece_values[captured_piece.piece_type] - 
                             self.piece_values[moving_piece.piece_type] // 10)
            
            # Promotions
            if move.promotion:
                score += self.piece_values[move.promotion]
            
            # Checks
            board.push(move)
            if board.is_check():
                score += 50
            board.pop()
            
            # Castle
            if board.is_castling(move):
                score += 30
                
            return score
        
        return sorted(moves, key=move_score, reverse=True)
        
    def _quiescence_search(self, board: chess.Board, alpha: int, beta: int, depth: int) -> int:
        """
        Quiescence search to handle tactical sequences, consistent with minimax.
        Only considers captures and checks to avoid horizon effect.
        """
        self.quiescence_nodes += 1
    
        # Use the static evaluation as a baseline
        evaluation = self.evaluate_board(board)
    
        if depth <= 0:
            return evaluation
    
        # --- White's turn (Maximizer) ---
        if board.turn == chess.WHITE:
            if evaluation >= beta:
                return beta  # Fail-high
            alpha = max(alpha, evaluation)
            
            moves = [move for move in board.legal_moves if board.is_capture(move) or board.gives_check(move)]
            moves = self._order_moves(board, moves)
    
            for move in moves:
                board.push(move)
                score = self._quiescence_search(board, alpha, beta, depth - 1)
                board.pop()
                
                alpha = max(alpha, score)
                if alpha >= beta:
                    return beta  # Pruning
            return alpha
            
        # --- Black's turn (Minimizer) ---
        else:
            if evaluation <= alpha:
                return alpha  # Fail-low
            beta = min(beta, evaluation)
    
            moves = [move for move in board.legal_moves if board.is_capture(move) or board.gives_check(move)]
            moves = self._order_moves(board, moves)
    
            for move in moves:
                board.push(move)
                score = self._quiescence_search(board, alpha, beta, depth - 1)
                board.pop()
    
                beta = min(beta, score)
                if beta <= alpha:
                    return alpha  # Pruning
            return beta
        
    def _store_transposition(self, board: chess.Board, depth: int, score: int, 
                           flag: int, best_move: Optional[chess.Move]):
        """Store position in transposition table."""
        if not self.use_transposition_table:
            return
            
        # Use a simple hash of the FEN as key (fallback method)
        key = chess.polyglot.zobrist_hash(board)
        
        # Clear table if it gets too large
        if len(self.transposition_table) > self.max_table_size:
            self.transposition_table.clear()
            
        self.transposition_table[key] = (depth, score, flag, best_move)
        
    def _probe_transposition(self, board: chess.Board, depth: int, alpha: int, beta: int) -> Tuple[Optional[int], Optional[chess.Move]]:
        """Probe transposition table."""
        if not self.use_transposition_table:
            return None, None
            
        # Use a simple hash of the FEN as key (fallback method)
        key = chess.polyglot.zobrist_hash(board)
        
        if key in self.transposition_table:
            stored_depth, stored_score, flag, best_move = self.transposition_table[key]
            
            if stored_depth >= depth:
                self.transposition_hits += 1
                
                if flag == 0:  # Exact score
                    return stored_score, best_move
                elif flag == 1 and stored_score >= beta:  # Lower bound
                    return stored_score, best_move
                elif flag == 2 and stored_score <= alpha:  # Upper bound
                    return stored_score, best_move
                    
            return None, best_move  # Return best move even if depth doesn't match
            
        return None, None
        
    def _minimax(self, board: chess.Board, depth: int, alpha: int, beta: int, 
                maximizing_player: bool) -> Tuple[int, Optional[chess.Move]]:
        """
        Enhanced minimax with alpha-beta pruning, transposition tables, and quiescence search.
        """
        self.nodes_evaluated += 1
        
        # Probe transposition table
        tt_score, tt_move = self._probe_transposition(board, depth, alpha, beta)
        if tt_score is not None:
            return tt_score, tt_move
            
        if depth == 0 or board.is_game_over():
            if self.use_quiescence_search and depth == 0 and not board.is_game_over():
                score = self._quiescence_search(board, alpha, beta, self.max_quiescence_depth)
            else:
                score = self.evaluate_board(board)
            return score, None
        
        moves = list(board.legal_moves)
        moves = self._order_moves(board, moves, tt_move)
        
        best_move = None
        original_alpha = alpha
        
        if maximizing_player:
            max_eval = float('-inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self._minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            # Store in transposition table
            if max_eval <= original_alpha:
                flag = 2  # Upper bound
            elif max_eval >= beta:
                flag = 1  # Lower bound
            else:
                flag = 0  # Exact
            self._store_transposition(board, depth, max_eval, flag, best_move)
            
            return max_eval, best_move
        else:
            min_eval = float('inf')
            for move in moves:
                board.push(move)
                eval_score, _ = self._minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            # Store in transposition table
            if min_eval <= original_alpha:
                flag = 2  # Upper bound
            elif min_eval >= beta:
                flag = 1  # Lower bound
            else:
                flag = 0  # Exact
            self._store_transposition(board, depth, min_eval, flag, best_move)
            
            return min_eval, best_move
            
    def _iterative_deepening(self, board: chess.Board, max_time: float = 10.0) -> Tuple[Optional[chess.Move], int]:
        """
        Iterative deepening search with time management.
        Returns both the best move and its evaluation score.
        """
        start_time = time.time()
        best_move = None
        best_score = 0
        
        for current_depth in range(1, self.depth + 1):
            if time.time() - start_time > max_time:
                break
                
            maximizing = board.turn == chess.WHITE
            try:
                score, move = self._minimax(board, current_depth, float('-inf'), float('inf'), maximizing)
                if move:
                    best_move = move
                    best_score = score
            except:
                break  # Time's up or other issue
                
        return best_move, best_score
        
    def get_best_move(self, board: chess.Board) -> Optional[chess.Move]:
        """
        Get the best move for the current position.
        
        Args:
            board: Current board position
            
        Returns:
            Best move according to the bot's evaluation
        """
        move, _ = self.get_best_move_with_score(board)
        return move
    
    def get_best_move_with_score(self, board: chess.Board) -> Tuple[Optional[chess.Move], int]:
        """
        Get the best move and its evaluation score for the current position.
        
        Args:
            board: Current board position
            
        Returns:
            Tuple of (best_move, evaluation_score)
        """
        # Reset statistics
        self.nodes_evaluated = 0
        self.transposition_hits = 0
        self.quiescence_nodes = 0
        
        start_time = time.time()
        best_score = 0
        depth_used = self.depth
        
        # Check opening book first (if enabled)
        if self.use_opening_book:
            fen = board.fen()
            if fen in self.opening_moves:
                move_uci = random.choice(self.opening_moves[fen])
                try:
                    move = chess.Move.from_uci(move_uci)
                    if move in board.legal_moves:
                        print(f"{self.name} played from opening book: {move}")
                        # For opening book moves, return static evaluation
                        best_score = self.evaluate_board(board)
                        elapsed_time = time.time() - start_time
                        
                        # Record move quality
                        self._record_move_quality(board, move, best_score, best_score, 0, elapsed_time, "Opening Book")
                        
                        return move, best_score
                except:
                    pass
        
        # Use iterative deepening if enabled
        if self.use_iterative_deepening:
            best_move, best_score = self._iterative_deepening(board)
        else:
            maximizing = board.turn == chess.WHITE
            best_score, best_move = self._minimax(board, self.depth, float('-inf'), float('inf'), maximizing)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # Fallback to random move
        if best_move is None:
            legal_moves = list(board.legal_moves)
            if legal_moves:
                best_move = random.choice(legal_moves)
                best_score = self.evaluate_board(board)
                depth_used = 0
        
        # Record move quality for analysis
        if best_move:
            static_score = self.evaluate_board(board)
            self._record_move_quality(board, best_move, best_score, static_score, depth_used, elapsed_time)
        
        # Print statistics
        print(f"{self.name} statistics:")
        print(f"  - Time: {elapsed_time:.2f}s")
        print(f"  - Nodes evaluated: {self.nodes_evaluated}")
        print(f"  - Quiescence nodes: {self.quiescence_nodes}")
        print(f"  - Search evaluation: {best_score}")
        if self.use_transposition_table:
            print(f"  - Transposition hits: {self.transposition_hits}")
            print(f"  - Table size: {len(self.transposition_table)}")
        
        return best_move, best_score
    
    def _record_move_quality(self, board: chess.Board, move: chess.Move, search_score: int, 
                           static_score: int, depth_used: int, time_taken: float, move_type: str = "Search"):
        """
        Record move quality information for debugging and analysis.
        
        Args:
            board: Current board position
            move: The move that was chosen
            search_score: Score from the search algorithm
            static_score: Static evaluation of the position
            depth_used: Search depth used
            time_taken: Time taken to find the move
            move_type: Type of move (Search, Opening Book, etc.)
        """
        try:
            move_san = board.san(move)
            score_difference = search_score - static_score
            
            # Classify move quality based on score difference
            if abs(score_difference) < 50:
                quality = "Normal"
            elif score_difference > 200:
                quality = "Excellent"
            elif score_difference > 100:
                quality = "Good"
            elif score_difference < -200:
                quality = "Blunder"
            elif score_difference < -100:
                quality = "Poor"
            else:
                quality = "Questionable"
            
            move_info = {
                'move_number': len(self.move_quality_history) + 1,
                'move_san': move_san,
                'move_uci': move.uci(),
                'search_score': search_score,
                'static_score': static_score,
                'score_difference': score_difference,
                'depth_used': depth_used,
                'time_taken': time_taken,
                'nodes_evaluated': self.nodes_evaluated,
                'move_type': move_type,
                'quality': quality,
                'position_fen': board.fen()
            }
            
            self.move_quality_history.append(move_info)
            
            # Keep only last 50 moves to prevent memory issues
            if len(self.move_quality_history) > 50:
                self.move_quality_history.pop(0)
                
        except Exception as e:
            print(f"Error recording move quality: {e}")
    
    def get_move_quality_history(self) -> List[Dict]:
        """Get the move quality history for analysis."""
        return self.move_quality_history.copy()
    
    def clear_move_quality_history(self):
        """Clear the move quality history."""
        self.move_quality_history.clear()
        
    def set_depth(self, depth: int):
        """Set the search depth."""
        self.depth = depth
        print(f"{self.name} depth set to {depth}")
        
    def set_features(self, iterative_deepening: bool = None, 
                    quiescence_search: bool = None, 
                    transposition_table: bool = None):
        """Enable/disable bot features."""
        if iterative_deepening is not None:
            self.use_iterative_deepening = iterative_deepening
        if quiescence_search is not None:
            self.use_quiescence_search = quiescence_search
        if transposition_table is not None:
            self.use_transposition_table = transposition_table
            if not transposition_table:
                self.transposition_table.clear()
                
        print(f"{self.name} features updated:")
        print(f"  - Iterative deepening: {self.use_iterative_deepening}")
        print(f"  - Quiescence search: {self.use_quiescence_search}")
        print(f"  - Transposition table: {self.use_transposition_table}")
        
    def get_evaluation(self, board: chess.Board) -> int:
        """Get the evaluation of the current position."""
        return self.evaluate_board(board)
        
    def clear_transposition_table(self):
        """Clear the transposition table."""
        self.transposition_table.clear()
        print(f"{self.name} transposition table cleared")


# Preset configurations for different difficulty levels
class EasyBot(UnifiedChessBot):
    """Easy difficulty bot - fast and basic."""
    def __init__(self):
        super().__init__(
            depth=2, 
            name="EasyBot",
            use_iterative_deepening=False,
            use_quiescence_search=False,
            use_transposition_table=False
        )

class MediumBot(UnifiedChessBot):
    """Medium difficulty bot - balanced."""
    def __init__(self):
        super().__init__(
            depth=3,
            name="MediumBot", 
            use_iterative_deepening=True,
            use_quiescence_search=True,
            use_transposition_table=True
        )

class HardBot(UnifiedChessBot):
    """Hard difficulty bot - strong play."""
    def __init__(self):
        super().__init__(
            depth=4,
            name="HardBot",
            use_iterative_deepening=True,
            use_quiescence_search=True,
            use_transposition_table=True
        )

class ExpertBot(UnifiedChessBot):
    """Expert difficulty bot - very strong play."""
    def __init__(self):
        super().__init__(
            depth=5,
            name="ExpertBot",
            use_iterative_deepening=True,
            use_quiescence_search=True,
            use_transposition_table=True,
            max_quiescence_depth=8
        )