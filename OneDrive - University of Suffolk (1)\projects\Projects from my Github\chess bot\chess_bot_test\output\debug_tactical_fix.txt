PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python debug_tactical_fix.py  
Position:
r . . q k b . r
p p p . . p p p
. . . p b . . .
. . . . p . . .
. . . n . . . .
. . . K . . . .
P P P . P n P P
R N B Q . B N R
FEN: r2qkb1r/ppp2ppp/3pb3/4p3/3n4/3K4/PPP1PnPP/RNBQ1BNR w kq - 0 9
Turn: White
In check: True
Checkmate: False
Stalemate: False

CURRENT ANALYSIS:
==================================================
WHITE THREATS:
------------------------------
• Q on d1 is hanging
  Type: hanging_piece
  Value: 900

• R on h1 is hanging
  Type: hanging_piece
  Value: 500

• K on d3 is hanging
  Type: hanging_piece
  Value: 3900

• K can fork 2 pieces
  Type: fork
  Value: 640

BLACK THREATS:
------------------------------
• N can capture undefended Q
  Type: undefended_attack
  Value: 900

• N can capture undefended R
  Type: undefended_attack
  Value: 500

• Q is overloaded defending 3 pieces
  Type: overloaded_defender
  Value: 4200

• K is overloaded defending 2 pieces
  Type: overloaded_defender
  Value: 200

PIECE ANALYSIS:
------------------------------
White king on d3
White king in check: True
King attackers: ['f2']
  N on f2
Piece on f2: N
f2 attacks: ['d1', 'h1', 'd3', 'h3', 'e4', 'g4']
  Can attack Q on d1
  Can attack R on h1
  Can attack K on d3