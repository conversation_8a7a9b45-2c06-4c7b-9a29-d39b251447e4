import chess
import chess.engine
import random
import math
from typing import Optional, Tuple, List, Dict
import time

class MCTSNode:
    """
    Node for Monte Carlo Tree Search with enhanced evaluation.
    """
    
    def __init__(self, board: chess.Board, move: Optional[chess.Move] = None, parent: Optional['MCTSNode'] = None):
        self.board = board.copy()
        self.move = move
        self.parent = parent
        self.children: List['MCTSNode'] = []
        self.visits = 0
        self.wins = 0.0
        self.untried_moves = list(board.legal_moves)
        self.is_terminal = board.is_game_over()
        # Add move ordering for better tree search
        self._order_moves()
    
    def _order_moves(self):
        """Order moves by likely importance (captures, checks, etc.)"""
        def move_priority(move):
            score = 0
            # Prioritize captures
            if self.board.is_capture(move):
                captured_piece = self.board.piece_at(move.to_square)
                if captured_piece:
                    score += 1000 + captured_piece.piece_type
            # Prioritize checks
            board_copy = self.board.copy()
            board_copy.push(move)
            if board_copy.is_check():
                score += 500
            # Prioritize center moves
            if move.to_square in [chess.E4, chess.E5, chess.D4, chess.D5]:
                score += 100
            return score
        
        self.untried_moves.sort(key=move_priority, reverse=True)
    
    def is_fully_expanded(self) -> bool:
        """Check if all possible moves have been tried."""
        return len(self.untried_moves) == 0
    
    def best_child(self, c_param: float = 1.4) -> 'MCTSNode':
        """Select the best child using UCB1 formula."""
        if not self.children:
            return None
        
        choices_weights = []
        for child in self.children:
            if child.visits == 0:
                # Unvisited nodes get maximum priority
                choices_weights.append(float('inf'))
            else:
                # UCB1 formula with exploration parameter
                exploitation = child.wins / child.visits
                exploration = c_param * math.sqrt(2 * math.log(self.visits) / child.visits)
                choices_weights.append(exploitation + exploration)
        
        return self.children[choices_weights.index(max(choices_weights))]
    
    def expand(self) -> 'MCTSNode':
        """Expand the node by adding a new child."""
        if not self.untried_moves:
            return self
        
        move = self.untried_moves.pop()
        new_board = self.board.copy()
        new_board.push(move)
        child_node = MCTSNode(new_board, move, self)
        self.children.append(child_node)
        return child_node
    
    def rollout(self) -> float:
        """Perform a guided rollout with some heuristics."""
        current_board = self.board.copy()
        
        # Limit rollout depth to avoid infinite games
        max_rollout_depth = 30
        depth = 0
        
        while not current_board.is_game_over() and depth < max_rollout_depth:
            legal_moves = list(current_board.legal_moves)
            if not legal_moves:
                break
            
            # Use simple heuristics for rollout move selection
            move = self._select_rollout_move(current_board, legal_moves)
            current_board.push(move)
            depth += 1
        
        return self._evaluate_terminal_position(current_board)
    
    def _select_rollout_move(self, board: chess.Board, legal_moves: List[chess.Move]) -> chess.Move:
        """Select move for rollout using simple heuristics."""
        # Prioritize captures and checks
        good_moves = []
        for move in legal_moves:
            if board.is_capture(move):
                good_moves.append(move)
            else:
                # Check if move gives check
                board_copy = board.copy()
                board_copy.push(move)
                if board_copy.is_check():
                    good_moves.append(move)
        
        if good_moves:
            return random.choice(good_moves)
        else:
            return random.choice(legal_moves)
    
    def _evaluate_terminal_position(self, board: chess.Board) -> float:
        """Evaluate the terminal position of a rollout."""
        if board.is_checkmate():
            # If it's checkmate, the player to move lost
            return 0.0 if board.turn == self.board.turn else 1.0
        elif board.is_stalemate() or board.is_insufficient_material():
            return 0.5  # Draw
        elif board.is_fivefold_repetition() or board.is_seventyfive_moves():
            return 0.5  # Draw by repetition/50-move rule
        else:
            # For incomplete rollouts, use simple material evaluation
            return self._simple_material_eval(board)
    
    def _simple_material_eval(self, board: chess.Board) -> float:
        """Simple material-based evaluation for rollout endpoints."""
        piece_values = {
            chess.PAWN: 1, chess.KNIGHT: 3, chess.BISHOP: 3,
            chess.ROOK: 5, chess.QUEEN: 9, chess.KING: 0
        }
        
        white_material = 0
        black_material = 0
        
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = piece_values[piece.piece_type]
                if piece.color == chess.WHITE:
                    white_material += value
                else:
                    black_material += value
        
        # Normalize to [0, 1] range
        total_material = white_material + black_material
        if total_material == 0:
            return 0.5
        
        white_advantage = (white_material - black_material) / (2 * total_material)
        
        # Return from current player's perspective
        if self.board.turn == chess.WHITE:
            return 0.5 + white_advantage
        else:
            return 0.5 - white_advantage
    
    def backpropagate(self, result: float):
        """Backpropagate the result up the tree."""
        self.visits += 1
        self.wins += result
        if self.parent:
            # Flip the result for the parent (opponent's perspective)
            self.parent.backpropagate(1.0 - result)

class ChessBot:
    """
    Enhanced chess bot combining minimax with alpha-beta pruning and MCTS.
    """
    
    def __init__(self, depth: int = 4, name: str = "HybridChessBot", 
                 mcts_simulations: int = 2000, top_moves_count: int = 4,
                 use_hybrid: bool = True):
        """
        Initialize the chess bot with enhanced parameters.
        
        Args:
            depth: Search depth for minimax algorithm
            name: Name of the bot
            mcts_simulations: Number of MCTS simulations to run
            top_moves_count: Number of top moves from minimax to explore with MCTS
            use_hybrid: Whether to use hybrid approach or just minimax
        """
        self.depth = depth
        self.name = name
        self.nodes_evaluated = 0
        self.mcts_simulations = mcts_simulations
        self.top_moves_count = top_moves_count
        self.use_hybrid = use_hybrid
        
        # Enhanced piece values
        self.piece_values = {
            chess.PAWN: 100,
            chess.KNIGHT: 320,
            chess.BISHOP: 330,
            chess.ROOK: 500,
            chess.QUEEN: 900,
            chess.KING: 20000
        }
        
        # Enhanced position tables
        self.pawn_table = [
            0,  0,  0,  0,  0,  0,  0,  0,
            50, 50, 50, 50, 50, 50, 50, 50,
            10, 10, 20, 30, 30, 20, 10, 10,
            5,  5, 10, 27, 27, 10,  5,  5,
            0,  0,  0, 25, 25,  0,  0,  0,
            5, -5,-10,  0,  0,-10, -5,  5,
            5, 10, 10,-25,-25, 10, 10,  5,
            0,  0,  0,  0,  0,  0,  0,  0
        ]
        
        self.knight_table = [
            -50,-40,-30,-30,-30,-30,-40,-50,
            -40,-20,  0,  0,  0,  0,-20,-40,
            -30,  0, 10, 15, 15, 10,  0,-30,
            -30,  5, 15, 20, 20, 15,  5,-30,
            -30,  0, 15, 20, 20, 15,  0,-30,
            -30,  5, 10, 15, 15, 10,  5,-30,
            -40,-20,  0,  5,  5,  0,-20,-40,
            -50,-40,-30,-30,-30,-30,-40,-50
        ]
        
        self.bishop_table = [
            -20,-10,-10,-10,-10,-10,-10,-20,
            -10,  0,  0,  0,  0,  0,  0,-10,
            -10,  0,  5, 10, 10,  5,  0,-10,
            -10,  5,  5, 10, 10,  5,  5,-10,
            -10,  0, 10, 10, 10, 10,  0,-10,
            -10, 10, 10, 10, 10, 10, 10,-10,
            -10,  5,  0,  0,  0,  0,  5,-10,
            -20,-10,-10,-10,-10,-10,-10,-20
        ]
        
        # Transposition table for memoization
        self.transposition_table = {}
        self.max_tt_size = 100000
    
    def evaluate_board(self, board: chess.Board) -> int:
        """
        Enhanced board evaluation function.
        
        Args:
            board: Chess board to evaluate
            
        Returns:
            Evaluation score (positive for white advantage, negative for black)
        """
        # Check for game-ending positions
        if board.is_checkmate():
            return -20000 if board.turn else 20000
        
        if board.is_stalemate() or board.is_insufficient_material():
            return 0
        
        score = 0
        
        # Material and positional evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                value = self.piece_values[piece.piece_type]
                
                # Add positional bonuses
                if piece.piece_type == chess.PAWN:
                    table = self.pawn_table
                elif piece.piece_type == chess.KNIGHT:
                    table = self.knight_table
                elif piece.piece_type == chess.BISHOP:
                    table = self.bishop_table
                else:
                    table = None
                
                if table:
                    if piece.color == chess.WHITE:
                        value += table[square]
                    else:
                        value += table[chess.square_mirror(square)]
                
                if piece.color == chess.WHITE:
                    score += value
                else:
                    score -= value
        
        # Mobility evaluation
        current_player_mobility = len(list(board.legal_moves))
        
        # Switch turns to evaluate opponent mobility
        board.push(chess.Move.null())
        try:
            opponent_mobility = len(list(board.legal_moves))
        except:
            opponent_mobility = 0
        board.pop()
        
        mobility_bonus = (current_player_mobility - opponent_mobility) * 3
        if board.turn == chess.WHITE:
            score += mobility_bonus
        else:
            score -= mobility_bonus
        
        # King safety evaluation
        score += self._evaluate_king_safety(board, chess.WHITE)
        score -= self._evaluate_king_safety(board, chess.BLACK)
        
        # Center control
        center_squares = [chess.E4, chess.E5, chess.D4, chess.D5]
        for square in center_squares:
            attackers_white = len(board.attackers(chess.WHITE, square))
            attackers_black = len(board.attackers(chess.BLACK, square))
            score += (attackers_white - attackers_black) * 10
        
        return score
    
    def _evaluate_king_safety(self, board: chess.Board, color: chess.Color) -> int:
        """Evaluate king safety for the given color."""
        king_square = board.king(color)
        if king_square is None:
            return 0
        
        safety_score = 0
        
        # Penalty for exposed king
        attackers = board.attackers(not color, king_square)
        safety_score -= len(attackers) * 20
        
        # Bonus for castling rights
        if color == chess.WHITE:
            if board.has_kingside_castling_rights(chess.WHITE):
                safety_score += 30
            if board.has_queenside_castling_rights(chess.WHITE):
                safety_score += 20
        else:
            if board.has_kingside_castling_rights(chess.BLACK):
                safety_score += 30
            if board.has_queenside_castling_rights(chess.BLACK):
                safety_score += 20
        
        return safety_score
    
    def _order_moves(self, board: chess.Board, moves: List[chess.Move]) -> List[chess.Move]:
        """Order moves for better alpha-beta pruning."""
        def move_value(move):
            score = 0
            
            # Prioritize captures
            if board.is_capture(move):
                captured_piece = board.piece_at(move.to_square)
                moving_piece = board.piece_at(move.from_square)
                if captured_piece and moving_piece:
                    # MVV-LVA (Most Valuable Victim - Least Valuable Attacker)
                    score += 1000 + captured_piece.piece_type * 100 - moving_piece.piece_type
            
            # Prioritize checks
            board_copy = board.copy()
            board_copy.push(move)
            if board_copy.is_check():
                score += 500
            
            # Prioritize promotions
            if move.promotion:
                score += 800
            
            return score
        
        return sorted(moves, key=move_value, reverse=True)
    
    def minimax(self, board: chess.Board, depth: int, alpha: int, beta: int, 
                maximizing_player: bool) -> Tuple[int, Optional[chess.Move]]:
        """
        Enhanced minimax algorithm with alpha-beta pruning and move ordering.
        """
        self.nodes_evaluated += 1
        
        # Check transposition table
        board_hash = chess.polyglot.zobrist_hash(board)
        if board_hash in self.transposition_table:
            stored_depth, stored_value, stored_move = self.transposition_table[board_hash]
            if stored_depth >= depth:
                return stored_value, stored_move
        
        if depth == 0 or board.is_game_over():
            eval_score = self.evaluate_board(board)
            return eval_score, None
        
        legal_moves = list(board.legal_moves)
        ordered_moves = self._order_moves(board, legal_moves)
        
        best_move = None
        
        if maximizing_player:
            max_eval = float('-inf')
            for move in ordered_moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, False)
                board.pop()
                
                if eval_score > max_eval:
                    max_eval = eval_score
                    best_move = move
                
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            # Store in transposition table
            if len(self.transposition_table) < self.max_tt_size:
                self.transposition_table[board_hash] = (depth, max_eval, best_move)
            
            return max_eval, best_move
        else:
            min_eval = float('inf')
            for move in ordered_moves:
                board.push(move)
                eval_score, _ = self.minimax(board, depth - 1, alpha, beta, True)
                board.pop()
                
                if eval_score < min_eval:
                    min_eval = eval_score
                    best_move = move
                
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break  # Alpha-beta pruning
            
            # Store in transposition table
            if len(self.transposition_table) < self.max_tt_size:
                self.transposition_table[board_hash] = (depth, min_eval, best_move)
            
            return min_eval, best_move
    
    def mcts_search(self, board: chess.Board, simulations: int) -> chess.Move:
        """
        Enhanced Monte Carlo Tree Search.
        """
        root = MCTSNode(board)
        
        for _ in range(simulations):
            # Selection
            node = root
            while not node.is_terminal and node.is_fully_expanded():
                node = node.best_child()
            
            # Expansion
            if not node.is_terminal and not node.is_fully_expanded():
                node = node.expand()
            
            # Simulation (Rollout)
            result = node.rollout()
            
            # Backpropagation
            node.backpropagate(result)
        
        # Return the move with the highest visit count
        if root.children:
            best_child = max(root.children, key=lambda c: c.visits)
            return best_child.move
        else:
            # Fallback to random move
            legal_moves = list(board.legal_moves)
            return random.choice(legal_moves) if legal_moves else None
    
    def get_top_moves_minimax(self, board: chess.Board, count: int) -> List[Tuple[chess.Move, int]]:
        """
        Get the top moves according to minimax evaluation with better pruning.
        """
        moves_scores = []
        maximizing = board.turn == chess.WHITE
        
        legal_moves = list(board.legal_moves)
        ordered_moves = self._order_moves(board, legal_moves)
        
        for move in ordered_moves:
            board.push(move)
            score, _ = self.minimax(board, self.depth - 1, float('-inf'), float('inf'), not maximizing)
            board.pop()
            moves_scores.append((move, score))
        
        # Sort by score (descending for white, ascending for black)
        moves_scores.sort(key=lambda x: x[1], reverse=maximizing)
        
        return moves_scores[:count]
    
    def mcts_on_candidates(self, board: chess.Board, candidate_moves: List[chess.Move]) -> chess.Move:
        """
        Run enhanced MCTS only on the candidate moves.
        """
        if not candidate_moves:
            return None
        
        move_scores = {}
        simulations_per_move = max(100, self.mcts_simulations // len(candidate_moves))
        
        for move in candidate_moves:
            # Create a board with the candidate move played
            test_board = board.copy()
            test_board.push(move)
            
            # Run MCTS from this position
            root = MCTSNode(test_board)
            
            for _ in range(simulations_per_move):
                # Selection
                node = root
                while not node.is_terminal and node.is_fully_expanded():
                    node = node.best_child()
                
                # Expansion
                if not node.is_terminal and not node.is_fully_expanded():
                    node = node.expand()
                
                # Simulation (Rollout)
                result = node.rollout()
                
                # Backpropagation
                node.backpropagate(result)
            
            # Score is the win rate from the perspective of the current player
            if root.visits > 0:
                move_scores[move] = 1.0 - (root.wins / root.visits)
            else:
                move_scores[move] = 0.5
        
        # Return the move with the highest score
        best_move = max(move_scores.keys(), key=lambda m: move_scores[m])
        return best_move
    
    def get_best_move(self, board: chess.Board) -> chess.Move:
        """
        Get the best move using the enhanced hybrid approach.
        """
        self.nodes_evaluated = 0
        start_time = time.time()
        
        # Clear old transposition table entries periodically
        if len(self.transposition_table) > self.max_tt_size * 0.8:
            self.transposition_table.clear()
        
        if not self.use_hybrid:
            # Use only minimax with alpha-beta pruning
            maximizing = board.turn == chess.WHITE
            _, best_move = self.minimax(board, self.depth, float('-inf'), float('inf'), maximizing)
        else:
            # Enhanced hybrid approach
            print(f"{self.name} using enhanced hybrid approach")
            
            # Step 1: Use minimax to get top candidate moves
            print(f"Finding top {self.top_moves_count} moves with minimax (depth {self.depth})...")
            top_moves = self.get_top_moves_minimax(board, self.top_moves_count)
            
            if not top_moves:
                # Fallback to random move if no moves found
                legal_moves = list(board.legal_moves)
                best_move = random.choice(legal_moves) if legal_moves else None
            else:
                candidate_moves = [move for move, score in top_moves]
                print(f"Top moves: {[(str(move), score) for move, score in top_moves]}")
                
                # Step 2: Use MCTS to explore these candidate moves
                print(f"Exploring candidates with {self.mcts_simulations} MCTS simulations...")
                best_move = self.mcts_on_candidates(board, candidate_moves)
        
        end_time = time.time()
        
        if best_move is None:
            # Final fallback
            legal_moves = list(board.legal_moves)
            best_move = random.choice(legal_moves) if legal_moves else None
        
        print(f"Evaluated {self.nodes_evaluated} positions in {end_time - start_time:.2f}s")
        print(f"Selected move: {best_move}")
        print(f"Transposition table size: {len(self.transposition_table)}")
        
        return best_move
    
    def set_depth(self, depth: int):
        """Set the search depth for the bot."""
        self.depth = depth
        print(f"{self.name} search depth set to {depth}")
    
    def set_mcts_simulations(self, simulations: int):
        """Set the number of MCTS simulations."""
        self.mcts_simulations = simulations
        print(f"{self.name} MCTS simulations set to {simulations}")
    
    def set_top_moves_count(self, count: int):
        """Set the number of top moves to explore with MCTS."""
        self.top_moves_count = count
        print(f"{self.name} top moves count set to {count}")
    
    def set_hybrid_mode(self, use_hybrid: bool):
        """Enable or disable hybrid mode."""
        self.use_hybrid = use_hybrid
        mode = "hybrid (Minimax + MCTS)" if use_hybrid else "minimax only"
        print(f"{self.name} mode set to {mode}")
    
    def clear_transposition_table(self):
        """Clear the transposition table."""
        self.transposition_table.clear()
        print(f"{self.name} transposition table cleared")
    
    def get_config(self) -> Dict:
        """Get current bot configuration."""
        return {
            "name": self.name,
            "depth": self.depth,
            "mcts_simulations": self.mcts_simulations,
            "top_moves_count": self.top_moves_count,
            "use_hybrid": self.use_hybrid,
            "transposition_table_size": len(self.transposition_table)
        }

# Example usage and testing
if __name__ == "__main__":
    # Create the enhanced bot
    bot = ChessBot(depth=4, mcts_simulations=2000, top_moves_count=4, use_hybrid=True)
    
    # Test on a sample position
    board = chess.Board()
    print("Starting position:")
    print(board)
    
    # Make a few moves to get to an interesting position
    test_moves = ["e2e4", "e7e5", "g1f3", "b8c6"]
    for move_str in test_moves:
        move = chess.Move.from_uci(move_str)
        board.push(move)
    
    print("\nTest position:")
    print(board)
    
    # Get the bot's best move
    best_move = bot.get_best_move(board)
    print(f"\nBot's best move: {best_move}")
    
    # Show configuration
    print(f"\nBot configuration: {bot.get_config()}")