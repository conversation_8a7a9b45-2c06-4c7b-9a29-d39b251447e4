PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_tracker.py
Disabling undo button: No moves in stack
Enabling undo button: human_color=True, moves=1, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 814 positions in 0.14 seconds
Enabling undo button: human_color=True, moves=2, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=3, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 901 positions in 0.26 seconds
Enabling undo button: human_color=True, moves=4, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=5, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1371 positions in 0.27 seconds
CAPTURE: Black captured Queen (Q)
White captured pieces: []
Black captured pieces: ['Q']
Enabling undo button: human_color=True, moves=6, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=6, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=7, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 699 positions in 0.15 seconds
CAPTURE: Black captured Pawn (P)
White captured pieces: []
Black captured pieces: ['Q', 'P']
Enabling undo button: human_color=True, moves=8, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=8, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=9, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 624 positions in 0.13 seconds
CAPTURE: Black captured Rook (R)
White captured pieces: []
Black captured pieces: ['Q', 'P', 'R']
Enabling undo button: human_color=True, moves=10, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=10, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=11, turn=False
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 720 positions in 0.13 seconds
Board interaction disabled
Enabling undo button: human_color=True, moves=12, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=12, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=13, turn=False
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 615 positions in 0.12 seconds
Board interaction disabled
Enabling undo button: human_color=True, moves=14, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=14, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=15, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 599 positions in 0.13 seconds
CAPTURE: Black captured Pawn (P)
White captured pieces: []
Black captured pieces: ['Q', 'P', 'R', 'P']
Enabling undo button: human_color=True, moves=16, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=16, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=17, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 720 positions in 0.14 seconds
Enabling undo button: human_color=True, moves=18, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=18, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=16, turn=True
UNDO CAPTURE: Removed P from Black's captured pieces
Enabling undo button: human_color=True, moves=14, turn=True
Enabling undo button: human_color=True, moves=12, turn=True
Enabling undo button: human_color=True, moves=10, turn=True
UNDO CAPTURE: Removed R from Black's captured pieces
Enabling undo button: human_color=True, moves=8, turn=True
UNDO CAPTURE: Removed P from Black's captured pieces
Enabling undo button: human_color=True, moves=6, turn=True
CAPTURE: White captured Knight (n)
White captured pieces: ['n']
Black captured pieces: ['Q']
Enabling undo button: human_color=True, moves=7, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 626 positions in 0.17 seconds
Enabling undo button: human_color=True, moves=8, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=8, turn=True
Board interaction re-enabled
Colors switched: human_color=False, board.turn=True
Enabling undo button: human_color=False, moves=8, turn=True
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1254 positions in 0.24 seconds
Enabling undo button: human_color=False, moves=9, turn=False
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=9, turn=False
Board interaction re-enabled
Enabling undo button: human_color=False, moves=9, turn=False
CAPTURE: Black captured Pawn (P)
White captured pieces: ['n']
Black captured pieces: ['Q', 'P']
Enabling undo button: human_color=False, moves=10, turn=True
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 895 positions in 0.17 seconds
CAPTURE: White captured Knight (n)
White captured pieces: ['n', 'n']
Black captured pieces: ['Q', 'P']
Enabling undo button: human_color=False, moves=11, turn=False
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=11, turn=False
Board interaction re-enabled
UNDO CAPTURE: Removed n from White's captured pieces
UNDO CAPTURE: Removed P from Black's captured pieces
Enabling undo button: human_color=False, moves=9, turn=False
CAPTURE: Black captured Pawn (P)
White captured pieces: ['n']
Black captured pieces: ['Q', 'P']
Enabling undo button: human_color=False, moves=10, turn=True
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 895 positions in 0.15 seconds
CAPTURE: White captured Knight (n)
White captured pieces: ['n', 'n']
Black captured pieces: ['Q', 'P']
Enabling undo button: human_color=False, moves=11, turn=False
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=11, turn=False
Board interaction re-enabled
Colors switched: human_color=True, board.turn=False
Enabling undo button: human_color=True, moves=11, turn=False
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1296 positions in 0.38 seconds
Enabling undo button: human_color=True, moves=12, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=12, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=12, turn=True
Enabling undo button: human_color=True, moves=13, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 725 positions in 0.14 seconds
CAPTURE: Black captured Knight (N)
White captured pieces: ['n', 'n']
Black captured pieces: ['Q', 'P', 'N']
Enabling undo button: human_color=True, moves=14, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=14, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=15, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1136 positions in 0.30 seconds
Enabling undo button: human_color=True, moves=16, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=16, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=17, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2515 positions in 0.50 seconds
Enabling undo button: human_color=True, moves=18, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=18, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=19, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 2002 positions in 0.39 seconds
Enabling undo button: human_color=True, moves=20, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=20, turn=True
Board interaction re-enabled
CAPTURE: White captured Pawn (p)
White captured pieces: ['n', 'n', 'p']
Black captured pieces: ['Q', 'P', 'N']
Enabling undo button: human_color=True, moves=21, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 982 positions in 0.26 seconds
CAPTURE: Black captured Bishop (B)
White captured pieces: ['n', 'n', 'p']
Black captured pieces: ['Q', 'P', 'N', 'B']
Enabling undo button: human_color=True, moves=22, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=22, turn=True
Board interaction re-enabled
UNDO CAPTURE: Removed B from Black's captured pieces
UNDO CAPTURE: Removed p from White's captured pieces
Enabling undo button: human_color=True, moves=20, turn=True






================================================================
================================================================

tracker re-alignment:


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_tracker.py
Disabling undo button: No moves in stack
Enabling undo button: human_color=True, moves=1, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 760 positions in 0.16 seconds
Enabling undo button: human_color=True, moves=2, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=3, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 947 positions in 0.20 seconds
Enabling undo button: human_color=True, moves=4, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=5, turn=False
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 730 positions in 0.13 seconds
Board interaction disabled

🎯 CAPTURE: Black captured Pawn (P)
⚪ White captured pieces: None
⚫ Black captured pieces: P
--------------------------------------------------
Enabling undo button: human_color=True, moves=6, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=6, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=7, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1449 positions in 0.27 seconds
Enabling undo button: human_color=True, moves=8, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=8, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=9, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1441 positions in 0.32 seconds

🎯 CAPTURE: Black captured Pawn (P)
⚪ White captured pieces: None
⚫ Black captured pieces: P P
--------------------------------------------------
Enabling undo button: human_color=True, moves=10, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=10, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=11, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1164 positions in 0.23 seconds

🎯 CAPTURE: Black captured Bishop (B)
⚪ White captured pieces: None
⚫ Black captured pieces: P P B
--------------------------------------------------
Enabling undo button: human_color=True, moves=12, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=12, turn=True
Board interaction re-enabled

🎯 CAPTURE: White captured Knight (n)
⚪ White captured pieces: n
⚫ Black captured pieces: P P B
--------------------------------------------------
Enabling undo button: human_color=True, moves=13, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1467 positions in 0.27 seconds
Enabling undo button: human_color=True, moves=14, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=14, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=15, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 839 positions in 0.14 seconds
Enabling undo button: human_color=True, moves=16, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=16, turn=True
Board interaction re-enabled

🎯 CAPTURE: White captured Knight (n)
⚪ White captured pieces: n n
⚫ Black captured pieces: P P B
--------------------------------------------------
Enabling undo button: human_color=True, moves=17, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 497 positions in 0.08 seconds

🎯 CAPTURE: Black captured Bishop (B)
⚪ White captured pieces: n n
⚫ Black captured pieces: P P B B
--------------------------------------------------
Enabling undo button: human_color=True, moves=18, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=18, turn=True
Board interaction re-enabled

↩️ UNDO CAPTURE: Restored B to White (removed from Black's captures)
⚪ White captured pieces: n n
⚫ Black captured pieces: P P B
--------------------------------------------------

↩️ UNDO CAPTURE: Restored n to Black (removed from White's captures)
⚪ White captured pieces: n
⚫ Black captured pieces: P P B
--------------------------------------------------
Enabling undo button: human_color=True, moves=16, turn=True
Colors switched: human_color=False, board.turn=True
Enabling undo button: human_color=False, moves=16, turn=True
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1549 positions in 0.37 seconds
Enabling undo button: human_color=False, moves=17, turn=False
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=17, turn=False
Board interaction re-enabled
Enabling undo button: human_color=False, moves=17, turn=False

🎯 CAPTURE: Black captured Pawn (P)
⚪ White captured pieces: n
⚫ Black captured pieces: P P B P
--------------------------------------------------
Enabling undo button: human_color=False, moves=18, turn=True
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 396 positions in 0.06 seconds
Board interaction disabled

🎯 CAPTURE: White captured Knight (n)
⚪ White captured pieces: n n
⚫ Black captured pieces: P P B P
--------------------------------------------------
Enabling undo button: human_color=False, moves=19, turn=False
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=19, turn=False
Board interaction re-enabled

↩️ UNDO CAPTURE: Restored n to Black (removed from White's captures)
⚪ White captured pieces: n
⚫ Black captured pieces: P P B P
--------------------------------------------------

↩️ UNDO CAPTURE: Restored P to White (removed from Black's captures)
⚪ White captured pieces: n
⚫ Black captured pieces: P B P
--------------------------------------------------
Enabling undo button: human_color=False, moves=17, turn=False








==================================================


game reconstruction:


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_tracker.py
Disabling undo button: No moves in stack
Enabling undo button: human_color=True, moves=1, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 814 positions in 0.16 seconds
Enabling undo button: human_color=True, moves=2, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=3, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 901 positions in 0.15 seconds
Enabling undo button: human_color=True, moves=4, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=5, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot evaluated 1371 positions in 0.29 seconds

🎯 CAPTURE: Black captured Queen (Q)
⚪ White captured pieces: None
⚫ Black captured pieces: Q
--------------------------------------------------
Enabling undo button: human_color=True, moves=6, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=6, turn=True
Board interaction re-enabled
Colors switched: human_color=False, board.turn=True
Enabling undo button: human_color=False, moves=6, turn=True
Bot's turn after color switch, making bot move
=== DISABLING ALL GAME CONTROLS ===
ChessBot evaluated 175 positions in 0.04 seconds
Board interaction disabled

🎯 CAPTURE: White captured Knight (n)
⚪ White captured pieces: n
⚫ Black captured pieces: Q
--------------------------------------------------
Enabling undo button: human_color=False, moves=7, turn=False
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=False, moves=7, turn=False
Board interaction re-enabled
Enabling undo button: human_color=False, moves=7, turn=False

🔄 RECONSTRUCTING CAPTURES from 20 moves...
  Move 6: Black captured Queen (Q)
  Move 7: White captured Knight (n)
  Move 10: Black captured Pawn (P)
  Move 11: White captured Knight (n)
  Move 14: Black captured Knight (N)
📊 RECONSTRUCTION COMPLETE:
⚪ White captured: n n
⚫ Black captured: Q P N
--------------------------------------------------

📁 GAME LOADED: C:/Users/<USER>/OneDrive - University of Suffolk (1)/projects/AI/Programming AI/chess_bot_test/pgn/GUI_tracker.pgn
🎯 Captured pieces tracker reconstructed from game history









===========================================================


testing with hybrid bot:


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui_tracker.py
Disabling undo button: No moves in stack
Enabling undo button: human_color=True, moves=1, turn=False
ChessBot using hybrid approach (Minimax + MCTS)
=== DISABLING ALL GAME CONTROLS ===
Step 1: Finding top 3 moves with minimax...
Board interaction disabled
Top moves from minimax: ['b8c6', 'g8f6', 'g8h6']
Minimax scores: [-60, -17, 33]
Step 2: Exploring candidates with 800 MCTS simulations...
ChessBot evaluated 2593 positions in 15.47 seconds
Selected move: b8c6
Enabling undo button: human_color=True, moves=2, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=2, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=3, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['g8f6', 'c6b4', 'e7e6']
Minimax scores: [-183, -167, -108]
Step 2: Exploring candidates with 800 MCTS simulations...
ChessBot search depth set to 2
ChessBot evaluated 3212 positions in 14.48 seconds
Selected move: c6b4
Enabling undo button: human_color=True, moves=4, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=4, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=5, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['b4d5', 'b4c6', 'b4a2']
Minimax scores: [-143, -119, 32]
Step 2: Exploring candidates with 800 MCTS simulations...
ChessBot evaluated 835 positions in 14.61 seconds
Selected move: b4a2

🎯 CAPTURE: Black captured Pawn (P)
⚪ White captured pieces: None
⚫ Black captured pieces: P
--------------------------------------------------
Enabling undo button: human_color=True, moves=6, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=6, turn=True
Board interaction re-enabled
Enabling undo button: human_color=True, moves=7, turn=False
ChessBot using hybrid approach (Minimax + MCTS)
=== DISABLING ALL GAME CONTROLS ===
Step 1: Finding top 3 moves with minimax...
Board interaction disabled
Top moves from minimax: ['a2c1', 'a2c3', 'g8f6']
Minimax scores: [-258, 2, 8]
Step 2: Exploring candidates with 800 MCTS simulations...
ChessBot evaluated 894 positions in 13.55 seconds
Selected move: a2c1

🎯 CAPTURE: Black captured Bishop (B)
⚪ White captured pieces: None
⚫ Black captured pieces: P B
--------------------------------------------------
Enabling undo button: human_color=True, moves=8, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=8, turn=True
Board interaction re-enabled

🎯 CAPTURE: White captured Knight (n)
⚪ White captured pieces: n
⚫ Black captured pieces: P B
--------------------------------------------------
Enabling undo button: human_color=True, moves=9, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['e7e6', 'g8f6', 'd7d5']
Minimax scores: [-125, -117, -112]
Step 2: Exploring candidates with 800 MCTS simulations...
ChessBot evaluated 737 positions in 13.87 seconds
Selected move: g8f6
Enabling undo button: human_color=True, moves=10, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=10, turn=True
Board interaction re-enabled

🎯 CAPTURE: White captured Pawn (p)
⚪ White captured pieces: n p
⚫ Black captured pieces: P B
--------------------------------------------------
Enabling undo button: human_color=True, moves=11, turn=False
=== DISABLING ALL GAME CONTROLS ===
Board interaction disabled
ChessBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['e8f7']
Minimax scores: [-397]
Step 2: Exploring candidates with 800 MCTS simulations...
ChessBot evaluated 30 positions in 15.09 seconds
Selected move: e8f7

🎯 CAPTURE: Black captured Bishop (B)
⚪ White captured pieces: n p
⚫ Black captured pieces: P B B
--------------------------------------------------
Enabling undo button: human_color=True, moves=12, turn=True
=== RE-ENABLING ALL GAME CONTROLS ===
Game control buttons re-enabled
Enabling undo button: human_color=True, moves=12, turn=True
Board interaction re-enabled

🔄 RECONSTRUCTING CAPTURES from 15 moves...
  Move 9: White captured Bishop (b)
  Move 12: Black captured Knight (N)
  Move 15: White captured Knight (n)
📊 RECONSTRUCTION COMPLETE:
⚪ White captured: b n
⚫ Black captured: N
--------------------------------------------------

📁 GAME LOADED: C:/Users/<USER>/OneDrive - University of Suffolk (1)/projects/AI/Programming AI/chess_bot_test/pgn/tracker test.pgn
🎯 Captured pieces tracker reconstructed from game history

🔄 RECONSTRUCTING CAPTURES from 20 moves...
  Move 6: Black captured Queen (Q)
  Move 7: White captured Knight (n)
  Move 10: Black captured Pawn (P)
  Move 11: White captured Knight (n)
  Move 14: Black captured Knight (N)
📊 RECONSTRUCTION COMPLETE:
⚪ White captured: n n
⚫ Black captured: Q P N
--------------------------------------------------

📁 GAME LOADED: C:/Users/<USER>/OneDrive - University of Suffolk (1)/projects/AI/Programming AI/chess_bot_test/pgn/GUI_tracker.pgn
🎯 Captured pieces tracker reconstructed from game history

