PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_chess_bot.py
Running Chess Bot Unit Tests...
test_board_evaluation_checkmate (__main__.TestChessBot.test_board_evaluation_checkmate)
Test board evaluation in checkmate position. ... ok
test_board_evaluation_starting_position (__main__.TestChessBot.test_board_evaluation_starting_position)
Test board evaluation at starting position. ... ok
test_bot_initialization (__main__.TestChessBot.test_bot_initialization)
Test bot initialization. ... ok
test_bot_prefers_captures (__main__.TestChessBot.test_bot_prefers_captures)
Test that bot prefers capturing moves when beneficial. ... TestBot evaluated 143 positions in 0.04 seconds
ok
test_get_best_move_returns_legal_move (__main__.TestChessBot.test_get_best_move_returns_legal_move)
Test that get_best_move returns a legal move. ... TestBot evaluated 80 positions in 0.03 seconds
ok
test_minimax_depth_zero (__main__.TestChessBot.test_minimax_depth_zero)
Test minimax with depth 0. ... ok
test_piece_values (__main__.TestChessBot.test_piece_values)
Test that piece values are reasonable. ... ok
test_set_depth (__main__.TestChessBot.test_set_depth)
Test setting bot depth. ... TestBot search depth set to 4
ok
test_game_initialization (__main__.TestChessGame.test_game_initialization)
Test game initialization. ... ok
test_make_move_valid (__main__.TestChessGame.test_make_move_valid)
Test making a valid move. ... ok
test_reset_game (__main__.TestChessGame.test_reset_game)
Test resetting the game. ... Game reset to starting position.
ok
test_switch_colors (__main__.TestChessGame.test_switch_colors)
Test switching player colors. ... You are now playing as Black
ok
test_bot_performance_consistency (__main__.TestChessIntegration.test_bot_performance_consistency)
Test that bot returns consistent moves for the same position. ... ConsistencyBot evaluated 80 positions in 0.04 seconds
ConsistencyBot evaluated 80 positions in 0.09 seconds
ok
test_complete_game_simulation (__main__.TestChessIntegration.test_complete_game_simulation)
Test a complete short game simulation. ... Bot1 evaluated 21 positions in 0.01 seconds
Bot2 evaluated 21 positions in 0.02 seconds
Bot1 evaluated 23 positions in 0.01 seconds
Bot2 evaluated 23 positions in 0.02 seconds
Bot1 evaluated 25 positions in 0.05 seconds
Bot2 evaluated 24 positions in 0.03 seconds
Bot1 evaluated 22 positions in 0.01 seconds
Bot2 evaluated 26 positions in 0.02 seconds
Bot1 evaluated 19 positions in 0.01 seconds
Bot2 evaluated 29 positions in 0.03 seconds
Bot1 evaluated 3 positions in 0.00 seconds
Bot2 evaluated 24 positions in 0.01 seconds
Bot1 evaluated 22 positions in 0.02 seconds
Bot2 evaluated 21 positions in 0.01 seconds
Bot1 evaluated 33 positions in 0.01 seconds
Bot2 evaluated 18 positions in 0.01 seconds
Bot1 evaluated 37 positions in 0.09 seconds
Bot2 evaluated 2 positions in 0.00 seconds
Bot1 evaluated 19 positions in 0.03 seconds
Bot2 evaluated 22 positions in 0.01 seconds
ok

----------------------------------------------------------------------
Ran 14 tests in 2.608s

OK

Running performance test...
PerfBot evaluated 715 positions in 0.25 seconds
PerfBot evaluated 841 positions in 0.32 seconds
PerfBot evaluated 761 positions in 0.23 seconds
PerfBot evaluated 2300 positions in 1.27 seconds
PerfBot evaluated 1634 positions in 1.14 seconds
Performance test completed:
- 5 moves calculated in 3.41 seconds
- Average time per move: 0.68 seconds
- Total nodes evaluated: 1634









================================================================================
================================================================================


HYBRID:







                                                             python test_chess_bot.pyidan_1k98io6\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test>
Running Chess Bot Unit Tests...
test_board_evaluation_checkmate (__main__.TestChessBot.test_board_evaluation_checkmate)
Test board evaluation in checkmate position. ... ok
test_board_evaluation_starting_position (__main__.TestChessBot.test_board_evaluation_starting_position)
Test board evaluation at starting position. ... ok
test_bot_initialization (__main__.TestChessBot.test_bot_initialization)
Test bot initialization. ... ok
test_bot_prefers_captures (__main__.TestChessBot.test_bot_prefers_captures)
Test that bot prefers capturing moves when beneficial. ... TestBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['b1c3', 'g1f3', 'g1e2', 'd1h5', 'd1g4']
Minimax scores: [62, 54, 34, 28, 28]
Step 2: Exploring candidates with 1000 MCTS simulations...
TestBot evaluated 864 positions in 13.35 seconds
Selected move: d1h5
ok
test_get_best_move_returns_legal_move (__main__.TestChessBot.test_get_best_move_returns_legal_move)
Test that get_best_move returns a legal move. ... TestBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3']
Minimax scores: [44, 44, 0, 0, -10]
Step 2: Exploring candidates with 1000 MCTS simulations...
TestBot evaluated 420 positions in 14.91 seconds
Selected move: b1c3
ok
test_minimax_depth_zero (__main__.TestChessBot.test_minimax_depth_zero)
Test minimax with depth 0. ... ok
test_piece_values (__main__.TestChessBot.test_piece_values)
Test that piece values are reasonable. ... ok
test_set_depth (__main__.TestChessBot.test_set_depth)
Test setting bot depth. ... TestBot search depth set to 4
ok
test_game_initialization (__main__.TestChessGame.test_game_initialization)
Test game initialization. ... ok
test_make_move_valid (__main__.TestChessGame.test_make_move_valid)
Test making a valid move. ... ok
test_reset_game (__main__.TestChessGame.test_reset_game)
Test resetting the game. ... Game reset to starting position.
ok
test_switch_colors (__main__.TestChessGame.test_switch_colors)
Test switching player colors. ... You are now playing as Black
ok
test_bot_performance_consistency (__main__.TestChessIntegration.test_bot_performance_consistency)
Test that bot returns consistent moves for the same position. ... ConsistencyBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3']
Minimax scores: [44, 44, 0, 0, -10]
Step 2: Exploring candidates with 1000 MCTS simulations...
ConsistencyBot evaluated 420 positions in 13.05 seconds
Selected move: b1a3
ConsistencyBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3']
Minimax scores: [44, 44, 0, 0, -10]
Step 2: Exploring candidates with 1000 MCTS simulations...
ConsistencyBot evaluated 420 positions in 13.81 seconds
Selected move: g1f3
FAIL
test_complete_game_simulation (__main__.TestChessIntegration.test_complete_game_simulation)
Test a complete short game simulation. ... Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3']
Minimax scores: [10, 10, -30, -30, -60]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 20 positions in 14.72 seconds
Selected move: g1f3
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g8f6', 'b8c6', 'g8h6', 'b8a6', 'e7e6']
Minimax scores: [44, 44, 84, 84, 114]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 20 positions in 13.75 seconds
Selected move: e7e6
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['b1c3', 'f3e5', 'f3d4', 'b1a3', 'h1g1']
Minimax scores: [60, 22, 20, 20, 10]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 22 positions in 12.77 seconds
Selected move: b1a3
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['f8a3', 'g8f6', 'b8c6', 'g8e7', 'g8h6']
Minimax scores: [-170, 74, 74, 84, 114]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 30 positions in 12.32 seconds
Selected move: g8h6
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['a3c4', 'a3b5', 'f3e5', 'f3d4', 'h1g1']
Minimax scores: [57, 44, 24, 22, 12]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 22 positions in 14.79 seconds
Selected move: a3b5
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['b8c6', 'h6f5', 'h6g4', 'b8a6', 'f8b4']
Minimax scores: [104, 109, 122, 144, 148]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 28 positions in 14.21 seconds
Selected move: b8c6
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['b5c7', 'b5a7', 'b5d6', 'b5d4', 'f3e5']
Minimax scores: [196, 96, 59, 6, 0]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 27 positions in 12.38 seconds
Selected move: b5d6
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['f8d6', 'c7d6', 'e8e7']
Minimax scores: [-228, -208, 123]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 3 positions in 12.69 seconds
Selected move: c7d6
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['f3e5', 'f3d4', 'h1g1', 'a1b1', 'f3g5']
Minimax scores: [-300, -300, -310, -310, -316]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 21 positions in 12.99 seconds
Selected move: f3d4
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['c6d4', 'h6f5', 'h6g4', 'c6e5', 'd8h4']
Minimax scores: [-556, -239, -226, -204, -200]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 30 positions in 13.70 seconds
Selected move: d8h4
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['d4c6', 'd4e6', 'h1g1', 'a1b1', 'd4f5']
Minimax scores: [12, -197, -316, -316, -325]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 20 positions in 14.41 seconds
Selected move: d4c6
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['d7c6', 'b7c6', 'h4f2', 'h4h2', 'h6f5']
Minimax scores: [-190, -190, -68, -20, 81]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 34 positions in 14.25 seconds
Selected move: h4h2
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['h1h2', 'c6a7', 'c6e5', 'c6d4', 'h1g1']
Minimax scores: [794, -22, -114, -114, -124]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 25 positions in 14.04 seconds
Selected move: h1h2
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['d7c6', 'b7c6', 'h6f5', 'h6g4', 'h8g8']
Minimax scores: [570, 570, 843, 856, 886]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 18 positions in 14.70 seconds
Selected move: h8g8
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['h2h6', 'c6a7', 'c6e5', 'c6d4', 'h2h5']
Minimax scores: [1088, 898, 806, 806, 796]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 28 positions in 14.67 seconds
Selected move: h2h6
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g7h6', 'd7c6', 'b7c6', 'g8h8', 'f8e7']
Minimax scores: [706, 868, 868, 1184, 1184]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 16 positions in 13.71 seconds
Selected move: f8e7
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['c6e7', 'h6h7', 'h6e6', 'c6a7', 'c6e5']
Minimax scores: [1411, 1228, 1212, 1180, 1088]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 32 positions in 12.36 seconds
Selected move: c6e7
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g7h6', 'e8e7', 'g8h8', 'g8f8', 'e8f8']
Minimax scores: [1027, 1168, 1505, 1505, 1505]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 17 positions in 13.47 seconds
Selected move: e8e7
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['h6e6', 'h6h7', 'a1b1', 'h6g6', 'h6f6']
Minimax scores: [1240, 1232, 1082, 1080, 1080]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 24 positions in 13.44 seconds
Selected move: h6f6
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['e7f6', 'g7f6', 'g8h8', 'g8f8', 'g8e8']
Minimax scores: [650, 680, 1164, 1164, 1164]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 20 positions in 13.40 seconds
Selected move: g8h8
ok

======================================================================
FAIL: test_bot_performance_consistency (__main__.TestChessIntegration.test_bot_performance_consistency)
Test that bot returns consistent moves for the same position.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test\test_chess_bot.py", line 167, in test_bot_performance_consistency
    self.assertEqual(move1, move2)
AssertionError: Move.from_uci('b1a3') != Move.from_uci('g1f3')

----------------------------------------------------------------------
Ran 14 tests in 328.809s

FAILED (failures=1)

Running performance test...
PerfBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3']
Minimax scores: [6, 6, -34, -34, -64]
Step 2: Exploring candidates with 1000 MCTS simulations...
PerfBot evaluated 1714 positions in 13.66 seconds
Selected move: g1f3
PerfBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g8f6', 'b8c6', 'g8h6', 'b8a6', 'e7e6']
Minimax scores: [48, 48, 88, 88, 118]
Step 2: Exploring candidates with 1000 MCTS simulations...
PerfBot evaluated 2152 positions in 12.88 seconds
Selected move: e7e6
PerfBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['f3e5', 'f3d4', 'b1c3', 'b1a3', 'h1g1']
Minimax scores: [103, 73, 16, 9, 4]
Step 2: Exploring candidates with 1000 MCTS simulations...
PerfBot evaluated 3218 positions in 14.50 seconds
Selected move: b1a3
PerfBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['d8f6', 'f8c5', 'f8b4', 'f8d6', 'f8a3']
Minimax scores: [-159, -23, -15, 27, 68]
Step 2: Exploring candidates with 1000 MCTS simulations...
PerfBot evaluated 6020 positions in 16.51 seconds
Selected move: f8d6
PerfBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['a3b5', 'a3c4', 'f3d4', 'h1g1', 'a1b1']
Minimax scores: [240, 117, 38, 3, 3]
Step 2: Exploring candidates with 1000 MCTS simulations...
PerfBot evaluated 3495 positions in 15.32 seconds
Selected move: a3b5
Performance test completed:
- 5 moves calculated in 72.98 seconds
- Average time per move: 14.60 seconds
- Total nodes evaluated: 3495



--------------------------------------------------


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_chess_bot.py
Running Chess Bot Unit Tests...
test_board_evaluation_checkmate (__main__.TestChessBot.test_board_evaluation_checkmate)
Test board evaluation in checkmate position. ... ok
test_board_evaluation_starting_position (__main__.TestChessBot.test_board_evaluation_starting_position)
Test board evaluation at starting position. ... ok
test_bot_initialization (__main__.TestChessBot.test_bot_initialization)
Test bot initialization. ... ok
test_bot_prefers_captures (__main__.TestChessBot.test_bot_prefers_captures)
Test that bot prefers capturing moves when beneficial. ... TestBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['b1c3', 'g1f3', 'g1e2', 'd1h5', 'd1g4']
Minimax scores: [62, 54, 34, 28, 28]
Step 2: Exploring candidates with 1000 MCTS simulations...
TestBot evaluated 864 positions in 14.89 seconds
Selected move: g1f3
ok
test_get_best_move_returns_legal_move (__main__.TestChessBot.test_get_best_move_returns_legal_move)
Test that get_best_move returns a legal move. ... TestBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3']
Minimax scores: [44, 44, 0, 0, -10]
Step 2: Exploring candidates with 1000 MCTS simulations...
TestBot evaluated 420 positions in 16.30 seconds
Selected move: e2e3
ok
test_minimax_depth_zero (__main__.TestChessBot.test_minimax_depth_zero)
Test minimax with depth 0. ... ok
test_piece_values (__main__.TestChessBot.test_piece_values)
Test that piece values are reasonable. ... ok
test_set_depth (__main__.TestChessBot.test_set_depth)
Test setting bot depth. ... TestBot search depth set to 4
ok
test_game_initialization (__main__.TestChessGame.test_game_initialization)
Test game initialization. ... ok
test_make_move_valid (__main__.TestChessGame.test_make_move_valid)
Test making a valid move. ... ok
test_reset_game (__main__.TestChessGame.test_reset_game)
Test resetting the game. ... Game reset to starting position.
ok
test_switch_colors (__main__.TestChessGame.test_switch_colors)
Test switching player colors. ... You are now playing as Black
ok
test_bot_performance_consistency (__main__.TestChessIntegration.test_bot_performance_consistency)
Test that bot returns consistent moves for the same position when using pure minimax. ... ConsistencyBot evaluated 80 positions in 0.03 seconds
Selected move: g1f3
ConsistencyBot evaluated 80 positions in 0.07 seconds
Selected move: g1f3
ok
test_complete_game_simulation (__main__.TestChessIntegration.test_complete_game_simulation)
Test a complete short game simulation. ... Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3']
Minimax scores: [10, 10, -30, -30, -60]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 20 positions in 15.20 seconds
Selected move: g1f3
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g8f6', 'b8c6', 'g8h6', 'b8a6', 'e7e6']
Minimax scores: [44, 44, 84, 84, 114]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 20 positions in 14.19 seconds
Selected move: b8c6
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['b1c3', 'f3e5', 'f3d4', 'b1a3', 'h1g1']
Minimax scores: [6, -32, -34, -34, -44]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 22 positions in 13.46 seconds
Selected move: b1a3
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g8f6', 'c6d4', 'g8h6', 'c6e5', 'a8b8']
Minimax scores: [4, 42, 44, 44, 54]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 22 positions in 14.24 seconds
Selected move: g8h6
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['a3c4', 'a3b5', 'f3e5', 'f3d4', 'h1g1']
Minimax scores: [1, -12, -32, -34, -44]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 22 positions in 14.40 seconds
Selected move: a3c4
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['h6f5', 'h6g4', 'c6d4', 'c6e5', 'h8g8']
Minimax scores: [52, 65, 85, 87, 97]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 22 positions in 16.35 seconds
Selected move: h6g4
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['c4d6', 'f3e5', 'f3d4', 'c4e5', 'c4e3']
Minimax scores: [11, -27, -29, -32, -39]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 25 positions in 16.24 seconds
Selected move: f3e5
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g4e5', 'c6e5', 'g4f2', 'g4h2', 'g4e3']
Minimax scores: [-289, -281, -69, -29, 68]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 26 positions in 13.84 seconds
Selected move: c6e5
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['c4e5', 'c4d6', 'c4e3', 'h1g1', 'a1b1']
Minimax scores: [-26, -329, -379, -379, -379]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 22 positions in 14.40 seconds
Selected move: c4e5
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g4e5', 'g4f2', 'g4h2', 'g4e3', 'g4f6']
Minimax scores: [-304, -80, -40, 57, 62]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 23 positions in 15.72 seconds
Selected move: g4f2
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['e1f2', 'e5d7', 'e5f7', 'h1g1', 'a1b1']
Minimax scores: [156, -45, -46, -176, -176]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 25 positions in 14.63 seconds
Selected move: e5d7
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['f2d1', 'f2h1', 'e8d7', 'd8d7', 'c8d7']
Minimax scores: [-819, -403, -286, -286, -286]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 25 positions in 15.05 seconds
Selected move: c8d7
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['e1f2', 'h1g1', 'a1b1', 'e2e3', 'd2d3']
Minimax scores: [-54, -386, -386, -406, -406]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 17 positions in 15.56 seconds
Selected move: a1b1
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['f2d1', 'f2h1', 'f2d3', 'f2e4', 'f2g4']
Minimax scores: [-1156, -740, -331, -310, -290]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 33 positions in 16.18 seconds
Selected move: f2d1
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['e1d1', 'h1g1', 'b1a1', 'e2e3', 'd2d3']
Minimax scores: [-954, -1252, -1252, -1272, -1272]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 17 positions in 16.91 seconds
Selected move: h1g1
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['d1b2', 'd1e3', 'd1c3', 'd1f2', 'd7g4']
Minimax scores: [-1316, -1203, -1196, -1186, -1158]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 31 positions in 14.74 seconds
Selected move: d1e3
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['d2e3', 'g1h1', 'e1f2', 'b1a1', 'd2d3']
Minimax scores: [-974, -1305, -1305, -1305, -1325]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 16 positions in 15.16 seconds
Selected move: e1f2
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['e3f1', 'e3c2', 'e3g2', 'e3g4', 'e3d5']
Minimax scores: [-1480, -1338, -1312, -1214, -1202]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 35 positions in 13.88 seconds
Selected move: e3g4
Bot1 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['f2g3', 'f2f3', 'f2e1']
Minimax scores: [-1282, -1282, -1282]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot1 evaluated 3 positions in 15.48 seconds
Selected move: f2e1
Bot2 using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g4h2', 'g4e5', 'g4e3', 'g4f6', 'd7a4']
Minimax scores: [-1298, -1204, -1203, -1194, -1190]
Step 2: Exploring candidates with 1000 MCTS simulations...
Bot2 evaluated 31 positions in 14.07 seconds
Selected move: g4e3
ok

----------------------------------------------------------------------
Ran 14 tests in 331.839s

OK

Running performance test...
PerfBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3', 'e2e3']
Minimax scores: [6, 6, -34, -34, -64]
Step 2: Exploring candidates with 1000 MCTS simulations...
PerfBot evaluated 1714 positions in 14.51 seconds
Selected move: g1f3
PerfBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g8f6', 'b8c6', 'g8h6', 'b8a6', 'e7e6']
Minimax scores: [48, 48, 88, 88, 118]
Step 2: Exploring candidates with 1000 MCTS simulations...
PerfBot evaluated 2152 positions in 19.70 seconds
Selected move: g8h6
PerfBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['f3e5', 'f3g5', 'f3d4', 'b1c3', 'b1a3']
Minimax scores: [88, 86, 20, 8, 1]
Step 2: Exploring candidates with 1000 MCTS simulations...
PerfBot evaluated 2400 positions in 16.77 seconds
Selected move: f3g5
PerfBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['h6g4', 'b8c6', 'h6f5', 'e7e6', 'e7e5']
Minimax scores: [-48, 39, 39, 68, 73]
Step 2: Exploring candidates with 1000 MCTS simulations...
PerfBot evaluated 2725 positions in 15.72 seconds
Selected move: e7e5
PerfBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 5 moves with minimax...
Top moves from minimax: ['g5f3', 'h2h4', 'f2f4', 'd2d3', 'g5e4']
Minimax scores: [60, 47, 22, 21, 15]
Step 2: Exploring candidates with 1000 MCTS simulations...
PerfBot evaluated 4576 positions in 17.52 seconds
Selected move: g5f3
Performance test completed:
- 5 moves calculated in 84.75 seconds
- Average time per move: 16.95 seconds
- Total nodes evaluated: 4576






============================================================================
============================================================================






TRANSPOSITION:


PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_chess_bot.py
Running Chess Bot Unit Tests...
test_board_evaluation_checkmate (__main__.TestChessBot.test_board_evaluation_checkmate)
Test board evaluation in checkmate position. ... ok
test_board_evaluation_starting_position (__main__.TestChessBot.test_board_evaluation_starting_position)
Test board evaluation at starting position. ... ok
test_bot_initialization (__main__.TestChessBot.test_bot_initialization)
Test bot initialization. ... ok
test_bot_prefers_captures (__main__.TestChessBot.test_bot_prefers_captures)
Test that bot prefers capturing moves when beneficial. ... TestBot evaluated 143 positions in 0.10 seconds (TT: 0.0% hit rate, 143 entries)
ok
test_get_best_move_returns_legal_move (__main__.TestChessBot.test_get_best_move_returns_legal_move)
Test that get_best_move returns a legal move. ... TestBot evaluated 80 positions in 0.07 seconds (TT: 0.0% hit rate, 80 entries)
ok
test_minimax_depth_zero (__main__.TestChessBot.test_minimax_depth_zero)
Test minimax with depth 0. ... ok
test_piece_values (__main__.TestChessBot.test_piece_values)
Test that piece values are reasonable. ... ok
test_set_depth (__main__.TestChessBot.test_set_depth)
Test setting bot depth. ... TestBot search depth set to 4
ok
test_game_initialization (__main__.TestChessGame.test_game_initialization)
Test game initialization. ... ok
test_make_move_valid (__main__.TestChessGame.test_make_move_valid)
Test making a valid move. ... ok
test_reset_game (__main__.TestChessGame.test_reset_game)
Test resetting the game. ... Game reset to starting position.
ok
test_switch_colors (__main__.TestChessGame.test_switch_colors)
Test switching player colors. ... You are now playing as Black
ok
test_bot_performance_consistency (__main__.TestChessIntegration.test_bot_performance_consistency)
Test that bot returns consistent moves for the same position. ... ConsistencyBot evaluated 80 positions in 0.06 seconds (TT: 0.0% hit rate, 80 entries)
ConsistencyBot evaluated 1 positions in 0.00 seconds (TT: 1.2% hit rate, 80 entries)
ok
test_complete_game_simulation (__main__.TestChessIntegration.test_complete_game_simulation)
Test a complete short game simulation. ... Bot1 evaluated 21 positions in 0.01 seconds (TT: 0.0% hit rate, 21 entries)
Bot2 evaluated 21 positions in 0.01 seconds (TT: 0.0% hit rate, 21 entries)
Bot1 evaluated 23 positions in 0.02 seconds (TT: 0.0% hit rate, 44 entries)
Bot2 evaluated 23 positions in 0.01 seconds (TT: 0.0% hit rate, 44 entries)
Bot1 evaluated 25 positions in 0.02 seconds (TT: 0.0% hit rate, 69 entries)
Bot2 evaluated 24 positions in 0.01 seconds (TT: 0.0% hit rate, 68 entries)
Bot1 evaluated 22 positions in 0.01 seconds (TT: 0.0% hit rate, 91 entries)
Bot2 evaluated 26 positions in 0.03 seconds (TT: 0.0% hit rate, 94 entries)
Bot1 evaluated 19 positions in 0.01 seconds (TT: 0.0% hit rate, 110 entries)
Bot2 evaluated 29 positions in 0.02 seconds (TT: 0.0% hit rate, 123 entries)
Bot1 evaluated 3 positions in 0.00 seconds (TT: 0.0% hit rate, 113 entries)
Bot2 evaluated 24 positions in 0.01 seconds (TT: 0.0% hit rate, 147 entries)
Bot1 evaluated 22 positions in 0.01 seconds (TT: 0.0% hit rate, 135 entries)
Bot2 evaluated 21 positions in 0.01 seconds (TT: 0.0% hit rate, 168 entries)
Bot1 evaluated 33 positions in 0.02 seconds (TT: 0.0% hit rate, 168 entries)
Bot2 evaluated 18 positions in 0.01 seconds (TT: 0.0% hit rate, 186 entries)
Bot1 evaluated 37 positions in 0.02 seconds (TT: 0.0% hit rate, 205 entries)
Bot2 evaluated 2 positions in 0.00 seconds (TT: 0.0% hit rate, 188 entries)
Bot1 evaluated 19 positions in 0.01 seconds (TT: 0.0% hit rate, 224 entries)
Bot2 evaluated 22 positions in 0.01 seconds (TT: 0.0% hit rate, 210 entries)
ok

----------------------------------------------------------------------
Ran 14 tests in 0.603s

OK

Running performance test...
PerfBot evaluated 715 positions in 0.26 seconds (TT: 20.8% hit rate, 566 entries)
PerfBot evaluated 607 positions in 0.33 seconds (TT: 18.2% hit rate, 1020 entries)
PerfBot evaluated 851 positions in 0.53 seconds (TT: 16.4% hit rate, 1685 entries)
PerfBot evaluated 1945 positions in 0.95 seconds (TT: 13.1% hit rate, 3365 entries)
PerfBot evaluated 1463 positions in 0.61 seconds (TT: 12.4% hit rate, 4573 entries)
Performance test completed:
- 5 moves calculated in 2.69 seconds
- Average time per move: 0.54 seconds
- Total nodes evaluated: 1463