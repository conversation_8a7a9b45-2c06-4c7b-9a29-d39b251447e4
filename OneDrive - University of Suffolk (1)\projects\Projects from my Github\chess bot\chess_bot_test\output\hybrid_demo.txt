PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python hybrid_demo.py
Hybrid Chess Bot Demonstration
Combining Minimax + Alpha-Beta Pruning + Monte Carlo Tree Search
================================================================================
=== Comparing Pure Minimax vs Hybrid Approach ===


============================================================
Position: Opening
FEN: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
============================================================

--- PureMinimax Analysis ---
PureMinimax evaluated 715 positions in 0.30 seconds
Selected move: g1f3
Selected move: g1f3 (Nf3)
Time taken: 0.31 seconds
Nodes evaluated: 715
----------------------------------------

--- HybridBot Analysis ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 4 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3']
Minimax scores: [6, 6, -34, -34]
Step 2: Exploring candidates with 800 MCTS simulations...
HybridBot evaluated 1714 positions in 16.03 seconds
Selected move: b1c3
Selected move: b1c3 (Nc3)
Time taken: 16.18 seconds
Nodes evaluated: 1714
----------------------------------------

============================================================
Position: Sicilian Defense
FEN: rnbqkbnr/pp1ppppp/8/2p5/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2
============================================================

--- PureMinimax Analysis ---
PureMinimax evaluated 1724 positions in 0.87 seconds
Selected move: d1h5
Selected move: d1h5 (Qh5)
Time taken: 1.00 seconds
Nodes evaluated: 1724
----------------------------------------

--- HybridBot Analysis ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 4 moves with minimax...
Top moves from minimax: ['d1h5', 'd1f3', 'f1b5', 'f1c4']
Minimax scores: [113, 113, 105, 105]
Step 2: Exploring candidates with 800 MCTS simulations...
HybridBot evaluated 4235 positions in 14.60 seconds
Selected move: d1h5
Selected move: d1h5 (Qh5)
Time taken: 14.73 seconds
Nodes evaluated: 4235
----------------------------------------

============================================================
Position: Middle Game
FEN: r1bq1rk1/ppp2ppp/2n1bn2/2bpp3/2B1P3/3P1N2/PPP1NPPP/R1BQ1RK1 w - - 0 8
============================================================

--- PureMinimax Analysis ---
PureMinimax evaluated 4486 positions in 2.35 seconds
Selected move: e4d5
Selected move: e4d5 (exd5)
Time taken: 2.41 seconds
Nodes evaluated: 4486
----------------------------------------

--- HybridBot Analysis ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 4 moves with minimax...
Top moves from minimax: ['e4d5', 'c4b5', 'c4b3', 'c4d5']
Minimax scores: [-103, -266, -290, -297]
Step 2: Exploring candidates with 800 MCTS simulations...
HybridBot evaluated 11903 positions in 23.22 seconds
Selected move: c4b3
Selected move: c4b3 (Bb3)
Time taken: 23.33 seconds
Nodes evaluated: 11903
----------------------------------------

============================================================
Position: Tactical
FEN: r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4
============================================================

--- PureMinimax Analysis ---
PureMinimax evaluated 6859 positions in 3.02 seconds
Selected move: b1c3
Selected move: b1c3 (Nc3)
Time taken: 3.17 seconds
Nodes evaluated: 6859
----------------------------------------

--- HybridBot Analysis ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 4 moves with minimax...
Top moves from minimax: ['b1c3', 'b1d2', 'e1e2', 'e1f1']
Minimax scores: [54, 40, 0, 0]
Step 2: Exploring candidates with 800 MCTS simulations...
HybridBot evaluated 9870 positions in 20.57 seconds
Selected move: e1f1
Selected move: e1f1 (Kf1)
Time taken: 20.60 seconds
Nodes evaluated: 9870
----------------------------------------

=== Analyzing Hybrid Components ===

Analyzing tactical position:
FEN: r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4
Position:
r . b q k . . r
p p p p . p p p
. . n . . n . .
. . b . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q K . . R

Step-by-step analysis:
1. Getting top moves from minimax...
Top 4 moves from minimax:
  1. Nc3 (score: 54)
  2. Nbd2 (score: 40)
  3. Ke2 (score: 0)
  4. Kf1 (score: 0)

2. Running MCTS on these 4 candidates...

3. Final selection: Kf1
   Note: MCTS chose Kf1 over minimax's top choice Nc3

=== Performance Comparison ===

Testing performance on middle game position:
FEN: r1bq1rk1/ppp2ppp/2n1bn2/2bpp3/2B1P3/3P1N2/PPP1NPPP/R1BQ1RK1 w - - 0 8

Testing Pure Minimax D3...
Pure Minimax D3 evaluated 4486 positions in 1.64 seconds
Selected move: e4d5
  Move: exd5
  Time: 1.65s
  Nodes: 4486
  NPS: 2718

Testing Pure Minimax D4...
Pure Minimax D4 evaluated 87029 positions in 30.85 seconds
Selected move: e4d5
  Move: exd5
  Time: 30.85s
  Nodes: 87029
  NPS: 2821

Testing Hybrid D3 + 300 MCTS...
Hybrid D3 + 300 MCTS using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['e4d5', 'c4b5', 'c4b3']
Minimax scores: [-103, -266, -290]
Step 2: Exploring candidates with 300 MCTS simulations...
Hybrid D3 + 300 MCTS evaluated 11903 positions in 9.59 seconds
Selected move: e4d5
  Move: exd5
  Time: 9.60s
  Nodes: 11903
  NPS: 1240

Testing Hybrid D3 + 600 MCTS...
Hybrid D3 + 600 MCTS using hybrid approach (Minimax + MCTS)
Step 1: Finding top 4 moves with minimax...
Top moves from minimax: ['e4d5', 'c4b5', 'c4b3', 'c4d5']
Minimax scores: [-103, -266, -290, -297]
Step 2: Exploring candidates with 600 MCTS simulations...
Hybrid D3 + 600 MCTS evaluated 11903 positions in 14.34 seconds
Selected move: e4d5
  Move: exd5
  Time: 14.35s
  Nodes: 11903
  NPS: 829

Testing Hybrid D4 + 400 MCTS...
Hybrid D4 + 400 MCTS using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['e4d5', 'c4d5', 'c4b5']
Minimax scores: [-283, -359, -361]
Step 2: Exploring candidates with 400 MCTS simulations...
Hybrid D4 + 400 MCTS evaluated 483737 positions in 169.01 seconds
Selected move: e4d5
  Move: exd5
  Time: 169.02s
  Nodes: 483737
  NPS: 2862

Performance Summary:
----------------------------------------------------------------------
Configuration             Move     Time     Nodes    NPS
----------------------------------------------------------------------
Pure Minimax D3           exd5     1.65     4486     2718    
Pure Minimax D4           exd5     30.85    87029    2821
Hybrid D3 + 300 MCTS      exd5     9.60     11903    1240
Hybrid D3 + 600 MCTS      exd5     14.35    11903    829
Hybrid D4 + 400 MCTS      exd5     169.02   483737   2862

=== Hybrid Bot vs Pure Minimax Bot ===

Starting game: HybridWhite vs MinimaxBlack
Initial position:
r n b q k b n r
p p p p p p p p
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . . . . .
P P P P P P P P
R N B Q K B N R

Move 1: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3']
Minimax scores: [6, 6, -34]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 1714 positions in 7.89 seconds
Selected move: g1f3
Played: Nf3
Position after move:
r n b q k b n r
p p p p p p p p
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . . N . .
P P P P P P P P
R N B Q K B . R

Move 2: MinimaxBlack to move
MinimaxBlack evaluated 841 positions in 0.23 seconds
Selected move: g8f6
Played: Nf6
Position after move:
r n b q k b . r
p p p p p p p p
. . . . . n . .
. . . . . . . .
. . . . . . . .
. . . . . N . .
P P P P P P P P
R N B Q K B . R

Move 3: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['f3e5', 'f3g5', 'f3d4']
Minimax scores: [78, 40, -4]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 2322 positions in 9.41 seconds
Selected move: f3g5
Played: Ng5
Position after move:
r n b q k b . r
p p p p p p p p
. . . . . n . .
. . . . . . N .
. . . . . . . .
. . . . . . . .
P P P P P P P P
R N B Q K B . R

Move 4: MinimaxBlack to move
MinimaxBlack evaluated 2111 positions in 0.59 seconds
Selected move: f6g4
Played: Ng4
Position after move:
r n b q k b . r
p p p p p p p p
. . . . . . . .
. . . . . . N .
. . . . . . n .
. . . . . . . .
P P P P P P P P
R N B Q K B . R

Move 5: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['b1c3', 'e2e3', 'b1a3']
Minimax scores: [98, 62, 58]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 3166 positions in 8.40 seconds
Selected move: e2e3
Played: e3
Position after move:
r n b q k b . r
p p p p p p p p
. . . . . . . .
. . . . . . N .
. . . . . . n .
. . . . P . . .
P P P P . P P P
R N B Q K B . R

Move 6: MinimaxBlack to move
MinimaxBlack evaluated 4579 positions in 1.31 seconds
Selected move: f7f5
Played: f5
Position after move:
r n b q k b . r
p p p p p . p p
. . . . . . . .
. . . . . p N .
. . . . . . n .
. . . . P . . .
P P P P . P P P
R N B Q K B . R

Move 7: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['f1b5', 'g5h7', 'b1c3']
Minimax scores: [140, 124, 112]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 7993 positions in 9.42 seconds
Selected move: b1c3
Played: Nc3
Position after move:
r n b q k b . r
p p p p p . p p
. . . . . . . .
. . . . . p N .
. . . . . . n .
. . N . P . . .
P P P P . P P P
R . B Q K B . R

Move 8: MinimaxBlack to move
MinimaxBlack evaluated 2008 positions in 0.56 seconds
Selected move: b8c6
Played: Nc6
Position after move:
r . b q k b . r
p p p p p . p p
. . n . . . . .
. . . . . p N .
. . . . . . n .
. . N . P . . .
P P P P . P P P
R . B Q K B . R

Move 9: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['f1b5', 'd1f3', 'c3d5']
Minimax scores: [160, 160, 138]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 12411 positions in 11.27 seconds
Selected move: d1f3
Played: Qf3
Position after move:
r . b q k b . r
p p p p p . p p
. . n . . . . .
. . . . . p N .
. . . . . . n .
. . N . P Q . .
P P P P . P P P
R . B . K B . R

Move 10: MinimaxBlack to move
MinimaxBlack evaluated 3941 positions in 1.30 seconds
Selected move: e7e6
Played: e6
Position after move:
r . b q k b . r
p p p p . . p p
. . n . p . . .
. . . . . p N .
. . . . . . n .
. . N . P Q . .
P P P P . P P P
R . B . K B . R

Move 11: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['g5e6', 'g5h3', 'g5h7']
Minimax scores: [190, 138, 132]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 12513 positions in 11.30 seconds
Selected move: g5h3
Played: Nh3
Position after move:
r . b q k b . r
p p p p . . p p
. . n . p . . .
. . . . . p . .
. . . . . . n .
. . N . P Q . N
P P P P . P P P
R . B . K B . R

Move 12: MinimaxBlack to move
MinimaxBlack evaluated 6008 positions in 1.88 seconds
Selected move: f8b4
Played: Bb4
Position after move:
r . b q k . . r
p p p p . . p p
. . n . p . . .
. . . . . p . .
. b . . . . n .
. . N . P Q . N
P P P P . P P P
R . B . K B . R

Move 13: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['c3b5', 'c3e2', 'c3a4']
Minimax scores: [112, 110, 80]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 14126 positions in 12.36 seconds
Selected move: c3a4
Played: Na4
Position after move:
r . b q k . . r
p p p p . . p p
. . n . p . . .
. . . . . p . .
N b . . . . n .
. . . . P Q . N
P P P P . P P P
R . B . K B . R

Move 14: MinimaxBlack to move
MinimaxBlack evaluated 7681 positions in 3.78 seconds
Selected move: c6e5
Played: Nce5
Position after move:
r . b q k . . r
p p p p . . p p
. . . . p . . .
. . . . n p . .
N b . . . . n .
. . . . P Q . N
P P P P . P P P
R . B . K B . R

Move 15: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['f3f4', 'f3g3', 'f3e2']
Minimax scores: [72, 70, -62]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 10062 positions in 13.00 seconds
Selected move: f3f4
Played: Qf4
Position after move:
r . b q k . . r
p p p p . . p p
. . . . p . . .
. . . . n p . .
N b . . . Q n .
. . . . P . . N
P P P P . P P P
R . B . K B . R

Move 16: MinimaxBlack to move
MinimaxBlack evaluated 12439 positions in 4.49 seconds
Selected move: d8e7
Played: Qe7
Position after move:
r . b . k . . r
p p p p q . p p
. . . . p . . .
. . . . n p . .
N b . . . Q n .
. . . . P . . N
P P P P . P P P
R . B . K B . R

Move 17: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['h3g5', 'f1b5', 'e1e2']
Minimax scores: [96, 66, 66]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 11752 positions in 11.39 seconds
Selected move: e1e2
Played: Ke2
Position after move:
r . b . k . . r
p p p p q . p p
. . . . p . . .
. . . . n p . .
N b . . . Q n .
. . . . P . . N
P P P P K P P P
R . B . . B . R

Move 18: MinimaxBlack to move
MinimaxBlack evaluated 12102 positions in 3.69 seconds
Selected move: b7b5
Played: b5
Position after move:
r . b . k . . r
p . p p q . p p
. . . . p . . .
. p . . n p . .
N b . . . Q n .
. . . . P . . N
P P P P K P P P
R . B . . B . R

Move 19: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['h3g5', 'a4b6', 'f4d4']
Minimax scores: [14, 7, -22]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 10662 positions in 12.06 seconds
Selected move: f4d4
Played: Qd4
Position after move:
r . b . k . . r
p . p p q . p p
. . . . p . . .
. p . . n p . .
N b . Q . . n .
. . . . P . . N
P P P P K P P P
R . B . . B . R

Move 20: MinimaxBlack to move
MinimaxBlack evaluated 17745 positions in 5.63 seconds
Selected move: b5a4
Played: bxa4
Position after move:
r . b . k . . r
p . p p q . p p
. . . . p . . .
. . . . n p . .
p b . Q . . n .
. . . . P . . N
P P P P K P P P
R . B . . B . R


Game completed after 20 moves
Final position:
r . b . k . . r
p . p p q . p p
. . . . p . . .
. . . . n p . .
p b . Q . . n .
. . . . P . . N
P P P P K P P P
R . B . . B . R
Game ended early for demonstration

Game PGN:
[Event "Hybrid vs Minimax Demo"]
[Site "?"]
[Date "????.??.??"]
[Round "?"]
[White "HybridWhite"]
[Black "MinimaxBlack"]
[Result "*"]

1. Nf3 Nf6 2. Ng5 Ng4 3. e3 f5 4. Nc3 Nc6 5. Qf3 e6 6. Nh3 Bb4 7. Na4 Nce5 8. Qf4 Qe7 9. Ke2 b5 10. Qd4 bxa4 *       

================================================================================
Demonstration completed successfully!

Key Benefits of the Hybrid Approach:
1. Minimax provides quick evaluation of all moves
2. Alpha-beta pruning reduces search space efficiently
3. MCTS explores promising moves more deeply
4. Combination leverages strengths of both approaches
5. Configurable balance between speed and depth







---------------------------------------------------




PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python hybrid_demo.py
Hybrid Chess Bot Demonstration
Combining Minimax + Alpha-Beta Pruning + Monte Carlo Tree Search
================================================================================
=== Comparing Pure Minimax vs Hybrid Approach ===


============================================================
Position: Opening
FEN: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1        
============================================================

--- PureMinimax Analysis ---
PureMinimax evaluated 710 positions in 0.40 seconds
Selected move: g1f3
Selected move: g1f3 (Nf3)
Time taken: 0.40 seconds
Nodes evaluated: 710
----------------------------------------

--- HybridBot Analysis ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 4 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3', 'b1a3']
Minimax scores: [16, 16, -24, -34]
Step 2: Exploring candidates with 800 MCTS simulations...
HybridBot evaluated 1732 positions in 14.99 seconds
Selected move: g1h3
Selected move: g1h3 (Nh3)
Time taken: 14.99 seconds
Nodes evaluated: 1732
----------------------------------------

============================================================
Position: Sicilian Defense
FEN: rnbqkbnr/pp1ppppp/8/2p5/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2    
============================================================

--- PureMinimax Analysis ---
PureMinimax evaluated 1537 positions in 1.01 seconds
Selected move: d1f3
Selected move: d1f3 (Qf3)
Time taken: 1.01 seconds
Nodes evaluated: 1537
----------------------------------------

--- HybridBot Analysis ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 4 moves with minimax...
Top moves from minimax: ['d1f3', 'd1h5', 'd1g4', 'f1b5']
Minimax scores: [193, 188, 185, 145]
Step 2: Exploring candidates with 800 MCTS simulations...
HybridBot evaluated 3358 positions in 23.12 seconds
Selected move: f1b5
Selected move: f1b5 (Bb5)
Time taken: 23.13 seconds
Nodes evaluated: 3358
----------------------------------------

============================================================
Position: Middle Game
FEN: r1bq1rk1/ppp2ppp/2n1bn2/2bpp3/2B1P3/3P1N2/PPP1NPPP/R1BQ1RK1 w - - 0 8
============================================================

--- PureMinimax Analysis ---
PureMinimax evaluated 1268 positions in 1.36 seconds
Selected move: e4d5
Selected move: e4d5 (exd5)
Time taken: 1.36 seconds
Nodes evaluated: 1268
----------------------------------------

--- HybridBot Analysis ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 4 moves with minimax...
Top moves from minimax: ['e4d5', 'c4b5', 'c4b3', 'c4d5']
Minimax scores: [-102, -271, -316, -322]
Step 2: Exploring candidates with 800 MCTS simulations...
HybridBot evaluated 4303 positions in 24.53 seconds
Selected move: e4d5
Selected move: e4d5 (exd5)
Time taken: 24.53 seconds
Nodes evaluated: 4303
----------------------------------------

============================================================
Position: Tactical
FEN: r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4
============================================================

--- PureMinimax Analysis ---
PureMinimax evaluated 1838 positions in 1.38 seconds
Selected move: b1c3
Selected move: b1c3 (Nc3)
Time taken: 1.38 seconds
Nodes evaluated: 1838
----------------------------------------

--- HybridBot Analysis ---
HybridBot using hybrid approach (Minimax + MCTS)
Step 1: Finding top 4 moves with minimax...
Top moves from minimax: ['b1c3', 'b1d2', 'c1d2', 'd1d2']
Minimax scores: [64, 50, 20, 15]
Step 2: Exploring candidates with 800 MCTS simulations...
HybridBot evaluated 4436 positions in 19.15 seconds
Selected move: c1d2
Selected move: c1d2 (Bd2)
Time taken: 19.16 seconds
Nodes evaluated: 4436
----------------------------------------

=== Analyzing Hybrid Components ===

Analyzing tactical position:
FEN: r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4
Position:
r . b q k . . r
p p p p . p p p
. . n . . n . .
. . b . p . . .
. . B . P . . .
. . . P . N . .
P P P . . P P P
R N B Q K . . R

Step-by-step analysis:
1. Getting top moves from minimax...
Top 4 moves from minimax:
  1. Nc3 (score: 64)
  2. Nbd2 (score: 50)
  3. Bd2 (score: 20)
  4. Qd2 (score: 15)

2. Running MCTS on these 4 candidates...

3. Final selection: Nbd2
   Note: MCTS chose Nbd2 over minimax's top choice Nc3

=== Performance Comparison ===

Testing performance on middle game position:
FEN: r1bq1rk1/ppp2ppp/2n1bn2/2bpp3/2B1P3/3P1N2/PPP1NPPP/R1BQ1RK1 w - - 0 8

Testing Pure Minimax D3...
Pure Minimax D3 evaluated 1268 positions in 1.44 seconds
Selected move: e4d5
  Move: exd5
  Time: 1.48s
  Nodes: 1268
  NPS: 857

Testing Pure Minimax D4...
Pure Minimax D4 evaluated 4065 positions in 4.42 seconds
Selected move: e4d5
  Move: exd5
  Time: 4.42s
  Nodes: 4065
  NPS: 919

Testing Hybrid D3 + 300 MCTS...
Hybrid D3 + 300 MCTS using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['e4d5', 'c4b5', 'c4b3']
Minimax scores: [-102, -271, -316]
Step 2: Exploring candidates with 300 MCTS simulations...
Hybrid D3 + 300 MCTS evaluated 4303 positions in 12.25 seconds
Selected move: e4d5
  Move: exd5
  Time: 12.25s
  Nodes: 4303
  NPS: 351

Testing Hybrid D3 + 600 MCTS...
Hybrid D3 + 600 MCTS using hybrid approach (Minimax + MCTS)
Step 1: Finding top 4 moves with minimax...
Top moves from minimax: ['e4d5', 'c4b5', 'c4b3', 'c4d5']
Minimax scores: [-102, -271, -316, -322]
Step 2: Exploring candidates with 600 MCTS simulations...
Hybrid D3 + 600 MCTS evaluated 4303 positions in 19.07 seconds
Selected move: c4d5
  Move: Bxd5
  Time: 19.07s
  Nodes: 4303
  NPS: 226

Testing Hybrid D4 + 400 MCTS...
Hybrid D4 + 400 MCTS using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['e4d5', 'c4d5', 'c4b5']
Minimax scores: [-333, -399, -406]
Step 2: Exploring candidates with 400 MCTS simulations...
Hybrid D4 + 400 MCTS evaluated 65213 positions in 97.98 seconds
Selected move: e4d5
  Move: exd5
  Time: 98.01s
  Nodes: 65213
  NPS: 665

Performance Summary:
----------------------------------------------------------------------
Configuration             Move     Time     Nodes    NPS
----------------------------------------------------------------------
Pure Minimax D3           exd5     1.48     1268     857
Pure Minimax D4           exd5     4.42     4065     919     
Hybrid D3 + 300 MCTS      exd5     12.25    4303     351
Hybrid D3 + 600 MCTS      Bxd5     19.07    4303     226     
Hybrid D4 + 400 MCTS      exd5     98.01    65213    665

=== Hybrid Bot vs Pure Minimax Bot ===

Starting game: HybridWhite vs MinimaxBlack
Initial position:
r n b q k b n r
p p p p p p p p
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . . . . .
P P P P P P P P
R N B Q K B N R

Move 1: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['g1f3', 'b1c3', 'g1h3']
Minimax scores: [16, 16, -24]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 1732 positions in 17.98 seconds
Selected move: g1f3
Played: Nf3
Position after move:
r n b q k b n r
p p p p p p p p
. . . . . . . .
. . . . . . . .
. . . . . . . .
. . . . . N . .
P P P P P P P P
R N B Q K B . R

Move 2: MinimaxBlack to move
MinimaxBlack evaluated 779 positions in 0.74 seconds
Selected move: g8f6
Played: Nf6
Position after move:
r n b q k b . r
p p p p p p p p
. . . . . n . .
. . . . . . . .
. . . . . . . .
. . . . . N . .
P P P P P P P P
R N B Q K B . R

Move 3: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['f3e5', 'f3g5', 'b1c3']
Minimax scores: [63, 45, -6]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 2131 positions in 15.21 seconds
Selected move: f3e5
Played: Ne5
Position after move:
r n b q k b . r
p p p p p p p p
. . . . . n . .
. . . . N . . .
. . . . . . . .
. . . . . . . .
P P P P P P P P
R N B Q K B . R

Move 4: MinimaxBlack to move
MinimaxBlack evaluated 1528 positions in 1.17 seconds
Selected move: f6e4
Played: Ne4
Position after move:
r n b q k b . r
p p p p p p p p
. . . . . . . .
. . . . N . . .
. . . . n . . .
. . . . . . . .
P P P P P P P P
R N B Q K B . R

Move 5: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['d2d3', 'f2f3', 'b1a3']
Minimax scores: [84, 74, 64]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 3258 positions in 15.79 seconds
Selected move: d2d3
Played: d3
Position after move:
r n b q k b . r
p p p p p p p p
. . . . . . . .
. . . . N . . .
. . . . n . . .
. . . P . . . .
P P P . P P P P
R N B Q K B . R

Move 6: MinimaxBlack to move
MinimaxBlack evaluated 994 positions in 2.87 seconds
Selected move: b8c6
Played: Nc6
Position after move:
r . b q k b . r
p p p p p p p p
. . n . . . . .
. . . . N . . .
. . . . n . . .
. . . P . . . .
P P P . P P P P
R N B Q K B . R

Move 7: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['e5c6', 'd3e4', 'e5f7']
Minimax scores: [231, 132, 75]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 2949 positions in 23.45 seconds
Selected move: e5c6
Played: Nxc6
Position after move:
r . b q k b . r
p p p p p p p p
. . N . . . . .
. . . . . . . .
. . . . n . . .
. . . P . . . .
P P P . P P P P
R N B Q K B . R

Move 8: MinimaxBlack to move
MinimaxBlack evaluated 862 positions in 0.91 seconds
Selected move: d7c6
Played: dxc6
Position after move:
r . b q k b . r
p p p . p p p p
. . p . . . . .
. . . . . . . .
. . . . n . . .
. . . P . . . .
P P P . P P P P
R N B Q K B . R

Move 9: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['d3e4', 'c1f4', 'c1e3']
Minimax scores: [241, 54, 37]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 3784 positions in 17.74 seconds
Selected move: d3e4
Played: dxe4
Position after move:
r . b q k b . r
p p p . p p p p
. . p . . . . .
. . . . . . . .
. . . . P . . .
. . . . . . . .
P P P . P P P P
R N B Q K B . R

Move 10: MinimaxBlack to move
MinimaxBlack evaluated 353 positions in 0.38 seconds
Selected move: c8g4
Played: Bg4
Position after move:
r . . q k b . r
p p p . p p p p
. . p . . . . .
. . . . . . . .
. . . . P . b .
. . . . . . . .
P P P . P P P P
R N B Q K B . R

Move 11: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['b1c3', 'd1d2', 'c1f4']
Minimax scores: [254, 244, 239]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 2227 positions in 13.99 seconds
Selected move: d1d2
Played: Qd2
Position after move:
r . . q k b . r
p p p . p p p p
. . p . . . . .
. . . . . . . .
. . . . P . b .
. . . . . . . .
P P P Q P P P P
R N B . K B . R

Move 12: MinimaxBlack to move
MinimaxBlack evaluated 438 positions in 0.23 seconds
Selected move: d8d2
Played: Qxd2+
Position after move:
r . . . k b . r
p p p . p p p p
. . p . . . . .
. . . . . . . .
. . . . P . b .
. . . . . . . .
P P P q P P P P
R N B . K B . R

Check!
Move 13: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['b1d2', 'c1d2', 'e1d2']
Minimax scores: [226, 225, 148]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 319 positions in 9.89 seconds
Selected move: c1d2
Played: Bxd2
Position after move:
r . . . k b . r
p p p . p p p p
. . p . . . . .
. . . . . . . .
. . . . P . b .
. . . . . . . .
P P P B P P P P
R N . . K B . R

Move 14: MinimaxBlack to move
MinimaxBlack evaluated 1585 positions in 0.99 seconds
Selected move: e8c8
Played: O-O-O
Position after move:
. . k r . b . r
p p p . p p p p
. . p . . . . .
. . . . . . . .
. . . . P . b .
. . . . . . . .
P P P B P P P P
R N . . K B . R

Move 15: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['d2a5', 'd2f4', 'd2g5']
Minimax scores: [341, 337, 324]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 5067 positions in 15.22 seconds
Selected move: d2g5
Played: Bg5
Position after move:
. . k r . b . r
p p p . p p p p
. . p . . . . .
. . . . . . B .
. . . . P . b .
. . . . . . . .
P P P . P P P P
R N . . K B . R

Move 16: MinimaxBlack to move
MinimaxBlack evaluated 2850 positions in 3.28 seconds
Selected move: f7f6
Played: f6
Position after move:
. . k r . b . r
p p p . p . p p
. . p . . p . .
. . . . . . B .
. . . . P . b .
. . . . . . . .
P P P . P P P P
R N . . K B . R

Move 17: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['g5f4', 'g5h4', 'g5e3']
Minimax scores: [362, 324, 319]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 2430 positions in 11.48 seconds
Selected move: g5e3
Played: Be3
Position after move:
. . k r . b . r
p p p . p . p p
. . p . . p . .
. . . . . . . .
. . . . P . b .
. . . . B . . .
P P P . P P P P
R N . . K B . R

Move 18: MinimaxBlack to move
MinimaxBlack evaluated 1252 positions in 1.07 seconds
Selected move: c8b8
Played: Kb8
Position after move:
. k . r . b . r
p p p . p . p p
. . p . . p . .
. . . . . . . .
. . . . P . b .
. . . . B . . .
P P P . P P P P
R N . . K B . R

Move 19: HybridWhite to move
HybridWhite using hybrid approach (Minimax + MCTS)
Step 1: Finding top 3 moves with minimax...
Top moves from minimax: ['f2f3', 'h2h3', 'b1c3']
Minimax scores: [389, 374, 369]
Step 2: Exploring candidates with 600 MCTS simulations...
HybridWhite evaluated 4558 positions in 12.08 seconds
Selected move: f2f3
Played: f3
Position after move:
. k . r . b . r
p p p . p . p p
. . p . . p . .
. . . . . . . .
. . . . P . b .
. . . . B P . .
P P P . P . P P
R N . . K B . R

Move 20: MinimaxBlack to move
MinimaxBlack evaluated 1201 positions in 0.80 seconds
Selected move: g4h5
Played: Bh5
Position after move:
. k . r . b . r
p p p . p . p p
. . p . . p . .
. . . . . . . b
. . . . P . . .
. . . . B P . .
P P P . P . P P
R N . . K B . R


Game completed after 20 moves
Final position:
. k . r . b . r
p p p . p . p p
. . p . . p . .
. . . . . . . b
. . . . P . . .
. . . . B P . .
P P P . P . P P
R N . . K B . R
Game ended early for demonstration

Game PGN:
[Event "Hybrid vs Minimax Demo"]
[Site "?"]
[Date "????.??.??"]
[Round "?"]
[White "HybridWhite"]
[Black "MinimaxBlack"]
[Result "*"]

1. Nf3 Nf6 2. Ne5 Ne4 3. d3 Nc6 4. Nxc6 dxc6 5. dxe4 Bg4 6. Qd2 Qxd2+ 7. Bxd2 O-O-O 8. Bg5 f6 9. Be3 Kb8 10. f3 Bh5 *

================================================================================
Demonstration completed successfully!

Key Benefits of the Hybrid Approach:
1. Minimax provides quick evaluation of all moves
2. Alpha-beta pruning reduces search space efficiently
3. MCTS explores promising moves more deeply
4. Combination leverages strengths of both approaches
5. Configurable balance between speed and depth