#!/usr/bin/env python3
"""
Debug evaluation function to understand asymmetry in equal positions.
"""

import chess
from chess_bot import ChessBot

def debug_evaluation():
    """Debug the evaluation function for symmetric positions."""
    
    # Create a bot instance
    bot = ChessBot(depth=3)
    
    # Create board and make the moves from the example
    board = chess.Board()
    
    print("=== DEBUGGING EVALUATION ASYMMETRY ===")
    print(f"Starting position evaluation: {bot.evaluate_board(board)}")
    
    # Move 1: Nf3
    board.push(chess.Move.from_uci("g1f3"))
    eval_after_nf3 = bot.evaluate_board(board)
    print(f"After 1. Nf3: {eval_after_nf3}")
    
    # Move 1: ...Nf6
    board.push(chess.Move.from_uci("g8f6"))
    eval_after_nf6 = bot.evaluate_board(board)
    print(f"After 1... Nf6: {eval_after_nf6}")
    
    # Move 2: Nc3
    board.push(chess.Move.from_uci("b1c3"))
    eval_after_nc3 = bot.evaluate_board(board)
    print(f"After 2. Nc3: {eval_after_nc3}")
    
    # Move 2: ...Nc6
    board.push(chess.Move.from_uci("b8c6"))
    eval_after_nc6 = bot.evaluate_board(board)
    print(f"After 2... Nc6: {eval_after_nc6}")
    
    print("\n=== DETAILED ANALYSIS OF FINAL POSITION ===")
    analyze_position_components(bot, board)

def analyze_position_components(bot, board):
    """Analyze individual components of the evaluation."""
    
    print(f"Board FEN: {board.fen()}")
    print(f"Turn: {'White' if board.turn == chess.WHITE else 'Black'}")
    
    # Material evaluation
    material_score = 0
    positional_score = 0
    
    # Check if it's endgame
    is_endgame = bot._is_endgame(board)
    print(f"Is endgame: {is_endgame}")
    
    print("\n--- PIECE-BY-PIECE ANALYSIS ---")
    
    for square in chess.SQUARES:
        piece = board.piece_at(square)
        if piece:
            square_name = chess.square_name(square)
            value = bot.piece_values[piece.piece_type]
            
            # Calculate positional bonus
            positional_bonus = 0
            if piece.piece_type == chess.PAWN:
                table = bot.pawn_table
                positional_bonus = table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
            elif piece.piece_type == chess.KNIGHT:
                table = bot.knight_table
                positional_bonus = table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
            elif piece.piece_type == chess.BISHOP:
                table = bot.bishop_table
                positional_bonus = table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
            elif piece.piece_type == chess.ROOK:
                table = bot.rook_table
                positional_bonus = table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
            elif piece.piece_type == chess.QUEEN:
                table = bot.queen_table
                positional_bonus = table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
            elif piece.piece_type == chess.KING:
                table = bot.king_eg_table if is_endgame else bot.king_mg_table
                positional_bonus = table[square if piece.color == chess.WHITE else chess.square_mirror(square)]
            
            total_value = value + positional_bonus
            color_str = "White" if piece.color == chess.WHITE else "Black"
            
            print(f"{color_str} {piece.symbol().upper()} on {square_name}: {value} + {positional_bonus} = {total_value}")
            
            if piece.color == chess.WHITE:
                material_score += total_value
                positional_score += positional_bonus
            else:
                material_score -= total_value
                positional_score -= positional_bonus
    
    print(f"\nMaterial + Positional score: {material_score}")
    print(f"Positional component only: {positional_score}")
    
    # Mobility evaluation
    original_turn = board.turn
    board.turn = chess.WHITE
    white_mobility = board.legal_moves.count()
    board.turn = chess.BLACK
    black_mobility = board.legal_moves.count()
    board.turn = original_turn
    
    mobility_score = (white_mobility - black_mobility) * 2
    
    print(f"\nMobility analysis:")
    print(f"White legal moves: {white_mobility}")
    print(f"Black legal moves: {black_mobility}")
    print(f"Mobility score: {mobility_score}")
    
    total_evaluation = material_score + mobility_score
    print(f"\nTotal evaluation: {material_score} + {mobility_score} = {total_evaluation}")
    
    # Compare with bot's evaluation
    bot_eval = bot.evaluate_board(board)
    print(f"Bot's evaluation: {bot_eval}")
    print(f"Match: {total_evaluation == bot_eval}")

if __name__ == "__main__":
    debug_evaluation()