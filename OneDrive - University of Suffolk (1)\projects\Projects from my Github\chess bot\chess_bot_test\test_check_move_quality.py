#!/usr/bin/env python3
"""
Test script to verify that check moves with good evaluation are not flagged as poor.
"""

import chess
from tactical_evaluation_test import TacticalEvaluator, MoveQuality

def test_check_move_quality():
    """Test that a check move with good evaluation is not flagged as poor."""
    
    print("Testing check move quality evaluation...")
    
    # Create a position where a Knight can give check
    # This simulates the Nd3+ scenario
    board = chess.Board()
    board.set_fen("rnbqkb1r/pppppppp/5n2/8/3P4/2N5/PPP1PPPP/R1BQKBNR b KQkq - 0 1")
    
    # Black Knight moves to d3 with check: Nd3+
    move = chess.Move.from_uci("f6d3")  # This should give check
    
    # Verify the move gives check
    board_after = board.copy()
    board_after.push(move)
    gives_check = board_after.is_check()
    
    print(f"Move Nd3+ gives check: {gives_check}")
    
    evaluator = TacticalEvaluator()
    
    # Simulate the evaluation scenario from the original issue:
    # - Eval before: +1.11 (White ahead)
    # - Eval after: -4.58 (Black ahead) 
    # - This means Black gained 5.69 pawns worth of advantage
    eval_before = 111  # +1.11 in centipawns
    eval_after = -458  # -4.58 in centipawns
    move_number = 3    # Early in the game
    
    quality, explanation = evaluator.evaluate_move_quality(board, move, eval_before, eval_after, move_number)
    
    print(f"Move quality: {quality.value}")
    print(f"Explanation: {explanation}")
    
    # Calculate the evaluation change from Black's perspective
    eval_change = eval_after - eval_before  # -569 from White's perspective
    eval_change_black = -eval_change  # +569 from Black's perspective
    
    print(f"Evaluation change (Black's perspective): +{eval_change_black/100:.2f} pawns")
    
    # This should NOT be rated as Poor since:
    # 1. It's a check move (tactical shot)
    # 2. It gains huge advantage (***** pawns)
    if quality in [MoveQuality.POOR, MoveQuality.BLUNDER]:
        print("❌ FAILED: Excellent check move incorrectly flagged as poor/blunder")
        return False
    else:
        print("✅ PASSED: Check move with good evaluation correctly not flagged as poor")
        return True

def test_non_check_hanging_piece():
    """Test that non-check moves that leave pieces hanging are still flagged appropriately."""
    
    print("\nTesting non-check hanging piece...")
    
    # Create a position where a piece moves and becomes hanging (no check)
    board = chess.Board()
    board.set_fen("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")
    
    # White Knight to a bad square where it hangs
    move = chess.Move.from_uci("g1h3")  # Nh3, a poor move that hangs the Knight
    
    # Verify this doesn't give check
    board_after = board.copy()
    board_after.push(move)
    gives_check = board_after.is_check()
    
    print(f"Move Nh3 gives check: {gives_check}")
    
    evaluator = TacticalEvaluator()
    
    # Simulate a move that loses material (bad evaluation)
    eval_before = 0
    eval_after = -200  # Lost 2 pawns worth
    move_number = 2
    
    quality, explanation = evaluator.evaluate_move_quality(board, move, eval_before, eval_after, move_number)
    
    print(f"Move quality: {quality.value}")
    print(f"Explanation: {explanation}")
    
    # This SHOULD be flagged as poor since it's not a check and loses material
    if quality in [MoveQuality.POOR, MoveQuality.BLUNDER]:
        print("✅ PASSED: Bad non-check move correctly flagged as poor")
        return True
    else:
        print("❌ FAILED: Bad move not flagged appropriately")
        return False

if __name__ == "__main__":
    print("Testing move quality evaluation fixes...")
    print("=" * 50)
    
    results = []
    results.append(test_check_move_quality())
    results.append(test_non_check_hanging_piece())
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n" + "=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ Move quality evaluation fixes appear to be working!")
    else:
        print("❌ Some issues remain - further debugging needed.")